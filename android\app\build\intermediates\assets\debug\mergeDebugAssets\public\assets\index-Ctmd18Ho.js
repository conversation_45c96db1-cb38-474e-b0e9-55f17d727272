(function(){const be=document.createElement("link").relList;if(be&&be.supports&&be.supports("modulepreload"))return;for(const v of document.querySelectorAll('link[rel="modulepreload"]'))_(v);new MutationObserver(v=>{for(const V of v)if(V.type==="childList")for(const B of V.addedNodes)B.tagName==="LINK"&&B.rel==="modulepreload"&&_(B)}).observe(document,{childList:!0,subtree:!0});function Te(v){const V={};return v.integrity&&(V.integrity=v.integrity),v.referrerPolicy&&(V.referrerPolicy=v.referrerPolicy),v.crossOrigin==="use-credentials"?V.credentials="include":v.crossOrigin==="anonymous"?V.credentials="omit":V.credentials="same-origin",V}function _(v){if(v.ep)return;v.ep=!0;const V=Te(v);fetch(v.href,V)}})();var sf={exports:{}},Ri={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fh;function jm(){if(fh)return Ri;fh=1;var J=Symbol.for("react.transitional.element"),be=Symbol.for("react.fragment");function Te(_,v,V){var B=null;if(V!==void 0&&(B=""+V),v.key!==void 0&&(B=""+v.key),"key"in v){V={};for(var He in v)He!=="key"&&(V[He]=v[He])}else V=v;return v=V.ref,{$$typeof:J,type:_,key:B,ref:v!==void 0?v:null,props:V}}return Ri.Fragment=be,Ri.jsx=Te,Ri.jsxs=Te,Ri}var dh;function zm(){return dh||(dh=1,sf.exports=jm()),sf.exports}var g=zm(),rf={exports:{}},ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oh;function Lm(){if(oh)return ce;oh=1;var J=Symbol.for("react.transitional.element"),be=Symbol.for("react.portal"),Te=Symbol.for("react.fragment"),_=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),V=Symbol.for("react.consumer"),B=Symbol.for("react.context"),He=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),M=Symbol.for("react.memo"),U=Symbol.for("react.lazy"),X=Symbol.iterator;function me(A){return A===null||typeof A!="object"?null:(A=X&&A[X]||A["@@iterator"],typeof A=="function"?A:null)}var ut={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},dt=Object.assign,E={};function Ke(A,w,k){this.props=A,this.context=w,this.refs=E,this.updater=k||ut}Ke.prototype.isReactComponent={},Ke.prototype.setState=function(A,w){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,w,"setState")},Ke.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function ua(){}ua.prototype=Ke.prototype;function Ea(A,w,k){this.props=A,this.context=w,this.refs=E,this.updater=k||ut}var Je=Ea.prototype=new ua;Je.constructor=Ea,dt(Je,Ke.prototype),Je.isPureReactComponent=!0;var Lt=Array.isArray,ge={H:null,A:null,T:null,S:null,V:null},Dt=Object.prototype.hasOwnProperty;function Ot(A,w,k,Z,I,fe){return k=fe.ref,{$$typeof:J,type:A,key:w,ref:k!==void 0?k:null,props:fe}}function Ie(A,w){return Ot(A.type,w,void 0,void 0,void 0,A.props)}function xt(A){return typeof A=="object"&&A!==null&&A.$$typeof===J}function Wt(A){var w={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(k){return w[k]})}var ia=/\/+/g;function Ue(A,w){return typeof A=="object"&&A!==null&&A.key!=null?Wt(""+A.key):w.toString(36)}function za(){}function ca(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(za,za):(A.status="pending",A.then(function(w){A.status==="pending"&&(A.status="fulfilled",A.value=w)},function(w){A.status==="pending"&&(A.status="rejected",A.reason=w)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function it(A,w,k,Z,I){var fe=typeof A;(fe==="undefined"||fe==="boolean")&&(A=null);var ie=!1;if(A===null)ie=!0;else switch(fe){case"bigint":case"string":case"number":ie=!0;break;case"object":switch(A.$$typeof){case J:case be:ie=!0;break;case U:return ie=A._init,it(ie(A._payload),w,k,Z,I)}}if(ie)return I=I(A),ie=Z===""?"."+Ue(A,0):Z,Lt(I)?(k="",ie!=null&&(k=ie.replace(ia,"$&/")+"/"),it(I,w,k,"",function(Ft){return Ft})):I!=null&&(xt(I)&&(I=Ie(I,k+(I.key==null||A&&A.key===I.key?"":(""+I.key).replace(ia,"$&/")+"/")+ie)),w.push(I)),1;ie=0;var vt=Z===""?".":Z+":";if(Lt(A))for(var ye=0;ye<A.length;ye++)Z=A[ye],fe=vt+Ue(Z,ye),ie+=it(Z,w,k,fe,I);else if(ye=me(A),typeof ye=="function")for(A=ye.call(A),ye=0;!(Z=A.next()).done;)Z=Z.value,fe=vt+Ue(Z,ye++),ie+=it(Z,w,k,fe,I);else if(fe==="object"){if(typeof A.then=="function")return it(ca(A),w,k,Z,I);throw w=String(A),Error("Objects are not valid as a React child (found: "+(w==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":w)+"). If you meant to render a collection of children, use an array instead.")}return ie}function G(A,w,k){if(A==null)return A;var Z=[],I=0;return it(A,Z,"","",function(fe){return w.call(k,fe,I++)}),Z}function q(A){if(A._status===-1){var w=A._result;w=w(),w.then(function(k){(A._status===0||A._status===-1)&&(A._status=1,A._result=k)},function(k){(A._status===0||A._status===-1)&&(A._status=2,A._result=k)}),A._status===-1&&(A._status=0,A._result=w)}if(A._status===1)return A._result.default;throw A._result}var ee=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var w=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(w))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function le(){}return ce.Children={map:G,forEach:function(A,w,k){G(A,function(){w.apply(this,arguments)},k)},count:function(A){var w=0;return G(A,function(){w++}),w},toArray:function(A){return G(A,function(w){return w})||[]},only:function(A){if(!xt(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},ce.Component=Ke,ce.Fragment=Te,ce.Profiler=v,ce.PureComponent=Ea,ce.StrictMode=_,ce.Suspense=$,ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ge,ce.__COMPILER_RUNTIME={__proto__:null,c:function(A){return ge.H.useMemoCache(A)}},ce.cache=function(A){return function(){return A.apply(null,arguments)}},ce.cloneElement=function(A,w,k){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var Z=dt({},A.props),I=A.key,fe=void 0;if(w!=null)for(ie in w.ref!==void 0&&(fe=void 0),w.key!==void 0&&(I=""+w.key),w)!Dt.call(w,ie)||ie==="key"||ie==="__self"||ie==="__source"||ie==="ref"&&w.ref===void 0||(Z[ie]=w[ie]);var ie=arguments.length-2;if(ie===1)Z.children=k;else if(1<ie){for(var vt=Array(ie),ye=0;ye<ie;ye++)vt[ye]=arguments[ye+2];Z.children=vt}return Ot(A.type,I,void 0,void 0,fe,Z)},ce.createContext=function(A){return A={$$typeof:B,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:V,_context:A},A},ce.createElement=function(A,w,k){var Z,I={},fe=null;if(w!=null)for(Z in w.key!==void 0&&(fe=""+w.key),w)Dt.call(w,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(I[Z]=w[Z]);var ie=arguments.length-2;if(ie===1)I.children=k;else if(1<ie){for(var vt=Array(ie),ye=0;ye<ie;ye++)vt[ye]=arguments[ye+2];I.children=vt}if(A&&A.defaultProps)for(Z in ie=A.defaultProps,ie)I[Z]===void 0&&(I[Z]=ie[Z]);return Ot(A,fe,void 0,void 0,null,I)},ce.createRef=function(){return{current:null}},ce.forwardRef=function(A){return{$$typeof:He,render:A}},ce.isValidElement=xt,ce.lazy=function(A){return{$$typeof:U,_payload:{_status:-1,_result:A},_init:q}},ce.memo=function(A,w){return{$$typeof:M,type:A,compare:w===void 0?null:w}},ce.startTransition=function(A){var w=ge.T,k={};ge.T=k;try{var Z=A(),I=ge.S;I!==null&&I(k,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(le,ee)}catch(fe){ee(fe)}finally{ge.T=w}},ce.unstable_useCacheRefresh=function(){return ge.H.useCacheRefresh()},ce.use=function(A){return ge.H.use(A)},ce.useActionState=function(A,w,k){return ge.H.useActionState(A,w,k)},ce.useCallback=function(A,w){return ge.H.useCallback(A,w)},ce.useContext=function(A){return ge.H.useContext(A)},ce.useDebugValue=function(){},ce.useDeferredValue=function(A,w){return ge.H.useDeferredValue(A,w)},ce.useEffect=function(A,w,k){var Z=ge.H;if(typeof k=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(A,w)},ce.useId=function(){return ge.H.useId()},ce.useImperativeHandle=function(A,w,k){return ge.H.useImperativeHandle(A,w,k)},ce.useInsertionEffect=function(A,w){return ge.H.useInsertionEffect(A,w)},ce.useLayoutEffect=function(A,w){return ge.H.useLayoutEffect(A,w)},ce.useMemo=function(A,w){return ge.H.useMemo(A,w)},ce.useOptimistic=function(A,w){return ge.H.useOptimistic(A,w)},ce.useReducer=function(A,w,k){return ge.H.useReducer(A,w,k)},ce.useRef=function(A){return ge.H.useRef(A)},ce.useState=function(A){return ge.H.useState(A)},ce.useSyncExternalStore=function(A,w,k){return ge.H.useSyncExternalStore(A,w,k)},ce.useTransition=function(){return ge.H.useTransition()},ce.version="19.1.0",ce}var hh;function yf(){return hh||(hh=1,rf.exports=Lm()),rf.exports}var Y=yf(),ff={exports:{}},Oi={},df={exports:{}},of={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mh;function xm(){return mh||(mh=1,function(J){function be(G,q){var ee=G.length;G.push(q);e:for(;0<ee;){var le=ee-1>>>1,A=G[le];if(0<v(A,q))G[le]=q,G[ee]=A,ee=le;else break e}}function Te(G){return G.length===0?null:G[0]}function _(G){if(G.length===0)return null;var q=G[0],ee=G.pop();if(ee!==q){G[0]=ee;e:for(var le=0,A=G.length,w=A>>>1;le<w;){var k=2*(le+1)-1,Z=G[k],I=k+1,fe=G[I];if(0>v(Z,ee))I<A&&0>v(fe,Z)?(G[le]=fe,G[I]=ee,le=I):(G[le]=Z,G[k]=ee,le=k);else if(I<A&&0>v(fe,ee))G[le]=fe,G[I]=ee,le=I;else break e}}return q}function v(G,q){var ee=G.sortIndex-q.sortIndex;return ee!==0?ee:G.id-q.id}if(J.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var V=performance;J.unstable_now=function(){return V.now()}}else{var B=Date,He=B.now();J.unstable_now=function(){return B.now()-He}}var $=[],M=[],U=1,X=null,me=3,ut=!1,dt=!1,E=!1,Ke=!1,ua=typeof setTimeout=="function"?setTimeout:null,Ea=typeof clearTimeout=="function"?clearTimeout:null,Je=typeof setImmediate<"u"?setImmediate:null;function Lt(G){for(var q=Te(M);q!==null;){if(q.callback===null)_(M);else if(q.startTime<=G)_(M),q.sortIndex=q.expirationTime,be($,q);else break;q=Te(M)}}function ge(G){if(E=!1,Lt(G),!dt)if(Te($)!==null)dt=!0,Dt||(Dt=!0,Ue());else{var q=Te(M);q!==null&&it(ge,q.startTime-G)}}var Dt=!1,Ot=-1,Ie=5,xt=-1;function Wt(){return Ke?!0:!(J.unstable_now()-xt<Ie)}function ia(){if(Ke=!1,Dt){var G=J.unstable_now();xt=G;var q=!0;try{e:{dt=!1,E&&(E=!1,Ea(Ot),Ot=-1),ut=!0;var ee=me;try{t:{for(Lt(G),X=Te($);X!==null&&!(X.expirationTime>G&&Wt());){var le=X.callback;if(typeof le=="function"){X.callback=null,me=X.priorityLevel;var A=le(X.expirationTime<=G);if(G=J.unstable_now(),typeof A=="function"){X.callback=A,Lt(G),q=!0;break t}X===Te($)&&_($),Lt(G)}else _($);X=Te($)}if(X!==null)q=!0;else{var w=Te(M);w!==null&&it(ge,w.startTime-G),q=!1}}break e}finally{X=null,me=ee,ut=!1}q=void 0}}finally{q?Ue():Dt=!1}}}var Ue;if(typeof Je=="function")Ue=function(){Je(ia)};else if(typeof MessageChannel<"u"){var za=new MessageChannel,ca=za.port2;za.port1.onmessage=ia,Ue=function(){ca.postMessage(null)}}else Ue=function(){ua(ia,0)};function it(G,q){Ot=ua(function(){G(J.unstable_now())},q)}J.unstable_IdlePriority=5,J.unstable_ImmediatePriority=1,J.unstable_LowPriority=4,J.unstable_NormalPriority=3,J.unstable_Profiling=null,J.unstable_UserBlockingPriority=2,J.unstable_cancelCallback=function(G){G.callback=null},J.unstable_forceFrameRate=function(G){0>G||125<G?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Ie=0<G?Math.floor(1e3/G):5},J.unstable_getCurrentPriorityLevel=function(){return me},J.unstable_next=function(G){switch(me){case 1:case 2:case 3:var q=3;break;default:q=me}var ee=me;me=q;try{return G()}finally{me=ee}},J.unstable_requestPaint=function(){Ke=!0},J.unstable_runWithPriority=function(G,q){switch(G){case 1:case 2:case 3:case 4:case 5:break;default:G=3}var ee=me;me=G;try{return q()}finally{me=ee}},J.unstable_scheduleCallback=function(G,q,ee){var le=J.unstable_now();switch(typeof ee=="object"&&ee!==null?(ee=ee.delay,ee=typeof ee=="number"&&0<ee?le+ee:le):ee=le,G){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=ee+A,G={id:U++,callback:q,priorityLevel:G,startTime:ee,expirationTime:A,sortIndex:-1},ee>le?(G.sortIndex=ee,be(M,G),Te($)===null&&G===Te(M)&&(E?(Ea(Ot),Ot=-1):E=!0,it(ge,ee-le))):(G.sortIndex=A,be($,G),dt||ut||(dt=!0,Dt||(Dt=!0,Ue()))),G},J.unstable_shouldYield=Wt,J.unstable_wrapCallback=function(G){var q=me;return function(){var ee=me;me=q;try{return G.apply(this,arguments)}finally{me=ee}}}}(of)),of}var yh;function qm(){return yh||(yh=1,df.exports=xm()),df.exports}var hf={exports:{}},bt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gh;function wm(){if(gh)return bt;gh=1;var J=yf();function be($){var M="https://react.dev/errors/"+$;if(1<arguments.length){M+="?args[]="+encodeURIComponent(arguments[1]);for(var U=2;U<arguments.length;U++)M+="&args[]="+encodeURIComponent(arguments[U])}return"Minified React error #"+$+"; visit "+M+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Te(){}var _={d:{f:Te,r:function(){throw Error(be(522))},D:Te,C:Te,L:Te,m:Te,X:Te,S:Te,M:Te},p:0,findDOMNode:null},v=Symbol.for("react.portal");function V($,M,U){var X=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:v,key:X==null?null:""+X,children:$,containerInfo:M,implementation:U}}var B=J.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function He($,M){if($==="font")return"";if(typeof M=="string")return M==="use-credentials"?M:""}return bt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=_,bt.createPortal=function($,M){var U=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!M||M.nodeType!==1&&M.nodeType!==9&&M.nodeType!==11)throw Error(be(299));return V($,M,null,U)},bt.flushSync=function($){var M=B.T,U=_.p;try{if(B.T=null,_.p=2,$)return $()}finally{B.T=M,_.p=U,_.d.f()}},bt.preconnect=function($,M){typeof $=="string"&&(M?(M=M.crossOrigin,M=typeof M=="string"?M==="use-credentials"?M:"":void 0):M=null,_.d.C($,M))},bt.prefetchDNS=function($){typeof $=="string"&&_.d.D($)},bt.preinit=function($,M){if(typeof $=="string"&&M&&typeof M.as=="string"){var U=M.as,X=He(U,M.crossOrigin),me=typeof M.integrity=="string"?M.integrity:void 0,ut=typeof M.fetchPriority=="string"?M.fetchPriority:void 0;U==="style"?_.d.S($,typeof M.precedence=="string"?M.precedence:void 0,{crossOrigin:X,integrity:me,fetchPriority:ut}):U==="script"&&_.d.X($,{crossOrigin:X,integrity:me,fetchPriority:ut,nonce:typeof M.nonce=="string"?M.nonce:void 0})}},bt.preinitModule=function($,M){if(typeof $=="string")if(typeof M=="object"&&M!==null){if(M.as==null||M.as==="script"){var U=He(M.as,M.crossOrigin);_.d.M($,{crossOrigin:U,integrity:typeof M.integrity=="string"?M.integrity:void 0,nonce:typeof M.nonce=="string"?M.nonce:void 0})}}else M==null&&_.d.M($)},bt.preload=function($,M){if(typeof $=="string"&&typeof M=="object"&&M!==null&&typeof M.as=="string"){var U=M.as,X=He(U,M.crossOrigin);_.d.L($,U,{crossOrigin:X,integrity:typeof M.integrity=="string"?M.integrity:void 0,nonce:typeof M.nonce=="string"?M.nonce:void 0,type:typeof M.type=="string"?M.type:void 0,fetchPriority:typeof M.fetchPriority=="string"?M.fetchPriority:void 0,referrerPolicy:typeof M.referrerPolicy=="string"?M.referrerPolicy:void 0,imageSrcSet:typeof M.imageSrcSet=="string"?M.imageSrcSet:void 0,imageSizes:typeof M.imageSizes=="string"?M.imageSizes:void 0,media:typeof M.media=="string"?M.media:void 0})}},bt.preloadModule=function($,M){if(typeof $=="string")if(M){var U=He(M.as,M.crossOrigin);_.d.m($,{as:typeof M.as=="string"&&M.as!=="script"?M.as:void 0,crossOrigin:U,integrity:typeof M.integrity=="string"?M.integrity:void 0})}else _.d.m($)},bt.requestFormReset=function($){_.d.r($)},bt.unstable_batchedUpdates=function($,M){return $(M)},bt.useFormState=function($,M,U){return B.H.useFormState($,M,U)},bt.useFormStatus=function(){return B.H.useHostTransitionStatus()},bt.version="19.1.0",bt}var vh;function Ym(){if(vh)return hf.exports;vh=1;function J(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(J)}catch(be){console.error(be)}}return J(),hf.exports=wm(),hf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sh;function Vm(){if(Sh)return Oi;Sh=1;var J=qm(),be=yf(),Te=Ym();function _(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function v(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function V(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function B(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function He(e){if(V(e)!==e)throw Error(_(188))}function $(e){var t=e.alternate;if(!t){if(t=V(e),t===null)throw Error(_(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var u=n.alternate;if(u===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===a)return He(n),e;if(u===l)return He(n),t;u=u.sibling}throw Error(_(188))}if(a.return!==l.return)a=n,l=u;else{for(var r=!1,o=n.child;o;){if(o===a){r=!0,a=n,l=u;break}if(o===l){r=!0,l=n,a=u;break}o=o.sibling}if(!r){for(o=u.child;o;){if(o===a){r=!0,a=u,l=n;break}if(o===l){r=!0,l=u,a=n;break}o=o.sibling}if(!r)throw Error(_(189))}}if(a.alternate!==l)throw Error(_(190))}if(a.tag!==3)throw Error(_(188));return a.stateNode.current===a?e:t}function M(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=M(e),t!==null)return t;e=e.sibling}return null}var U=Object.assign,X=Symbol.for("react.element"),me=Symbol.for("react.transitional.element"),ut=Symbol.for("react.portal"),dt=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),Ke=Symbol.for("react.profiler"),ua=Symbol.for("react.provider"),Ea=Symbol.for("react.consumer"),Je=Symbol.for("react.context"),Lt=Symbol.for("react.forward_ref"),ge=Symbol.for("react.suspense"),Dt=Symbol.for("react.suspense_list"),Ot=Symbol.for("react.memo"),Ie=Symbol.for("react.lazy"),xt=Symbol.for("react.activity"),Wt=Symbol.for("react.memo_cache_sentinel"),ia=Symbol.iterator;function Ue(e){return e===null||typeof e!="object"?null:(e=ia&&e[ia]||e["@@iterator"],typeof e=="function"?e:null)}var za=Symbol.for("react.client.reference");function ca(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===za?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case dt:return"Fragment";case Ke:return"Profiler";case E:return"StrictMode";case ge:return"Suspense";case Dt:return"SuspenseList";case xt:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case ut:return"Portal";case Je:return(e.displayName||"Context")+".Provider";case Ea:return(e._context.displayName||"Context")+".Consumer";case Lt:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ot:return t=e.displayName||null,t!==null?t:ca(e.type)||"Memo";case Ie:t=e._payload,e=e._init;try{return ca(e(t))}catch{}}return null}var it=Array.isArray,G=be.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=Te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ee={pending:!1,data:null,method:null,action:null},le=[],A=-1;function w(e){return{current:e}}function k(e){0>A||(e.current=le[A],le[A]=null,A--)}function Z(e,t){A++,le[A]=e.current,e.current=t}var I=w(null),fe=w(null),ie=w(null),vt=w(null);function ye(e,t){switch(Z(ie,t),Z(fe,e),Z(I,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?xo(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=xo(t),e=qo(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}k(I),Z(I,e)}function Ft(){k(I),k(fe),k(ie)}function Au(e){e.memoizedState!==null&&Z(vt,e);var t=I.current,a=qo(t,e.type);t!==a&&(Z(fe,e),Z(I,a))}function cl(e){fe.current===e&&(k(I),k(fe)),vt.current===e&&(k(vt),Ti._currentValue=ee)}var sl=Object.prototype.hasOwnProperty,Fl=J.unstable_scheduleCallback,Cn=J.unstable_cancelCallback,La=J.unstable_shouldYield,sa=J.unstable_requestPaint,_t=J.unstable_now,Pl=J.unstable_getCurrentPriorityLevel,Rn=J.unstable_ImmediatePriority,Mt=J.unstable_UserBlockingPriority,pa=J.unstable_NormalPriority,Mi=J.unstable_LowPriority,On=J.unstable_IdlePriority,Eu=J.log,Bi=J.unstable_setDisableYieldValue,Il=null,je=null;function ra(e){if(typeof Eu=="function"&&Bi(e),je&&typeof je.setStrictMode=="function")try{je.setStrictMode(Il,e)}catch{}}var St=Math.clz32?Math.clz32:_n,as=Math.log,pu=Math.LN2;function _n(e){return e>>>=0,e===0?32:31-(as(e)/pu|0)|0}var en=256,tn=4194304;function Ta(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Mn(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,u=e.suspendedLanes,r=e.pingedLanes;e=e.warmLanes;var o=l&134217727;return o!==0?(l=o&~u,l!==0?n=Ta(l):(r&=o,r!==0?n=Ta(r):a||(a=o&~e,a!==0&&(n=Ta(a))))):(o=l&~u,o!==0?n=Ta(o):r!==0?n=Ta(r):a||(a=l&~e,a!==0&&(n=Ta(a)))),n===0?0:t!==0&&t!==n&&(t&u)===0&&(u=n&-n,a=t&-t,u>=a||u===32&&(a&4194048)!==0)?t:n}function xa(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function Na(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Tu(){var e=en;return en<<=1,(en&4194048)===0&&(en=256),e}function Nu(){var e=tn;return tn<<=1,(tn&62914560)===0&&(tn=4194304),e}function Bn(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function rl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function an(e,t,a,l,n,u){var r=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var o=e.entanglements,S=e.expirationTimes,D=e.hiddenUpdates;for(a=r&~a;0<a;){var H=31-St(a),x=1<<H;o[H]=0,S[H]=-1;var R=D[H];if(R!==null)for(D[H]=null,H=0;H<R.length;H++){var O=R[H];O!==null&&(O.lane&=-536870913)}a&=~x}l!==0&&Gi(e,l,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(r&~t))}function Gi(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-St(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function ln(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-St(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function bu(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Du(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function qa(){var e=q.p;return e!==0?e:(e=window.event,e===void 0?32:nh(e.type))}function nn(e,t){var a=q.p;try{return q.p=e,t()}finally{q.p=a}}var ze=Math.random().toString(36).slice(2),Ye="__reactFiber$"+ze,se="__reactProps$"+ze,fa="__reactContainer$"+ze,qe="__reactEvents$"+ze,un="__reactListeners$"+ze,At="__reactHandles$"+ze,Bt="__reactResources$"+ze,et="__reactMarker$"+ze;function qt(e){delete e[Ye],delete e[se],delete e[qe],delete e[un],delete e[At]}function Pt(e){var t=e[Ye];if(t)return t;for(var a=e.parentNode;a;){if(t=a[fa]||a[Ye]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Xo(e);e!==null;){if(a=e[Ye])return a;e=Xo(e)}return t}e=a,a=e.parentNode}return null}function da(e){if(e=e[Ye]||e[fa]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function ba(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(_(33))}function fl(e){var t=e[Bt];return t||(t=e[Bt]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function Ve(e){e[et]=!0}var cn=new Set,Gn={};function wa(e,t){Da(e,t),Da(e+"Capture",t)}function Da(e,t){for(Gn[e]=t,e=0;e<t.length;e++)cn.add(t[e])}var oa=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Hn={},ha={};function Un(e){return sl.call(ha,e)?!0:sl.call(Hn,e)?!1:oa.test(e)?ha[e]=!0:(Hn[e]=!0,!1)}function dl(e,t,a){if(Un(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function wt(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function ma(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var jn,Hi;function Ya(e){if(jn===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);jn=t&&t[1]||"",Hi=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+jn+e+Hi}var Va=!1;function zn(e,t){if(!e||Va)return"";Va=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var x=function(){throw Error()};if(Object.defineProperty(x.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(x,[])}catch(O){var R=O}Reflect.construct(e,[],x)}else{try{x.call()}catch(O){R=O}e.call(x.prototype)}}else{try{throw Error()}catch(O){R=O}(x=e())&&typeof x.catch=="function"&&x.catch(function(){})}}catch(O){if(O&&R&&typeof O.stack=="string")return[O.stack,R.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),r=u[0],o=u[1];if(r&&o){var S=r.split(`
`),D=o.split(`
`);for(n=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;n<D.length&&!D[n].includes("DetermineComponentFrameRoot");)n++;if(l===S.length||n===D.length)for(l=S.length-1,n=D.length-1;1<=l&&0<=n&&S[l]!==D[n];)n--;for(;1<=l&&0<=n;l--,n--)if(S[l]!==D[n]){if(l!==1||n!==1)do if(l--,n--,0>n||S[l]!==D[n]){var H=`
`+S[l].replace(" at new "," at ");return e.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",e.displayName)),H}while(1<=l&&0<=n);break}}}finally{Va=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Ya(a):""}function ls(e){switch(e.tag){case 26:case 27:case 5:return Ya(e.type);case 16:return Ya("Lazy");case 13:return Ya("Suspense");case 19:return Ya("SuspenseList");case 0:case 15:return zn(e.type,!1);case 11:return zn(e.type.render,!1);case 1:return zn(e.type,!0);case 31:return Ya("Activity");default:return""}}function Ln(e){try{var t="";do t+=ls(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function ot(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function xn(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function Ca(e){var t=xn(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,u=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(r){l=""+r,u.call(this,r)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(r){l=""+r},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function ol(e){e._valueTracker||(e._valueTracker=Ca(e))}function Ui(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=xn(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function hl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var qn=/[\n"\\]/g;function Et(e){return e.replace(qn,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function Cu(e,t,a,l,n,u,r,o){e.name="",r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?e.type=r:e.removeAttribute("type"),t!=null?r==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+ot(t)):e.value!==""+ot(t)&&(e.value=""+ot(t)):r!=="submit"&&r!=="reset"||e.removeAttribute("value"),t!=null?ml(e,r,ot(t)):a!=null?ml(e,r,ot(a)):l!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.name=""+ot(o):e.removeAttribute("name")}function wn(e,t,a,l,n,u,r,o){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||a!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;a=a!=null?""+ot(a):"",t=t!=null?""+ot(t):a,o||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=o?e.checked:!!l,e.defaultChecked=!!l,r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.name=r)}function ml(e,t,a){t==="number"&&hl(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Ra(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+ot(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function ji(e,t,a){if(t!=null&&(t=""+ot(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+ot(a):""}function Yn(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(_(92));if(it(l)){if(1<l.length)throw Error(_(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=ot(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function yl(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var Ru=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function zi(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||Ru.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function Vn(e,t,a){if(t!=null&&typeof t!="object")throw Error(_(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&zi(e,n,l)}else for(var u in t)t.hasOwnProperty(u)&&zi(e,u,t[u])}function Yt(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Ou=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Xa=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function gl(e){return Xa.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var vl=null;function Sl(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var We=null,Oa=null;function sn(e){var t=da(e);if(t&&(e=t.stateNode)){var a=e[se]||null;e:switch(e=t.stateNode,t.type){case"input":if(Cu(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+Et(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[se]||null;if(!n)throw Error(_(90));Cu(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Ui(l)}break e;case"textarea":ji(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Ra(e,!!a.multiple,t,!1)}}}var Al=!1;function _u(e,t,a){if(Al)return e(t,a);Al=!0;try{var l=e(t);return l}finally{if(Al=!1,(We!==null||Oa!==null)&&(jc(),We&&(t=We,e=Oa,Oa=We=null,sn(t),e)))for(t=0;t<e.length;t++)sn(e[t])}}function El(e,t){var a=e.stateNode;if(a===null)return null;var l=a[se]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(_(231,t,typeof a));return a}var ya=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Mu=!1;if(ya)try{var Ee={};Object.defineProperty(Ee,"passive",{get:function(){Mu=!0}}),window.addEventListener("test",Ee,Ee),window.removeEventListener("test",Ee,Ee)}catch{Mu=!1}var tt=null,Bu=null,Xn=null;function Li(){if(Xn)return Xn;var e,t=Bu,a=t.length,l,n="value"in tt?tt.value:tt.textContent,u=n.length;for(e=0;e<a&&t[e]===n[e];e++);var r=a-e;for(l=1;l<=r&&t[a-l]===n[u-l];l++);return Xn=n.slice(e,1<l?1-l:void 0)}function ga(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Vt(){return!0}function $a(){return!1}function at(e){function t(a,l,n,u,r){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=u,this.target=r,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(a=e[o],this[o]=a?a(u):u[o]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Vt:$a,this.isPropagationStopped=$a,this}return U(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Vt)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Vt)},persist:function(){},isPersistent:Vt}),t}var Le={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Xe=at(Le),pt=U({},Le,{view:0,detail:0}),It=at(pt),ht,_a,rn,fn=U({},pt,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:Hu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==rn&&(rn&&e.type==="mousemove"?(ht=e.screenX-rn.screenX,_a=e.screenY-rn.screenY):_a=ht=0,rn=e),ht)},movementY:function(e){return"movementY"in e?e.movementY:_a}}),xi=at(fn),pl=U({},fn,{dataTransfer:0}),Tl=at(pl),Nl=U({},pt,{relatedTarget:0}),Gu=at(Nl),ns=U({},Le,{animationName:0,elapsedTime:0,pseudoElement:0}),us=at(ns),qi=U({},Le,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),wi=at(qi),Yi=U({},Le,{data:0}),bl=at(Yi),dn={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Dl={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},is={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cs(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=is[e])?!!t[e]:!1}function Hu(){return cs}var ss=U({},pt,{key:function(e){if(e.key){var t=dn[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=ga(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Dl[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:Hu,charCode:function(e){return e.type==="keypress"?ga(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?ga(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),rs=at(ss),fs=U({},fn,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Vi=at(fs),ds=U({},pt,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:Hu}),Xi=at(ds),os=U({},Le,{propertyName:0,elapsedTime:0,pseudoElement:0}),$i=at(os),Uu=U({},fn,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),hs=at(Uu),ju=U({},Le,{newState:0,oldState:0}),ms=at(ju),ys=[9,13,27,32],zu=ya&&"CompositionEvent"in window,Cl=null;ya&&"documentMode"in document&&(Cl=document.documentMode);var gs=ya&&"TextEvent"in window&&!Cl,Lu=ya&&(!zu||Cl&&8<Cl&&11>=Cl),Qi=" ",ki=!1;function Zi(e,t){switch(e){case"keyup":return ys.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Ki(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Rl=!1;function vs(e,t){switch(e){case"compositionend":return Ki(t);case"keypress":return t.which!==32?null:(ki=!0,Qi);case"textInput":return e=t.data,e===Qi&&ki?null:e;default:return null}}function Ss(e,t){if(Rl)return e==="compositionend"||!zu&&Zi(e,t)?(e=Li(),Xn=Bu=tt=null,Rl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return Lu&&t.locale!=="ko"?null:t.data;default:return null}}var As={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Ji(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!As[e.type]:t==="textarea"}function Wi(e,t,a,l){We?Oa?Oa.push(l):Oa=[l]:We=l,t=Yc(t,"onChange"),0<t.length&&(a=new Xe("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var Ol=null,va=null;function Fi(e){Ho(e,0)}function Qa(e){var t=ba(e);if(Ui(t))return e}function Pi(e,t){if(e==="change")return t}var Ii=!1;if(ya){var Gt;if(ya){var $n="oninput"in document;if(!$n){var xu=document.createElement("div");xu.setAttribute("oninput","return;"),$n=typeof xu.oninput=="function"}Gt=$n}else Gt=!1;Ii=Gt&&(!document.documentMode||9<document.documentMode)}function ec(){Ol&&(Ol.detachEvent("onpropertychange",tc),va=Ol=null)}function tc(e){if(e.propertyName==="value"&&Qa(va)){var t=[];Wi(t,va,e,Sl(e)),_u(Fi,t)}}function Es(e,t,a){e==="focusin"?(ec(),Ol=t,va=a,Ol.attachEvent("onpropertychange",tc)):e==="focusout"&&ec()}function ps(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Qa(va)}function Ts(e,t){if(e==="click")return Qa(t)}function Ns(e,t){if(e==="input"||e==="change")return Qa(t)}function bs(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var Tt=typeof Object.is=="function"?Object.is:bs;function ct(e,t){if(Tt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!sl.call(t,n)||!Tt(e[n],t[n]))return!1}return!0}function qu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function wu(e,t){var a=qu(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=qu(a)}}function Yu(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Yu(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function Vu(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=hl(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=hl(e.document)}return t}function Qn(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var ac=ya&&"documentMode"in document&&11>=document.documentMode,ka=null,kn=null,_l=null,Zn=!1;function Xu(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Zn||ka==null||ka!==hl(l)||(l=ka,"selectionStart"in l&&Qn(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),_l&&ct(_l,l)||(_l=l,l=Yc(kn,"onSelect"),0<l.length&&(t=new Xe("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=ka)))}function Ma(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Za={animationend:Ma("Animation","AnimationEnd"),animationiteration:Ma("Animation","AnimationIteration"),animationstart:Ma("Animation","AnimationStart"),transitionrun:Ma("Transition","TransitionRun"),transitionstart:Ma("Transition","TransitionStart"),transitioncancel:Ma("Transition","TransitionCancel"),transitionend:Ma("Transition","TransitionEnd")},i={},c={};ya&&(c=document.createElement("div").style,"AnimationEvent"in window||(delete Za.animationend.animation,delete Za.animationiteration.animation,delete Za.animationstart.animation),"TransitionEvent"in window||delete Za.transitionend.transition);function s(e){if(i[e])return i[e];if(!Za[e])return e;var t=Za[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in c)return i[e]=t[a];return e}var d=s("animationend"),h=s("animationiteration"),m=s("animationstart"),y=s("transitionrun"),T=s("transitionstart"),C=s("transitioncancel"),j=s("transitionend"),Q=new Map,F="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");F.push("scrollEnd");function K(e,t){Q.set(e,t),wa(t,[e])}var ue=new WeakMap;function ve(e,t){if(typeof e=="object"&&e!==null){var a=ue.get(e);return a!==void 0?a:(t={value:e,source:t,stack:Ln(t)},ue.set(e,t),t)}return{value:e,source:t,stack:Ln(t)}}var Me=[],Ht=0,on=0;function lc(){for(var e=Ht,t=on=Ht=0;t<e;){var a=Me[t];Me[t++]=null;var l=Me[t];Me[t++]=null;var n=Me[t];Me[t++]=null;var u=Me[t];if(Me[t++]=null,l!==null&&n!==null){var r=l.pending;r===null?n.next=n:(n.next=r.next,r.next=n),l.pending=n}u!==0&&gf(a,n,u)}}function nc(e,t,a,l){Me[Ht++]=e,Me[Ht++]=t,Me[Ht++]=a,Me[Ht++]=l,on|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function Ds(e,t,a,l){return nc(e,t,a,l),uc(e)}function Kn(e,t){return nc(e,null,null,t),uc(e)}function gf(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,u=e.return;u!==null;)u.childLanes|=a,l=u.alternate,l!==null&&(l.childLanes|=a),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&t!==null&&(n=31-St(a),e=u.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),u):null}function uc(e){if(50<mi)throw mi=0,Br=null,Error(_(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Jn={};function Eh(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Xt(e,t,a,l){return new Eh(e,t,a,l)}function Cs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Ka(e,t){var a=e.alternate;return a===null?(a=Xt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function vf(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function ic(e,t,a,l,n,u){var r=0;if(l=e,typeof e=="function")Cs(e)&&(r=1);else if(typeof e=="string")r=Tm(e,a,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case xt:return e=Xt(31,a,t,n),e.elementType=xt,e.lanes=u,e;case dt:return hn(a.children,n,u,t);case E:r=8,n|=24;break;case Ke:return e=Xt(12,a,t,n|2),e.elementType=Ke,e.lanes=u,e;case ge:return e=Xt(13,a,t,n),e.elementType=ge,e.lanes=u,e;case Dt:return e=Xt(19,a,t,n),e.elementType=Dt,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ua:case Je:r=10;break e;case Ea:r=9;break e;case Lt:r=11;break e;case Ot:r=14;break e;case Ie:r=16,l=null;break e}r=29,a=Error(_(130,e===null?"null":typeof e,"")),l=null}return t=Xt(r,a,t,n),t.elementType=e,t.type=l,t.lanes=u,t}function hn(e,t,a,l){return e=Xt(7,e,l,t),e.lanes=a,e}function Rs(e,t,a){return e=Xt(6,e,null,t),e.lanes=a,e}function Os(e,t,a){return t=Xt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var Wn=[],Fn=0,cc=null,sc=0,ea=[],ta=0,mn=null,Ja=1,Wa="";function yn(e,t){Wn[Fn++]=sc,Wn[Fn++]=cc,cc=e,sc=t}function Sf(e,t,a){ea[ta++]=Ja,ea[ta++]=Wa,ea[ta++]=mn,mn=e;var l=Ja;e=Wa;var n=32-St(l)-1;l&=~(1<<n),a+=1;var u=32-St(t)+n;if(30<u){var r=n-n%5;u=(l&(1<<r)-1).toString(32),l>>=r,n-=r,Ja=1<<32-St(t)+n|a<<n|l,Wa=u+e}else Ja=1<<u|a<<n|l,Wa=e}function _s(e){e.return!==null&&(yn(e,1),Sf(e,1,0))}function Ms(e){for(;e===cc;)cc=Wn[--Fn],Wn[Fn]=null,sc=Wn[--Fn],Wn[Fn]=null;for(;e===mn;)mn=ea[--ta],ea[ta]=null,Wa=ea[--ta],ea[ta]=null,Ja=ea[--ta],ea[ta]=null}var Ct=null,$e=null,Ne=!1,gn=null,Ba=!1,Bs=Error(_(519));function vn(e){var t=Error(_(418,""));throw ku(ve(t,e)),Bs}function Af(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[Ye]=e,t[se]=l,a){case"dialog":he("cancel",t),he("close",t);break;case"iframe":case"object":case"embed":he("load",t);break;case"video":case"audio":for(a=0;a<gi.length;a++)he(gi[a],t);break;case"source":he("error",t);break;case"img":case"image":case"link":he("error",t),he("load",t);break;case"details":he("toggle",t);break;case"input":he("invalid",t),wn(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),ol(t);break;case"select":he("invalid",t);break;case"textarea":he("invalid",t),Yn(t,l.value,l.defaultValue,l.children),ol(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||Lo(t.textContent,a)?(l.popover!=null&&(he("beforetoggle",t),he("toggle",t)),l.onScroll!=null&&he("scroll",t),l.onScrollEnd!=null&&he("scrollend",t),l.onClick!=null&&(t.onclick=Vc),t=!0):t=!1,t||vn(e)}function Ef(e){for(Ct=e.return;Ct;)switch(Ct.tag){case 5:case 13:Ba=!1;return;case 27:case 3:Ba=!0;return;default:Ct=Ct.return}}function $u(e){if(e!==Ct)return!1;if(!Ne)return Ef(e),Ne=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Zr(e.type,e.memoizedProps)),a=!a),a&&$e&&vn(e),Ef(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){$e=Aa(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}$e=null}}else t===27?(t=$e,Ql(e.type)?(e=Fr,Fr=null,$e=e):$e=t):$e=Ct?Aa(e.stateNode.nextSibling):null;return!0}function Qu(){$e=Ct=null,Ne=!1}function pf(){var e=gn;return e!==null&&(zt===null?zt=e:zt.push.apply(zt,e),gn=null),e}function ku(e){gn===null?gn=[e]:gn.push(e)}var Gs=w(null),Sn=null,Fa=null;function Ml(e,t,a){Z(Gs,t._currentValue),t._currentValue=a}function Pa(e){e._currentValue=Gs.current,k(Gs)}function Hs(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function Us(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var r=n.child;u=u.firstContext;e:for(;u!==null;){var o=u;u=n;for(var S=0;S<t.length;S++)if(o.context===t[S]){u.lanes|=a,o=u.alternate,o!==null&&(o.lanes|=a),Hs(u.return,a,e),l||(r=null);break e}u=o.next}}else if(n.tag===18){if(r=n.return,r===null)throw Error(_(341));r.lanes|=a,u=r.alternate,u!==null&&(u.lanes|=a),Hs(r,a,e),r=null}else r=n.child;if(r!==null)r.return=n;else for(r=n;r!==null;){if(r===e){r=null;break}if(n=r.sibling,n!==null){n.return=r.return,r=n;break}r=r.return}n=r}}function Zu(e,t,a,l){e=null;for(var n=t,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var r=n.alternate;if(r===null)throw Error(_(387));if(r=r.memoizedProps,r!==null){var o=n.type;Tt(n.pendingProps.value,r.value)||(e!==null?e.push(o):e=[o])}}else if(n===vt.current){if(r=n.alternate,r===null)throw Error(_(387));r.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Ti):e=[Ti])}n=n.return}e!==null&&Us(t,e,a,l),t.flags|=262144}function rc(e){for(e=e.firstContext;e!==null;){if(!Tt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function An(e){Sn=e,Fa=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Nt(e){return Tf(Sn,e)}function fc(e,t){return Sn===null&&An(e),Tf(e,t)}function Tf(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Fa===null){if(e===null)throw Error(_(308));Fa=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Fa=Fa.next=t;return a}var ph=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},Th=J.unstable_scheduleCallback,Nh=J.unstable_NormalPriority,lt={$$typeof:Je,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function js(){return{controller:new ph,data:new Map,refCount:0}}function Ku(e){e.refCount--,e.refCount===0&&Th(Nh,function(){e.controller.abort()})}var Ju=null,zs=0,Pn=0,In=null;function bh(e,t){if(Ju===null){var a=Ju=[];zs=0,Pn=xr(),In={status:"pending",value:void 0,then:function(l){a.push(l)}}}return zs++,t.then(Nf,Nf),t}function Nf(){if(--zs===0&&Ju!==null){In!==null&&(In.status="fulfilled");var e=Ju;Ju=null,Pn=0,In=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Dh(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var bf=G.S;G.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&bh(e,t),bf!==null&&bf(e,t)};var En=w(null);function Ls(){var e=En.current;return e!==null?e:Ge.pooledCache}function dc(e,t){t===null?Z(En,En.current):Z(En,t.pool)}function Df(){var e=Ls();return e===null?null:{parent:lt._currentValue,pool:e}}var Wu=Error(_(460)),Cf=Error(_(474)),oc=Error(_(542)),xs={then:function(){}};function Rf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function hc(){}function Of(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(hc,hc),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Mf(e),e;default:if(typeof t.status=="string")t.then(hc,hc);else{if(e=Ge,e!==null&&100<e.shellSuspendCounter)throw Error(_(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Mf(e),e}throw Fu=t,Wu}}var Fu=null;function _f(){if(Fu===null)throw Error(_(459));var e=Fu;return Fu=null,e}function Mf(e){if(e===Wu||e===oc)throw Error(_(483))}var Bl=!1;function qs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ws(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Gl(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Hl(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(De&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=uc(e),gf(e,null,a),t}return nc(e,l,t,a),uc(e)}function Pu(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,ln(e,a)}}function Ys(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,u=null;if(a=a.firstBaseUpdate,a!==null){do{var r={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};u===null?n=u=r:u=u.next=r,a=a.next}while(a!==null);u===null?n=u=t:u=u.next=t}else n=u=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var Vs=!1;function Iu(){if(Vs){var e=In;if(e!==null)throw e}}function ei(e,t,a,l){Vs=!1;var n=e.updateQueue;Bl=!1;var u=n.firstBaseUpdate,r=n.lastBaseUpdate,o=n.shared.pending;if(o!==null){n.shared.pending=null;var S=o,D=S.next;S.next=null,r===null?u=D:r.next=D,r=S;var H=e.alternate;H!==null&&(H=H.updateQueue,o=H.lastBaseUpdate,o!==r&&(o===null?H.firstBaseUpdate=D:o.next=D,H.lastBaseUpdate=S))}if(u!==null){var x=n.baseState;r=0,H=D=S=null,o=u;do{var R=o.lane&-536870913,O=R!==o.lane;if(O?(Se&R)===R:(l&R)===R){R!==0&&R===Pn&&(Vs=!0),H!==null&&(H=H.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var ne=e,te=o;R=t;var _e=a;switch(te.tag){case 1:if(ne=te.payload,typeof ne=="function"){x=ne.call(_e,x,R);break e}x=ne;break e;case 3:ne.flags=ne.flags&-65537|128;case 0:if(ne=te.payload,R=typeof ne=="function"?ne.call(_e,x,R):ne,R==null)break e;x=U({},x,R);break e;case 2:Bl=!0}}R=o.callback,R!==null&&(e.flags|=64,O&&(e.flags|=8192),O=n.callbacks,O===null?n.callbacks=[R]:O.push(R))}else O={lane:R,tag:o.tag,payload:o.payload,callback:o.callback,next:null},H===null?(D=H=O,S=x):H=H.next=O,r|=R;if(o=o.next,o===null){if(o=n.shared.pending,o===null)break;O=o,o=O.next,O.next=null,n.lastBaseUpdate=O,n.shared.pending=null}}while(!0);H===null&&(S=x),n.baseState=S,n.firstBaseUpdate=D,n.lastBaseUpdate=H,u===null&&(n.shared.lanes=0),Yl|=r,e.lanes=r,e.memoizedState=x}}function Bf(e,t){if(typeof e!="function")throw Error(_(191,e));e.call(t)}function Gf(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)Bf(a[e],t)}var eu=w(null),mc=w(0);function Hf(e,t){e=ul,Z(mc,e),Z(eu,t),ul=e|t.baseLanes}function Xs(){Z(mc,ul),Z(eu,eu.current)}function $s(){ul=mc.current,k(eu),k(mc)}var Ul=0,re=null,Re=null,Fe=null,yc=!1,tu=!1,pn=!1,gc=0,ti=0,au=null,Ch=0;function ke(){throw Error(_(321))}function Qs(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!Tt(e[a],t[a]))return!1;return!0}function ks(e,t,a,l,n,u){return Ul=u,re=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,G.H=e===null||e.memoizedState===null?gd:vd,pn=!1,u=a(l,n),pn=!1,tu&&(u=jf(t,a,l,n)),Uf(e),u}function Uf(e){G.H=Tc;var t=Re!==null&&Re.next!==null;if(Ul=0,Fe=Re=re=null,yc=!1,ti=0,au=null,t)throw Error(_(300));e===null||st||(e=e.dependencies,e!==null&&rc(e)&&(st=!0))}function jf(e,t,a,l){re=e;var n=0;do{if(tu&&(au=null),ti=0,tu=!1,25<=n)throw Error(_(301));if(n+=1,Fe=Re=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}G.H=Hh,u=t(a,l)}while(tu);return u}function Rh(){var e=G.H,t=e.useState()[0];return t=typeof t.then=="function"?ai(t):t,e=e.useState()[0],(Re!==null?Re.memoizedState:null)!==e&&(re.flags|=1024),t}function Zs(){var e=gc!==0;return gc=0,e}function Ks(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function Js(e){if(yc){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}yc=!1}Ul=0,Fe=Re=re=null,tu=!1,ti=gc=0,au=null}function Ut(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Fe===null?re.memoizedState=Fe=e:Fe=Fe.next=e,Fe}function Pe(){if(Re===null){var e=re.alternate;e=e!==null?e.memoizedState:null}else e=Re.next;var t=Fe===null?re.memoizedState:Fe.next;if(t!==null)Fe=t,Re=e;else{if(e===null)throw re.alternate===null?Error(_(467)):Error(_(310));Re=e,e={memoizedState:Re.memoizedState,baseState:Re.baseState,baseQueue:Re.baseQueue,queue:Re.queue,next:null},Fe===null?re.memoizedState=Fe=e:Fe=Fe.next=e}return Fe}function Ws(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ai(e){var t=ti;return ti+=1,au===null&&(au=[]),e=Of(au,e,t),t=re,(Fe===null?t.memoizedState:Fe.next)===null&&(t=t.alternate,G.H=t===null||t.memoizedState===null?gd:vd),e}function vc(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ai(e);if(e.$$typeof===Je)return Nt(e)}throw Error(_(438,String(e)))}function Fs(e){var t=null,a=re.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=re.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Ws(),re.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=Wt;return t.index++,a}function Ia(e,t){return typeof t=="function"?t(e):t}function Sc(e){var t=Pe();return Ps(t,Re,e)}function Ps(e,t,a){var l=e.queue;if(l===null)throw Error(_(311));l.lastRenderedReducer=a;var n=e.baseQueue,u=l.pending;if(u!==null){if(n!==null){var r=n.next;n.next=u.next,u.next=r}t.baseQueue=n=u,l.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var o=r=null,S=null,D=t,H=!1;do{var x=D.lane&-536870913;if(x!==D.lane?(Se&x)===x:(Ul&x)===x){var R=D.revertLane;if(R===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),x===Pn&&(H=!0);else if((Ul&R)===R){D=D.next,R===Pn&&(H=!0);continue}else x={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(o=S=x,r=u):S=S.next=x,re.lanes|=R,Yl|=R;x=D.action,pn&&a(u,x),u=D.hasEagerState?D.eagerState:a(u,x)}else R={lane:x,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},S===null?(o=S=R,r=u):S=S.next=R,re.lanes|=x,Yl|=x;D=D.next}while(D!==null&&D!==t);if(S===null?r=u:S.next=o,!Tt(u,e.memoizedState)&&(st=!0,H&&(a=In,a!==null)))throw a;e.memoizedState=u,e.baseState=r,e.baseQueue=S,l.lastRenderedState=u}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Is(e){var t=Pe(),a=t.queue;if(a===null)throw Error(_(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,u=t.memoizedState;if(n!==null){a.pending=null;var r=n=n.next;do u=e(u,r.action),r=r.next;while(r!==n);Tt(u,t.memoizedState)||(st=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),a.lastRenderedState=u}return[u,l]}function zf(e,t,a){var l=re,n=Pe(),u=Ne;if(u){if(a===void 0)throw Error(_(407));a=a()}else a=t();var r=!Tt((Re||n).memoizedState,a);r&&(n.memoizedState=a,st=!0),n=n.queue;var o=qf.bind(null,l,n,e);if(li(2048,8,o,[e]),n.getSnapshot!==t||r||Fe!==null&&Fe.memoizedState.tag&1){if(l.flags|=2048,lu(9,Ac(),xf.bind(null,l,n,a,t),null),Ge===null)throw Error(_(349));u||(Ul&124)!==0||Lf(l,t,a)}return a}function Lf(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=re.updateQueue,t===null?(t=Ws(),re.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function xf(e,t,a,l){t.value=a,t.getSnapshot=l,wf(t)&&Yf(e)}function qf(e,t,a){return a(function(){wf(t)&&Yf(e)})}function wf(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!Tt(e,a)}catch{return!0}}function Yf(e){var t=Kn(e,2);t!==null&&Kt(t,e,2)}function er(e){var t=Ut();if(typeof e=="function"){var a=e;if(e=a(),pn){ra(!0);try{a()}finally{ra(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ia,lastRenderedState:e},t}function Vf(e,t,a,l){return e.baseState=a,Ps(e,Re,typeof l=="function"?l:Ia)}function Oh(e,t,a,l,n){if(pc(e))throw Error(_(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(r){u.listeners.push(r)}};G.T!==null?a(!0):u.isTransition=!1,l(u),a=t.pending,a===null?(u.next=t.pending=u,Xf(t,u)):(u.next=a.next,t.pending=a.next=u)}}function Xf(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var u=G.T,r={};G.T=r;try{var o=a(n,l),S=G.S;S!==null&&S(r,o),$f(e,t,o)}catch(D){tr(e,t,D)}finally{G.T=u}}else try{u=a(n,l),$f(e,t,u)}catch(D){tr(e,t,D)}}function $f(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){Qf(e,t,l)},function(l){return tr(e,t,l)}):Qf(e,t,a)}function Qf(e,t,a){t.status="fulfilled",t.value=a,kf(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Xf(e,a)))}function tr(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,kf(t),t=t.next;while(t!==l)}e.action=null}function kf(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function Zf(e,t){return t}function Kf(e,t){if(Ne){var a=Ge.formState;if(a!==null){e:{var l=re;if(Ne){if($e){t:{for(var n=$e,u=Ba;n.nodeType!==8;){if(!u){n=null;break t}if(n=Aa(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){$e=Aa(n.nextSibling),l=n.data==="F!";break e}}vn(l)}l=!1}l&&(t=a[0])}}return a=Ut(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Zf,lastRenderedState:t},a.queue=l,a=hd.bind(null,re,l),l.dispatch=a,l=er(!1),u=ir.bind(null,re,!1,l.queue),l=Ut(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=Oh.bind(null,re,n,u,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function Jf(e){var t=Pe();return Wf(t,Re,e)}function Wf(e,t,a){if(t=Ps(e,t,Zf)[0],e=Sc(Ia)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=ai(t)}catch(r){throw r===Wu?oc:r}else l=t;t=Pe();var n=t.queue,u=n.dispatch;return a!==t.memoizedState&&(re.flags|=2048,lu(9,Ac(),_h.bind(null,n,a),null)),[l,u,e]}function _h(e,t){e.action=t}function Ff(e){var t=Pe(),a=Re;if(a!==null)return Wf(t,a,e);Pe(),t=t.memoizedState,a=Pe();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function lu(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=re.updateQueue,t===null&&(t=Ws(),re.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function Ac(){return{destroy:void 0,resource:void 0}}function Pf(){return Pe().memoizedState}function Ec(e,t,a,l){var n=Ut();l=l===void 0?null:l,re.flags|=e,n.memoizedState=lu(1|t,Ac(),a,l)}function li(e,t,a,l){var n=Pe();l=l===void 0?null:l;var u=n.memoizedState.inst;Re!==null&&l!==null&&Qs(l,Re.memoizedState.deps)?n.memoizedState=lu(t,u,a,l):(re.flags|=e,n.memoizedState=lu(1|t,u,a,l))}function If(e,t){Ec(8390656,8,e,t)}function ed(e,t){li(2048,8,e,t)}function td(e,t){return li(4,2,e,t)}function ad(e,t){return li(4,4,e,t)}function ld(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function nd(e,t,a){a=a!=null?a.concat([e]):null,li(4,4,ld.bind(null,t,e),a)}function ar(){}function ud(e,t){var a=Pe();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Qs(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function id(e,t){var a=Pe();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Qs(t,l[1]))return l[0];if(l=e(),pn){ra(!0);try{e()}finally{ra(!1)}}return a.memoizedState=[l,t],l}function lr(e,t,a){return a===void 0||(Ul&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=ro(),re.lanes|=e,Yl|=e,a)}function cd(e,t,a,l){return Tt(a,t)?a:eu.current!==null?(e=lr(e,a,l),Tt(e,t)||(st=!0),e):(Ul&42)===0?(st=!0,e.memoizedState=a):(e=ro(),re.lanes|=e,Yl|=e,t)}function sd(e,t,a,l,n){var u=q.p;q.p=u!==0&&8>u?u:8;var r=G.T,o={};G.T=o,ir(e,!1,t,a);try{var S=n(),D=G.S;if(D!==null&&D(o,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var H=Dh(S,l);ni(e,t,H,Zt(e))}else ni(e,t,l,Zt(e))}catch(x){ni(e,t,{then:function(){},status:"rejected",reason:x},Zt())}finally{q.p=u,G.T=r}}function Mh(){}function nr(e,t,a,l){if(e.tag!==5)throw Error(_(476));var n=rd(e).queue;sd(e,n,t,ee,a===null?Mh:function(){return fd(e),a(l)})}function rd(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ee,baseState:ee,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ia,lastRenderedState:ee},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ia,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function fd(e){var t=rd(e).next.queue;ni(e,t,{},Zt())}function ur(){return Nt(Ti)}function dd(){return Pe().memoizedState}function od(){return Pe().memoizedState}function Bh(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Zt();e=Gl(a);var l=Hl(t,e,a);l!==null&&(Kt(l,t,a),Pu(l,t,a)),t={cache:js()},e.payload=t;return}t=t.return}}function Gh(e,t,a){var l=Zt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},pc(e)?md(t,a):(a=Ds(e,t,a,l),a!==null&&(Kt(a,e,l),yd(a,t,l)))}function hd(e,t,a){var l=Zt();ni(e,t,a,l)}function ni(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(pc(e))md(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var r=t.lastRenderedState,o=u(r,a);if(n.hasEagerState=!0,n.eagerState=o,Tt(o,r))return nc(e,t,n,0),Ge===null&&lc(),!1}catch{}finally{}if(a=Ds(e,t,n,l),a!==null)return Kt(a,e,l),yd(a,t,l),!0}return!1}function ir(e,t,a,l){if(l={lane:2,revertLane:xr(),action:l,hasEagerState:!1,eagerState:null,next:null},pc(e)){if(t)throw Error(_(479))}else t=Ds(e,a,l,2),t!==null&&Kt(t,e,2)}function pc(e){var t=e.alternate;return e===re||t!==null&&t===re}function md(e,t){tu=yc=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function yd(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,ln(e,a)}}var Tc={readContext:Nt,use:vc,useCallback:ke,useContext:ke,useEffect:ke,useImperativeHandle:ke,useLayoutEffect:ke,useInsertionEffect:ke,useMemo:ke,useReducer:ke,useRef:ke,useState:ke,useDebugValue:ke,useDeferredValue:ke,useTransition:ke,useSyncExternalStore:ke,useId:ke,useHostTransitionStatus:ke,useFormState:ke,useActionState:ke,useOptimistic:ke,useMemoCache:ke,useCacheRefresh:ke},gd={readContext:Nt,use:vc,useCallback:function(e,t){return Ut().memoizedState=[e,t===void 0?null:t],e},useContext:Nt,useEffect:If,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ec(4194308,4,ld.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ec(4194308,4,e,t)},useInsertionEffect:function(e,t){Ec(4,2,e,t)},useMemo:function(e,t){var a=Ut();t=t===void 0?null:t;var l=e();if(pn){ra(!0);try{e()}finally{ra(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=Ut();if(a!==void 0){var n=a(t);if(pn){ra(!0);try{a(t)}finally{ra(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=Gh.bind(null,re,e),[l.memoizedState,e]},useRef:function(e){var t=Ut();return e={current:e},t.memoizedState=e},useState:function(e){e=er(e);var t=e.queue,a=hd.bind(null,re,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:ar,useDeferredValue:function(e,t){var a=Ut();return lr(a,e,t)},useTransition:function(){var e=er(!1);return e=sd.bind(null,re,e.queue,!0,!1),Ut().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=re,n=Ut();if(Ne){if(a===void 0)throw Error(_(407));a=a()}else{if(a=t(),Ge===null)throw Error(_(349));(Se&124)!==0||Lf(l,t,a)}n.memoizedState=a;var u={value:a,getSnapshot:t};return n.queue=u,If(qf.bind(null,l,u,e),[e]),l.flags|=2048,lu(9,Ac(),xf.bind(null,l,u,a,t),null),a},useId:function(){var e=Ut(),t=Ge.identifierPrefix;if(Ne){var a=Wa,l=Ja;a=(l&~(1<<32-St(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=gc++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Ch++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:ur,useFormState:Kf,useActionState:Kf,useOptimistic:function(e){var t=Ut();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=ir.bind(null,re,!0,a),a.dispatch=t,[e,t]},useMemoCache:Fs,useCacheRefresh:function(){return Ut().memoizedState=Bh.bind(null,re)}},vd={readContext:Nt,use:vc,useCallback:ud,useContext:Nt,useEffect:ed,useImperativeHandle:nd,useInsertionEffect:td,useLayoutEffect:ad,useMemo:id,useReducer:Sc,useRef:Pf,useState:function(){return Sc(Ia)},useDebugValue:ar,useDeferredValue:function(e,t){var a=Pe();return cd(a,Re.memoizedState,e,t)},useTransition:function(){var e=Sc(Ia)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:ai(e),t]},useSyncExternalStore:zf,useId:dd,useHostTransitionStatus:ur,useFormState:Jf,useActionState:Jf,useOptimistic:function(e,t){var a=Pe();return Vf(a,Re,e,t)},useMemoCache:Fs,useCacheRefresh:od},Hh={readContext:Nt,use:vc,useCallback:ud,useContext:Nt,useEffect:ed,useImperativeHandle:nd,useInsertionEffect:td,useLayoutEffect:ad,useMemo:id,useReducer:Is,useRef:Pf,useState:function(){return Is(Ia)},useDebugValue:ar,useDeferredValue:function(e,t){var a=Pe();return Re===null?lr(a,e,t):cd(a,Re.memoizedState,e,t)},useTransition:function(){var e=Is(Ia)[0],t=Pe().memoizedState;return[typeof e=="boolean"?e:ai(e),t]},useSyncExternalStore:zf,useId:dd,useHostTransitionStatus:ur,useFormState:Ff,useActionState:Ff,useOptimistic:function(e,t){var a=Pe();return Re!==null?Vf(a,Re,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Fs,useCacheRefresh:od},nu=null,ui=0;function Nc(e){var t=ui;return ui+=1,nu===null&&(nu=[]),Of(nu,e,t)}function ii(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function bc(e,t){throw t.$$typeof===X?Error(_(525)):(e=Object.prototype.toString.call(t),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function Sd(e){var t=e._init;return t(e._payload)}function Ad(e){function t(N,p){if(e){var b=N.deletions;b===null?(N.deletions=[p],N.flags|=16):b.push(p)}}function a(N,p){if(!e)return null;for(;p!==null;)t(N,p),p=p.sibling;return null}function l(N){for(var p=new Map;N!==null;)N.key!==null?p.set(N.key,N):p.set(N.index,N),N=N.sibling;return p}function n(N,p){return N=Ka(N,p),N.index=0,N.sibling=null,N}function u(N,p,b){return N.index=b,e?(b=N.alternate,b!==null?(b=b.index,b<p?(N.flags|=67108866,p):b):(N.flags|=67108866,p)):(N.flags|=1048576,p)}function r(N){return e&&N.alternate===null&&(N.flags|=67108866),N}function o(N,p,b,z){return p===null||p.tag!==6?(p=Rs(b,N.mode,z),p.return=N,p):(p=n(p,b),p.return=N,p)}function S(N,p,b,z){var W=b.type;return W===dt?H(N,p,b.props.children,z,b.key):p!==null&&(p.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===Ie&&Sd(W)===p.type)?(p=n(p,b.props),ii(p,b),p.return=N,p):(p=ic(b.type,b.key,b.props,null,N.mode,z),ii(p,b),p.return=N,p)}function D(N,p,b,z){return p===null||p.tag!==4||p.stateNode.containerInfo!==b.containerInfo||p.stateNode.implementation!==b.implementation?(p=Os(b,N.mode,z),p.return=N,p):(p=n(p,b.children||[]),p.return=N,p)}function H(N,p,b,z,W){return p===null||p.tag!==7?(p=hn(b,N.mode,z,W),p.return=N,p):(p=n(p,b),p.return=N,p)}function x(N,p,b){if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return p=Rs(""+p,N.mode,b),p.return=N,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case me:return b=ic(p.type,p.key,p.props,null,N.mode,b),ii(b,p),b.return=N,b;case ut:return p=Os(p,N.mode,b),p.return=N,p;case Ie:var z=p._init;return p=z(p._payload),x(N,p,b)}if(it(p)||Ue(p))return p=hn(p,N.mode,b,null),p.return=N,p;if(typeof p.then=="function")return x(N,Nc(p),b);if(p.$$typeof===Je)return x(N,fc(N,p),b);bc(N,p)}return null}function R(N,p,b,z){var W=p!==null?p.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return W!==null?null:o(N,p,""+b,z);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case me:return b.key===W?S(N,p,b,z):null;case ut:return b.key===W?D(N,p,b,z):null;case Ie:return W=b._init,b=W(b._payload),R(N,p,b,z)}if(it(b)||Ue(b))return W!==null?null:H(N,p,b,z,null);if(typeof b.then=="function")return R(N,p,Nc(b),z);if(b.$$typeof===Je)return R(N,p,fc(N,b),z);bc(N,b)}return null}function O(N,p,b,z,W){if(typeof z=="string"&&z!==""||typeof z=="number"||typeof z=="bigint")return N=N.get(b)||null,o(p,N,""+z,W);if(typeof z=="object"&&z!==null){switch(z.$$typeof){case me:return N=N.get(z.key===null?b:z.key)||null,S(p,N,z,W);case ut:return N=N.get(z.key===null?b:z.key)||null,D(p,N,z,W);case Ie:var de=z._init;return z=de(z._payload),O(N,p,b,z,W)}if(it(z)||Ue(z))return N=N.get(b)||null,H(p,N,z,W,null);if(typeof z.then=="function")return O(N,p,b,Nc(z),W);if(z.$$typeof===Je)return O(N,p,b,fc(p,z),W);bc(p,z)}return null}function ne(N,p,b,z){for(var W=null,de=null,P=p,ae=p=0,ft=null;P!==null&&ae<b.length;ae++){P.index>ae?(ft=P,P=null):ft=P.sibling;var pe=R(N,P,b[ae],z);if(pe===null){P===null&&(P=ft);break}e&&P&&pe.alternate===null&&t(N,P),p=u(pe,p,ae),de===null?W=pe:de.sibling=pe,de=pe,P=ft}if(ae===b.length)return a(N,P),Ne&&yn(N,ae),W;if(P===null){for(;ae<b.length;ae++)P=x(N,b[ae],z),P!==null&&(p=u(P,p,ae),de===null?W=P:de.sibling=P,de=P);return Ne&&yn(N,ae),W}for(P=l(P);ae<b.length;ae++)ft=O(P,N,ae,b[ae],z),ft!==null&&(e&&ft.alternate!==null&&P.delete(ft.key===null?ae:ft.key),p=u(ft,p,ae),de===null?W=ft:de.sibling=ft,de=ft);return e&&P.forEach(function(Wl){return t(N,Wl)}),Ne&&yn(N,ae),W}function te(N,p,b,z){if(b==null)throw Error(_(151));for(var W=null,de=null,P=p,ae=p=0,ft=null,pe=b.next();P!==null&&!pe.done;ae++,pe=b.next()){P.index>ae?(ft=P,P=null):ft=P.sibling;var Wl=R(N,P,pe.value,z);if(Wl===null){P===null&&(P=ft);break}e&&P&&Wl.alternate===null&&t(N,P),p=u(Wl,p,ae),de===null?W=Wl:de.sibling=Wl,de=Wl,P=ft}if(pe.done)return a(N,P),Ne&&yn(N,ae),W;if(P===null){for(;!pe.done;ae++,pe=b.next())pe=x(N,pe.value,z),pe!==null&&(p=u(pe,p,ae),de===null?W=pe:de.sibling=pe,de=pe);return Ne&&yn(N,ae),W}for(P=l(P);!pe.done;ae++,pe=b.next())pe=O(P,N,ae,pe.value,z),pe!==null&&(e&&pe.alternate!==null&&P.delete(pe.key===null?ae:pe.key),p=u(pe,p,ae),de===null?W=pe:de.sibling=pe,de=pe);return e&&P.forEach(function(Um){return t(N,Um)}),Ne&&yn(N,ae),W}function _e(N,p,b,z){if(typeof b=="object"&&b!==null&&b.type===dt&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case me:e:{for(var W=b.key;p!==null;){if(p.key===W){if(W=b.type,W===dt){if(p.tag===7){a(N,p.sibling),z=n(p,b.props.children),z.return=N,N=z;break e}}else if(p.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===Ie&&Sd(W)===p.type){a(N,p.sibling),z=n(p,b.props),ii(z,b),z.return=N,N=z;break e}a(N,p);break}else t(N,p);p=p.sibling}b.type===dt?(z=hn(b.props.children,N.mode,z,b.key),z.return=N,N=z):(z=ic(b.type,b.key,b.props,null,N.mode,z),ii(z,b),z.return=N,N=z)}return r(N);case ut:e:{for(W=b.key;p!==null;){if(p.key===W)if(p.tag===4&&p.stateNode.containerInfo===b.containerInfo&&p.stateNode.implementation===b.implementation){a(N,p.sibling),z=n(p,b.children||[]),z.return=N,N=z;break e}else{a(N,p);break}else t(N,p);p=p.sibling}z=Os(b,N.mode,z),z.return=N,N=z}return r(N);case Ie:return W=b._init,b=W(b._payload),_e(N,p,b,z)}if(it(b))return ne(N,p,b,z);if(Ue(b)){if(W=Ue(b),typeof W!="function")throw Error(_(150));return b=W.call(b),te(N,p,b,z)}if(typeof b.then=="function")return _e(N,p,Nc(b),z);if(b.$$typeof===Je)return _e(N,p,fc(N,b),z);bc(N,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,p!==null&&p.tag===6?(a(N,p.sibling),z=n(p,b),z.return=N,N=z):(a(N,p),z=Rs(b,N.mode,z),z.return=N,N=z),r(N)):a(N,p)}return function(N,p,b,z){try{ui=0;var W=_e(N,p,b,z);return nu=null,W}catch(P){if(P===Wu||P===oc)throw P;var de=Xt(29,P,null,N.mode);return de.lanes=z,de.return=N,de}finally{}}}var uu=Ad(!0),Ed=Ad(!1),aa=w(null),Ga=null;function jl(e){var t=e.alternate;Z(nt,nt.current&1),Z(aa,e),Ga===null&&(t===null||eu.current!==null||t.memoizedState!==null)&&(Ga=e)}function pd(e){if(e.tag===22){if(Z(nt,nt.current),Z(aa,e),Ga===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ga=e)}}else zl()}function zl(){Z(nt,nt.current),Z(aa,aa.current)}function el(e){k(aa),Ga===e&&(Ga=null),k(nt)}var nt=w(0);function Dc(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Wr(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function cr(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:U({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var sr={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=Zt(),n=Gl(l);n.payload=t,a!=null&&(n.callback=a),t=Hl(e,n,l),t!==null&&(Kt(t,e,l),Pu(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=Zt(),n=Gl(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=Hl(e,n,l),t!==null&&(Kt(t,e,l),Pu(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Zt(),l=Gl(a);l.tag=2,t!=null&&(l.callback=t),t=Hl(e,l,a),t!==null&&(Kt(t,e,a),Pu(t,e,a))}};function Td(e,t,a,l,n,u,r){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,u,r):t.prototype&&t.prototype.isPureReactComponent?!ct(a,l)||!ct(n,u):!0}function Nd(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&sr.enqueueReplaceState(t,t.state,null)}function Tn(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=U({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var Cc=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function bd(e){Cc(e)}function Dd(e){console.error(e)}function Cd(e){Cc(e)}function Rc(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Rd(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function rr(e,t,a){return a=Gl(a),a.tag=3,a.payload={element:null},a.callback=function(){Rc(e,t)},a}function Od(e){return e=Gl(e),e.tag=3,e}function _d(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var u=l.value;e.payload=function(){return n(u)},e.callback=function(){Rd(t,a,l)}}var r=a.stateNode;r!==null&&typeof r.componentDidCatch=="function"&&(e.callback=function(){Rd(t,a,l),typeof n!="function"&&(Vl===null?Vl=new Set([this]):Vl.add(this));var o=l.stack;this.componentDidCatch(l.value,{componentStack:o!==null?o:""})})}function Uh(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&Zu(t,a,n,!0),a=aa.current,a!==null){switch(a.tag){case 13:return Ga===null?Hr():a.alternate===null&&Qe===0&&(Qe=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===xs?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),jr(e,l,n)),!1;case 22:return a.flags|=65536,l===xs?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),jr(e,l,n)),!1}throw Error(_(435,a.tag))}return jr(e,l,n),Hr(),!1}if(Ne)return t=aa.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==Bs&&(e=Error(_(422),{cause:l}),ku(ve(e,a)))):(l!==Bs&&(t=Error(_(423),{cause:l}),ku(ve(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=ve(l,a),n=rr(e.stateNode,l,n),Ys(e,n),Qe!==4&&(Qe=2)),!1;var u=Error(_(520),{cause:l});if(u=ve(u,a),hi===null?hi=[u]:hi.push(u),Qe!==4&&(Qe=2),t===null)return!0;l=ve(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=rr(a.stateNode,l,e),Ys(a,e),!1;case 1:if(t=a.type,u=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Vl===null||!Vl.has(u))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Od(n),_d(n,e,a,l),Ys(a,n),!1}a=a.return}while(a!==null);return!1}var Md=Error(_(461)),st=!1;function mt(e,t,a,l){t.child=e===null?Ed(t,null,a,l):uu(t,e.child,a,l)}function Bd(e,t,a,l,n){a=a.render;var u=t.ref;if("ref"in l){var r={};for(var o in l)o!=="ref"&&(r[o]=l[o])}else r=l;return An(t),l=ks(e,t,a,r,u,n),o=Zs(),e!==null&&!st?(Ks(e,t,n),tl(e,t,n)):(Ne&&o&&_s(t),t.flags|=1,mt(e,t,l,n),t.child)}function Gd(e,t,a,l,n){if(e===null){var u=a.type;return typeof u=="function"&&!Cs(u)&&u.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=u,Hd(e,t,u,l,n)):(e=ic(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!vr(e,n)){var r=u.memoizedProps;if(a=a.compare,a=a!==null?a:ct,a(r,l)&&e.ref===t.ref)return tl(e,t,n)}return t.flags|=1,e=Ka(u,l),e.ref=t.ref,e.return=t,t.child=e}function Hd(e,t,a,l,n){if(e!==null){var u=e.memoizedProps;if(ct(u,l)&&e.ref===t.ref)if(st=!1,t.pendingProps=l=u,vr(e,n))(e.flags&131072)!==0&&(st=!0);else return t.lanes=e.lanes,tl(e,t,n)}return fr(e,t,a,l,n)}function Ud(e,t,a){var l=t.pendingProps,n=l.children,u=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=u!==null?u.baseLanes|a:a,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~l}else t.childLanes=0,t.child=null;return jd(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&dc(t,u!==null?u.cachePool:null),u!==null?Hf(t,u):Xs(),pd(t);else return t.lanes=t.childLanes=536870912,jd(e,t,u!==null?u.baseLanes|a:a,a)}else u!==null?(dc(t,u.cachePool),Hf(t,u),zl(),t.memoizedState=null):(e!==null&&dc(t,null),Xs(),zl());return mt(e,t,n,a),t.child}function jd(e,t,a,l){var n=Ls();return n=n===null?null:{parent:lt._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&dc(t,null),Xs(),pd(t),e!==null&&Zu(e,t,l,!0),null}function Oc(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(_(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function fr(e,t,a,l,n){return An(t),a=ks(e,t,a,l,void 0,n),l=Zs(),e!==null&&!st?(Ks(e,t,n),tl(e,t,n)):(Ne&&l&&_s(t),t.flags|=1,mt(e,t,a,n),t.child)}function zd(e,t,a,l,n,u){return An(t),t.updateQueue=null,a=jf(t,l,a,n),Uf(e),l=Zs(),e!==null&&!st?(Ks(e,t,u),tl(e,t,u)):(Ne&&l&&_s(t),t.flags|=1,mt(e,t,a,u),t.child)}function Ld(e,t,a,l,n){if(An(t),t.stateNode===null){var u=Jn,r=a.contextType;typeof r=="object"&&r!==null&&(u=Nt(r)),u=new a(l,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=sr,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=l,u.state=t.memoizedState,u.refs={},qs(t),r=a.contextType,u.context=typeof r=="object"&&r!==null?Nt(r):Jn,u.state=t.memoizedState,r=a.getDerivedStateFromProps,typeof r=="function"&&(cr(t,a,r,l),u.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(r=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),r!==u.state&&sr.enqueueReplaceState(u,u.state,null),ei(t,l,u,n),Iu(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){u=t.stateNode;var o=t.memoizedProps,S=Tn(a,o);u.props=S;var D=u.context,H=a.contextType;r=Jn,typeof H=="object"&&H!==null&&(r=Nt(H));var x=a.getDerivedStateFromProps;H=typeof x=="function"||typeof u.getSnapshotBeforeUpdate=="function",o=t.pendingProps!==o,H||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o||D!==r)&&Nd(t,u,l,r),Bl=!1;var R=t.memoizedState;u.state=R,ei(t,l,u,n),Iu(),D=t.memoizedState,o||R!==D||Bl?(typeof x=="function"&&(cr(t,a,x,l),D=t.memoizedState),(S=Bl||Td(t,a,S,l,R,D,r))?(H||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=D),u.props=l,u.state=D,u.context=r,l=S):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{u=t.stateNode,ws(e,t),r=t.memoizedProps,H=Tn(a,r),u.props=H,x=t.pendingProps,R=u.context,D=a.contextType,S=Jn,typeof D=="object"&&D!==null&&(S=Nt(D)),o=a.getDerivedStateFromProps,(D=typeof o=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r!==x||R!==S)&&Nd(t,u,l,S),Bl=!1,R=t.memoizedState,u.state=R,ei(t,l,u,n),Iu();var O=t.memoizedState;r!==x||R!==O||Bl||e!==null&&e.dependencies!==null&&rc(e.dependencies)?(typeof o=="function"&&(cr(t,a,o,l),O=t.memoizedState),(H=Bl||Td(t,a,H,l,R,O,S)||e!==null&&e.dependencies!==null&&rc(e.dependencies))?(D||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,O,S),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,O,S)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=O),u.props=l,u.state=O,u.context=S,l=H):(typeof u.componentDidUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),l=!1)}return u=l,Oc(e,t),l=(t.flags&128)!==0,u||l?(u=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&l?(t.child=uu(t,e.child,null,n),t.child=uu(t,null,a,n)):mt(e,t,a,n),t.memoizedState=u.state,e=t.child):e=tl(e,t,n),e}function xd(e,t,a,l){return Qu(),t.flags|=256,mt(e,t,a,l),t.child}var dr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function or(e){return{baseLanes:e,cachePool:Df()}}function hr(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=la),e}function qd(e,t,a){var l=t.pendingProps,n=!1,u=(t.flags&128)!==0,r;if((r=u)||(r=e!==null&&e.memoizedState===null?!1:(nt.current&2)!==0),r&&(n=!0,t.flags&=-129),r=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ne){if(n?jl(t):zl(),Ne){var o=$e,S;if(S=o){e:{for(S=o,o=Ba;S.nodeType!==8;){if(!o){o=null;break e}if(S=Aa(S.nextSibling),S===null){o=null;break e}}o=S}o!==null?(t.memoizedState={dehydrated:o,treeContext:mn!==null?{id:Ja,overflow:Wa}:null,retryLane:536870912,hydrationErrors:null},S=Xt(18,null,null,0),S.stateNode=o,S.return=t,t.child=S,Ct=t,$e=null,S=!0):S=!1}S||vn(t)}if(o=t.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return Wr(o)?t.lanes=32:t.lanes=536870912,null;el(t)}return o=l.children,l=l.fallback,n?(zl(),n=t.mode,o=_c({mode:"hidden",children:o},n),l=hn(l,n,a,null),o.return=t,l.return=t,o.sibling=l,t.child=o,n=t.child,n.memoizedState=or(a),n.childLanes=hr(e,r,a),t.memoizedState=dr,l):(jl(t),mr(t,o))}if(S=e.memoizedState,S!==null&&(o=S.dehydrated,o!==null)){if(u)t.flags&256?(jl(t),t.flags&=-257,t=yr(e,t,a)):t.memoizedState!==null?(zl(),t.child=e.child,t.flags|=128,t=null):(zl(),n=l.fallback,o=t.mode,l=_c({mode:"visible",children:l.children},o),n=hn(n,o,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,uu(t,e.child,null,a),l=t.child,l.memoizedState=or(a),l.childLanes=hr(e,r,a),t.memoizedState=dr,t=n);else if(jl(t),Wr(o)){if(r=o.nextSibling&&o.nextSibling.dataset,r)var D=r.dgst;r=D,l=Error(_(419)),l.stack="",l.digest=r,ku({value:l,source:null,stack:null}),t=yr(e,t,a)}else if(st||Zu(e,t,a,!1),r=(a&e.childLanes)!==0,st||r){if(r=Ge,r!==null&&(l=a&-a,l=(l&42)!==0?1:bu(l),l=(l&(r.suspendedLanes|a))!==0?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,Kn(e,l),Kt(r,e,l),Md;o.data==="$?"||Hr(),t=yr(e,t,a)}else o.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=S.treeContext,$e=Aa(o.nextSibling),Ct=t,Ne=!0,gn=null,Ba=!1,e!==null&&(ea[ta++]=Ja,ea[ta++]=Wa,ea[ta++]=mn,Ja=e.id,Wa=e.overflow,mn=t),t=mr(t,l.children),t.flags|=4096);return t}return n?(zl(),n=l.fallback,o=t.mode,S=e.child,D=S.sibling,l=Ka(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,D!==null?n=Ka(D,n):(n=hn(n,o,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,o=e.child.memoizedState,o===null?o=or(a):(S=o.cachePool,S!==null?(D=lt._currentValue,S=S.parent!==D?{parent:D,pool:D}:S):S=Df(),o={baseLanes:o.baseLanes|a,cachePool:S}),n.memoizedState=o,n.childLanes=hr(e,r,a),t.memoizedState=dr,l):(jl(t),a=e.child,e=a.sibling,a=Ka(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(r=t.deletions,r===null?(t.deletions=[e],t.flags|=16):r.push(e)),t.child=a,t.memoizedState=null,a)}function mr(e,t){return t=_c({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function _c(e,t){return e=Xt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function yr(e,t,a){return uu(t,e.child,null,a),e=mr(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function wd(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),Hs(e.return,t,a)}function gr(e,t,a,l,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=a,u.tailMode=n)}function Yd(e,t,a){var l=t.pendingProps,n=l.revealOrder,u=l.tail;if(mt(e,t,l.children,a),l=nt.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&wd(e,a,t);else if(e.tag===19)wd(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(Z(nt,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&Dc(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),gr(t,!1,n,a,u);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Dc(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}gr(t,!0,a,null,u);break;case"together":gr(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function tl(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),Yl|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(Zu(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(_(153));if(t.child!==null){for(e=t.child,a=Ka(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Ka(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function vr(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&rc(e)))}function jh(e,t,a){switch(t.tag){case 3:ye(t,t.stateNode.containerInfo),Ml(t,lt,e.memoizedState.cache),Qu();break;case 27:case 5:Au(t);break;case 4:ye(t,t.stateNode.containerInfo);break;case 10:Ml(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(jl(t),t.flags|=128,null):(a&t.child.childLanes)!==0?qd(e,t,a):(jl(t),e=tl(e,t,a),e!==null?e.sibling:null);jl(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(Zu(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return Yd(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),Z(nt,nt.current),l)break;return null;case 22:case 23:return t.lanes=0,Ud(e,t,a);case 24:Ml(t,lt,e.memoizedState.cache)}return tl(e,t,a)}function Vd(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)st=!0;else{if(!vr(e,a)&&(t.flags&128)===0)return st=!1,jh(e,t,a);st=(e.flags&131072)!==0}else st=!1,Ne&&(t.flags&1048576)!==0&&Sf(t,sc,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")Cs(l)?(e=Tn(l,e),t.tag=1,t=Ld(null,t,l,e,a)):(t.tag=0,t=fr(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===Lt){t.tag=11,t=Bd(null,t,l,e,a);break e}else if(n===Ot){t.tag=14,t=Gd(null,t,l,e,a);break e}}throw t=ca(l)||l,Error(_(306,t,""))}}return t;case 0:return fr(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=Tn(l,t.pendingProps),Ld(e,t,l,n,a);case 3:e:{if(ye(t,t.stateNode.containerInfo),e===null)throw Error(_(387));l=t.pendingProps;var u=t.memoizedState;n=u.element,ws(e,t),ei(t,l,null,a);var r=t.memoizedState;if(l=r.cache,Ml(t,lt,l),l!==u.cache&&Us(t,[lt],a,!0),Iu(),l=r.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:r.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=xd(e,t,l,a);break e}else if(l!==n){n=ve(Error(_(424)),t),ku(n),t=xd(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for($e=Aa(e.firstChild),Ct=t,Ne=!0,gn=null,Ba=!0,a=Ed(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(Qu(),l===n){t=tl(e,t,a);break e}mt(e,t,l,a)}t=t.child}return t;case 26:return Oc(e,t),e===null?(a=Zo(t.type,null,t.pendingProps,null))?t.memoizedState=a:Ne||(a=t.type,e=t.pendingProps,l=Xc(ie.current).createElement(a),l[Ye]=t,l[se]=e,gt(l,a,e),Ve(l),t.stateNode=l):t.memoizedState=Zo(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return Au(t),e===null&&Ne&&(l=t.stateNode=$o(t.type,t.pendingProps,ie.current),Ct=t,Ba=!0,n=$e,Ql(t.type)?(Fr=n,$e=Aa(l.firstChild)):$e=n),mt(e,t,t.pendingProps.children,a),Oc(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ne&&((n=l=$e)&&(l=rm(l,t.type,t.pendingProps,Ba),l!==null?(t.stateNode=l,Ct=t,$e=Aa(l.firstChild),Ba=!1,n=!0):n=!1),n||vn(t)),Au(t),n=t.type,u=t.pendingProps,r=e!==null?e.memoizedProps:null,l=u.children,Zr(n,u)?l=null:r!==null&&Zr(n,r)&&(t.flags|=32),t.memoizedState!==null&&(n=ks(e,t,Rh,null,null,a),Ti._currentValue=n),Oc(e,t),mt(e,t,l,a),t.child;case 6:return e===null&&Ne&&((e=a=$e)&&(a=fm(a,t.pendingProps,Ba),a!==null?(t.stateNode=a,Ct=t,$e=null,e=!0):e=!1),e||vn(t)),null;case 13:return qd(e,t,a);case 4:return ye(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=uu(t,null,l,a):mt(e,t,l,a),t.child;case 11:return Bd(e,t,t.type,t.pendingProps,a);case 7:return mt(e,t,t.pendingProps,a),t.child;case 8:return mt(e,t,t.pendingProps.children,a),t.child;case 12:return mt(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Ml(t,t.type,l.value),mt(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,An(t),n=Nt(n),l=l(n),t.flags|=1,mt(e,t,l,a),t.child;case 14:return Gd(e,t,t.type,t.pendingProps,a);case 15:return Hd(e,t,t.type,t.pendingProps,a);case 19:return Yd(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=_c(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Ka(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Ud(e,t,a);case 24:return An(t),l=Nt(lt),e===null?(n=Ls(),n===null&&(n=Ge,u=js(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=a),n=u),t.memoizedState={parent:l,cache:n},qs(t),Ml(t,lt,n)):((e.lanes&a)!==0&&(ws(e,t),ei(t,null,null,a),Iu()),n=e.memoizedState,u=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Ml(t,lt,l)):(l=u.cache,Ml(t,lt,l),l!==n.cache&&Us(t,[lt],a,!0))),mt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(_(156,t.tag))}function al(e){e.flags|=4}function Xd(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Po(t)){if(t=aa.current,t!==null&&((Se&4194048)===Se?Ga!==null:(Se&62914560)!==Se&&(Se&536870912)===0||t!==Ga))throw Fu=xs,Cf;e.flags|=8192}}function Mc(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?Nu():536870912,e.lanes|=t,ru|=t)}function ci(e,t){if(!Ne)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function we(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function zh(e,t,a){var l=t.pendingProps;switch(Ms(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return we(t),null;case 1:return we(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),Pa(lt),Ft(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&($u(t)?al(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,pf())),we(t),null;case 26:return a=t.memoizedState,e===null?(al(t),a!==null?(we(t),Xd(t,a)):(we(t),t.flags&=-16777217)):a?a!==e.memoizedState?(al(t),we(t),Xd(t,a)):(we(t),t.flags&=-16777217):(e.memoizedProps!==l&&al(t),we(t),t.flags&=-16777217),null;case 27:cl(t),a=ie.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&al(t);else{if(!l){if(t.stateNode===null)throw Error(_(166));return we(t),null}e=I.current,$u(t)?Af(t):(e=$o(n,l,a),t.stateNode=e,al(t))}return we(t),null;case 5:if(cl(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&al(t);else{if(!l){if(t.stateNode===null)throw Error(_(166));return we(t),null}if(e=I.current,$u(t))Af(t);else{switch(n=Xc(ie.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[Ye]=t,e[se]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(gt(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&al(t)}}return we(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&al(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(_(166));if(e=ie.current,$u(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=Ct,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[Ye]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||Lo(e.nodeValue,a)),e||vn(t)}else e=Xc(e).createTextNode(l),e[Ye]=t,t.stateNode=e}return we(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=$u(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(_(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(_(317));n[Ye]=t}else Qu(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;we(t),n=!1}else n=pf(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(el(t),t):(el(t),null)}if(el(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),Mc(t,t.updateQueue),we(t),null;case 4:return Ft(),e===null&&Vr(t.stateNode.containerInfo),we(t),null;case 10:return Pa(t.type),we(t),null;case 19:if(k(nt),n=t.memoizedState,n===null)return we(t),null;if(l=(t.flags&128)!==0,u=n.rendering,u===null)if(l)ci(n,!1);else{if(Qe!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Dc(e),u!==null){for(t.flags|=128,ci(n,!1),e=u.updateQueue,t.updateQueue=e,Mc(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)vf(a,e),a=a.sibling;return Z(nt,nt.current&1|2),t.child}e=e.sibling}n.tail!==null&&_t()>Hc&&(t.flags|=128,l=!0,ci(n,!1),t.lanes=4194304)}else{if(!l)if(e=Dc(u),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,Mc(t,e),ci(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!Ne)return we(t),null}else 2*_t()-n.renderingStartTime>Hc&&a!==536870912&&(t.flags|=128,l=!0,ci(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=_t(),t.sibling=null,e=nt.current,Z(nt,l?e&1|2:e&1),t):(we(t),null);case 22:case 23:return el(t),$s(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(we(t),t.subtreeFlags&6&&(t.flags|=8192)):we(t),a=t.updateQueue,a!==null&&Mc(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&k(En),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),Pa(lt),we(t),null;case 25:return null;case 30:return null}throw Error(_(156,t.tag))}function Lh(e,t){switch(Ms(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return Pa(lt),Ft(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return cl(t),null;case 13:if(el(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(_(340));Qu()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return k(nt),null;case 4:return Ft(),null;case 10:return Pa(t.type),null;case 22:case 23:return el(t),$s(),e!==null&&k(En),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return Pa(lt),null;case 25:return null;default:return null}}function $d(e,t){switch(Ms(t),t.tag){case 3:Pa(lt),Ft();break;case 26:case 27:case 5:cl(t);break;case 4:Ft();break;case 13:el(t);break;case 19:k(nt);break;case 10:Pa(t.type);break;case 22:case 23:el(t),$s(),e!==null&&k(En);break;case 24:Pa(lt)}}function si(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var u=a.create,r=a.inst;l=u(),r.destroy=l}a=a.next}while(a!==n)}}catch(o){Be(t,t.return,o)}}function Ll(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var u=n.next;l=u;do{if((l.tag&e)===e){var r=l.inst,o=r.destroy;if(o!==void 0){r.destroy=void 0,n=t;var S=a,D=o;try{D()}catch(H){Be(n,S,H)}}}l=l.next}while(l!==u)}}catch(H){Be(t,t.return,H)}}function Qd(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Gf(t,a)}catch(l){Be(e,e.return,l)}}}function kd(e,t,a){a.props=Tn(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Be(e,t,l)}}function ri(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){Be(e,t,n)}}function Ha(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){Be(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){Be(e,t,n)}else a.current=null}function Zd(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){Be(e,e.return,n)}}function Sr(e,t,a){try{var l=e.stateNode;nm(l,e.type,a,t),l[se]=t}catch(n){Be(e,e.return,n)}}function Kd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ql(e.type)||e.tag===4}function Ar(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Kd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ql(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Er(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=Vc));else if(l!==4&&(l===27&&Ql(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Er(e,t,a),e=e.sibling;e!==null;)Er(e,t,a),e=e.sibling}function Bc(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Ql(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(Bc(e,t,a),e=e.sibling;e!==null;)Bc(e,t,a),e=e.sibling}function Jd(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);gt(t,l,a),t[Ye]=e,t[se]=a}catch(u){Be(e,e.return,u)}}var ll=!1,Ze=!1,pr=!1,Wd=typeof WeakSet=="function"?WeakSet:Set,rt=null;function xh(e,t){if(e=e.containerInfo,Qr=Jc,e=Vu(e),Qn(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{a.nodeType,u.nodeType}catch{a=null;break e}var r=0,o=-1,S=-1,D=0,H=0,x=e,R=null;t:for(;;){for(var O;x!==a||n!==0&&x.nodeType!==3||(o=r+n),x!==u||l!==0&&x.nodeType!==3||(S=r+l),x.nodeType===3&&(r+=x.nodeValue.length),(O=x.firstChild)!==null;)R=x,x=O;for(;;){if(x===e)break t;if(R===a&&++D===n&&(o=r),R===u&&++H===l&&(S=r),(O=x.nextSibling)!==null)break;x=R,R=x.parentNode}x=O}a=o===-1||S===-1?null:{start:o,end:S}}else a=null}a=a||{start:0,end:0}}else a=null;for(kr={focusedElem:e,selectionRange:a},Jc=!1,rt=t;rt!==null;)if(t=rt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,rt=e;else for(;rt!==null;){switch(t=rt,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,a=t,n=u.memoizedProps,u=u.memoizedState,l=a.stateNode;try{var ne=Tn(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(ne,u),l.__reactInternalSnapshotBeforeUpdate=e}catch(te){Be(a,a.return,te)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Jr(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Jr(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(_(163))}if(e=t.sibling,e!==null){e.return=t.return,rt=e;break}rt=t.return}}function Fd(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:xl(e,a),l&4&&si(5,a);break;case 1:if(xl(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(r){Be(a,a.return,r)}else{var n=Tn(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(r){Be(a,a.return,r)}}l&64&&Qd(a),l&512&&ri(a,a.return);break;case 3:if(xl(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Gf(e,t)}catch(r){Be(a,a.return,r)}}break;case 27:t===null&&l&4&&Jd(a);case 26:case 5:xl(e,a),t===null&&l&4&&Zd(a),l&512&&ri(a,a.return);break;case 12:xl(e,a);break;case 13:xl(e,a),l&4&&eo(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Zh.bind(null,a),dm(e,a))));break;case 22:if(l=a.memoizedState!==null||ll,!l){t=t!==null&&t.memoizedState!==null||Ze,n=ll;var u=Ze;ll=l,(Ze=t)&&!u?ql(e,a,(a.subtreeFlags&8772)!==0):xl(e,a),ll=n,Ze=u}break;case 30:break;default:xl(e,a)}}function Pd(e){var t=e.alternate;t!==null&&(e.alternate=null,Pd(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&qt(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var xe=null,jt=!1;function nl(e,t,a){for(a=a.child;a!==null;)Id(e,t,a),a=a.sibling}function Id(e,t,a){if(je&&typeof je.onCommitFiberUnmount=="function")try{je.onCommitFiberUnmount(Il,a)}catch{}switch(a.tag){case 26:Ze||Ha(a,t),nl(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ze||Ha(a,t);var l=xe,n=jt;Ql(a.type)&&(xe=a.stateNode,jt=!1),nl(e,t,a),Si(a.stateNode),xe=l,jt=n;break;case 5:Ze||Ha(a,t);case 6:if(l=xe,n=jt,xe=null,nl(e,t,a),xe=l,jt=n,xe!==null)if(jt)try{(xe.nodeType===9?xe.body:xe.nodeName==="HTML"?xe.ownerDocument.body:xe).removeChild(a.stateNode)}catch(u){Be(a,t,u)}else try{xe.removeChild(a.stateNode)}catch(u){Be(a,t,u)}break;case 18:xe!==null&&(jt?(e=xe,Vo(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),Ci(e)):Vo(xe,a.stateNode));break;case 4:l=xe,n=jt,xe=a.stateNode.containerInfo,jt=!0,nl(e,t,a),xe=l,jt=n;break;case 0:case 11:case 14:case 15:Ze||Ll(2,a,t),Ze||Ll(4,a,t),nl(e,t,a);break;case 1:Ze||(Ha(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&kd(a,t,l)),nl(e,t,a);break;case 21:nl(e,t,a);break;case 22:Ze=(l=Ze)||a.memoizedState!==null,nl(e,t,a),Ze=l;break;default:nl(e,t,a)}}function eo(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{Ci(e)}catch(a){Be(t,t.return,a)}}function qh(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Wd),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Wd),t;default:throw Error(_(435,e.tag))}}function Tr(e,t){var a=qh(e);t.forEach(function(l){var n=Kh.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function $t(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],u=e,r=t,o=r;e:for(;o!==null;){switch(o.tag){case 27:if(Ql(o.type)){xe=o.stateNode,jt=!1;break e}break;case 5:xe=o.stateNode,jt=!1;break e;case 3:case 4:xe=o.stateNode.containerInfo,jt=!0;break e}o=o.return}if(xe===null)throw Error(_(160));Id(u,r,n),xe=null,jt=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)to(t,e),t=t.sibling}var Sa=null;function to(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:$t(t,e),Qt(e),l&4&&(Ll(3,e,e.return),si(3,e),Ll(5,e,e.return));break;case 1:$t(t,e),Qt(e),l&512&&(Ze||a===null||Ha(a,a.return)),l&64&&ll&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Sa;if($t(t,e),Qt(e),l&512&&(Ze||a===null||Ha(a,a.return)),l&4){var u=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":u=n.getElementsByTagName("title")[0],(!u||u[et]||u[Ye]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(l),n.head.insertBefore(u,n.querySelector("head > title"))),gt(u,l,a),u[Ye]=e,Ve(u),l=u;break e;case"link":var r=Wo("link","href",n).get(l+(a.href||""));if(r){for(var o=0;o<r.length;o++)if(u=r[o],u.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&u.getAttribute("rel")===(a.rel==null?null:a.rel)&&u.getAttribute("title")===(a.title==null?null:a.title)&&u.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){r.splice(o,1);break t}}u=n.createElement(l),gt(u,l,a),n.head.appendChild(u);break;case"meta":if(r=Wo("meta","content",n).get(l+(a.content||""))){for(o=0;o<r.length;o++)if(u=r[o],u.getAttribute("content")===(a.content==null?null:""+a.content)&&u.getAttribute("name")===(a.name==null?null:a.name)&&u.getAttribute("property")===(a.property==null?null:a.property)&&u.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&u.getAttribute("charset")===(a.charSet==null?null:a.charSet)){r.splice(o,1);break t}}u=n.createElement(l),gt(u,l,a),n.head.appendChild(u);break;default:throw Error(_(468,l))}u[Ye]=e,Ve(u),l=u}e.stateNode=l}else Fo(n,e.type,e.stateNode);else e.stateNode=Jo(n,l,e.memoizedProps);else u!==l?(u===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):u.count--,l===null?Fo(n,e.type,e.stateNode):Jo(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Sr(e,e.memoizedProps,a.memoizedProps)}break;case 27:$t(t,e),Qt(e),l&512&&(Ze||a===null||Ha(a,a.return)),a!==null&&l&4&&Sr(e,e.memoizedProps,a.memoizedProps);break;case 5:if($t(t,e),Qt(e),l&512&&(Ze||a===null||Ha(a,a.return)),e.flags&32){n=e.stateNode;try{yl(n,"")}catch(O){Be(e,e.return,O)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,Sr(e,n,a!==null?a.memoizedProps:n)),l&1024&&(pr=!0);break;case 6:if($t(t,e),Qt(e),l&4){if(e.stateNode===null)throw Error(_(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(O){Be(e,e.return,O)}}break;case 3:if(kc=null,n=Sa,Sa=$c(t.containerInfo),$t(t,e),Sa=n,Qt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Ci(t.containerInfo)}catch(O){Be(e,e.return,O)}pr&&(pr=!1,ao(e));break;case 4:l=Sa,Sa=$c(e.stateNode.containerInfo),$t(t,e),Qt(e),Sa=l;break;case 12:$t(t,e),Qt(e);break;case 13:$t(t,e),Qt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Or=_t()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Tr(e,l)));break;case 22:n=e.memoizedState!==null;var S=a!==null&&a.memoizedState!==null,D=ll,H=Ze;if(ll=D||n,Ze=H||S,$t(t,e),Ze=H,ll=D,Qt(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||S||ll||Ze||Nn(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){S=a=t;try{if(u=S.stateNode,n)r=u.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none";else{o=S.stateNode;var x=S.memoizedProps.style,R=x!=null&&x.hasOwnProperty("display")?x.display:null;o.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(O){Be(S,S.return,O)}}}else if(t.tag===6){if(a===null){S=t;try{S.stateNode.nodeValue=n?"":S.memoizedProps}catch(O){Be(S,S.return,O)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Tr(e,a))));break;case 19:$t(t,e),Qt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Tr(e,l)));break;case 30:break;case 21:break;default:$t(t,e),Qt(e)}}function Qt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(Kd(l)){a=l;break}l=l.return}if(a==null)throw Error(_(160));switch(a.tag){case 27:var n=a.stateNode,u=Ar(e);Bc(e,u,n);break;case 5:var r=a.stateNode;a.flags&32&&(yl(r,""),a.flags&=-33);var o=Ar(e);Bc(e,o,r);break;case 3:case 4:var S=a.stateNode.containerInfo,D=Ar(e);Er(e,D,S);break;default:throw Error(_(161))}}catch(H){Be(e,e.return,H)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ao(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;ao(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function xl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Fd(e,t.alternate,t),t=t.sibling}function Nn(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Ll(4,t,t.return),Nn(t);break;case 1:Ha(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&kd(t,t.return,a),Nn(t);break;case 27:Si(t.stateNode);case 26:case 5:Ha(t,t.return),Nn(t);break;case 22:t.memoizedState===null&&Nn(t);break;case 30:Nn(t);break;default:Nn(t)}e=e.sibling}}function ql(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,u=t,r=u.flags;switch(u.tag){case 0:case 11:case 15:ql(n,u,a),si(4,u);break;case 1:if(ql(n,u,a),l=u,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(D){Be(l,l.return,D)}if(l=u,n=l.updateQueue,n!==null){var o=l.stateNode;try{var S=n.shared.hiddenCallbacks;if(S!==null)for(n.shared.hiddenCallbacks=null,n=0;n<S.length;n++)Bf(S[n],o)}catch(D){Be(l,l.return,D)}}a&&r&64&&Qd(u),ri(u,u.return);break;case 27:Jd(u);case 26:case 5:ql(n,u,a),a&&l===null&&r&4&&Zd(u),ri(u,u.return);break;case 12:ql(n,u,a);break;case 13:ql(n,u,a),a&&r&4&&eo(n,u);break;case 22:u.memoizedState===null&&ql(n,u,a),ri(u,u.return);break;case 30:break;default:ql(n,u,a)}t=t.sibling}}function Nr(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Ku(a))}function br(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ku(e))}function Ua(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)lo(e,t,a,l),t=t.sibling}function lo(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Ua(e,t,a,l),n&2048&&si(9,t);break;case 1:Ua(e,t,a,l);break;case 3:Ua(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Ku(e)));break;case 12:if(n&2048){Ua(e,t,a,l),e=t.stateNode;try{var u=t.memoizedProps,r=u.id,o=u.onPostCommit;typeof o=="function"&&o(r,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){Be(t,t.return,S)}}else Ua(e,t,a,l);break;case 13:Ua(e,t,a,l);break;case 23:break;case 22:u=t.stateNode,r=t.alternate,t.memoizedState!==null?u._visibility&2?Ua(e,t,a,l):fi(e,t):u._visibility&2?Ua(e,t,a,l):(u._visibility|=2,iu(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&Nr(r,t);break;case 24:Ua(e,t,a,l),n&2048&&br(t.alternate,t);break;default:Ua(e,t,a,l)}}function iu(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,r=t,o=a,S=l,D=r.flags;switch(r.tag){case 0:case 11:case 15:iu(u,r,o,S,n),si(8,r);break;case 23:break;case 22:var H=r.stateNode;r.memoizedState!==null?H._visibility&2?iu(u,r,o,S,n):fi(u,r):(H._visibility|=2,iu(u,r,o,S,n)),n&&D&2048&&Nr(r.alternate,r);break;case 24:iu(u,r,o,S,n),n&&D&2048&&br(r.alternate,r);break;default:iu(u,r,o,S,n)}t=t.sibling}}function fi(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:fi(a,l),n&2048&&Nr(l.alternate,l);break;case 24:fi(a,l),n&2048&&br(l.alternate,l);break;default:fi(a,l)}t=t.sibling}}var di=8192;function cu(e){if(e.subtreeFlags&di)for(e=e.child;e!==null;)no(e),e=e.sibling}function no(e){switch(e.tag){case 26:cu(e),e.flags&di&&e.memoizedState!==null&&bm(Sa,e.memoizedState,e.memoizedProps);break;case 5:cu(e);break;case 3:case 4:var t=Sa;Sa=$c(e.stateNode.containerInfo),cu(e),Sa=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=di,di=16777216,cu(e),di=t):cu(e));break;default:cu(e)}}function uo(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function oi(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];rt=l,co(l,e)}uo(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)io(e),e=e.sibling}function io(e){switch(e.tag){case 0:case 11:case 15:oi(e),e.flags&2048&&Ll(9,e,e.return);break;case 3:oi(e);break;case 12:oi(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,Gc(e)):oi(e);break;default:oi(e)}}function Gc(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];rt=l,co(l,e)}uo(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Ll(8,t,t.return),Gc(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,Gc(t));break;default:Gc(t)}e=e.sibling}}function co(e,t){for(;rt!==null;){var a=rt;switch(a.tag){case 0:case 11:case 15:Ll(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Ku(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,rt=l;else e:for(a=e;rt!==null;){l=rt;var n=l.sibling,u=l.return;if(Pd(l),l===a){rt=null;break e}if(n!==null){n.return=u,rt=n;break e}rt=u}}}var wh={getCacheForType:function(e){var t=Nt(lt),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Yh=typeof WeakMap=="function"?WeakMap:Map,De=0,Ge=null,oe=null,Se=0,Ce=0,kt=null,wl=!1,su=!1,Dr=!1,ul=0,Qe=0,Yl=0,bn=0,Cr=0,la=0,ru=0,hi=null,zt=null,Rr=!1,Or=0,Hc=1/0,Uc=null,Vl=null,yt=0,Xl=null,fu=null,du=0,_r=0,Mr=null,so=null,mi=0,Br=null;function Zt(){if((De&2)!==0&&Se!==0)return Se&-Se;if(G.T!==null){var e=Pn;return e!==0?e:xr()}return qa()}function ro(){la===0&&(la=(Se&536870912)===0||Ne?Tu():536870912);var e=aa.current;return e!==null&&(e.flags|=32),la}function Kt(e,t,a){(e===Ge&&(Ce===2||Ce===9)||e.cancelPendingCommit!==null)&&(ou(e,0),$l(e,Se,la,!1)),rl(e,a),((De&2)===0||e!==Ge)&&(e===Ge&&((De&2)===0&&(bn|=a),Qe===4&&$l(e,Se,la,!1)),ja(e))}function fo(e,t,a){if((De&6)!==0)throw Error(_(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||xa(e,t),n=l?$h(e,t):Ur(e,t,!0),u=l;do{if(n===0){su&&!l&&$l(e,t,0,!1);break}else{if(a=e.current.alternate,u&&!Vh(a)){n=Ur(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var r=0;else r=e.pendingLanes&-536870913,r=r!==0?r:r&536870912?536870912:0;if(r!==0){t=r;e:{var o=e;n=hi;var S=o.current.memoizedState.isDehydrated;if(S&&(ou(o,r).flags|=256),r=Ur(o,r,!1),r!==2){if(Dr&&!S){o.errorRecoveryDisabledLanes|=u,bn|=u,n=4;break e}u=zt,zt=n,u!==null&&(zt===null?zt=u:zt.push.apply(zt,u))}n=r}if(u=!1,n!==2)continue}}if(n===1){ou(e,0),$l(e,t,0,!0);break}e:{switch(l=e,u=n,u){case 0:case 1:throw Error(_(345));case 4:if((t&4194048)!==t)break;case 6:$l(l,t,la,!wl);break e;case 2:zt=null;break;case 3:case 5:break;default:throw Error(_(329))}if((t&62914560)===t&&(n=Or+300-_t(),10<n)){if($l(l,t,la,!wl),Mn(l,0,!0)!==0)break e;l.timeoutHandle=wo(oo.bind(null,l,a,zt,Uc,Rr,t,la,bn,ru,wl,u,2,-0,0),n);break e}oo(l,a,zt,Uc,Rr,t,la,bn,ru,wl,u,0,-0,0)}}break}while(!0);ja(e)}function oo(e,t,a,l,n,u,r,o,S,D,H,x,R,O){if(e.timeoutHandle=-1,x=t.subtreeFlags,(x&8192||(x&16785408)===16785408)&&(pi={stylesheets:null,count:0,unsuspend:Nm},no(t),x=Dm(),x!==null)){e.cancelPendingCommit=x(Ao.bind(null,e,t,u,a,l,n,r,o,S,H,1,R,O)),$l(e,u,r,!D);return}Ao(e,t,u,a,l,n,r,o,S)}function Vh(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],u=n.getSnapshot;n=n.value;try{if(!Tt(u(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function $l(e,t,a,l){t&=~Cr,t&=~bn,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var u=31-St(n),r=1<<u;l[u]=-1,n&=~r}a!==0&&Gi(e,a,t)}function jc(){return(De&6)===0?(yi(0),!1):!0}function Gr(){if(oe!==null){if(Ce===0)var e=oe.return;else e=oe,Fa=Sn=null,Js(e),nu=null,ui=0,e=oe;for(;e!==null;)$d(e.alternate,e),e=e.return;oe=null}}function ou(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,im(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),Gr(),Ge=e,oe=a=Ka(e.current,null),Se=t,Ce=0,kt=null,wl=!1,su=xa(e,t),Dr=!1,ru=la=Cr=bn=Yl=Qe=0,zt=hi=null,Rr=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-St(l),u=1<<n;t|=e[n],l&=~u}return ul=t,lc(),a}function ho(e,t){re=null,G.H=Tc,t===Wu||t===oc?(t=_f(),Ce=3):t===Cf?(t=_f(),Ce=4):Ce=t===Md?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,kt=t,oe===null&&(Qe=1,Rc(e,ve(t,e.current)))}function mo(){var e=G.H;return G.H=Tc,e===null?Tc:e}function yo(){var e=G.A;return G.A=wh,e}function Hr(){Qe=4,wl||(Se&4194048)!==Se&&aa.current!==null||(su=!0),(Yl&134217727)===0&&(bn&134217727)===0||Ge===null||$l(Ge,Se,la,!1)}function Ur(e,t,a){var l=De;De|=2;var n=mo(),u=yo();(Ge!==e||Se!==t)&&(Uc=null,ou(e,t)),t=!1;var r=Qe;e:do try{if(Ce!==0&&oe!==null){var o=oe,S=kt;switch(Ce){case 8:Gr(),r=6;break e;case 3:case 2:case 9:case 6:aa.current===null&&(t=!0);var D=Ce;if(Ce=0,kt=null,hu(e,o,S,D),a&&su){r=0;break e}break;default:D=Ce,Ce=0,kt=null,hu(e,o,S,D)}}Xh(),r=Qe;break}catch(H){ho(e,H)}while(!0);return t&&e.shellSuspendCounter++,Fa=Sn=null,De=l,G.H=n,G.A=u,oe===null&&(Ge=null,Se=0,lc()),r}function Xh(){for(;oe!==null;)go(oe)}function $h(e,t){var a=De;De|=2;var l=mo(),n=yo();Ge!==e||Se!==t?(Uc=null,Hc=_t()+500,ou(e,t)):su=xa(e,t);e:do try{if(Ce!==0&&oe!==null){t=oe;var u=kt;t:switch(Ce){case 1:Ce=0,kt=null,hu(e,t,u,1);break;case 2:case 9:if(Rf(u)){Ce=0,kt=null,vo(t);break}t=function(){Ce!==2&&Ce!==9||Ge!==e||(Ce=7),ja(e)},u.then(t,t);break e;case 3:Ce=7;break e;case 4:Ce=5;break e;case 7:Rf(u)?(Ce=0,kt=null,vo(t)):(Ce=0,kt=null,hu(e,t,u,7));break;case 5:var r=null;switch(oe.tag){case 26:r=oe.memoizedState;case 5:case 27:var o=oe;if(!r||Po(r)){Ce=0,kt=null;var S=o.sibling;if(S!==null)oe=S;else{var D=o.return;D!==null?(oe=D,zc(D)):oe=null}break t}}Ce=0,kt=null,hu(e,t,u,5);break;case 6:Ce=0,kt=null,hu(e,t,u,6);break;case 8:Gr(),Qe=6;break e;default:throw Error(_(462))}}Qh();break}catch(H){ho(e,H)}while(!0);return Fa=Sn=null,G.H=l,G.A=n,De=a,oe!==null?0:(Ge=null,Se=0,lc(),Qe)}function Qh(){for(;oe!==null&&!La();)go(oe)}function go(e){var t=Vd(e.alternate,e,ul);e.memoizedProps=e.pendingProps,t===null?zc(e):oe=t}function vo(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=zd(a,t,t.pendingProps,t.type,void 0,Se);break;case 11:t=zd(a,t,t.pendingProps,t.type.render,t.ref,Se);break;case 5:Js(t);default:$d(a,t),t=oe=vf(t,ul),t=Vd(a,t,ul)}e.memoizedProps=e.pendingProps,t===null?zc(e):oe=t}function hu(e,t,a,l){Fa=Sn=null,Js(t),nu=null,ui=0;var n=t.return;try{if(Uh(e,n,t,a,Se)){Qe=1,Rc(e,ve(a,e.current)),oe=null;return}}catch(u){if(n!==null)throw oe=n,u;Qe=1,Rc(e,ve(a,e.current)),oe=null;return}t.flags&32768?(Ne||l===1?e=!0:su||(Se&536870912)!==0?e=!1:(wl=e=!0,(l===2||l===9||l===3||l===6)&&(l=aa.current,l!==null&&l.tag===13&&(l.flags|=16384))),So(t,e)):zc(t)}function zc(e){var t=e;do{if((t.flags&32768)!==0){So(t,wl);return}e=t.return;var a=zh(t.alternate,t,ul);if(a!==null){oe=a;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);Qe===0&&(Qe=5)}function So(e,t){do{var a=Lh(e.alternate,e);if(a!==null){a.flags&=32767,oe=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){oe=e;return}oe=e=a}while(e!==null);Qe=6,oe=null}function Ao(e,t,a,l,n,u,r,o,S){e.cancelPendingCommit=null;do Lc();while(yt!==0);if((De&6)!==0)throw Error(_(327));if(t!==null){if(t===e.current)throw Error(_(177));if(u=t.lanes|t.childLanes,u|=on,an(e,a,u,r,o,S),e===Ge&&(oe=Ge=null,Se=0),fu=t,Xl=e,du=a,_r=u,Mr=n,so=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Jh(pa,function(){return bo(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=G.T,G.T=null,n=q.p,q.p=2,r=De,De|=4;try{xh(e,t,a)}finally{De=r,q.p=n,G.T=l}}yt=1,Eo(),po(),To()}}function Eo(){if(yt===1){yt=0;var e=Xl,t=fu,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=G.T,G.T=null;var l=q.p;q.p=2;var n=De;De|=4;try{to(t,e);var u=kr,r=Vu(e.containerInfo),o=u.focusedElem,S=u.selectionRange;if(r!==o&&o&&o.ownerDocument&&Yu(o.ownerDocument.documentElement,o)){if(S!==null&&Qn(o)){var D=S.start,H=S.end;if(H===void 0&&(H=D),"selectionStart"in o)o.selectionStart=D,o.selectionEnd=Math.min(H,o.value.length);else{var x=o.ownerDocument||document,R=x&&x.defaultView||window;if(R.getSelection){var O=R.getSelection(),ne=o.textContent.length,te=Math.min(S.start,ne),_e=S.end===void 0?te:Math.min(S.end,ne);!O.extend&&te>_e&&(r=_e,_e=te,te=r);var N=wu(o,te),p=wu(o,_e);if(N&&p&&(O.rangeCount!==1||O.anchorNode!==N.node||O.anchorOffset!==N.offset||O.focusNode!==p.node||O.focusOffset!==p.offset)){var b=x.createRange();b.setStart(N.node,N.offset),O.removeAllRanges(),te>_e?(O.addRange(b),O.extend(p.node,p.offset)):(b.setEnd(p.node,p.offset),O.addRange(b))}}}}for(x=[],O=o;O=O.parentNode;)O.nodeType===1&&x.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<x.length;o++){var z=x[o];z.element.scrollLeft=z.left,z.element.scrollTop=z.top}}Jc=!!Qr,kr=Qr=null}finally{De=n,q.p=l,G.T=a}}e.current=t,yt=2}}function po(){if(yt===2){yt=0;var e=Xl,t=fu,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=G.T,G.T=null;var l=q.p;q.p=2;var n=De;De|=4;try{Fd(e,t.alternate,t)}finally{De=n,q.p=l,G.T=a}}yt=3}}function To(){if(yt===4||yt===3){yt=0,sa();var e=Xl,t=fu,a=du,l=so;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?yt=5:(yt=0,fu=Xl=null,No(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Vl=null),Du(a),t=t.stateNode,je&&typeof je.onCommitFiberRoot=="function")try{je.onCommitFiberRoot(Il,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=G.T,n=q.p,q.p=2,G.T=null;try{for(var u=e.onRecoverableError,r=0;r<l.length;r++){var o=l[r];u(o.value,{componentStack:o.stack})}}finally{G.T=t,q.p=n}}(du&3)!==0&&Lc(),ja(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===Br?mi++:(mi=0,Br=e):mi=0,yi(0)}}function No(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Ku(t)))}function Lc(e){return Eo(),po(),To(),bo()}function bo(){if(yt!==5)return!1;var e=Xl,t=_r;_r=0;var a=Du(du),l=G.T,n=q.p;try{q.p=32>a?32:a,G.T=null,a=Mr,Mr=null;var u=Xl,r=du;if(yt=0,fu=Xl=null,du=0,(De&6)!==0)throw Error(_(331));var o=De;if(De|=4,io(u.current),lo(u,u.current,r,a),De=o,yi(0,!1),je&&typeof je.onPostCommitFiberRoot=="function")try{je.onPostCommitFiberRoot(Il,u)}catch{}return!0}finally{q.p=n,G.T=l,No(e,t)}}function Do(e,t,a){t=ve(a,t),t=rr(e.stateNode,t,2),e=Hl(e,t,2),e!==null&&(rl(e,2),ja(e))}function Be(e,t,a){if(e.tag===3)Do(e,e,a);else for(;t!==null;){if(t.tag===3){Do(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Vl===null||!Vl.has(l))){e=ve(a,e),a=Od(2),l=Hl(t,a,2),l!==null&&(_d(a,l,t,e),rl(l,2),ja(l));break}}t=t.return}}function jr(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Yh;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(Dr=!0,n.add(a),e=kh.bind(null,e,t,a),t.then(e,e))}function kh(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Ge===e&&(Se&a)===a&&(Qe===4||Qe===3&&(Se&62914560)===Se&&300>_t()-Or?(De&2)===0&&ou(e,0):Cr|=a,ru===Se&&(ru=0)),ja(e)}function Co(e,t){t===0&&(t=Nu()),e=Kn(e,t),e!==null&&(rl(e,t),ja(e))}function Zh(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),Co(e,a)}function Kh(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(_(314))}l!==null&&l.delete(t),Co(e,a)}function Jh(e,t){return Fl(e,t)}var xc=null,mu=null,zr=!1,qc=!1,Lr=!1,Dn=0;function ja(e){e!==mu&&e.next===null&&(mu===null?xc=mu=e:mu=mu.next=e),qc=!0,zr||(zr=!0,Fh())}function yi(e,t){if(!Lr&&qc){Lr=!0;do for(var a=!1,l=xc;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var u=0;else{var r=l.suspendedLanes,o=l.pingedLanes;u=(1<<31-St(42|e)+1)-1,u&=n&~(r&~o),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(a=!0,Mo(l,u))}else u=Se,u=Mn(l,l===Ge?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||xa(l,u)||(a=!0,Mo(l,u));l=l.next}while(a);Lr=!1}}function Wh(){Ro()}function Ro(){qc=zr=!1;var e=0;Dn!==0&&(um()&&(e=Dn),Dn=0);for(var t=_t(),a=null,l=xc;l!==null;){var n=l.next,u=Oo(l,t);u===0?(l.next=null,a===null?xc=n:a.next=n,n===null&&(mu=a)):(a=l,(e!==0||(u&3)!==0)&&(qc=!0)),l=n}yi(e)}function Oo(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var r=31-St(u),o=1<<r,S=n[r];S===-1?((o&a)===0||(o&l)!==0)&&(n[r]=Na(o,t)):S<=t&&(e.expiredLanes|=o),u&=~o}if(t=Ge,a=Se,a=Mn(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(Ce===2||Ce===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Cn(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||xa(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&Cn(l),Du(a)){case 2:case 8:a=Mt;break;case 32:a=pa;break;case 268435456:a=On;break;default:a=pa}return l=_o.bind(null,e),a=Fl(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&Cn(l),e.callbackPriority=2,e.callbackNode=null,2}function _o(e,t){if(yt!==0&&yt!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(Lc()&&e.callbackNode!==a)return null;var l=Se;return l=Mn(e,e===Ge?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(fo(e,l,t),Oo(e,_t()),e.callbackNode!=null&&e.callbackNode===a?_o.bind(null,e):null)}function Mo(e,t){if(Lc())return null;fo(e,t,!0)}function Fh(){cm(function(){(De&6)!==0?Fl(Rn,Wh):Ro()})}function xr(){return Dn===0&&(Dn=Tu()),Dn}function Bo(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:gl(""+e)}function Go(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Ph(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var u=Bo((n[se]||null).action),r=l.submitter;r&&(t=(t=r[se]||null)?Bo(t.formAction):r.getAttribute("formAction"),t!==null&&(u=t,r=null));var o=new Xe("action","action",null,l,n);e.push({event:o,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Dn!==0){var S=r?Go(n,r):new FormData(n);nr(a,{pending:!0,data:S,method:n.method,action:u},null,S)}}else typeof u=="function"&&(o.preventDefault(),S=r?Go(n,r):new FormData(n),nr(a,{pending:!0,data:S,method:n.method,action:u},u,S))},currentTarget:n}]})}}for(var qr=0;qr<F.length;qr++){var wr=F[qr],Ih=wr.toLowerCase(),em=wr[0].toUpperCase()+wr.slice(1);K(Ih,"on"+em)}K(d,"onAnimationEnd"),K(h,"onAnimationIteration"),K(m,"onAnimationStart"),K("dblclick","onDoubleClick"),K("focusin","onFocus"),K("focusout","onBlur"),K(y,"onTransitionRun"),K(T,"onTransitionStart"),K(C,"onTransitionCancel"),K(j,"onTransitionEnd"),Da("onMouseEnter",["mouseout","mouseover"]),Da("onMouseLeave",["mouseout","mouseover"]),Da("onPointerEnter",["pointerout","pointerover"]),Da("onPointerLeave",["pointerout","pointerover"]),wa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),wa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),wa("onBeforeInput",["compositionend","keypress","textInput","paste"]),wa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),wa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),wa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var gi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),tm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(gi));function Ho(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var u=void 0;if(t)for(var r=l.length-1;0<=r;r--){var o=l[r],S=o.instance,D=o.currentTarget;if(o=o.listener,S!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=D;try{u(n)}catch(H){Cc(H)}n.currentTarget=null,u=S}else for(r=0;r<l.length;r++){if(o=l[r],S=o.instance,D=o.currentTarget,o=o.listener,S!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=D;try{u(n)}catch(H){Cc(H)}n.currentTarget=null,u=S}}}}function he(e,t){var a=t[qe];a===void 0&&(a=t[qe]=new Set);var l=e+"__bubble";a.has(l)||(Uo(t,e,2,!1),a.add(l))}function Yr(e,t,a){var l=0;t&&(l|=4),Uo(a,e,l,t)}var wc="_reactListening"+Math.random().toString(36).slice(2);function Vr(e){if(!e[wc]){e[wc]=!0,cn.forEach(function(a){a!=="selectionchange"&&(tm.has(a)||Yr(a,!1,e),Yr(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[wc]||(t[wc]=!0,Yr("selectionchange",!1,t))}}function Uo(e,t,a,l){switch(nh(t)){case 2:var n=Om;break;case 8:n=_m;break;default:n=af}a=n.bind(null,t,a,e),n=void 0,!Mu||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Xr(e,t,a,l,n){var u=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var r=l.tag;if(r===3||r===4){var o=l.stateNode.containerInfo;if(o===n)break;if(r===4)for(r=l.return;r!==null;){var S=r.tag;if((S===3||S===4)&&r.stateNode.containerInfo===n)return;r=r.return}for(;o!==null;){if(r=Pt(o),r===null)return;if(S=r.tag,S===5||S===6||S===26||S===27){l=u=r;continue e}o=o.parentNode}}l=l.return}_u(function(){var D=u,H=Sl(a),x=[];e:{var R=Q.get(e);if(R!==void 0){var O=Xe,ne=e;switch(e){case"keypress":if(ga(a)===0)break e;case"keydown":case"keyup":O=rs;break;case"focusin":ne="focus",O=Gu;break;case"focusout":ne="blur",O=Gu;break;case"beforeblur":case"afterblur":O=Gu;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=xi;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=Tl;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=Xi;break;case d:case h:case m:O=us;break;case j:O=$i;break;case"scroll":case"scrollend":O=It;break;case"wheel":O=hs;break;case"copy":case"cut":case"paste":O=wi;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=Vi;break;case"toggle":case"beforetoggle":O=ms}var te=(t&4)!==0,_e=!te&&(e==="scroll"||e==="scrollend"),N=te?R!==null?R+"Capture":null:R;te=[];for(var p=D,b;p!==null;){var z=p;if(b=z.stateNode,z=z.tag,z!==5&&z!==26&&z!==27||b===null||N===null||(z=El(p,N),z!=null&&te.push(vi(p,z,b))),_e)break;p=p.return}0<te.length&&(R=new O(R,ne,null,a,H),x.push({event:R,listeners:te}))}}if((t&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",O=e==="mouseout"||e==="pointerout",R&&a!==vl&&(ne=a.relatedTarget||a.fromElement)&&(Pt(ne)||ne[fa]))break e;if((O||R)&&(R=H.window===H?H:(R=H.ownerDocument)?R.defaultView||R.parentWindow:window,O?(ne=a.relatedTarget||a.toElement,O=D,ne=ne?Pt(ne):null,ne!==null&&(_e=V(ne),te=ne.tag,ne!==_e||te!==5&&te!==27&&te!==6)&&(ne=null)):(O=null,ne=D),O!==ne)){if(te=xi,z="onMouseLeave",N="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(te=Vi,z="onPointerLeave",N="onPointerEnter",p="pointer"),_e=O==null?R:ba(O),b=ne==null?R:ba(ne),R=new te(z,p+"leave",O,a,H),R.target=_e,R.relatedTarget=b,z=null,Pt(H)===D&&(te=new te(N,p+"enter",ne,a,H),te.target=b,te.relatedTarget=_e,z=te),_e=z,O&&ne)t:{for(te=O,N=ne,p=0,b=te;b;b=yu(b))p++;for(b=0,z=N;z;z=yu(z))b++;for(;0<p-b;)te=yu(te),p--;for(;0<b-p;)N=yu(N),b--;for(;p--;){if(te===N||N!==null&&te===N.alternate)break t;te=yu(te),N=yu(N)}te=null}else te=null;O!==null&&jo(x,R,O,te,!1),ne!==null&&_e!==null&&jo(x,_e,ne,te,!0)}}e:{if(R=D?ba(D):window,O=R.nodeName&&R.nodeName.toLowerCase(),O==="select"||O==="input"&&R.type==="file")var W=Pi;else if(Ji(R))if(Ii)W=Ns;else{W=ps;var de=Es}else O=R.nodeName,!O||O.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?D&&Yt(D.elementType)&&(W=Pi):W=Ts;if(W&&(W=W(e,D))){Wi(x,W,a,H);break e}de&&de(e,R,D),e==="focusout"&&D&&R.type==="number"&&D.memoizedProps.value!=null&&ml(R,"number",R.value)}switch(de=D?ba(D):window,e){case"focusin":(Ji(de)||de.contentEditable==="true")&&(ka=de,kn=D,_l=null);break;case"focusout":_l=kn=ka=null;break;case"mousedown":Zn=!0;break;case"contextmenu":case"mouseup":case"dragend":Zn=!1,Xu(x,a,H);break;case"selectionchange":if(ac)break;case"keydown":case"keyup":Xu(x,a,H)}var P;if(zu)e:{switch(e){case"compositionstart":var ae="onCompositionStart";break e;case"compositionend":ae="onCompositionEnd";break e;case"compositionupdate":ae="onCompositionUpdate";break e}ae=void 0}else Rl?Zi(e,a)&&(ae="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(ae="onCompositionStart");ae&&(Lu&&a.locale!=="ko"&&(Rl||ae!=="onCompositionStart"?ae==="onCompositionEnd"&&Rl&&(P=Li()):(tt=H,Bu="value"in tt?tt.value:tt.textContent,Rl=!0)),de=Yc(D,ae),0<de.length&&(ae=new bl(ae,e,null,a,H),x.push({event:ae,listeners:de}),P?ae.data=P:(P=Ki(a),P!==null&&(ae.data=P)))),(P=gs?vs(e,a):Ss(e,a))&&(ae=Yc(D,"onBeforeInput"),0<ae.length&&(de=new bl("onBeforeInput","beforeinput",null,a,H),x.push({event:de,listeners:ae}),de.data=P)),Ph(x,e,D,a,H)}Ho(x,t)})}function vi(e,t,a){return{instance:e,listener:t,currentTarget:a}}function Yc(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=El(e,a),n!=null&&l.unshift(vi(e,n,u)),n=El(e,t),n!=null&&l.push(vi(e,n,u))),e.tag===3)return l;e=e.return}return[]}function yu(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function jo(e,t,a,l,n){for(var u=t._reactName,r=[];a!==null&&a!==l;){var o=a,S=o.alternate,D=o.stateNode;if(o=o.tag,S!==null&&S===l)break;o!==5&&o!==26&&o!==27||D===null||(S=D,n?(D=El(a,u),D!=null&&r.unshift(vi(a,D,S))):n||(D=El(a,u),D!=null&&r.push(vi(a,D,S)))),a=a.return}r.length!==0&&e.push({event:t,listeners:r})}var am=/\r\n?/g,lm=/\u0000|\uFFFD/g;function zo(e){return(typeof e=="string"?e:""+e).replace(am,`
`).replace(lm,"")}function Lo(e,t){return t=zo(t),zo(e)===t}function Vc(){}function Oe(e,t,a,l,n,u){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||yl(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&yl(e,""+l);break;case"className":wt(e,"class",l);break;case"tabIndex":wt(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":wt(e,a,l);break;case"style":Vn(e,l,u);break;case"data":if(t!=="object"){wt(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=gl(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(a==="formAction"?(t!=="input"&&Oe(e,t,"name",n.name,n,null),Oe(e,t,"formEncType",n.formEncType,n,null),Oe(e,t,"formMethod",n.formMethod,n,null),Oe(e,t,"formTarget",n.formTarget,n,null)):(Oe(e,t,"encType",n.encType,n,null),Oe(e,t,"method",n.method,n,null),Oe(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=gl(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=Vc);break;case"onScroll":l!=null&&he("scroll",e);break;case"onScrollEnd":l!=null&&he("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(_(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(_(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=gl(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":he("beforetoggle",e),he("toggle",e),dl(e,"popover",l);break;case"xlinkActuate":ma(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":ma(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":ma(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":ma(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":ma(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":ma(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":ma(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":ma(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":ma(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":dl(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Ou.get(a)||a,dl(e,a,l))}}function $r(e,t,a,l,n,u){switch(a){case"style":Vn(e,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(_(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(_(60));e.innerHTML=a}}break;case"children":typeof l=="string"?yl(e,l):(typeof l=="number"||typeof l=="bigint")&&yl(e,""+l);break;case"onScroll":l!=null&&he("scroll",e);break;case"onScrollEnd":l!=null&&he("scrollend",e);break;case"onClick":l!=null&&(e.onclick=Vc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Gn.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),u=e[se]||null,u=u!=null?u[a]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof l=="function")){typeof u!="function"&&u!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):dl(e,a,l)}}}function gt(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":he("error",e),he("load",e);var l=!1,n=!1,u;for(u in a)if(a.hasOwnProperty(u)){var r=a[u];if(r!=null)switch(u){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(_(137,t));default:Oe(e,t,u,r,a,null)}}n&&Oe(e,t,"srcSet",a.srcSet,a,null),l&&Oe(e,t,"src",a.src,a,null);return;case"input":he("invalid",e);var o=u=r=n=null,S=null,D=null;for(l in a)if(a.hasOwnProperty(l)){var H=a[l];if(H!=null)switch(l){case"name":n=H;break;case"type":r=H;break;case"checked":S=H;break;case"defaultChecked":D=H;break;case"value":u=H;break;case"defaultValue":o=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(_(137,t));break;default:Oe(e,t,l,H,a,null)}}wn(e,u,o,S,D,r,n,!1),ol(e);return;case"select":he("invalid",e),l=r=u=null;for(n in a)if(a.hasOwnProperty(n)&&(o=a[n],o!=null))switch(n){case"value":u=o;break;case"defaultValue":r=o;break;case"multiple":l=o;default:Oe(e,t,n,o,a,null)}t=u,a=r,e.multiple=!!l,t!=null?Ra(e,!!l,t,!1):a!=null&&Ra(e,!!l,a,!0);return;case"textarea":he("invalid",e),u=n=l=null;for(r in a)if(a.hasOwnProperty(r)&&(o=a[r],o!=null))switch(r){case"value":l=o;break;case"defaultValue":n=o;break;case"children":u=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(_(91));break;default:Oe(e,t,r,o,a,null)}Yn(e,l,n,u),ol(e);return;case"option":for(S in a)if(a.hasOwnProperty(S)&&(l=a[S],l!=null))switch(S){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Oe(e,t,S,l,a,null)}return;case"dialog":he("beforetoggle",e),he("toggle",e),he("cancel",e),he("close",e);break;case"iframe":case"object":he("load",e);break;case"video":case"audio":for(l=0;l<gi.length;l++)he(gi[l],e);break;case"image":he("error",e),he("load",e);break;case"details":he("toggle",e);break;case"embed":case"source":case"link":he("error",e),he("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in a)if(a.hasOwnProperty(D)&&(l=a[D],l!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(_(137,t));default:Oe(e,t,D,l,a,null)}return;default:if(Yt(t)){for(H in a)a.hasOwnProperty(H)&&(l=a[H],l!==void 0&&$r(e,t,H,l,a,void 0));return}}for(o in a)a.hasOwnProperty(o)&&(l=a[o],l!=null&&Oe(e,t,o,l,a,null))}function nm(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,r=null,o=null,S=null,D=null,H=null;for(O in a){var x=a[O];if(a.hasOwnProperty(O)&&x!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":S=x;default:l.hasOwnProperty(O)||Oe(e,t,O,null,l,x)}}for(var R in l){var O=l[R];if(x=a[R],l.hasOwnProperty(R)&&(O!=null||x!=null))switch(R){case"type":u=O;break;case"name":n=O;break;case"checked":D=O;break;case"defaultChecked":H=O;break;case"value":r=O;break;case"defaultValue":o=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(_(137,t));break;default:O!==x&&Oe(e,t,R,O,l,x)}}Cu(e,r,o,S,D,H,u,n);return;case"select":O=r=o=R=null;for(u in a)if(S=a[u],a.hasOwnProperty(u)&&S!=null)switch(u){case"value":break;case"multiple":O=S;default:l.hasOwnProperty(u)||Oe(e,t,u,null,l,S)}for(n in l)if(u=l[n],S=a[n],l.hasOwnProperty(n)&&(u!=null||S!=null))switch(n){case"value":R=u;break;case"defaultValue":o=u;break;case"multiple":r=u;default:u!==S&&Oe(e,t,n,u,l,S)}t=o,a=r,l=O,R!=null?Ra(e,!!a,R,!1):!!l!=!!a&&(t!=null?Ra(e,!!a,t,!0):Ra(e,!!a,a?[]:"",!1));return;case"textarea":O=R=null;for(o in a)if(n=a[o],a.hasOwnProperty(o)&&n!=null&&!l.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:Oe(e,t,o,null,l,n)}for(r in l)if(n=l[r],u=a[r],l.hasOwnProperty(r)&&(n!=null||u!=null))switch(r){case"value":R=n;break;case"defaultValue":O=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(_(91));break;default:n!==u&&Oe(e,t,r,n,l,u)}ji(e,R,O);return;case"option":for(var ne in a)if(R=a[ne],a.hasOwnProperty(ne)&&R!=null&&!l.hasOwnProperty(ne))switch(ne){case"selected":e.selected=!1;break;default:Oe(e,t,ne,null,l,R)}for(S in l)if(R=l[S],O=a[S],l.hasOwnProperty(S)&&R!==O&&(R!=null||O!=null))switch(S){case"selected":e.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:Oe(e,t,S,R,l,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var te in a)R=a[te],a.hasOwnProperty(te)&&R!=null&&!l.hasOwnProperty(te)&&Oe(e,t,te,null,l,R);for(D in l)if(R=l[D],O=a[D],l.hasOwnProperty(D)&&R!==O&&(R!=null||O!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(_(137,t));break;default:Oe(e,t,D,R,l,O)}return;default:if(Yt(t)){for(var _e in a)R=a[_e],a.hasOwnProperty(_e)&&R!==void 0&&!l.hasOwnProperty(_e)&&$r(e,t,_e,void 0,l,R);for(H in l)R=l[H],O=a[H],!l.hasOwnProperty(H)||R===O||R===void 0&&O===void 0||$r(e,t,H,R,l,O);return}}for(var N in a)R=a[N],a.hasOwnProperty(N)&&R!=null&&!l.hasOwnProperty(N)&&Oe(e,t,N,null,l,R);for(x in l)R=l[x],O=a[x],!l.hasOwnProperty(x)||R===O||R==null&&O==null||Oe(e,t,x,R,l,O)}var Qr=null,kr=null;function Xc(e){return e.nodeType===9?e:e.ownerDocument}function xo(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function qo(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Zr(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var Kr=null;function um(){var e=window.event;return e&&e.type==="popstate"?e===Kr?!1:(Kr=e,!0):(Kr=null,!1)}var wo=typeof setTimeout=="function"?setTimeout:void 0,im=typeof clearTimeout=="function"?clearTimeout:void 0,Yo=typeof Promise=="function"?Promise:void 0,cm=typeof queueMicrotask=="function"?queueMicrotask:typeof Yo<"u"?function(e){return Yo.resolve(null).then(e).catch(sm)}:wo;function sm(e){setTimeout(function(){throw e})}function Ql(e){return e==="head"}function Vo(e,t){var a=t,l=0,n=0;do{var u=a.nextSibling;if(e.removeChild(a),u&&u.nodeType===8)if(a=u.data,a==="/$"){if(0<l&&8>l){a=l;var r=e.ownerDocument;if(a&1&&Si(r.documentElement),a&2&&Si(r.body),a&4)for(a=r.head,Si(a),r=a.firstChild;r;){var o=r.nextSibling,S=r.nodeName;r[et]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&r.rel.toLowerCase()==="stylesheet"||a.removeChild(r),r=o}}if(n===0){e.removeChild(u),Ci(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=u}while(a);Ci(t)}function Jr(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Jr(a),qt(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function rm(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[et])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=Aa(e.nextSibling),e===null)break}return null}function fm(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Aa(e.nextSibling),e===null))return null;return e}function Wr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function dm(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Aa(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Fr=null;function Xo(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function $o(e,t,a){switch(t=Xc(a),e){case"html":if(e=t.documentElement,!e)throw Error(_(452));return e;case"head":if(e=t.head,!e)throw Error(_(453));return e;case"body":if(e=t.body,!e)throw Error(_(454));return e;default:throw Error(_(451))}}function Si(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);qt(e)}var na=new Map,Qo=new Set;function $c(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var il=q.d;q.d={f:om,r:hm,D:mm,C:ym,L:gm,m:vm,X:Am,S:Sm,M:Em};function om(){var e=il.f(),t=jc();return e||t}function hm(e){var t=da(e);t!==null&&t.tag===5&&t.type==="form"?fd(t):il.r(e)}var gu=typeof document>"u"?null:document;function ko(e,t,a){var l=gu;if(l&&typeof t=="string"&&t){var n=Et(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),Qo.has(n)||(Qo.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),gt(t,"link",e),Ve(t),l.head.appendChild(t)))}}function mm(e){il.D(e),ko("dns-prefetch",e,null)}function ym(e,t){il.C(e,t),ko("preconnect",e,t)}function gm(e,t,a){il.L(e,t,a);var l=gu;if(l&&e&&t){var n='link[rel="preload"][as="'+Et(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+Et(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+Et(a.imageSizes)+'"]')):n+='[href="'+Et(e)+'"]';var u=n;switch(t){case"style":u=vu(e);break;case"script":u=Su(e)}na.has(u)||(e=U({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),na.set(u,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Ai(u))||t==="script"&&l.querySelector(Ei(u))||(t=l.createElement("link"),gt(t,"link",e),Ve(t),l.head.appendChild(t)))}}function vm(e,t){il.m(e,t);var a=gu;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+Et(l)+'"][href="'+Et(e)+'"]',u=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Su(e)}if(!na.has(u)&&(e=U({rel:"modulepreload",href:e},t),na.set(u,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Ei(u)))return}l=a.createElement("link"),gt(l,"link",e),Ve(l),a.head.appendChild(l)}}}function Sm(e,t,a){il.S(e,t,a);var l=gu;if(l&&e){var n=fl(l).hoistableStyles,u=vu(e);t=t||"default";var r=n.get(u);if(!r){var o={loading:0,preload:null};if(r=l.querySelector(Ai(u)))o.loading=5;else{e=U({rel:"stylesheet",href:e,"data-precedence":t},a),(a=na.get(u))&&Pr(e,a);var S=r=l.createElement("link");Ve(S),gt(S,"link",e),S._p=new Promise(function(D,H){S.onload=D,S.onerror=H}),S.addEventListener("load",function(){o.loading|=1}),S.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Qc(r,t,l)}r={type:"stylesheet",instance:r,count:1,state:o},n.set(u,r)}}}function Am(e,t){il.X(e,t);var a=gu;if(a&&e){var l=fl(a).hoistableScripts,n=Su(e),u=l.get(n);u||(u=a.querySelector(Ei(n)),u||(e=U({src:e,async:!0},t),(t=na.get(n))&&Ir(e,t),u=a.createElement("script"),Ve(u),gt(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function Em(e,t){il.M(e,t);var a=gu;if(a&&e){var l=fl(a).hoistableScripts,n=Su(e),u=l.get(n);u||(u=a.querySelector(Ei(n)),u||(e=U({src:e,async:!0,type:"module"},t),(t=na.get(n))&&Ir(e,t),u=a.createElement("script"),Ve(u),gt(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function Zo(e,t,a,l){var n=(n=ie.current)?$c(n):null;if(!n)throw Error(_(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=vu(a.href),a=fl(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=vu(a.href);var u=fl(n).hoistableStyles,r=u.get(e);if(r||(n=n.ownerDocument||n,r={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,r),(u=n.querySelector(Ai(e)))&&!u._p&&(r.instance=u,r.state.loading=5),na.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},na.set(e,a),u||pm(n,e,a,r.state))),t&&l===null)throw Error(_(528,""));return r}if(t&&l!==null)throw Error(_(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=Su(a),a=fl(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(_(444,e))}}function vu(e){return'href="'+Et(e)+'"'}function Ai(e){return'link[rel="stylesheet"]['+e+"]"}function Ko(e){return U({},e,{"data-precedence":e.precedence,precedence:null})}function pm(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),gt(t,"link",a),Ve(t),e.head.appendChild(t))}function Su(e){return'[src="'+Et(e)+'"]'}function Ei(e){return"script[async]"+e}function Jo(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+Et(a.href)+'"]');if(l)return t.instance=l,Ve(l),l;var n=U({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),Ve(l),gt(l,"style",n),Qc(l,a.precedence,e),t.instance=l;case"stylesheet":n=vu(a.href);var u=e.querySelector(Ai(n));if(u)return t.state.loading|=4,t.instance=u,Ve(u),u;l=Ko(a),(n=na.get(n))&&Pr(l,n),u=(e.ownerDocument||e).createElement("link"),Ve(u);var r=u;return r._p=new Promise(function(o,S){r.onload=o,r.onerror=S}),gt(u,"link",l),t.state.loading|=4,Qc(u,a.precedence,e),t.instance=u;case"script":return u=Su(a.src),(n=e.querySelector(Ei(u)))?(t.instance=n,Ve(n),n):(l=a,(n=na.get(u))&&(l=U({},a),Ir(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),Ve(n),gt(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(_(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Qc(l,a.precedence,e));return t.instance}function Qc(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,u=n,r=0;r<l.length;r++){var o=l[r];if(o.dataset.precedence===t)u=o;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Pr(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function Ir(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var kc=null;function Wo(e,t,a){if(kc===null){var l=new Map,n=kc=new Map;n.set(a,l)}else n=kc,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var u=a[n];if(!(u[et]||u[Ye]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var r=u.getAttribute(t)||"";r=e+r;var o=l.get(r);o?o.push(u):l.set(r,[u])}}return l}function Fo(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function Tm(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Po(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var pi=null;function Nm(){}function bm(e,t,a){if(pi===null)throw Error(_(475));var l=pi;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=vu(a.href),u=e.querySelector(Ai(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Zc.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=u,Ve(u);return}u=e.ownerDocument||e,a=Ko(a),(n=na.get(n))&&Pr(a,n),u=u.createElement("link"),Ve(u);var r=u;r._p=new Promise(function(o,S){r.onload=o,r.onerror=S}),gt(u,"link",a),t.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Zc.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Dm(){if(pi===null)throw Error(_(475));var e=pi;return e.stylesheets&&e.count===0&&ef(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&ef(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Zc(){if(this.count--,this.count===0){if(this.stylesheets)ef(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Kc=null;function ef(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Kc=new Map,t.forEach(Cm,e),Kc=null,Zc.call(e))}function Cm(e,t){if(!(t.state.loading&4)){var a=Kc.get(e);if(a)var l=a.get(null);else{a=new Map,Kc.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var r=n[u];(r.nodeName==="LINK"||r.getAttribute("media")!=="not all")&&(a.set(r.dataset.precedence,r),l=r)}l&&a.set(null,l)}n=t.instance,r=n.getAttribute("data-precedence"),u=a.get(r)||l,u===l&&a.set(null,n),a.set(r,n),this.count++,l=Zc.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Ti={$$typeof:Je,Provider:null,Consumer:null,_currentValue:ee,_currentValue2:ee,_threadCount:0};function Rm(e,t,a,l,n,u,r,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Bn(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Bn(0),this.hiddenUpdates=Bn(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=r,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Io(e,t,a,l,n,u,r,o,S,D,H,x){return e=new Rm(e,t,a,r,o,S,D,x),t=1,u===!0&&(t|=24),u=Xt(3,null,null,t),e.current=u,u.stateNode=e,t=js(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:l,isDehydrated:a,cache:t},qs(u),e}function eh(e){return e?(e=Jn,e):Jn}function th(e,t,a,l,n,u){n=eh(n),l.context===null?l.context=n:l.pendingContext=n,l=Gl(t),l.payload={element:a},u=u===void 0?null:u,u!==null&&(l.callback=u),a=Hl(e,l,t),a!==null&&(Kt(a,e,t),Pu(a,e,t))}function ah(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function tf(e,t){ah(e,t),(e=e.alternate)&&ah(e,t)}function lh(e){if(e.tag===13){var t=Kn(e,67108864);t!==null&&Kt(t,e,67108864),tf(e,67108864)}}var Jc=!0;function Om(e,t,a,l){var n=G.T;G.T=null;var u=q.p;try{q.p=2,af(e,t,a,l)}finally{q.p=u,G.T=n}}function _m(e,t,a,l){var n=G.T;G.T=null;var u=q.p;try{q.p=8,af(e,t,a,l)}finally{q.p=u,G.T=n}}function af(e,t,a,l){if(Jc){var n=lf(l);if(n===null)Xr(e,t,l,Wc,a),uh(e,l);else if(Bm(n,e,t,a,l))l.stopPropagation();else if(uh(e,l),t&4&&-1<Mm.indexOf(e)){for(;n!==null;){var u=da(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var r=Ta(u.pendingLanes);if(r!==0){var o=u;for(o.pendingLanes|=2,o.entangledLanes|=2;r;){var S=1<<31-St(r);o.entanglements[1]|=S,r&=~S}ja(u),(De&6)===0&&(Hc=_t()+500,yi(0))}}break;case 13:o=Kn(u,2),o!==null&&Kt(o,u,2),jc(),tf(u,2)}if(u=lf(l),u===null&&Xr(e,t,l,Wc,a),u===n)break;n=u}n!==null&&l.stopPropagation()}else Xr(e,t,l,null,a)}}function lf(e){return e=Sl(e),nf(e)}var Wc=null;function nf(e){if(Wc=null,e=Pt(e),e!==null){var t=V(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=B(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Wc=e,null}function nh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Pl()){case Rn:return 2;case Mt:return 8;case pa:case Mi:return 32;case On:return 268435456;default:return 32}default:return 32}}var uf=!1,kl=null,Zl=null,Kl=null,Ni=new Map,bi=new Map,Jl=[],Mm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function uh(e,t){switch(e){case"focusin":case"focusout":kl=null;break;case"dragenter":case"dragleave":Zl=null;break;case"mouseover":case"mouseout":Kl=null;break;case"pointerover":case"pointerout":Ni.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":bi.delete(t.pointerId)}}function Di(e,t,a,l,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:u,targetContainers:[n]},t!==null&&(t=da(t),t!==null&&lh(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Bm(e,t,a,l,n){switch(t){case"focusin":return kl=Di(kl,e,t,a,l,n),!0;case"dragenter":return Zl=Di(Zl,e,t,a,l,n),!0;case"mouseover":return Kl=Di(Kl,e,t,a,l,n),!0;case"pointerover":var u=n.pointerId;return Ni.set(u,Di(Ni.get(u)||null,e,t,a,l,n)),!0;case"gotpointercapture":return u=n.pointerId,bi.set(u,Di(bi.get(u)||null,e,t,a,l,n)),!0}return!1}function ih(e){var t=Pt(e.target);if(t!==null){var a=V(t);if(a!==null){if(t=a.tag,t===13){if(t=B(a),t!==null){e.blockedOn=t,nn(e.priority,function(){if(a.tag===13){var l=Zt();l=bu(l);var n=Kn(a,l);n!==null&&Kt(n,a,l),tf(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Fc(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=lf(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);vl=l,a.target.dispatchEvent(l),vl=null}else return t=da(a),t!==null&&lh(t),e.blockedOn=a,!1;t.shift()}return!0}function ch(e,t,a){Fc(e)&&a.delete(t)}function Gm(){uf=!1,kl!==null&&Fc(kl)&&(kl=null),Zl!==null&&Fc(Zl)&&(Zl=null),Kl!==null&&Fc(Kl)&&(Kl=null),Ni.forEach(ch),bi.forEach(ch)}function Pc(e,t){e.blockedOn===t&&(e.blockedOn=null,uf||(uf=!0,J.unstable_scheduleCallback(J.unstable_NormalPriority,Gm)))}var Ic=null;function sh(e){Ic!==e&&(Ic=e,J.unstable_scheduleCallback(J.unstable_NormalPriority,function(){Ic===e&&(Ic=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(nf(l||a)===null)continue;break}var u=da(a);u!==null&&(e.splice(t,3),t-=3,nr(u,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function Ci(e){function t(S){return Pc(S,e)}kl!==null&&Pc(kl,e),Zl!==null&&Pc(Zl,e),Kl!==null&&Pc(Kl,e),Ni.forEach(t),bi.forEach(t);for(var a=0;a<Jl.length;a++){var l=Jl[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Jl.length&&(a=Jl[0],a.blockedOn===null);)ih(a),a.blockedOn===null&&Jl.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],u=a[l+1],r=n[se]||null;if(typeof u=="function")r||sh(a);else if(r){var o=null;if(u&&u.hasAttribute("formAction")){if(n=u,r=u[se]||null)o=r.formAction;else if(nf(n)!==null)continue}else o=r.action;typeof o=="function"?a[l+1]=o:(a.splice(l,3),l-=3),sh(a)}}}function cf(e){this._internalRoot=e}es.prototype.render=cf.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(_(409));var a=t.current,l=Zt();th(a,l,e,t,null,null)},es.prototype.unmount=cf.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;th(e.current,2,null,e,null,null),jc(),t[fa]=null}};function es(e){this._internalRoot=e}es.prototype.unstable_scheduleHydration=function(e){if(e){var t=qa();e={blockedOn:null,target:e,priority:t};for(var a=0;a<Jl.length&&t!==0&&t<Jl[a].priority;a++);Jl.splice(a,0,e),a===0&&ih(e)}};var rh=be.version;if(rh!=="19.1.0")throw Error(_(527,rh,"19.1.0"));q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=$(t),e=e!==null?M(e):null,e=e===null?null:e.stateNode,e};var Hm={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:G,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var ts=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!ts.isDisabled&&ts.supportsFiber)try{Il=ts.inject(Hm),je=ts}catch{}}return Oi.createRoot=function(e,t){if(!v(e))throw Error(_(299));var a=!1,l="",n=bd,u=Dd,r=Cd,o=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(r=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(o=t.unstable_transitionCallbacks)),t=Io(e,1,!1,null,null,a,l,n,u,r,o,null),e[fa]=t.current,Vr(e),new cf(t)},Oi.hydrateRoot=function(e,t,a){if(!v(e))throw Error(_(299));var l=!1,n="",u=bd,r=Dd,o=Cd,S=null,D=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(u=a.onUncaughtError),a.onCaughtError!==void 0&&(r=a.onCaughtError),a.onRecoverableError!==void 0&&(o=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(S=a.unstable_transitionCallbacks),a.formState!==void 0&&(D=a.formState)),t=Io(e,1,!0,t,a??null,l,n,u,r,o,S,D),t.context=eh(null),a=t.current,l=Zt(),l=bu(l),n=Gl(l),n.callback=null,Hl(a,n,l),a=l,t.current.lanes=a,rl(t,a),ja(t),e[fa]=t.current,Vr(e),new es(t)},Oi.version="19.1.0",Oi}var Ah;function Xm(){if(Ah)return ff.exports;Ah=1;function J(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(J)}catch(be){console.error(be)}}return J(),ff.exports=Vm(),ff.exports}var $m=Xm();const f={BANG:"BANG",MISSED:"MISSED",BEER:"BEER",PANIC:"PANIC",CAT_BALOU:"CAT_BALOU",STAGECOACH:"STAGECOACH",WELLS_FARGO:"WELLS_FARGO",GATLING:"GATLING",INDIANS:"INDIANS",DUEL:"DUEL",GENERAL_STORE:"GENERAL_STORE",SALOON:"SALOON",BARREL:"BARREL",SCOPE:"SCOPE",MUSTANG:"MUSTANG",JAIL:"JAIL",DYNAMITE:"DYNAMITE",VOLCANIC:"VOLCANIC",SCHOFIELD:"SCHOFIELD",REMINGTON:"REMINGTON",REV_CARABINE:"REV_CARABINE",WINCHESTER:"WINCHESTER",BINOCULAR:"BINOCULAR",HIDEOUT:"HIDEOUT",BRAWL:"BRAWL",DODGE:"DODGE",PUNCH:"PUNCH",RAG_TIME:"RAG_TIME",SPRINGFIELD:"SPRINGFIELD",TEQUILA:"TEQUILA",WHISKY:"WHISKY",BIBLE:"BIBLE",BUFFALO_RIFLE:"BUFFALO_RIFLE",CAN_CAN:"CAN_CAN",CANTEEN:"CANTEEN",CONESTOGA:"CONESTOGA",DERRINGER:"DERRINGER",HOWITZER:"HOWITZER",IRON_PLATE:"IRON_PLATE",KNIFE:"KNIFE",PEPPERBOX:"PEPPERBOX",PONY_EXPRESS:"PONY_EXPRESS",SOMBRERO:"SOMBRERO",TEN_GALLON_HAT:"TEN_GALLON_HAT"},L={HEARTS:"HEARTS",DIAMONDS:"DIAMONDS",CLUBS:"CLUBS",SPADES:"SPADES"},mf={[f.VOLCANIC]:1,[f.SCHOFIELD]:2,[f.REMINGTON]:3,[f.REV_CARABINE]:4,[f.WINCHESTER]:5,[f.PEPPERBOX]:1,[f.BUFFALO_RIFLE]:5},Jt={WEAPON:"WEAPON",DEFENSIVE:"DEFENSIVE",SPECIAL:"SPECIAL",GREEN:"GREEN"},Rt={[f.BANG]:"/images/cards/bang.png",[f.MISSED]:"/images/cards/missed.png",[f.BEER]:"/images/cards/beer.png",[f.PANIC]:"/images/cards/panic.png",[f.CAT_BALOU]:"/images/cards/cat_balou.png",[f.STAGECOACH]:"/images/cards/stagecoach.png",[f.WELLS_FARGO]:"/images/cards/wells_fargo.png",[f.GATLING]:"/images/cards/gatling.png",[f.INDIANS]:"/images/cards/indians.png",[f.DUEL]:"/images/cards/duel.png",[f.GENERAL_STORE]:"/images/cards/general_store.png",[f.SALOON]:"/images/cards/saloon.png",[f.BARREL]:"/images/cards/barrel.png",[f.SCOPE]:"/images/cards/scope.png",[f.MUSTANG]:"/images/cards/mustang.png",[f.JAIL]:"/images/cards/jail.png",[f.DYNAMITE]:"/images/cards/dynamite.png",[f.VOLCANIC]:"/images/cards/volcanic.png",[f.SCHOFIELD]:"/images/cards/schofield.png",[f.REMINGTON]:"/images/cards/remington.png",[f.REV_CARABINE]:"/images/cards/rev_carabine.png",[f.WINCHESTER]:"/images/cards/winchester.png",[f.BINOCULAR]:"/assets/cards/dodge_city/binocular.png",[f.HIDEOUT]:"/assets/cards/dodge_city/hideout.png",[f.BRAWL]:"/assets/cards/dodge_city/brawl.png",[f.DODGE]:"/assets/cards/dodge_city/dodge.png",[f.PUNCH]:"/assets/cards/dodge_city/punch.png",[f.RAG_TIME]:"/assets/cards/dodge_city/rag_time.png",[f.SPRINGFIELD]:"/assets/cards/dodge_city/springfield.png",[f.TEQUILA]:"/assets/cards/dodge_city/tequila.png",[f.WHISKY]:"/assets/cards/dodge_city/whisky.png",[f.BIBLE]:"/assets/cards/dodge_city/bible.png",[f.BUFFALO_RIFLE]:"/assets/cards/dodge_city/buffalo_rifle.png",[f.CAN_CAN]:"/assets/cards/dodge_city/can_can.png",[f.CANTEEN]:"/assets/cards/dodge_city/canteen.png",[f.CONESTOGA]:"/assets/cards/dodge_city/conestoga.png",[f.DERRINGER]:"/assets/cards/dodge_city/derringer.png",[f.HOWITZER]:"/assets/cards/dodge_city/howitzer.png",[f.IRON_PLATE]:"/assets/cards/dodge_city/iron_plate.png",[f.KNIFE]:"/assets/cards/dodge_city/knife.png",[f.PEPPERBOX]:"/assets/cards/dodge_city/pepperbox.png",[f.PONY_EXPRESS]:"/assets/cards/dodge_city/pony_express.png",[f.SOMBRERO]:"/assets/cards/dodge_city/sombrero.png",[f.TEN_GALLON_HAT]:"/assets/cards/dodge_city/ten_gallon_hat.png"},_i={"Bart Cassidy":"/images/characters/bart_cassidy.png","Black Jack":"/images/characters/black_jack.png","Calamity Janet":"/images/characters/calamity_janet.png","El Gringo":"/images/characters/el_gringo.png","Jesse Jones":"/images/characters/jesse_jones.png",Jourdonnais:"/images/characters/jourdonnais.png","Kit Carlson":"/images/characters/kit_carlson.png","Lucky Duke":"/images/characters/lucky_duke.png","Paul Regret":"/images/characters/paul_regret.png","Pedro Ramirez":"/images/characters/pedro_ramirez.png","Rose Doolan":"/images/characters/rose_doolan.png","Sid Ketchum":"/images/characters/sid_ketchum.png","Slab the Killer":"/images/characters/slab_the_killer.png","Suzy Lafayette":"/images/characters/suzy_lafayette.png","Vulture Sam":"/images/characters/vulture_sam.png","Willy the Kid":"/images/characters/willy_the_kid.png","Apache Kid":"/assets/characters/dodge_city/apache_kid.png","Belle Star":"/assets/characters/dodge_city/belle_star.png","Bill Noface":"/assets/characters/dodge_city/bill_noface.png","Chuck Wengam":"/assets/characters/dodge_city/chuck_wengam.png","Doc Holyday":"/assets/characters/dodge_city/doc_holyday.png","Elena Fuente":"/assets/characters/dodge_city/elena_fuente.png","Greg Digger":"/assets/characters/dodge_city/greg_digger.png","Herb Hunter":"/assets/characters/dodge_city/herb_hunter.png","José Delgado":"/assets/characters/dodge_city/jose_delgado.png","Molly Stark":"/assets/characters/dodge_city/molly_stark.png","Pat Brennan":"/assets/characters/dodge_city/pat_brennan.png","Pixie Pete":"/assets/characters/dodge_city/pixie_pete.png","Sean Mallory":"/assets/characters/dodge_city/sean_mallory.png","Tequila Joe":"/assets/characters/dodge_city/tequila_joe.png","Vera Custer":"/assets/characters/dodge_city/vera_custer.png"},Qm="/images/box/card_back.png",Ae={SHERIFF:"SHERIFF",DEPUTY:"DEPUTY",OUTLAW:"OUTLAW",RENEGADE:"RENEGADE"},km={[Ae.SHERIFF]:"/images/roles/sheriff.png",[Ae.DEPUTY]:"/images/roles/deputy.png",[Ae.OUTLAW]:"/images/roles/outlaw.png",[Ae.RENEGADE]:"/images/roles/renegade.png"},Zm=[{name:"Bart Cassidy",life:4,ability:"Draws a card when he loses a life point",abilityType:"ON_DAMAGE_TAKEN"},{name:"Black Jack",life:4,ability:"Shows second card when drawing; draws again if heart/diamond",abilityType:"ON_DRAW"},{name:"Calamity Janet",life:4,ability:"Can play BANG! as Missed! and vice versa",abilityType:"CARD_SUBSTITUTION"},{name:"El Gringo",life:3,ability:"Draws from attacker when hit",abilityType:"ON_DAMAGE_TAKEN"},{name:"Jesse Jones",life:4,ability:"Can draw first card from another player",abilityType:"DRAW_CHOICE"},{name:"Jourdonnais",life:4,ability:"Has built-in Barrel effect",abilityType:"BUILT_IN_BARREL"},{name:"Kit Carlson",life:4,ability:"Sees top 3 cards when drawing, chooses 2",abilityType:"DRAW_CHOICE"},{name:"Lucky Duke",life:4,ability:"Flips 2 cards for checks, chooses one",abilityType:"LUCKY_DRAW"},{name:"Paul Regret",life:3,ability:"Has built-in Mustang effect",abilityType:"BUILT_IN_MUSTANG"},{name:"Pedro Ramirez",life:4,ability:"Can draw first card from discard pile",abilityType:"DRAW_CHOICE"},{name:"Rose Doolan",life:4,ability:"She sees all players at a distance decreased by 1",abilityType:"BUILT_IN_SCOPE"},{name:"Sid Ketchum",life:4,ability:"Can discard 2 cards to regain 1 life point",abilityType:"ACTIVE_ABILITY"},{name:"Slab the Killer",life:4,ability:"Players need 2 Missed! to avoid his BANG!",abilityType:"ATTACK_MODIFIER"},{name:"Suzy Lafayette",life:4,ability:"Draws a card when she has no cards in hand",abilityType:"AUTO_DRAW"},{name:"Vulture Sam",life:4,ability:"Takes cards of eliminated players",abilityType:"ON_ELIMINATION"},{name:"Willy the Kid",life:4,ability:"Can play any number of BANG! cards",abilityType:"UNLIMITED_BANG"},{name:"Apache Kid",life:3,ability:"Unaffected by Diamond cards played by other players (except during Duels)",abilityType:"DIAMOND_IMMUNITY"},{name:"Belle Star",life:4,ability:"During her turn, no card in front of any other player has any effect",abilityType:"DISABLE_EQUIPMENT"},{name:"Bill Noface",life:4,ability:"Draws 1 card plus 1 for each injury (lost life point) during phase 1",abilityType:"INJURY_DRAW"},{name:"Chuck Wengam",life:4,ability:"Can lose 1 life to draw 2 cards (multiple times per turn, not last life)",abilityType:"LIFE_FOR_CARDS"},{name:"Doc Holyday",life:4,ability:"Once per turn, discard 2 cards for BANG! effect (doesn't count toward limit)",abilityType:"DISCARD_FOR_BANG"},{name:"Elena Fuente",life:3,ability:"Can use any card in her hand as a Missed!",abilityType:"ANY_AS_MISSED"},{name:"Greg Digger",life:4,ability:"Regains 2 life when another character is eliminated",abilityType:"HEAL_ON_ELIMINATION"},{name:"Herb Hunter",life:4,ability:"Draws 2 extra cards when another character is eliminated",abilityType:"DRAW_ON_ELIMINATION"},{name:"José Delgado",life:4,ability:"Can discard a blue card to draw 2 cards (twice per turn)",abilityType:"BLUE_FOR_CARDS"},{name:"Molly Stark",life:4,ability:"Draws 1 card when voluntarily discarding Missed!/Beer/BANG! on others' turns",abilityType:"VOLUNTARY_DISCARD_DRAW"},{name:"Pat Brennan",life:4,ability:"Can draw 1 card from in play instead of 2 from deck during phase 1",abilityType:"STEAL_OR_DRAW"},{name:"Pixie Pete",life:3,ability:"Draws 3 cards instead of 2 during phase 1",abilityType:"ENHANCED_DRAW"},{name:"Sean Mallory",life:3,ability:"Can hold up to 10 cards in hand during phase 3",abilityType:"EXTENDED_HAND_LIMIT"},{name:"Tequila Joe",life:4,ability:"Regains 2 life from Beer instead of 1 (only 1 from other healing cards)",abilityType:"ENHANCED_BEER"},{name:"Vera Custer",life:3,ability:"At turn start, copies another character's ability until next turn",abilityType:"COPY_ABILITY"}];function Km(){var wu,Yu,Vu,Qn,ac,ka,kn,_l,Zn,Xu,Ma,Za;const[J,be]=Y.useState("setup"),[Te,_]=Y.useState(null),[v,V]=Y.useState([]),[B,He]=Y.useState(0),[$,M]=Y.useState([]),[U,X]=Y.useState([]),[me,ut]=Y.useState(4),[dt,E]=Y.useState("Welcome to BANG!"),[Ke,ua]=Y.useState(null),[Ea,Je]=Y.useState({}),[Lt,ge]=Y.useState({}),[Dt,Ot]=Y.useState([]),Ie=(i,c=!1,s=3e3)=>{if(Ke&&(clearTimeout(Ke),ua(null)),E(i),c){const d=setTimeout(()=>{E(""),ua(null)},s);ua(d)}},xt=Y.useCallback(()=>{gl(120),Sl(!0)},[]),Wt=Y.useCallback(()=>{Sl(!1),gl(120)},[]),ia=i=>{const c=Math.floor(i/60),s=i%60;return`${c}:${s.toString().padStart(2,"0")}`},[Ue,za]=Y.useState(!0),[ca,it]=Y.useState(null),G=Y.useCallback(()=>{if(!ca&&Ue){const i=new(window.AudioContext||window.webkitAudioContext);return it(i),i}return ca},[ca,Ue]),q=Y.useCallback(i=>{if(!Ue)return;if(i==="dominating")try{const s=new Audio("/assets/audio/Dominating.mp3");s.volume=.7,s.play().catch(d=>{console.log("Could not play dominating sound:",d),q("killingSpree")});return}catch(s){console.log("Error creating dominating audio:",s),q("killingSpree");return}if(i==="unstoppable")try{const s=new Audio("/assets/audio/Unstoppable.mp3");s.volume=.8,s.play().catch(d=>{console.log("Could not play unstoppable sound:",d),q("victory")});return}catch(s){console.log("Error creating unstoppable audio:",s),q("victory");return}if(i==="godlike")try{const s=new Audio("/assets/audio/Godlike.mp3");s.volume=.7,s.play().catch(d=>{console.log("Could not play godlike sound:",d),q("damage")});return}catch(s){console.log("Error creating godlike audio:",s),q("damage");return}const c=G();if(c)try{const s=c.createOscillator(),d=c.createGain();switch(s.connect(d),d.connect(c.destination),i){case"turnStart":s.frequency.setValueAtTime(800,c.currentTime),s.frequency.setValueAtTime(600,c.currentTime+.1),d.gain.setValueAtTime(.3,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.8),s.start(c.currentTime),s.stop(c.currentTime+.8);break;case"damage":s.frequency.setValueAtTime(200,c.currentTime),s.frequency.exponentialRampToValueAtTime(100,c.currentTime+.3),d.gain.setValueAtTime(.4,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.3),s.start(c.currentTime),s.stop(c.currentTime+.3);break;case"cardPlay":s.frequency.setValueAtTime(400,c.currentTime),s.frequency.setValueAtTime(300,c.currentTime+.05),d.gain.setValueAtTime(.2,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.15),s.start(c.currentTime),s.stop(c.currentTime+.15);break;case"cardDraw":s.frequency.setValueAtTime(500,c.currentTime),s.frequency.setValueAtTime(450,c.currentTime+.1),d.gain.setValueAtTime(.15,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2);break;case"elimination":s.frequency.setValueAtTime(150,c.currentTime),s.frequency.exponentialRampToValueAtTime(50,c.currentTime+1),d.gain.setValueAtTime(.5,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+1),s.start(c.currentTime),s.stop(c.currentTime+1);break;case"defense":s.frequency.setValueAtTime(600,c.currentTime),s.frequency.setValueAtTime(800,c.currentTime+.1),d.gain.setValueAtTime(.25,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.4),s.start(c.currentTime),s.stop(c.currentTime+.4);break;case"heal":s.frequency.setValueAtTime(400,c.currentTime),s.frequency.setValueAtTime(600,c.currentTime+.2),s.frequency.setValueAtTime(800,c.currentTime+.4),d.gain.setValueAtTime(.2,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.6),s.start(c.currentTime),s.stop(c.currentTime+.6);break;case"killingSpree":{s.type="sawtooth",s.frequency.setValueAtTime(80,c.currentTime),s.frequency.exponentialRampToValueAtTime(40,c.currentTime+.3),d.gain.setValueAtTime(.6,c.currentTime),d.gain.exponentialRampToValueAtTime(.1,c.currentTime+.3),s.start(c.currentTime);const h=c.createOscillator(),m=c.createGain();h.connect(m),m.connect(c.destination),h.type="sawtooth",h.frequency.setValueAtTime(160,c.currentTime+.1),h.frequency.exponentialRampToValueAtTime(80,c.currentTime+.4),m.gain.setValueAtTime(.4,c.currentTime+.1),m.gain.exponentialRampToValueAtTime(.01,c.currentTime+.4),h.start(c.currentTime+.1),h.stop(c.currentTime+.4);const y=c.createOscillator(),T=c.createGain();y.connect(T),T.connect(c.destination),y.type="triangle",y.frequency.setValueAtTime(200,c.currentTime+.5),y.frequency.exponentialRampToValueAtTime(400,c.currentTime+1.2),T.gain.setValueAtTime(.5,c.currentTime+.5),T.gain.exponentialRampToValueAtTime(.01,c.currentTime+1.2),y.start(c.currentTime+.5),y.stop(c.currentTime+1.2);const C=c.createOscillator(),j=c.createGain();C.connect(j),j.connect(c.destination),C.type="square",C.frequency.setValueAtTime(300,c.currentTime+1),C.frequency.setValueAtTime(350,c.currentTime+1.5),j.gain.setValueAtTime(.3,c.currentTime+1),j.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),C.start(c.currentTime+1),C.stop(c.currentTime+2),s.stop(c.currentTime+.3);break}case"victory":{s.type="triangle",s.frequency.setValueAtTime(440,c.currentTime),s.frequency.setValueAtTime(554,c.currentTime+.3),s.frequency.setValueAtTime(659,c.currentTime+.6),s.frequency.setValueAtTime(880,c.currentTime+.9),d.gain.setValueAtTime(.5,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),s.start(c.currentTime);const h=c.createOscillator(),m=c.createGain();h.connect(m),m.connect(c.destination),h.type="triangle",h.frequency.setValueAtTime(330,c.currentTime),h.frequency.setValueAtTime(415,c.currentTime+.3),h.frequency.setValueAtTime(494,c.currentTime+.6),h.frequency.setValueAtTime(659,c.currentTime+.9),m.gain.setValueAtTime(.3,c.currentTime),m.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),h.start(c.currentTime),h.stop(c.currentTime+2),s.stop(c.currentTime+2);break}case"warning":s.frequency.setValueAtTime(1e3,c.currentTime),s.frequency.setValueAtTime(800,c.currentTime+.1),d.gain.setValueAtTime(.3,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2);break;default:s.frequency.setValueAtTime(440,c.currentTime),d.gain.setValueAtTime(.2,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2)}}catch(s){console.log("Audio playback failed:",s)}},[Ue,G]),ee=(i,c,s,d)=>{const h=`${Date.now()}-${Math.random()}-${bu+1}`;Du(T=>T+1);const m={id:h,card:i,fromPlayerIndex:c,toPlayerIndex:s,phase:"toTarget",progress:0,startTime:Date.now()};ln(T=>[...T,m]);const y=()=>{const T=Date.now()-m.startTime,j=Math.min(T/800,1);ln(Q=>Q.map(F=>F.id===h?{...F,progress:j}:F)),j<1?requestAnimationFrame(y):setTimeout(()=>{m.phase="toDiscard",m.startTime=Date.now(),m.progress=0;const Q=()=>{const F=Date.now()-m.startTime,ue=Math.min(F/600,1);ln(ve=>ve.map(Me=>Me.id===h?{...Me,progress:ue}:Me)),ue<1?requestAnimationFrame(Q):ln(ve=>ve.filter(Me=>Me.id!==h))};requestAnimationFrame(Q)},300)};requestAnimationFrame(y)},le=Y.useCallback((i,c,s=2e3)=>{const d=`${i}-${c}-${Date.now()}`;Je(h=>({...h,[d]:{playerIndex:i,effectType:c,timestamp:Date.now()}})),setTimeout(()=>{Je(h=>{const m={...h};return delete m[d],m})},s)},[Je]),A=(i,c,s="damage")=>{const d=document.querySelector(`[data-player-index="${i}"]`);if(!d)return;const h=document.createElement("div");h.className=s==="damage"?"floating-damage":"floating-heal",h.textContent=s==="damage"?`-${c}`:`+${c}`,d.style.position="relative",d.appendChild(h),setTimeout(()=>{h.parentNode&&h.parentNode.removeChild(h)},2e3)},w=()=>{const i=document.querySelector(".game-board");i&&(i.classList.add("screen-shake"),setTimeout(()=>{i.classList.remove("screen-shake")},800))},[k,Z]=Y.useState(null),[I,fe]=Y.useState(!1),[ie,vt]=Y.useState(null),[ye,Ft]=Y.useState(null),[Au,cl]=Y.useState(!1),[sl,Fl]=Y.useState(null),[Cn,La]=Y.useState(!1),[sa,_t]=Y.useState(null),[Pl,Rn]=Y.useState(null),[Mt,pa]=Y.useState(null),[Mi,On]=Y.useState(!1),[Eu,Bi]=Y.useState(null),[Il,je]=Y.useState(!1),[ra,St]=Y.useState(!1),[as,pu]=Y.useState(!1),[_n,en]=Y.useState(null),[tn,Ta]=Y.useState(null),[Mn,xa]=Y.useState(!1),[Na,Tu]=Y.useState(null),[Nu,Bn]=Y.useState(null),[rl,an]=Y.useState(!1),[Gi,ln]=Y.useState([]),[bu,Du]=Y.useState(0),[qa,nn]=Y.useState(!1),[ze,Ye]=Y.useState([]),[se,fa]=Y.useState([]),[qe,un]=Y.useState(0),[At,Bt]=Y.useState("draw"),[et,qt]=Y.useState(!1),[Pt,da]=Y.useState(!1),[ba,fl]=Y.useState(0),Ve=Y.useRef(-1),[cn,Gn]=Y.useState(!1),[wa,Da]=Y.useState(!1),[oa,Hn]=Y.useState(0),[ha,Un]=Y.useState([]),[dl,wt]=Y.useState(0),[ma,jn]=Y.useState(new Set),[Hi,Ya]=Y.useState(!1),[Va,zn]=Y.useState(null),[ls,Ln]=Y.useState(!1),[ot,xn]=Y.useState(null),[Ca,ol]=Y.useState(null),[Ui,hl]=Y.useState(!1),[qn,Et]=Y.useState(null),[Cu,wn]=Y.useState(!1),[ml,Ra]=Y.useState(null),[ji,Yn]=Y.useState(null),[yl,Ru]=Y.useState(!1),[zi,Vn]=Y.useState(null),[Yt,Ou]=Y.useState(null),[Xa,gl]=Y.useState(120),[vl,Sl]=Y.useState(!1),[We,Oa]=Y.useState(1),[sn,Al]=Y.useState([""]),_u=Y.useCallback(()=>{var c;Sl(!1),E(`Time's up! ${(c=v[B])==null?void 0:c.name}'s turn is over.`);const i=v[B];if(i&&i.hand.length>i.health){const s=i.hand.length-i.health,d=[...v],h=[];for(let m=0;m<s;m++){const y=Math.floor(Math.random()*d[B].hand.length),T=d[B].hand.splice(y,1)[0];h.push(T)}V(d),X(m=>[...m,...h]),E(`${i.name} auto-discarded ${s} cards due to time limit.`)}Bt("timeUp")},[v,B,Sl,E,X,V,Bt]);Y.useEffect(()=>{At==="timeUp"&&setTimeout(()=>{let c=(B+1)%v.length;for(let s=0;s<v.length&&!v[c].isAlive;s++)c=(c+1)%v.length;He(c),Bt("draw"),qt(!1),wt(0),E(`${v[c].name}'s turn!`)},1500)},[At,B,v,He,Bt,qt,wt,E]),Y.useEffect(()=>{let i=null;return vl&&Xa>0&&J==="playing"?i=setInterval(()=>{gl(c=>c<=1?(_u(),0):c-1)},1e3):(!vl||Xa<=0)&&clearInterval(i),()=>clearInterval(i)},[vl,J,Xa,v,B,_u]);const El=()=>{var m;if(me<4||me>7){E("Please select 4-7 players");return}let i=ya();const c=Mu(me),s=[...Zm].sort(()=>Math.random()-.5),d=[];for(let y=0;y<me;y++){const T=s[y],C=c[y],j=C===Ae.SHERIFF?T.life+1:T.life,Q=Ee(i,j),F=Q.drawnCards||[];i=Q.updatedDeck;let K,ue;y<We?(K=((m=sn[y])==null?void 0:m.trim())||`Player ${y+1}`,ue=!1):(K=`Bot ${y-We+1}`,ue=!0),d.push({id:y,character:T,role:C,health:j,maxHealth:j,hand:Array.isArray(F)?F:[],inPlay:[],isAlive:!0,isBot:ue,name:K})}const h=d.findIndex(y=>y.role===Ae.SHERIFF);He(h),V(d),M(i),be("playing"),Bt("draw"),qt(!1),wt(0),E(`Game started! ${d[h].name} is the Sheriff and goes first. Cards will be drawn automatically.`)},ya=()=>{const i=[{type:f.BANG,suit:L.SPADES,value:"A"},{type:f.BANG,suit:L.CLUBS,value:"2"},{type:f.BANG,suit:L.CLUBS,value:"3"},{type:f.BANG,suit:L.CLUBS,value:"4"},{type:f.BANG,suit:L.CLUBS,value:"5"},{type:f.BANG,suit:L.CLUBS,value:"6"},{type:f.BANG,suit:L.CLUBS,value:"7"},{type:f.BANG,suit:L.CLUBS,value:"8"},{type:f.BANG,suit:L.CLUBS,value:"9"},{type:f.BANG,suit:L.DIAMONDS,value:"2"},{type:f.BANG,suit:L.DIAMONDS,value:"3"},{type:f.BANG,suit:L.DIAMONDS,value:"4"},{type:f.BANG,suit:L.DIAMONDS,value:"5"},{type:f.BANG,suit:L.DIAMONDS,value:"6"},{type:f.BANG,suit:L.DIAMONDS,value:"7"},{type:f.BANG,suit:L.DIAMONDS,value:"8"},{type:f.BANG,suit:L.DIAMONDS,value:"9"},{type:f.BANG,suit:L.HEARTS,value:"12"},{type:f.BANG,suit:L.HEARTS,value:"13"},{type:f.BANG,suit:L.HEARTS,value:"A"},{type:f.BANG,suit:L.SPADES,value:"K"},{type:f.BANG,suit:L.SPADES,value:"Q"},{type:f.BANG,suit:L.SPADES,value:"J"},{type:f.BANG,suit:L.SPADES,value:"10"},{type:f.BANG,suit:L.SPADES,value:"9"},{type:f.MISSED,suit:L.SPADES,value:"8"},{type:f.MISSED,suit:L.SPADES,value:"7"},{type:f.MISSED,suit:L.SPADES,value:"6"},{type:f.MISSED,suit:L.SPADES,value:"5"},{type:f.MISSED,suit:L.SPADES,value:"4"},{type:f.MISSED,suit:L.SPADES,value:"3"},{type:f.MISSED,suit:L.SPADES,value:"2"},{type:f.MISSED,suit:L.CLUBS,value:"A"},{type:f.MISSED,suit:L.CLUBS,value:"K"},{type:f.MISSED,suit:L.CLUBS,value:"Q"},{type:f.MISSED,suit:L.CLUBS,value:"J"},{type:f.MISSED,suit:L.CLUBS,value:"10"},{type:f.BEER,suit:L.HEARTS,value:"6"},{type:f.BEER,suit:L.HEARTS,value:"7"},{type:f.BEER,suit:L.HEARTS,value:"8"},{type:f.BEER,suit:L.HEARTS,value:"9"},{type:f.BEER,suit:L.HEARTS,value:"10"},{type:f.BEER,suit:L.HEARTS,value:"J"},{type:f.PANIC,suit:L.HEARTS,value:"4"},{type:f.PANIC,suit:L.DIAMONDS,value:"J"},{type:f.PANIC,suit:L.DIAMONDS,value:"Q"},{type:f.PANIC,suit:L.DIAMONDS,value:"A"},{type:f.CAT_BALOU,suit:L.DIAMONDS,value:"K"},{type:f.CAT_BALOU,suit:L.DIAMONDS,value:"10"},{type:f.CAT_BALOU,suit:L.HEARTS,value:"K"},{type:f.CAT_BALOU,suit:L.HEARTS,value:"Q"},{type:f.STAGECOACH,suit:L.SPADES,value:"9"},{type:f.STAGECOACH,suit:L.SPADES,value:"9"},{type:f.WELLS_FARGO,suit:L.HEARTS,value:"3"},{type:f.GATLING,suit:L.HEARTS,value:"10"},{type:f.INDIANS,suit:L.DIAMONDS,value:"K"},{type:f.INDIANS,suit:L.DIAMONDS,value:"A"},{type:f.DUEL,suit:L.CLUBS,value:"8"},{type:f.DUEL,suit:L.DIAMONDS,value:"J"},{type:f.DUEL,suit:L.SPADES,value:"J"},{type:f.GENERAL_STORE,suit:L.CLUBS,value:"9"},{type:f.GENERAL_STORE,suit:L.SPADES,value:"Q"},{type:f.SALOON,suit:L.HEARTS,value:"5"},{type:f.BARREL,suit:L.SPADES,value:"K"},{type:f.BARREL,suit:L.SPADES,value:"Q"},{type:f.SCOPE,suit:L.SPADES,value:"A"},{type:f.MUSTANG,suit:L.HEARTS,value:"8"},{type:f.MUSTANG,suit:L.HEARTS,value:"9"},{type:f.JAIL,suit:L.SPADES,value:"10"},{type:f.JAIL,suit:L.SPADES,value:"J"},{type:f.JAIL,suit:L.HEARTS,value:"4"},{type:f.DYNAMITE,suit:L.HEARTS,value:"2"},{type:f.VOLCANIC,suit:L.SPADES,value:"10"},{type:f.VOLCANIC,suit:L.CLUBS,value:"10"},{type:f.SCHOFIELD,suit:L.CLUBS,value:"K"},{type:f.SCHOFIELD,suit:L.CLUBS,value:"Q"},{type:f.SCHOFIELD,suit:L.SPADES,value:"K"},{type:f.REMINGTON,suit:L.CLUBS,value:"K"},{type:f.REV_CARABINE,suit:L.CLUBS,value:"A"},{type:f.WINCHESTER,suit:L.SPADES,value:"8"},{type:f.DODGE,suit:L.CLUBS,value:"A"},{type:f.DODGE,suit:L.SPADES,value:"A"},{type:f.PUNCH,suit:L.HEARTS,value:"A"},{type:f.PUNCH,suit:L.DIAMONDS,value:"A"},{type:f.BRAWL,suit:L.CLUBS,value:"J"},{type:f.RAG_TIME,suit:L.HEARTS,value:"Q"},{type:f.TEQUILA,suit:L.HEARTS,value:"4"},{type:f.WHISKY,suit:L.HEARTS,value:"3"},{type:f.BINOCULAR,suit:L.CLUBS,value:"Q"},{type:f.HIDEOUT,suit:L.SPADES,value:"Q"},{type:f.BIBLE,suit:L.HEARTS,value:"K"},{type:f.CAN_CAN,suit:L.CLUBS,value:"K"},{type:f.CANTEEN,suit:L.HEARTS,value:"8"},{type:f.CONESTOGA,suit:L.HEARTS,value:"9"},{type:f.IRON_PLATE,suit:L.SPADES,value:"K"},{type:f.PONY_EXPRESS,suit:L.SPADES,value:"J"},{type:f.SOMBRERO,suit:L.CLUBS,value:"9"},{type:f.TEN_GALLON_HAT,suit:L.HEARTS,value:"10"},{type:f.DERRINGER,suit:L.CLUBS,value:"4"},{type:f.PEPPERBOX,suit:L.DIAMONDS,value:"8"},{type:f.KNIFE,suit:L.SPADES,value:"9"},{type:f.SPRINGFIELD,suit:L.CLUBS,value:"8"},{type:f.BUFFALO_RIFLE,suit:L.SPADES,value:"A"},{type:f.HOWITZER,suit:L.HEARTS,value:"2"}];return i.forEach(c=>{[f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.PEPPERBOX,f.BUFFALO_RIFLE].includes(c.type)?(c.equipmentType=Jt.WEAPON,c.range=mf[c.type]):[f.BARREL,f.MUSTANG,f.SOMBRERO].includes(c.type)?c.equipmentType=Jt.DEFENSIVE:[f.SCOPE,f.JAIL,f.DYNAMITE].includes(c.type)?c.equipmentType=Jt.SPECIAL:[f.BINOCULAR,f.HIDEOUT,f.BIBLE,f.CAN_CAN,f.CANTEEN,f.CONESTOGA,f.PONY_EXPRESS,f.DERRINGER,f.TEN_GALLON_HAT,f.HOWITZER,f.KNIFE].includes(c.type)&&(c.equipmentType=Jt.GREEN)}),i.sort(()=>Math.random()-.5)},Mu=i=>{const c=[];c.push(Ae.SHERIFF);const s=i<=4?2:i<=6?3:4;for(let m=0;m<s;m++)c.push(Ae.OUTLAW);const d=i<=6?1:2;for(let m=0;m<d;m++)c.push(Ae.RENEGADE);const h=i<=4?0:i===5?1:2;for(let m=0;m<h;m++)c.push(Ae.DEPUTY);return c.sort(()=>Math.random()-.5)},Ee=Y.useCallback((i,c)=>{let s=[...i],d=[...U];const h=[];for(let m=0;m<c;m++){if(s.length===0){if(d.length===0){console.warn("Cannot reshuffle: both deck and discard pile are empty!");break}const y=d[d.length-1],C=[...d.slice(0,-1)];for(let j=C.length-1;j>0;j--){const Q=Math.floor(Math.random()*(j+1));[C[j],C[Q]]=[C[Q],C[j]]}s=C,d=[y],M(s),X(d),E("Deck reshuffled from discard pile!")}s.length>0&&(h.push(s[0]),s=s.slice(1))}return{drawnCards:h,updatedDeck:s,updatedDiscardPile:d}},[U,M,X,E]),tt=(i,c,s)=>{const d=s.filter(K=>K.isAlive).length,h=s.map((K,ue)=>({...K,originalIndex:ue})).filter(K=>K.isAlive),m=h.findIndex(K=>K.originalIndex===i),y=h.findIndex(K=>K.originalIndex===c);if(m===-1||y===-1)return 1/0;const T=(y-m+d)%d,C=(m-y+d)%d;let j=Math.min(T,C);const Q=s[c];Q&&Q.character.name==="Paul Regret"&&(j+=1);const F=s[i];return F&&F.character.name==="Rose Doolan"&&(j=Math.max(1,j-1)),j},Bu=i=>{const c=i.inPlay.find(h=>h.equipmentType===Jt.WEAPON),s=c?c.range:1,d=i.character.abilityType==="BUILT_IN_SCOPE"||i.inPlay.some(h=>h.type===f.SCOPE)?1:0;return s+d},Xn=i=>1+(i.character.abilityType==="BUILT_IN_MUSTANG"||i.inPlay.some(s=>s.type===f.MUSTANG)?1:0),Li=(i,c,s)=>{const d=s[i],h=s[c];if(!d.isAlive||!h.isAlive)return!1;const m=tt(i,c,s),y=Bu(d),T=Xn(h);return m<=y&&m>=T},ga=i=>{if($.length===0)return!1;const c=$[0];if(M($.slice(1)),X([...U,c]),i.character.name==="Lucky Duke"&&$.length>0){const s=$[0];M($.slice(1)),X([...U,s]);const d=c.suit===L.HEARTS?c:s.suit===L.HEARTS?s:c;return E(`${i.character.name} flipped ${c.suit} ${c.value} and ${s.suit} ${s.value}, chose ${d.suit} ${d.value}`),d.suit===L.HEARTS}return E(`${i.character.name} flipped ${c.suit} ${c.value} for barrel check`),c.suit===L.HEARTS},Vt=i=>i.character.name==="Jourdonnais"||i.inPlay.some(c=>c.type===f.BARREL),$a=Y.useCallback(i=>i.character.name==="Willy the Kid"||i.inPlay.some(c=>c.type===f.VOLCANIC)?!0:dl===0,[dl]),at=i=>i.role===Ae.SHERIFF,Le=(i,c)=>{if(!i.inPlay.some(h=>h.type===c))return!1;const d=`${v.findIndex(h=>h.id===i.id)}-${c}`;return!ma.has(d)},Xe=(i,c)=>{const s=[...v],d=s[i].inPlay.findIndex(h=>h.type===c);if(d>=0){const h=s[i].inPlay.splice(d,1)[0];return X(m=>[...m,h]),V(s),!0}return!1},pt=(i,c)=>{const s=i[c],d=i.find(y=>y.character.name==="Vulture Sam"&&y.isAlive);d&&d!==s&&(It(d,"ON_ELIMINATION",{eliminatedPlayer:s}),i[c].hand=[],i[c].inPlay=[]);const h=i.find(y=>y.character.name==="Greg Digger"&&y.isAlive);h&&h!==s&&It(h,"HEAL_ON_ELIMINATION",{eliminatedPlayer:s});const m=i.find(y=>y.character.name==="Herb Hunter"&&y.isAlive);m&&m!==s&&It(m,"DRAW_ON_ELIMINATION",{eliminatedPlayer:s})},It=(i,c,s={})=>{switch(c){case"ON_DAMAGE_TAKEN":if(i.character.name==="Bart Cassidy"){if($.length>0){const d=$[0];M($.slice(1)),V(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,d]}:m)),E(`${i.name} (Bart Cassidy) draws a card from taking damage!`)}}else if(i.character.name==="El Gringo"){const{attackerIndex:d}=s;if(d!==void 0&&v[d]&&v[d].hand.length>0){const h=v[d],m=Math.floor(Math.random()*h.hand.length),y=h.hand[m];V(T=>T.map((C,j)=>{if(j===d){const Q=[...C.hand];return Q.splice(m,1),{...C,hand:Q}}else if(C.id===i.id)return{...C,hand:[...C.hand,y]};return C})),E(`${i.name} (El Gringo) draws a card from ${h.name}!`)}}break;case"AUTO_DRAW":if(i.character.name==="Suzy Lafayette"&&i.hand.length===0&&$.length>0){const d=$[0];M($.slice(1)),V(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,d]}:m)),E(`${i.name} (Suzy Lafayette) draws a card for having no cards!`)}break;case"ON_ELIMINATION":if(i.character.name==="Vulture Sam"){const{eliminatedPlayer:d}=s;d&&d.hand.length>0&&(V(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,...d.hand]}:m)),E(`${i.name} (Vulture Sam) takes all cards from eliminated ${d.name}!`))}break;case"HEAL_ON_ELIMINATION":if(i.character.name==="Greg Digger"){const{eliminatedPlayer:d}=s;d&&d.id!==i.id&&(V(h=>h.map(m=>m.id===i.id?{...m,health:Math.min(m.health+2,m.character.life)}:m)),E(`${i.name} (Greg Digger) regained 2 life from elimination!`))}break;case"DRAW_ON_ELIMINATION":if(i.character.name==="Herb Hunter"){const{eliminatedPlayer:d}=s;if(d&&d.id!==i.id&&$.length>=2){const h=Ee($,2);V(m=>m.map(y=>y.id===i.id?{...y,hand:[...y.hand,...h.drawnCards]}:y)),M(h.updatedDeck),E(`${i.name} (Herb Hunter) drew 2 cards from elimination!`)}}break}},ht=i=>{Ft(i),cl(!0)},_a=i=>{const c={type:i.name,suit:"",value:"",isCharacter:!0,character:i};Ft(c),cl(!0)},rn=()=>{cl(!1),Ft(null)},fn=()=>{je(!1),be("setup"),V([]),He(0),M([]),X([]),Bt("draw"),qt(!1),wt(0),Oa(1),Al([""]),E("Welcome to BANG!"),St(!1)},xi=()=>{je(!1),St(!0),E("You are now spectating the game. You can watch until the game ends.")},pl=i=>{const c=setTimeout(()=>{La(!0),ht(i)},2e3);Fl(c)},Tl=()=>{sl&&(clearTimeout(sl),Fl(null)),La(!1)},Nl=()=>{sl&&(clearTimeout(sl),Fl(null)),La(!1)},Gu=Y.useCallback((i,c,s)=>{sa&&clearTimeout(sa),pa(null);const d=Date.now();Bi(d),Rn({card:c,cardIndex:s}),La(!1),On(!1);const h=i.currentTarget,m=setTimeout(()=>{const T=Date.now()-d;Pl&&Pl.card===c&&T>=1900&&(La(!0),On(!0),pa(c),h.classList.add("long-pressing"),navigator.vibrate&&navigator.vibrate(50))},2e3);_t(m)},[sa,Pl]),ns=Y.useCallback(i=>{i.currentTarget.classList.remove("long-pressing");const s=Date.now(),d=Eu?s-Eu:0;sa&&(clearTimeout(sa),_t(null),d<1900&&pa(null)),La(!1),Rn(null),Bi(null),setTimeout(()=>{On(!1)},300)},[sa,Eu]),us=Y.useCallback(i=>{sa&&(clearTimeout(sa),_t(null)),i.currentTarget.classList.remove("long-pressing"),pa(null),La(!1),Rn(null)},[sa]),qi=(i,c,s=500)=>{ge(d=>({...d,[i]:{type:c,timestamp:Date.now()}})),setTimeout(()=>{ge(d=>{const h={...d};return delete h[i],h})},s)},wi=()=>{qi(`draw-${Date.now()}`,"cardDraw",400)},Yi=i=>({[f.BANG]:"Deal 1 damage to a player within range. Target must play a Missed! card or lose 1 life point.",[f.MISSED]:"Play in response to a BANG! card to avoid taking damage.",[f.BEER]:"Regain 1 life point (up to your maximum). Cannot be played if you're already at maximum health. Requires 3+ players alive.",[f.SALOON]:"All players regain 1 life point (up to their maximum).",[f.PANIC]:"Draw a random card from a player at distance 1.",[f.CAT_BALOU]:"Force a player to discard a random card from their hand or remove a card from play.",[f.DUEL]:"Challenge another player to a duel. You and target alternate playing BANG! cards. First player who cannot play a BANG! loses 1 life point.",[f.JAIL]:"Place on another player. They must draw a Heart to escape or skip their turn. Cannot target the Sheriff or players already in jail.",[f.DYNAMITE]:"Place in front of you. Each turn, draw a card - if it's Spades 2-9, take 3 damage!",[f.BARREL]:"When targeted by BANG!, draw a card. If it's a Heart, the BANG! misses.",[f.MUSTANG]:"Increases distance from you to other players by 1.",[f.SCOPE]:"Decreases distance from other players to you by 1.",[f.VOLCANIC]:"Allows you to play unlimited BANG! cards per turn.",[f.SCHOFIELD]:"Range 2 weapon. Can target players at distance 1-2.",[f.REMINGTON]:"Range 3 weapon. Can target players at distance 1-3.",[f.REV_CARABINE]:"Range 4 weapon. Can target players at distance 1-4.",[f.WINCHESTER]:"Range 5 weapon. Can target players at distance 1-5.",[f.STAGECOACH]:"Draw 2 cards from the deck.",[f.WELLS_FARGO]:"Draw 3 cards from the deck.",[f.GENERAL_STORE]:"All players draw 1 card from a shared selection.",[f.INDIANS]:"All other players must play a BANG! card or lose 1 life point.",[f.GATLING]:"Deal 1 damage to all other players (they can play Missed! to avoid).",[f.DODGE]:"Play in response to a BANG! card to avoid taking damage (like Missed!) and draw 1 card.",[f.PUNCH]:"Deal 1 damage to a player at distance 1 (like BANG! but only range 1).",[f.BRAWL]:"Discard another card + Brawl: Force all other players to discard a card from hand or play.",[f.RAG_TIME]:"All players draw 1 card, then discard 1 card.",[f.TEQUILA]:"Discard another card + Tequila: Choose any player to regain 1 life point.",[f.WHISKY]:"Discard another card + Whisky: Regain 2 life points.",[f.BINOCULAR]:"GREEN: Discard to look at top 3 cards of deck, put them back in any order.",[f.HIDEOUT]:"GREEN: Discard when targeted by BANG! - draw a card, if Spade the BANG! misses.",[f.BIBLE]:"GREEN: Discard to avoid BANG! and draw 1 card.",[f.CAN_CAN]:"GREEN: Discard when playing Missed! to draw 1 card.",[f.CANTEEN]:"GREEN: Discard when playing Beer to regain 2 life instead of 1.",[f.CONESTOGA]:"GREEN: Discard during hand limit phase to discard 1 less card.",[f.IRON_PLATE]:"GREEN: Discard to avoid BANG!.",[f.PONY_EXPRESS]:"GREEN: Discard to draw 3 cards from deck.",[f.SOMBRERO]:"GREEN: Discard to avoid BANG!.",[f.TEN_GALLON_HAT]:"GREEN: Discard to avoid BANG!.",[f.DERRINGER]:"GREEN: Discard to play as BANG! at range 1 and draw 1 card.",[f.PEPPERBOX]:"Range 1 weapon. Can play unlimited BANG! cards per turn.",[f.KNIFE]:"GREEN: Discard to play as BANG! at range 1. Cannot be avoided with Missed!",[f.SPRINGFIELD]:"Discard another card + Springfield: BANG! at distance 2. Doesn't count toward BANG! limit.",[f.BUFFALO_RIFLE]:"Range 5 weapon. Can target players at distance 1-5.",[f.HOWITZER]:"GREEN: Discard to play as BANG! against all other players. Doesn't count toward BANG! limit."})[i]||"No description available.",bl=Y.useCallback(()=>{if(Ve.current===ba)return;if(At!=="draw"||et||Pt){Pt||E("You have already drawn cards this turn!");return}Ve.current=ba,da(!0);const i=v[B],c=[...v];let s=[...$],d=0;const h=i.inPlay.findIndex(y=>y.type===f.JAIL);if(h>=0&&s.length>0){let y=!1;if(i.character.name==="Lucky Duke"){if(s.length>=2){const T=s[0],C=s[1];s=s.slice(2),X([...U,T,C]);const j=T.suit===L.HEARTS?T:C.suit===L.HEARTS?C:T;y=j.suit===L.HEARTS,E(`${i.character.name} (Lucky Duke) drew 2 cards for jail escape and chose ${j.suit} ${j.value}`)}}else{const T=s[0];s=s.slice(1),X([...U,T]),y=T.suit===L.HEARTS,E(`${i.character.name} flipped ${T.suit} ${T.value} for jail escape`)}if(y){const T=c[B].inPlay.splice(h,1)[0];X([...U,T]),E(`${i.character.name} escaped from jail!`)}else{E(`${i.character.name} remains in jail and skips their turn.`),V(c),M(s),Qa();return}}const m=i.inPlay.findIndex(y=>y.type===f.DYNAMITE);if(m>=0&&s.length>0){const y=s[0];if(s=s.slice(1),X([...U,y]),y.suit===L.SPADES&&["2","3","4","5","6","7","8","9"].includes(y.value)){const T=c[B].inPlay.splice(m,1)[0];if(X([...U,T]),c[B].health-=3,c[B].health<=0){c[B].isAlive=!1,pt(c,B),E(`${i.character.name} (${i.role}) was killed by dynamite!`),q("dominating"),B===0&&!c[0].isBot&&je(!0),V(c),M(s),Gt(c);return}else q("godlike"),E(`${i.character.name} was hurt by dynamite! Health: ${c[B].health}`)}else{const T=c[B].inPlay.splice(m,1)[0];let C=(B+1)%v.length;for(;!c[C].isAlive;)C=(C+1)%v.length;c[C].inPlay.push(T),E(`${i.character.name} passed dynamite to ${c[C].character.name}`)}}if(i.character.abilityType==="DRAW_CHOICE"){if(i.character.name==="Jesse Jones"){const y=Ee(s,2);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length}else if(i.character.name==="Kit Carlson"){const y=Ee(s,3);y.drawnCards.length>=3?(c[B].hand.push(...y.drawnCards.slice(0,2)),X([...U,y.drawnCards[2]]),d=2):(c[B].hand.push(...y.drawnCards),d=y.drawnCards.length),s=y.updatedDeck}else if(i.character.name==="Pedro Ramirez")if(U.length>0){const y=U[U.length-1];c[B].hand.push(y),X(U.slice(0,-1)),d=1;const T=Ee(s,1);T.drawnCards.length>0&&(c[B].hand.push(...T.drawnCards),s=T.updatedDeck,d=2)}else{const y=Ee(s,2);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length}}else if(i.character.abilityType==="ON_DRAW"){const y=Ee(s,2);if(c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length,y.drawnCards.length>=2&&(y.drawnCards[1].suit===L.HEARTS||y.drawnCards[1].suit===L.DIAMONDS)){const T=Ee(s,1);T.drawnCards.length>0&&(c[B].hand.push(...T.drawnCards),s=T.updatedDeck,d=y.drawnCards.length+T.drawnCards.length,E(`${i.character.name} drew an extra card (Black Jack ability)!`))}}else if(i.character.abilityType==="INJURY_DRAW"){const y=i.character.life-i.health,T=1+y,C=Ee(s,T);c[B].hand.push(...C.drawnCards),s=C.updatedDeck,d=C.drawnCards.length,E(`${i.character.name} drew ${T} cards (1 + ${y} injuries)!`)}else if(i.character.abilityType==="ENHANCED_DRAW"){const y=Ee(s,3);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length,E(`${i.character.name} drew 3 cards!`)}else{const y=Ee(s,2);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length}if(i.character.name==="Suzy Lafayette"&&c[B].hand.length===0){const y=Ee(s,1);y.drawnCards.length>0&&(c[B].hand.push(...y.drawnCards),s=y.updatedDeck,E(`${i.character.name} drew a card (Suzy Lafayette ability)!`))}q("cardDraw"),le(B,"card-draw-effect",1e3),wi(),V(c),M(s),qt(!0),Bt("play"),E(`${i.name} drew ${d} cards. Now play cards or end turn.`),da(!1)},[At,et,Pt,v,B,$,U,V,M,qt,Bt,E,X,q,le,A,wi,Ee,da]),dn=(i,c,s=null)=>{if(i!==B){E("It's not your turn!");return}if(At!=="play"){E(At==="draw"?"You must draw cards first!":"You can only play cards during the play phase!");return}const d=v[i],h=d.hand[c];if(q("cardPlay"),qi(`play-${i}-${c}`,"cardPlay",600),d.character.name==="Calamity Janet")if(h.type===f.MISSED&&s!==null){Dl(i,c,s,!0);return}else h.type,f.BANG;switch(h.type){case f.BANG:if(s===null){E("Select a target for BANG!");return}Dl(i,c,s);break;case f.MISSED:d.character.name==="Calamity Janet"&&s!==null?Dl(i,c,s,!0):E("Missed! cards can only be played defensively!");break;case f.BEER:is(i,c);break;case f.PANIC:if(s===null){E("Select a target for Panic!");return}cs(i,c,s);break;case f.CAT_BALOU:if(s===null){E("Select a target for Cat Balou!");return}Hu(i,c,s);break;case f.STAGECOACH:ss(i,c);break;case f.WELLS_FARGO:rs(i,c);break;case f.GATLING:fs(i,c);break;case f.INDIANS:Vi(i,c);break;case f.DUEL:if(s===null){E("Select a target for Duel!");return}ds(i,c,s);break;case f.GENERAL_STORE:os(i,c);break;case f.SALOON:hs(i,c);break;case f.BARREL:case f.SCOPE:case f.MUSTANG:case f.VOLCANIC:case f.SCHOFIELD:case f.REMINGTON:case f.REV_CARABINE:case f.WINCHESTER:ju(i,c);break;case f.JAIL:if(s===null){E("Select a target for Jail!");return}ms(i,c,s);break;case f.DYNAMITE:ys(i,c);break;case f.DODGE:ju(i,c);break;case f.PUNCH:if(s===null){E("Select a target for Punch!");return}zu(i,c,s);break;case f.BRAWL:gs(i,c);break;case f.RAG_TIME:Ki(i,c);break;case f.TEQUILA:Rl(i,c,s);break;case f.WHISKY:Ss(i,c);break;case f.SPRINGFIELD:if(s===null){E("Select a target for Springfield!");return}As(i,c,s);break;case f.BINOCULAR:case f.HIDEOUT:case f.BIBLE:case f.CAN_CAN:case f.CANTEEN:case f.CONESTOGA:case f.IRON_PLATE:case f.PONY_EXPRESS:case f.SOMBRERO:case f.TEN_GALLON_HAT:case f.DERRINGER:case f.HOWITZER:case f.PEPPERBOX:case f.BUFFALO_RIFLE:ju(i,c);break;default:E(`Card ${h.type} not implemented yet`)}},Dl=(i,c,s,d=!1,h=null)=>{const m=v[i],y=v[s],T=m.hand[c];if(y.character.name==="Apache Kid"&&T.suit===L.DIAMONDS){E(`${T.type} (Diamond) cannot affect Apache Kid!`);return}if(!d&&!$a(m)){E(`${m.name} has already played a BANG! card this turn!`);return}if(!Li(i,s,v)){E("Target is out of range!");return}const C=[...v],j=C[i].hand[c];ee(j,i,s),C[i].hand.splice(c,1),X(K=>[...K,j]),d||wt(dl+1);let Q;h===f.PUNCH?Q="Punch":d?Q="Missed! (as BANG!)":Q="BANG!",E(`${m.name} played ${Q} on ${y.name}!`);let F=!1;if(Vt(y)&&!y.isBot){Ta({type:"BANG",attackerIndex:i,targetIndex:s,cardIndex:c,isSubstitution:d}),en({type:"BARREL_DEFENSE",playerIndex:s,message:`${y.name}, do you want to use your Barrel defense?`}),pu(!0);return}else Vt(y)&&y.isBot&&ga(y)&&(F=!0,E(`${y.character.name} defended with Barrel!`));if(!F){const K=m.character.name==="Slab the Killer"?2:1;let ue=0;if(Le(C[s],f.BIBLE)&&ue<K&&Xe(s,f.BIBLE)){ue++;const ve=Ee($,1);ve.drawnCards.length>0&&(C[s].hand.push(...ve.drawnCards),M(ve.updatedDeck)),E(`${y.name} used Bible to defend and drew 1 card!`)}if(Le(C[s],f.HIDEOUT)&&ue<K&&Xe(s,f.HIDEOUT)){const ve=Ee($,1);if(ve.drawnCards.length>0){const Me=ve.drawnCards[0];C[s].hand.push(Me),M(ve.updatedDeck),Me.suit===L.SPADES?(ue++,E(`${y.name} used Hideout, drew ${Me.type} of Spades - BANG! missed!`)):E(`${y.name} used Hideout, drew ${Me.type} of ${Me.suit} - BANG! still hits!`)}}Le(C[s],f.SOMBRERO)&&ue<K&&Xe(s,f.SOMBRERO)&&(ue++,E(`${y.name} used Sombrero to defend against BANG!!`)),Le(C[s],f.IRON_PLATE)&&ue<K&&Xe(s,f.IRON_PLATE)&&(ue++,E(`${y.name} used Iron Plate to defend against BANG!!`)),Le(C[s],f.TEN_GALLON_HAT)&&ue<K&&Xe(s,f.TEN_GALLON_HAT)&&(ue++,E(`${y.name} used Ten Gallon Hat to defend against BANG!!`));for(let ve=ue;ve<K;ve++){const Me=C[s].hand.findIndex(Ht=>Ht.type===f.MISSED||Ht.type===f.DODGE||C[s].character.name==="Calamity Janet"&&Ht.type===f.BANG);if(Me>=0){const Ht=C[s].hand.splice(Me,1)[0];if(X([...U,Ht]),ue++,Ht.type===f.DODGE){const on=Ee($,1);on.drawnCards.length>0&&(C[s].hand.push(...on.drawnCards),M(on.updatedDeck)),E(`${y.name} used Dodge to defend and drew 1 card!`)}else C[s].character.name==="Calamity Janet"&&Ht.type===f.BANG&&E(`${y.name} (Calamity Janet) used BANG! as Missed!`)}else break}ue>=K&&(F=!0,E(`${y.character.name} defended with ${ue} defensive card(s)`))}F?Vt(y)?le(s,"barrel-defense-effect",1e3):le(s,"dodge-effect",800):(C[s].health-=1,le(s,"damage-effect",1500),A(s,1,"damage"),It(C[s],"ON_DAMAGE_TAKEN",{attackerIndex:i}),C[s].health<=0?(C[s].isAlive=!1,le(s,"elimination-effect",2e3),w(),q("dominating"),pt(C,s),E(`${y.character.name} (${y.role}) was eliminated!`),s===0&&!C[0].isBot&&je(!0),Gt(C)):(q("godlike"),E(`${y.character.name} lost a life point! Health: ${C[s].health}`))),V(C)},is=(i,c)=>{const s=v[i];if(v.filter(j=>j.isAlive).length<3){Ie("Beer can only be used when there are 3 or more players alive!",!0,4e3);return}if(s.health>=s.maxHealth){Ie(`${s.name} is already at maximum health! Cannot play Beer.`,!0,4e3);return}const h=[...v],m=h[i].hand.splice(c,1)[0];X([...U,m]);const y=s.character.name==="Tequila Joe"?2:1,T=Math.min(y,s.character.life-s.health);h[i].health+=T,q("heal"),le(i,"healing-effect",2e3),A(i,T,"heal");const C=s.character.name==="Tequila Joe"?`${s.name} (Tequila Joe) gained ${T} life points from Beer! Health: ${h[i].health}`:`${s.name} gained ${T} life point! Health: ${h[i].health}`;E(C),V(h)},cs=(i,c,s)=>{const d=v[i],h=v[s];if(tt(i,s,v)!==1){E("Panic can only target players at distance 1!");return}if(h.hand.length===0&&h.inPlay.length===0){E("Target has no cards to steal!");return}const m=[...v],y=m[i].hand.splice(c,1)[0];X([...U,y]);const T=[...h.hand,...h.inPlay],C=Math.floor(Math.random()*T.length),j=T[C];C<h.hand.length?m[s].hand.splice(C,1):m[s].inPlay.splice(C-h.hand.length,1),m[i].hand.push(j),E(`${d.character.name} stole ${j.type} from ${h.character.name}!`),V(m)},Hu=(i,c,s)=>{const d=v[i],h=v[s],m=h.inPlay.filter(F=>F.type!==f.JAIL),y=[...h.hand,...m];if(y.length===0){E("Target has no cards that can be discarded!");return}const T=[...v],C=T[i].hand.splice(c,1)[0];X([...U,C]);const j=Math.floor(Math.random()*y.length),Q=y[j];if(j<h.hand.length){const F=h.hand.findIndex(K=>K===Q);T[s].hand.splice(F,1)}else{const F=h.inPlay.findIndex(K=>K===Q);T[s].inPlay.splice(F,1)}X([...U,Q]),E(`${d.character.name} forced ${h.character.name} to discard ${Q.type}!`),V(T)},ss=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);const m=Ee($,2);d[i].hand.push(...m.drawnCards),M(m.updatedDeck),E(`${s.character.name} drew ${m.drawnCards.length} cards with Stagecoach!`),V(d)},rs=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);const m=Ee($,3);d[i].hand.push(...m.drawnCards),M(m.updatedDeck),E(`${s.character.name} drew ${m.drawnCards.length} cards with Wells Fargo!`),V(d)},fs=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);for(let m=0;m<d.length;m++)if(m!==i&&d[m].isAlive){const y=d[m];let T=!1;if(Vt(y)&&ga(y)&&(T=!0,E(`${y.character.name} defended against Gatling with Barrel!`)),!T){const C=y.hand.findIndex(j=>j.type===f.MISSED||y.character.abilityType==="CARD_SUBSTITUTION"&&j.type===f.BANG);if(C>=0){const j=d[m].hand.splice(C,1)[0];X([...U,j]),T=!0}}T||(d[m].health-=1,d[m].health<=0?(d[m].isAlive=!1,pt(d,m),q("dominating"),E(`${y.character.name} (${y.role}) was eliminated by Gatling!`)):q("godlike"))}E(`${s.character.name} played Gatling!`),V(d),Gt(d)},Vi=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);for(let m=0;m<d.length;m++)if(m!==i&&d[m].isAlive){const y=d[m],T=y.hand.findIndex(C=>C.type===f.BANG||y.character.name==="Calamity Janet"&&C.type===f.MISSED);if(T>=0){const C=d[m].hand.splice(T,1)[0];X([...U,C])}else d[m].health-=1,d[m].health<=0?(d[m].isAlive=!1,pt(d,m),q("dominating"),E(`${y.character.name} (${y.role}) was eliminated by Indians!`)):q("godlike")}E(`${s.character.name} played Indians!`),V(d),Gt(d)},ds=(i,c,s)=>{const d=v[i],h=v[s],m=[...v],y=m[i].hand.splice(c,1)[0];X([...U,y]),E(`${d.character.name} challenged ${h.character.name} to a duel!`),Xi(i,s,!0,m)},Xi=(i,c,s,d)=>{const h=s?i:c,m=d[h],y=s?c:i,T=m.hand.findIndex(C=>C.type===f.BANG||m.character.name==="Calamity Janet"&&C.type===f.MISSED);if(T>=0){const C=d[h].hand.splice(T,1)[0];X(F=>[...F,C]);const j=C.type===f.MISSED?"Missed! (as BANG!)":"BANG!";E(`${m.character.name} played ${j} in the duel!`),V([...d]);const Q=m.isBot?1e3:1500;setTimeout(()=>{Xi(i,c,!s,d)},Q)}else d[h].health-=1,It(d[h],"ON_DAMAGE_TAKEN",{attackerIndex:y}),d[h].health<=0?(d[h].isAlive=!1,q("dominating"),pt(d,h),E(`${m.character.name} (${m.role}) was eliminated in the duel!`),Gt(d)):(q("godlike"),E(`${m.character.name} lost the duel and 1 life point! Health: ${d[h].health}`)),V(d)},os=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);const m=d.filter(j=>j.isAlive),y=Ee($,m.length),T=y.drawnCards;M(y.updatedDeck);const C=[];for(let j=0;j<v.length;j++){const Q=(i+j)%v.length;v[Q].isAlive&&C.push(Q)}Ye(T),fa(C),un(0),nn(!0),V(d),E(`${s.character.name} played General Store! ${m.length} cards revealed. Starting with ${s.character.name}, each player chooses one card clockwise.`)},$i=i=>{if(!ze[i])return;const c=se[qe],s=ze[i],d=v[c];if(c!==se[qe])return;const h=ze.filter((T,C)=>C!==i);Ye(h);const m=[...v];m[c].hand.push(s),V(m),E(`${d.character.name} chose ${s.type}!`);const y=qe+1;un(y),y>=se.length||h.length===0?setTimeout(()=>{nn(!1),Ye([]),fa([]),un(0),h.length>0?(X(T=>[...T,...h]),E(`General Store complete! All players chose their cards. ${h.length} card(s) discarded.`)):E("General Store complete! All cards were chosen by players.")},1e3):setTimeout(()=>{const T=se[y],C=v[T].character.name;E(`${C}'s turn to choose from General Store (${h.length} cards left).`)},800)},Uu=()=>{if(!qa||ze.length===0||se.length===0||qe>=se.length)return;const i=se[qe],c=v[i];if(!c||!c.isBot)return;let s=0,d=ct(ze[0],c,[]);for(let h=1;h<ze.length;h++){const m=ct(ze[h],c,[]);m>d&&(d=m,s=h)}setTimeout(()=>{$i(s)},300)},hs=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]),d.forEach(m=>{m.isAlive&&m.health<m.maxHealth&&(m.health+=1)}),E(`${s.name} played Saloon! All players regained 1 life point.`),V(d)},ju=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];if(d[i].inPlay.findIndex(y=>y.type===h.type)>=0){d[i].hand.push(h),V(d),E(`${s.name} already has ${h.type} equipped! Cannot have duplicate equipment.`);return}if(h.equipmentType===Jt.WEAPON){const y=d[i].inPlay.findIndex(T=>T.equipmentType===Jt.WEAPON);if(y>=0){const T=d[i].inPlay.splice(y,1)[0];X([...U,T]),E(`${s.name} replaced ${T.type} with ${h.type}!`)}else E(`${s.name} equipped ${h.type}!`)}else h.equipmentType===Jt.GREEN?E(`${s.name} equipped ${h.type}! (Can be used starting next turn)`):E(`${s.name} equipped ${h.type}!`);h.equipmentType===Jt.GREEN&&jn(y=>new Set([...y,`${i}-${h.type}`])),d[i].inPlay.push(h),V(d)},ms=(i,c,s)=>{const d=v[i],h=v[s];if(h.role===Ae.SHERIFF){Ie("Cannot jail the Sheriff!",!0,4e3);return}if(h.inPlay.some(C=>C.type===f.JAIL)){Ie(`${h.character.name} is already in jail! Cannot jail the same player twice.`,!0,4e3);return}const y=[...v],T=y[i].hand.splice(c,1)[0];y[s].inPlay.push(T),le(s,"jail-effect",500),E(`${d.character.name} put ${h.character.name} in jail!`),V(y)},ys=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];d[i].inPlay.push(h),E(`${s.character.name} placed Dynamite!`),V(d)},zu=(i,c,s)=>{if(tt(i,s,v)!==1){E("Punch can only target players at distance 1!");return}Dl(i,c,s,!0,f.PUNCH)},Cl=(i,c,s)=>{const d=v[i];if(B!==i){E("You can only activate green cards on your turn!");return}if(!Le(d,f.KNIFE)){E("This green card cannot be used right now!");return}if(tt(i,s,v)!==1){E("Knife can only target players at distance 1!");return}Xe(i,f.KNIFE);const h=v[s],m=[...v];E(`${d.name} used Knife on ${h.name}! Cannot be avoided with Missed!`);let y=!1;Vt(h)&&h.isBot&&ga(h)&&(y=!0,E(`${h.character.name} defended with Barrel!`)),y||(m[s].health-=1,It(m[s],"ON_DAMAGE_TAKEN",{attackerIndex:i}),m[s].health<=0?(m[s].isAlive=!1,pt(m,s),q("dominating"),E(`${h.character.name} was eliminated by Knife!`),s===0&&!m[0].isBot&&je(!0),Gt(m)):(q("godlike"),E(`${h.character.name} lost a life point to Knife! Health: ${m[s].health}`))),V(m)},gs=(i,c)=>{const s=v[i];if(s.hand.length<2){E("You need at least 2 cards to play Brawl (Brawl + another card)!");return}if(s.isBot){const d=[...v],h=d[i].hand.splice(c,1)[0],m=d[i].hand.splice(0,1)[0];X([...U,h,m]);let y=0;d.forEach((T,C)=>{if(C!==i&&T.isAlive){if(T.hand.length>0){const j=T.hand.splice(0,1)[0];X(Q=>[...Q,j]),y++}else if(T.inPlay.length>0){const j=T.inPlay.splice(0,1)[0];X(Q=>[...Q,j]),y++}}}),E(`${s.character.name} played Brawl! All other players discarded ${y} cards.`),V(d)}else{const d=[...v],h=d[i].hand.splice(c,1)[0];V(d),X([...U,h]),zn({type:"BRAWL_ADDITIONAL_DISCARD",playerIndex:i,brawlCard:h,message:"Choose an additional card to discard for Brawl:"}),Ya(!0)}},Lu=(i,c,s)=>{const d=v[i],h=v[c];if(s===f.DERRINGER&&tt(i,c,v)!==1){E("Derringer can only target players at range 1!");return}const m=[...v];E(`${d.name} used ${s} on ${h.name}!`);let y=!1;if(Vt(h)&&h.isBot&&ga(h)&&(y=!0,E(`${h.character.name} defended with Barrel!`)),!y){const T=d.character.name==="Slab the Killer"?2:1;let C=0;if(Le(m[c],f.BIBLE)&&C<T&&Xe(c,f.BIBLE)){C++;const j=Ee($,1);j.drawnCards.length>0&&(m[c].hand.push(...j.drawnCards),M(j.updatedDeck)),E(`${h.name} used Bible to defend and drew 1 card!`)}Le(m[c],f.SOMBRERO)&&C<T&&Xe(c,f.SOMBRERO)&&(C++,E(`${h.name} used Sombrero to defend!`));for(let j=C;j<T;j++){const Q=m[c].hand.findIndex(F=>F.type===f.MISSED||F.type===f.DODGE||m[c].character.name==="Calamity Janet"&&F.type===f.BANG);if(Q>=0){const F=m[c].hand.splice(Q,1)[0];if(X(K=>[...K,F]),C++,F.type===f.DODGE){const K=Ee($,1);K.drawnCards.length>0&&(m[c].hand.push(...K.drawnCards),M(K.updatedDeck)),E(`${h.name} used Dodge to defend and drew 1 card!`)}}else break}C>=T&&(y=!0,E(`${h.character.name} defended with ${C} defensive card(s)`))}y||(m[c].health-=1,It(m[c],"ON_DAMAGE_TAKEN",{attackerIndex:i}),m[c].health<=0?(m[c].isAlive=!1,pt(m,c),q("dominating"),E(`${h.character.name} was eliminated by ${s}!`),c===0&&!m[0].isBot&&je(!0),Gt(m)):(q("godlike"),E(`${h.character.name} lost a life point! Health: ${m[c].health}`))),V(m)},Qi=(i,c,s)=>{const d=v[i];if(B!==i){E("You can only activate green cards on your turn!");return}if(!Le(d,c)){E("This green card cannot be used right now!");return}switch(c){case f.DERRINGER:{if(tt(i,s,v)!==1){E("Derringer can only target players at range 1!");return}Xe(i,c),Lu(i,s,c);const h=Ee($,1);if(h.drawnCards.length>0){const m=[...v];m[i].hand.push(...h.drawnCards),V(m),M(h.updatedDeck),E(`${d.name} used Derringer and drew 1 card!`)}break}case f.CAN_CAN:{Xe(i,c);const h=[...v],m=v[s];if(m.hand.length>0||m.inPlay.length>0)if(m.isBot){if(m.hand.length>0){const y=m.hand.map((C,j)=>({card:C,index:j,priority:ct(C,m,[])}));y.sort((C,j)=>C.priority-j.priority);const T=h[s].hand.splice(y[0].index,1)[0];X(C=>[...C,T]),E(`${d.name} used Can Can! ${m.character.name} discarded ${T.type} from hand.`)}else if(m.inPlay.length>0){const y=h[s].inPlay.splice(0,1)[0];X(T=>[...T,y]),E(`${d.name} used Can Can! ${m.character.name} discarded ${y.type} from equipment.`)}}else{Ou(s),Vn(i),Ru(!0),E(`${d.name} used Can Can! ${m.character.name}, choose a card to discard:`),V(h);return}else E(`${d.name} used Can Can on ${m.character.name}, but they have no cards to discard!`);V(h);break}case f.KNIFE:{Cl(i,-1,s);break}default:E(`${c} targeting not implemented yet!`);break}},ki=(i,c)=>{const s=v[i];if(B!==i){E("You can only activate green cards on your turn!");return}if(!Le(s,c)){E("This green card cannot be used right now!");return}switch(c){case f.DERRINGER:{const d=v.map((h,m)=>({player:h,index:m})).filter(({player:h,index:m})=>h.isAlive&&m!==i&&tt(i,m,v)===1);if(d.length===0){E("No targets at range 1 for Derringer!");return}if(d.length===1){Xe(i,c),Lu(i,d[0].index,c);const h=Ee($,1);if(h.drawnCards.length>0){const m=[...v];m[i].hand.push(...h.drawnCards),V(m),M(h.updatedDeck),E(`${s.name} used Derringer and drew 1 card!`)}}else Z({playerIndex:i,cardIndex:-1,card:{type:c}}),fe(!0);break}case f.KNIFE:{const d=v.map((h,m)=>({player:h,index:m})).filter(({player:h,index:m})=>h.isAlive&&m!==i&&tt(i,m,v)===1);if(d.length===0){E("No targets at range 1 for Knife!");return}d.length===1?Cl(i,-1,d[0].index):(Z({playerIndex:i,cardIndex:-1,card:{type:c}}),fe(!0));break}case f.HOWITZER:{Xe(i,c);const d=v.map((y,T)=>({player:y,index:T})).filter(({player:y,index:T})=>T!==i&&y.isAlive);let h=0;const m=[...v];for(const{player:y,index:T}of d){let C=!1;if(Vt(y)&&y.isBot&&ga(y)){C=!0,E(`${y.character.name} defended with Barrel against Howitzer!`);continue}if(!C){let Q=0;if(Le(m[T],f.BIBLE)&&Q<1&&Xe(T,f.BIBLE)){Q++;const F=Ee($,1);F.drawnCards.length>0&&(m[T].hand.push(...F.drawnCards),M(F.updatedDeck)),E(`${y.character.name} used Bible to defend against Howitzer and drew 1 card!`)}if(Le(m[T],f.SOMBRERO)&&Q<1&&Xe(T,f.SOMBRERO)&&(Q++,E(`${y.character.name} used Sombrero to defend against Howitzer!`)),Le(m[T],f.IRON_PLATE)&&Q<1&&Xe(T,f.IRON_PLATE)&&(Q++,E(`${y.character.name} used Iron Plate to defend against Howitzer!`)),Le(m[T],f.TEN_GALLON_HAT)&&Q<1&&Xe(T,f.TEN_GALLON_HAT)&&(Q++,E(`${y.character.name} used Ten Gallon Hat to defend against Howitzer!`)),y.isBot&&Q<1){const F=m[T].hand.findIndex(K=>K.type===f.MISSED||K.type===f.DODGE||m[T].character.name==="Calamity Janet"&&K.type===f.BANG);if(F>=0){const K=m[T].hand.splice(F,1)[0];if(X(ue=>[...ue,K]),Q++,K.type===f.DODGE){const ue=Ee($,1);ue.drawnCards.length>0&&(m[T].hand.push(...ue.drawnCards),M(ue.updatedDeck)),E(`${y.character.name} used Dodge to defend against Howitzer and drew 1 card!`)}else E(`${y.character.name} used Missed! to defend against Howitzer!`)}}Q>=1&&(C=!0)}C||(m[T].health-=1,h++,It(m[T],"ON_DAMAGE_TAKEN",{attackerIndex:i}),m[T].health<=0?(m[T].isAlive=!1,pt(m,T),q("dominating"),E(`${y.character.name} was eliminated by Howitzer!`),T===0&&!m[0].isBot&&je(!0),Gt(m)):(q("godlike"),E(`${y.character.name} lost a life point to Howitzer! Health: ${m[T].health}`)))}V(m),E(`${s.name} used Howitzer! ${h} players took damage.`);break}case f.PONY_EXPRESS:{Xe(i,c);const d=Ee($,3);if(d.drawnCards.length>0){const h=[...v];h[i].hand.push(...d.drawnCards),V(h),M(d.updatedDeck),E(`${s.name} used Pony Express and drew ${d.drawnCards.length} cards!`)}else E(`${s.name} used Pony Express but no cards were available to draw!`);break}default:E(`${c} activation not implemented yet!`);break}},Zi=i=>{if(!Va)return;const{type:c,playerIndex:s}=Va,d=[...v];if(c==="BRAWL_ADDITIONAL_DISCARD"){const h=d[s].hand.splice(i,1)[0];X(y=>[...y,h]);let m=0;d.forEach((y,T)=>{if(T!==s&&y.isAlive){if(y.hand.length>0){const C=y.hand.splice(0,1)[0];X(j=>[...j,C]),m++}else if(y.inPlay.length>0){const C=y.inPlay.splice(0,1)[0];X(j=>[...j,C]),m++}}}),E(`${v[s].character.name} played Brawl! All other players discarded ${m} cards.`)}V(d),Ya(!1),zn(null)},Ki=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);let m=[...$];d.forEach(y=>{if(y.isAlive&&m.length>0){const T=m.shift();if(y.hand.push(T),y.isBot&&y.hand.length>0){const C=y.hand.reduce((Q,F,K)=>ct(F,y,[])<ct(Q.card,y,[])?{card:F,index:K}:Q,{card:y.hand[0],index:0}),j=y.hand.splice(C.index,1)[0];X(Q=>[...Q,j])}}}),M(m),E(`${s.character.name} played Rag Time! All players drew and discarded 1 card.`),V(d)},Rl=(i,c,s=null)=>{const d=v[i];if(d.hand.length<2){E("You need at least 2 cards to play Tequila (Tequila + another card)!");return}const h=s!==null?s:i,m=v[h];if(m.health>=m.character.life){E(`${m.character.name} is already at maximum health!`);return}if(d.isBot){const y=[...v],T=y[i].hand.splice(c,1)[0],C=y[i].hand.map((F,K)=>({card:F,index:K,priority:ct(F,d,[])}));C.sort((F,K)=>F.priority-K.priority);const j=y[i].hand.splice(C[0].index,1)[0];X([...U,T,j]),y[h].health=Math.min(y[h].health+1,y[h].character.life),le(h,"heal-effect",1e3),A(h,1,"heal");const Q=h===i?"themselves":m.character.name;E(`${d.character.name} played Tequila on ${Q} who regained 1 life! Health: ${y[h].health}`),V(y)}else xn(c),ol(h),Ln(!0),E("Choose a card to discard along with Tequila:")},vs=i=>{const c=v[B],s=v[Ca],d=[...v],h=d[B].hand.splice(ot,1)[0],m=i>ot?i-1:i,y=d[B].hand.splice(m,1)[0];X([...U,h,y]),d[Ca].health=Math.min(d[Ca].health+1,d[Ca].character.life),le(Ca,"heal-effect",1e3),A(Ca,1,"heal");const T=Ca===B?"themselves":s.character.name;E(`${c.character.name} played Tequila on ${T} who regained 1 life! Health: ${d[Ca].health}`),V(d),Ln(!1),xn(null),ol(null)},Ss=(i,c)=>{const s=v[i];if(s.hand.length<2){E("You need at least 2 cards to play Whisky (Whisky + another card)!");return}if(s.health>=s.character.life){E(`${s.character.name} is already at maximum health!`);return}if(s.isBot){const d=[...v],h=d[i].hand.splice(c,1)[0],m=d[i].hand.map((C,j)=>({card:C,index:j,priority:ct(C,s,[])}));m.sort((C,j)=>C.priority-j.priority);const y=d[i].hand.splice(m[0].index,1)[0];X([...U,h,y]);const T=Math.min(2,d[i].character.life-d[i].health);d[i].health+=T,le(i,"heal-effect",1e3),A(i,T,"heal"),E(`${s.character.name} drank Whisky and regained ${T} life! Health: ${d[i].health}`),V(d)}else Et(c),hl(!0),E("Choose a card to discard along with Whisky:")},As=(i,c,s)=>{const d=v[i];if(d.hand.length<2){E("You need at least 2 cards to play Springfield (Springfield + another card)!");return}if(tt(i,s,v)>2){E("Springfield can only target players at distance 2 or less!");return}if(d.isBot){const m=[...v],y=m[i].hand.splice(c,1)[0],T=m[i].hand.map((j,Q)=>({card:j,index:Q,priority:ct(j,d,[])}));T.sort((j,Q)=>j.priority-Q.priority);const C=m[i].hand.splice(T[0].index,1)[0];X([...U,y,C]),Dl(i,-1,s,!0,f.SPRINGFIELD)}else Ra(c),Yn(s),wn(!0),E("Choose a card to discard along with Springfield:")},Ji=i=>{const c=v[B],s=[...v],d=s[B].hand.splice(qn,1)[0],h=i>qn?i-1:i,m=s[B].hand.splice(h,1)[0];X([...U,d,m]);const y=Math.min(2,s[B].character.life-s[B].health);s[B].health+=y,le(B,"heal-effect",1e3),A(B,y,"heal"),E(`${c.character.name} drank Whisky and regained ${y} life! Health: ${s[B].health}`),V(s),hl(!1),Et(null)},Wi=i=>{const c=[...v],s=c[B].hand.splice(ml,1)[0],d=i>ml?i-1:i,h=c[B].hand.splice(d,1)[0];X([...U,s,h]),V(c),Dl(B,-1,ji,!0,f.SPRINGFIELD),wn(!1),Ra(null),Yn(null)},Ol=(i,c=!0)=>{const s=v[zi],d=v[Yt],h=[...v];if(c){const m=h[Yt].hand.splice(i,1)[0];X(y=>[...y,m]),E(`${s.character.name} used Can Can! ${d.character.name} discarded ${m.type} from hand.`)}else{const m=h[Yt].inPlay.splice(i,1)[0];X(y=>[...y,m]),E(`${s.character.name} used Can Can! ${d.character.name} discarded ${m.type} from equipment.`)}V(h),Ru(!1),Vn(null),Ou(null)},va=Y.useCallback(()=>{Wt();const i=v[B],c=[...v];if(i.character.name==="Suzy Lafayette"&&c[B].hand.length===0&&$.length>0){const d=$[0];c[B].hand.push(d),M($.slice(1)),E(`${i.character.name} drew a card at end of turn (Suzy Lafayette ability)!`)}V(c);let s=(B+1)%c.length;for(;!c[s].isAlive;)s=(s+1)%c.length;He(s),Bt("draw"),qt(!1),da(!1),fl(d=>d+1),wt(0),jn(new Set),setTimeout(()=>{le(s,"turn-start-effect",1500)},100),E(`${c[s].name}'s turn - Cards will be drawn automatically`),q("turnStart")},[v,B,$,M,V,He,Bt,qt,wt,E,le,Wt,q]),Fi=Y.useCallback((i,c)=>{const s=[...v],d=s[i],h=d.hand.map((y,T)=>({card:y,index:T,priority:ct(y,d,[])}));h.sort((y,T)=>y.priority-T.priority);const m=[];for(let y=0;y<c;y++){const T=h[y],C=s[i].hand.splice(T.index-y,1)[0];m.push(C),X(j=>[...j,C])}V(s),E(`${d.name} discarded ${c} card${c>1?"s":""} to hand limit: ${m.map(y=>y.type).join(", ")}`),setTimeout(()=>va(),1e3)},[v,X,V,E,va]),Qa=Y.useCallback(()=>{if(!et){E("You must draw cards before ending your turn!");return}const i=v[B],c=[...v];let s=i.health;i.character.name==="Sean Mallory"&&(s=Math.max(s,10));const d=c[B].hand.length-s;if(d>0)if(i.isBot){Fi(B,d);return}else{Hn(d),Un([]),Da(!0),E(`You must discard ${d} card${d>1?"s":""} (hand limit: ${s})`);return}va()},[et,v,B,Fi,va,E,Hn,Un,Da]),Pi=i=>{const c=[...ha],s=c.indexOf(i);s>=0?c.splice(s,1):c.length<oa&&c.push(i),Un(c)},Ii=()=>{if(ha.length!==oa){E(`Please select exactly ${oa} card${oa>1?"s":""} to discard.`);return}const i=[...v],c=[];[...ha].sort((d,h)=>h-d).forEach(d=>{const h=i[B].hand.splice(d,1)[0];c.push(h),X(m=>[...m,h])}),V(i),Da(!1),Un([]),Hn(0),E(`You discarded ${c.length} card${c.length>1?"s":""}: ${c.map(d=>d.type).join(", ")}`),setTimeout(()=>va(),1e3)},Gt=i=>{const c=i.find(T=>T.role===Ae.SHERIFF),s=i.filter(T=>T.role===Ae.OUTLAW&&T.isAlive),d=i.filter(T=>T.role===Ae.DEPUTY&&T.isAlive),h=i.filter(T=>T.role===Ae.RENEGADE&&T.isAlive),m=i[0],y=m&&!m.isBot&&m.isAlive;c.isAlive?s.length===0&&h.length===0&&(be("ended"),E("Game over! The Sheriff and Deputies win!"),y&&(m.role===Ae.SHERIFF||m.role===Ae.DEPUTY)?(_("won"),q("unstoppable")):_("lost")):h.length===1&&s.length===0&&d.length===0?(be("ended"),E("Game over! The Renegade wins!"),y&&m.role===Ae.RENEGADE?(_("won"),q("unstoppable")):_("lost")):(be("ended"),E("Game over! The Outlaws win!"),y&&m.role===Ae.OUTLAW?(_("won"),q("unstoppable")):_("lost"))},$n=i=>{if(pu(!1),_n.type==="BARREL_DEFENSE"){const c=v[_n.playerIndex];let s=!1;i&&ga(c)?(s=!0,E(`${c.character.name} defended with Barrel!`),q("defense")):i&&E(`${c.character.name} tried to use Barrel but failed!`),Ns(s)}en(null),Ta(null)},xu=i=>{if(xa(!1),Na.type==="MISSED_DEFENSE"&&Nu){const{attackerIndex:c,targetIndex:s,missedRequired:d,updatedPlayers:h}=Nu,m=v[s];let y=!1;if(i){let T=0;for(let C=0;C<d;C++){const j=h[s].hand.findIndex(Q=>Q.type===f.MISSED||m.character.name==="Calamity Janet"&&Q.type===f.BANG);if(j>=0){const Q=h[s].hand.splice(j,1)[0];X(F=>[...F,Q]),T++}else break}T>=d&&(y=!0,E(`${m.character.name} defended with ${T} Missed! card(s)`),q("defense"))}else E(`${m.character.name} chose not to use Missed! cards`);V(h),Ts(y,c,s)}Tu(null),Bn(null)},ec=()=>{an(!rl)},tc=()=>{be("setup"),_(null),an(!1),V([]),M([]),X([]),He(0),E("Welcome to BANG!"),je(!1),nn(!1),pu(!1),xa(!1)},Es=()=>{an(!1),_(null),v.length,v.filter(i=>!i.isBot).length,v.filter(i=>!i.isBot).map(i=>i.name),be("setup"),setTimeout(()=>{El()},100)},ps=()=>{za(!Ue),Ue||q("click")},Ts=(i,c,s)=>{const d=v[s],h=[...v];i?le(s,"dodge-effect",800):(h[s].health-=1,le(s,"damage-effect",1500),A(s,1,"damage"),It(h[s],"ON_DAMAGE_TAKEN",{attackerIndex:c}),h[s].health<=0?(h[s].isAlive=!1,pt(h,s),E(`${d.character.name} (${d.role}) was eliminated!`),q("dominating"),s===0&&!h[0].isBot&&je(!0),Gt(h)):(q("godlike"),E(`${d.character.name} lost a life point! Health: ${h[s].health}`))),V(h)},Ns=i=>{if(!tn)return;const{attackerIndex:c,targetIndex:s,isSubstitution:d}=tn,h=v[c],m=v[s],y=[...v];if(!i){const T=h.character.name==="Slab the Killer"?2:1,C=y[s].hand.filter(j=>j.type===f.MISSED||m.character.name==="Calamity Janet"&&j.type===f.BANG);if(C.length>=T)if(m.isBot){let j=0;for(let Q=0;Q<T;Q++){const F=y[s].hand.findIndex(K=>K.type===f.MISSED||m.character.name==="Calamity Janet"&&K.type===f.BANG);if(F>=0){const K=y[s].hand.splice(F,1)[0];X(ue=>[...ue,K]),j++}else break}j>=T&&(i=!0,E(`${m.character.name} defended with ${j} Missed! card(s)`))}else{Bn({attackerIndex:c,targetIndex:s,missedRequired:T,availableMissedCards:C.length,updatedPlayers:y}),Tu({type:"MISSED_DEFENSE",playerIndex:s,missedRequired:T,availableMissedCards:C.length,message:`${m.character.name}, do you want to use ${T} Missed! card${T>1?"s":""} to defend?`}),xa(!0);return}}i?Vt(m)?le(s,"barrel-defense-effect",1e3):le(s,"dodge-effect",800):(y[s].health-=1,le(s,"damage-effect",1500),A(s,1,"damage"),It(y[s],"ON_DAMAGE_TAKEN",{attackerIndex:c}),y[s].health<=0?(y[s].isAlive=!1,pt(y,s),E(`${m.character.name} (${m.role}) was eliminated!`),q("dominating"),s===0&&!y[0].isBot&&je(!0),Gt(y)):(q("godlike"),E(`${m.character.name} lost a life point! Health: ${y[s].health}`))),V(y)},bs=()=>{const i=v[B];if(i.hand.length<2){E("Need at least 2 cards to use Sid Ketchum's ability!");return}if(i.health>=i.maxHealth){Ie("Already at maximum health!",!0,4e3);return}const c=[...v];for(let s=0;s<2;s++){const d=Math.floor(Math.random()*c[B].hand.length),h=c[B].hand.splice(d,1)[0];X([...U,h])}c[B].health+=1,le(B,"healing-effect",2e3),A(B,1,"heal"),V(c),E(`${i.character.name} used Sid Ketchum's ability to gain 1 life point!`)},Tt=Y.useCallback((i,c)=>{const s=v.filter((d,h)=>d.isAlive&&h!==i);switch(c){case Ae.SHERIFF:case Ae.DEPUTY:return s.filter(d=>d.role===Ae.OUTLAW||d.role===Ae.RENEGADE);case Ae.OUTLAW:return s.filter(d=>d.role===Ae.SHERIFF||d.role===Ae.DEPUTY);case Ae.RENEGADE:return s.length>2?s.filter(d=>d.role!==Ae.RENEGADE):s;default:return s}},[v]),ct=Y.useCallback((i,c,s)=>{let d=0;if([f.BARREL,f.MUSTANG,f.SCOPE,f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.JAIL,f.DYNAMITE].includes(i.type)&&c.inPlay.some(h=>h.type===i.type))return 0;switch(i.type){case f.BANG:s.length>0&&$a(c)?d=8:$a(c)||(d=0);break;case f.BEER:c.health<c.maxHealth&&(d=9);break;case f.MISSED:d=2;break;case f.BARREL:case f.MUSTANG:case f.SCOPE:d=6;break;case f.VOLCANIC:case f.SCHOFIELD:case f.REMINGTON:case f.REV_CARABINE:case f.WINCHESTER:if(!c.inPlay.some(h=>[f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER].includes(h.type)))d=7;else{const h=c.inPlay.find(T=>[f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER].includes(T.type)),m=mf[h.type]||1;(mf[i.type]||1)>m?d=6:d=1}break;case f.GATLING:s.length>=2&&(d=10);break;case f.INDIANS:s.length>=2&&(d=9);break;case f.DUEL:s.length>0&&(d=7);break;case f.PANIC:case f.CAT_BALOU:s.length>0&&(d=6);break;case f.STAGECOACH:case f.WELLS_FARGO:d=6;break;case f.SALOON:c.health<c.maxHealth?d=8:d=3;break;case f.JAIL:{s.filter(m=>m.role!==Ae.SHERIFF&&!m.inPlay.some(y=>y.type===f.JAIL)).length>0?d=5:d=0;break}case f.DYNAMITE:d=4;break;case f.GENERAL_STORE:d=5;break;default:d=3}return d},[$a]),qu=Y.useCallback(async()=>{if(cn)return;const i=v[B];if(i.isBot){if(Gn(!0),At==="draw"&&!et&&(await new Promise(c=>setTimeout(c,1e3)),bl(),await new Promise(c=>setTimeout(c,500))),At==="play"){await new Promise(m=>setTimeout(m,1500));const c=Tt(B,i.role),s=[...i.hand];s.map((m,y)=>({card:m,index:y,priority:ct(m,i,c)})).sort((m,y)=>y.priority-m.priority);const h=Math.min(3,s.length);for(let m=0;m<h;m++){let y=!1;const C=[...v[B].hand].map((j,Q)=>({card:j,index:Q,priority:ct(j,v[B],c)}));C.sort((j,Q)=>Q.priority-j.priority);for(const j of C)if(j.priority>2){const Q=[f.BANG,f.PANIC,f.CAT_BALOU,f.DUEL,f.JAIL].includes(j.card.type);if(Q&&c.length>0){let F=-1;if(j.card.type===f.BANG||j.card.type===f.DUEL){const K=c.sort((ue,ve)=>ue.health-ve.health);F=v.findIndex(ue=>ue===K[0])}else if(j.card.type===f.JAIL){const K=c.filter(ue=>ue.role!==Ae.SHERIFF&&!ue.inPlay.some(ve=>ve.type===f.JAIL));if(K.length>0){const ue=K[Math.floor(Math.random()*K.length)];F=v.findIndex(ve=>ve===ue)}}else{const K=c[Math.floor(Math.random()*c.length)];F=v.findIndex(ue=>ue===K)}if(F!==-1){dn(B,j.index,F),y=!0;break}}else if(!Q){dn(B,j.index),y=!0;break}}if(!y)break;await new Promise(j=>setTimeout(j,800))}await new Promise(m=>setTimeout(m,1e3)),Qa()}Gn(!1)}},[cn,v,B,At,et,bl,Qa,dn,Tt,ct,Gn]);return Y.useEffect(()=>{if(J==="playing"&&B!==-1&&v.length>0){const i=v[B];if(i&&i.isBot&&!cn){const c=setTimeout(()=>{qu()},1e3);return()=>clearTimeout(c)}}},[B,At,J,v,cn,qu]),Y.useEffect(()=>{qa&&ze.length===0&&(nn(!1),Ye([]),fa([]),un(0),E("General Store complete! All cards have been taken."))},[qa,ze.length]),Y.useEffect(()=>{if(qa&&se.length>0&&qe<se.length&&ze.length>0){const i=se[qe],c=v[i];if(c&&c.isBot){const s=setTimeout(()=>{Uu()},1200);return()=>clearTimeout(s)}}},[qa,qe,se,ze.length,v,Uu]),Y.useEffect(()=>()=>{Ke&&clearTimeout(Ke)},[Ke]),Y.useEffect(()=>{if(J==="playing"&&B!==-1&&v.length>0){const i=v[B];if(i&&!i.isBot&&At==="draw"&&!et&&!Pt&&Ve.current!==ba){const c=setTimeout(()=>{bl()},500);return()=>{clearTimeout(c)}}}},[J,B,At,et,Pt,ba,bl,v]),Y.useEffect(()=>{if(J==="playing"&&B!==-1&&v.length>0){const i=v[B];i&&!i.isBot?xt():Wt()}else Wt()},[J,B,xt,Wt]),g.jsxs("div",{className:`bang-game ${J==="setup"?"setup-mode":""}`,children:[g.jsxs("div",{className:`game-header ${J!=="setup"?"hidden":""}`,children:[g.jsx("h1",{children:"🤠 BANG! The Card Game "}),g.jsx("p",{className:"game-subtitle",children:"The Wild West Card Game with Official Graphics"})]}),J==="setup"&&g.jsxs("div",{className:"setup-screen",children:[g.jsxs("div",{className:"setup-form",children:[g.jsxs("div",{className:"game-mode",children:[g.jsx("label",{children:"Game Mode: "}),g.jsxs("select",{value:We,onChange:i=>{const c=parseInt(i.target.value);Oa(c),me<c&&ut(c);const s=Array(c).fill("").map((d,h)=>sn[h]||"");Al(s)},children:[g.jsx("option",{value:"1",children:"Single Player (vs Bots)"}),g.jsx("option",{value:"2",children:"2 Human Players"}),g.jsx("option",{value:"3",children:"3 Human Players"}),g.jsx("option",{value:"4",children:"4 Human Players"}),g.jsx("option",{value:"5",children:"5 Human Players"}),g.jsx("option",{value:"6",children:"6 Human Players"}),g.jsx("option",{value:"7",children:"7 Human Players"})]})]}),g.jsx("div",{className:"human-players",children:Array(We).fill(0).map((i,c)=>g.jsxs("div",{className:"player-name",children:[g.jsxs("label",{children:["Player ",c+1," Name: "]}),g.jsx("input",{type:"text",value:sn[c]||"",onChange:s=>{const d=[...sn];d[c]=s.target.value,Al(d)},placeholder:`Enter Player ${c+1} name`,maxLength:"20"})]},`human-player-${c}`))}),g.jsxs("div",{className:"player-count",children:[g.jsx("label",{children:"Total Players: "}),g.jsx("select",{value:me,onChange:i=>ut(parseInt(i.target.value)),disabled:We===7,children:Array.from({length:8-We},(i,c)=>{const s=We+c;if(s<4)return null;const d=s-We;return g.jsxs("option",{value:s,children:[s," (",We," Human",We>1?"s":"",d>0?` + ${d} Bot${d>1?"s":""}`:"",")"]},s)}).filter(Boolean)})]})]}),me-We>0&&g.jsxs("p",{className:"bot-info",children:["🤖 ",me-We," AI bot",me-We>1?"s":""," will join the game and make intelligent decisions!"]}),g.jsx("button",{className:"start-button",onClick:El,children:"Start Game"})]}),J==="playing"&&g.jsxs("div",{className:"playing-container",children:[vl&&g.jsxs("div",{className:`turn-timer ${Xa<=30&&Xa>10?"warning":""} ${Xa<=10?"critical":""}`,children:["⏰ Time: ",ia(Xa)]}),g.jsx("button",{onClick:ec,className:"game-menu-button-fixed",title:"Game Menu",children:"☰"}),g.jsxs("div",{className:"top-ui-container",children:[g.jsx("div",{className:"message-box-playing",children:dt}),B!==-1&&!v[B].isBot&&g.jsxs("div",{className:"actions-right",children:[g.jsx("button",{onClick:bl,disabled:At!=="draw"||et,className:At!=="draw"||et?"disabled":"",children:et?"Cards Drawn ✓":"Auto-Drawing..."}),g.jsx("button",{onClick:Qa,disabled:!et,className:et?"":"disabled",children:"End Turn"}),v[B].character.name==="Sid Ketchum"&&v[B].hand.length>=2&&v[B].health<v[B].maxHealth&&g.jsx("button",{onClick:()=>bs(),children:"Sid Ketchum Ability"})]})]}),g.jsxs("div",{className:"game-board",children:[g.jsxs("div",{className:`current-player-area ${B!==0||v[0]&&v[0].isBot?"disabled":""}`,"data-player-index":"0",children:[v.length>0&&v[0]&&!v[0].isBot&&!ra&&g.jsxs("div",{className:"current-player effect-container","data-player-index":0,children:[g.jsx("div",{className:"player-status",children:g.jsxs("div",{className:"character-info",children:[g.jsxs("div",{className:"character-image-container",children:[g.jsx("img",{src:_i[v[0].character.name],alt:v[0].character.name,className:"character-image",onContextMenu:i=>{i.preventDefault(),_a(v[0].character)},onDoubleClick:()=>{_a(v[0].character)},onTouchStart:()=>pl({isCharacter:!0,character:v[0].character,type:v[0].character.name}),onTouchEnd:Tl,onTouchMove:Nl}),g.jsxs("div",{className:"player-name-overlay",children:[v[0].name,v[0].isBot&&g.jsx("span",{className:"bot-indicator-overlay",children:" 🤖"})]}),v[0].isAlive&&g.jsxs("div",{className:"health-display-overlay",children:["❤️ ",v[0].health,"/",v[0].maxHealth]})]}),!v[0].isAlive&&g.jsxs("div",{className:"role-display-below",children:["💀 ",v[0].role]}),g.jsxs("div",{className:"character-details",children:[at(v[0])&&g.jsx("div",{className:"sheriff-badge-current",children:"⭐"}),g.jsx("div",{className:"role-display",children:g.jsx("img",{src:km[v[0].role],alt:v[0].role,className:"role-image"})})]})]})}),g.jsxs("div",{className:"hand-area",children:[g.jsxs("h4",{children:["Your Hand (",v[0].hand.length," cards)",g.jsxs("span",{className:"hand-limit-info",children:["- Hand Limit: ",v[0].health]})]}),g.jsx("div",{className:"hand-cards",children:v[0]&&v[0].hand&&Array.isArray(v[0].hand)&&v[0].hand.map((i,c)=>{if(!i||!i.type)return null;const s=`play-0-${c}`,d=Lt[s];return g.jsxs("div",{"data-card-index":c,className:`hand-card ${[f.BARREL,f.MUSTANG,f.SCOPE,f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.JAIL,f.DYNAMITE].includes(i.type)&&v[0].inPlay.some(h=>h.type===i.type)?"duplicate-equipment":""} ${i.type===f.BANG&&!$a(v[0])?"bang-limit-reached":""} ${i.type===f.BEER&&v[0].health>=v[0].maxHealth?"beer-unplayable":""} ${d?`card-${d.type}-animation`:""}`,onClick:h=>{if(Mi||Cn||h.detail===0&&Pl)return;if(B!==0){E("It's not your turn!");return}[f.BANG,f.PANIC,f.CAT_BALOU,f.DUEL,f.JAIL,f.PUNCH,f.TEQUILA].includes(i.type)?(Z({playerIndex:0,cardIndex:c,card:i}),fe(!0)):dn(0,c)},onContextMenu:h=>{h.preventDefault(),ht(i)},onDoubleClick:h=>{Mi||Cn||Pl||h.detail!==0&&ht(i)},onMouseEnter:()=>vt(i),onMouseLeave:()=>vt(null),onTouchStart:h=>Gu(h,i,c),onTouchEnd:ns,onTouchMove:us,title:[f.BARREL,f.MUSTANG,f.SCOPE,f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.JAIL,f.DYNAMITE].includes(i.type)&&v[0].inPlay.some(h=>h.type===i.type)?`${i.type} - ${i.suit} ${i.value} (DUPLICATE - Cannot play!) | Right-click or double-click to preview`:i.type===f.BANG&&!$a(v[0])?`${i.type} - ${i.suit} ${i.value} (BANG! limit reached - Need Volcanic or Willy the Kid!) | Right-click or double-click to preview`:i.type===f.BEER&&v[0].health>=v[0].maxHealth?`${i.type} - ${i.suit} ${i.value} (Already at maximum health - Cannot play!) | Right-click or double-click to preview`:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"hand-card-image",onError:h=>{h.target.style.display="none",h.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},`hand-${c}-${i.type}-${i.suit}-${i.value}-${Date.now()}`)})})]}),v[0].inPlay.length>0&&g.jsxs("div",{className:"equipment-area",children:[g.jsx("h4",{children:"Equipment:"}),g.jsx("div",{className:"equipment-cards",children:v[0]&&v[0].inPlay&&Array.isArray(v[0].inPlay)&&v[0].inPlay.map((i,c)=>!i||!i.type?null:g.jsx("div",{className:`equipment-card ${i.equipmentType===Jt.GREEN?"green-card":""} ${i.equipmentType===Jt.GREEN&&Le(v[0],i.type)?"usable-green":""}`,title:`${i.type} ${i.equipmentType===Jt.GREEN?"(Green - Click to activate)":""}`,onClick:()=>{i.equipmentType===Jt.GREEN&&Le(v[0],i.type)&&ki(0,i.type)},onContextMenu:s=>{s.preventDefault(),ht(i)},onDoubleClick:()=>{ht(i)},onTouchStart:()=>pl(i),onTouchEnd:Tl,onTouchMove:Nl,children:g.jsx("img",{src:Rt[i.type],alt:i.type,className:"equipment-card-image"})},`equipment-${c}-${i.type}-${Date.now()}`))})]})]}),ra&&g.jsx("div",{className:"spectator-mode",children:g.jsxs("div",{className:"spectator-info",children:[g.jsx("h3",{children:"👻 Spectator Mode"}),g.jsx("p",{children:"You have been eliminated but are watching the game continue."}),g.jsx("p",{children:"The game will end when a winning condition is met."}),g.jsx("button",{className:"exit-spectator-button",onClick:fn,children:"Exit to Main Menu"})]})})]}),g.jsx("div",{className:"central-area",children:g.jsxs("div",{className:"game-info",children:[g.jsxs("div",{className:"deck-info",children:[g.jsxs("h4",{children:["Deck (",$.length,")"]}),g.jsx("div",{className:"deck-card",children:g.jsx("img",{src:Qm,alt:"Card Back",className:"deck-image"})})]}),g.jsxs("div",{className:"discard-info",children:[g.jsxs("h4",{children:["Discard (",U.length,")"]}),g.jsx("div",{className:`discard-card discard-pile ${U.length===0?"empty":""}`,children:U.length>0&&U[U.length-1]&&U[U.length-1].type&&g.jsx("img",{src:Rt[U[U.length-1].type],alt:U[U.length-1].type,className:"discard-image",onContextMenu:i=>{i.preventDefault(),ht(U[U.length-1])},onDoubleClick:()=>{ht(U[U.length-1])},onTouchStart:()=>pl(U[U.length-1]),onTouchEnd:Tl,onTouchMove:Nl,title:`${U[U.length-1].type} - ${U[U.length-1].suit} ${U[U.length-1].value} | Right-click, double-click, or touch-hold to preview`})})]})]})}),g.jsx("div",{className:"other-players-area",children:v.map((i,c)=>c!==0&&g.jsxs("div",{className:`other-player ${i.isAlive?"":"dead-player"} effect-container`,"data-player-index":c,children:[g.jsxs("div",{className:"player-info",children:[g.jsxs("div",{className:"character-image-container",children:[g.jsx("img",{src:_i[i.character.name],alt:i.character.name,className:"character-image",onError:s=>{s.target.style.display="none"},onContextMenu:s=>{s.preventDefault(),_a(i.character)},onDoubleClick:()=>{_a(i.character)},onTouchStart:()=>pl({isCharacter:!0,character:i.character,type:i.character.name}),onTouchEnd:Tl,onTouchMove:Nl}),g.jsxs("div",{className:"player-name-overlay",children:[i.name,i.isBot&&g.jsx("span",{className:"bot-indicator-overlay",children:" 🤖"})]}),i.isAlive&&g.jsxs("div",{className:"health-display-overlay",children:["❤️ ",i.health,"/",i.maxHealth]})]}),!i.isAlive&&g.jsxs("div",{className:"role-display-below",children:["💀 ",i.role]}),g.jsxs("div",{className:"player-details",children:[at(i)&&g.jsx("div",{className:"sheriff-badge-below",children:"⭐"}),g.jsxs("div",{className:"cards-count",children:["🃏 ",i.hand.length]})]})]}),i.inPlay.length>0&&g.jsx("div",{className:"other-player-equipment",children:i&&i.inPlay&&Array.isArray(i.inPlay)&&i.inPlay.map((s,d)=>!s||!s.type?null:g.jsx("div",{className:"small-card",title:s.type,onContextMenu:h=>{h.preventDefault(),ht(s)},onDoubleClick:()=>{ht(s)},onTouchStart:()=>pl(s),onTouchEnd:Tl,onTouchMove:Nl,children:g.jsx("img",{src:Rt[s.type],alt:s.type,className:"small-card-image"})},`player-${c}-equipment-${d}-${s.type}-${Date.now()}`))})]},`other-player-${c}-${i.character.name}`))})]})]}),J==="ended"&&g.jsxs("div",{className:"game-over",children:[g.jsx("div",{className:"game-result-header",children:Te==="won"?g.jsxs(g.Fragment,{children:[g.jsx("h2",{className:"victory-title",children:"🎉 Victory! 🎉"}),g.jsx("p",{className:"victory-subtitle",children:"Congratulations! You have won the game!"})]}):Te==="lost"?g.jsxs(g.Fragment,{children:[g.jsx("h2",{className:"defeat-title",children:"💀 Defeat 💀"}),g.jsx("p",{className:"defeat-subtitle",children:"Better luck next time!"})]}):g.jsxs(g.Fragment,{children:[g.jsx("h2",{children:"Game Over!"}),g.jsx("p",{children:"The game has ended."})]})}),g.jsx("div",{className:"final-roles",children:v.map((i,c)=>g.jsxs("div",{className:`player-result ${i.isAlive?"":"dead-player"}`,children:[g.jsx("img",{src:_i[i.character.name],alt:i.character.name,className:"character-image-result",onError:s=>{s.target.style.display="none"},onContextMenu:s=>{s.preventDefault(),_a(i.character)},onDoubleClick:()=>{_a(i.character)},onTouchStart:()=>pl({isCharacter:!0,character:i.character,type:i.character.name}),onTouchEnd:Tl,onTouchMove:Nl}),g.jsxs("h3",{children:[i.name,i.isBot&&g.jsx("span",{className:"bot-indicator-small",children:" 🤖"})]}),at(i)&&g.jsx("div",{className:"sheriff-badge-result",children:"⭐"}),g.jsx("p",{className:"character-name",children:i.character.name}),g.jsx("p",{className:"role",children:i.role}),g.jsx("p",{className:"status",children:i.isAlive?"Survived":"Eliminated"})]},`final-result-${c}-${i.character.name}`))}),g.jsx("button",{className:"restart-button",onClick:()=>{be("setup"),V([]),He(0),M([]),X([]),Bt("draw"),qt(!1),wt(0),Oa(1),Al([""]),E("Welcome to BANG!")},children:"Play Again"})]}),I&&k&&g.jsxs("div",{className:"target-selection",children:[g.jsxs("h3",{children:["Select a target for ",k.card.type]}),g.jsx("div",{className:"target-options",children:v.map((i,c)=>i.isAlive&&(k.card.type===f.JAIL?B!==c&&i.role!==Ae.SHERIFF&&!i.inPlay.some(d=>d.type===f.JAIL):k.card.type===f.TEQUILA?!0:B!==c)&&g.jsxs("div",{className:`target-option ${k.card.type===f.JAIL&&(i.role===Ae.SHERIFF||i.inPlay.some(d=>d.type===f.JAIL))?"invalid-target":""}`,onClick:()=>{k.cardIndex===-1?Qi(k.playerIndex,k.card.type,c):dn(k.playerIndex,k.cardIndex,c),fe(!1),Z(null)},children:[g.jsxs("div",{className:"target-player-name",children:[i.name,i.isBot&&g.jsx("span",{className:"bot-indicator-small",children:" 🤖"}),at(i)&&g.jsx("span",{className:"sheriff-badge-target",children:" ⭐"}),k.card.type===f.JAIL&&i.inPlay.some(d=>d.type===f.JAIL)&&g.jsx("span",{className:"jail-indicator",children:" 🔒 IN JAIL"})]}),g.jsx("div",{className:"target-character-name",children:i.character.name}),g.jsxs("div",{className:"target-info",children:["Health: ",i.health," | Distance: ",tt(B,c,v)]})]},`target-${c}-${i.character.name}`))}),g.jsx("button",{onClick:()=>{fe(!1),Z(null)},children:"Cancel"})]}),wa&&g.jsxs("div",{className:"discard-selection",children:[g.jsx("h3",{children:"Discard Cards - Hand Limit Exceeded"}),g.jsxs("p",{children:["You must discard ",oa," card",oa>1?"s":""," (Hand limit: ",v[B].health,")"]}),g.jsx("div",{className:"discard-cards",children:v[B]&&v[B].hand&&Array.isArray(v[B].hand)&&v[B].hand.map((i,c)=>!i||!i.type?null:g.jsxs("div",{className:`discard-card ${ha.includes(c)?"selected":""}`,onClick:()=>Pi(c),onContextMenu:s=>{s.preventDefault(),ht(i)},onDoubleClick:()=>{ht(i)},title:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"discard-card-image"}),g.jsx("div",{className:"card-name",children:i.type}),ha.includes(c)&&g.jsx("div",{className:"selected-indicator",children:"✓"})]},`discard-${c}-${i.type}-${i.suit}-${i.value}-${Date.now()}`))}),g.jsx("div",{className:"discard-actions",children:g.jsxs("button",{onClick:Ii,disabled:ha.length!==oa,className:ha.length!==oa?"disabled":"",children:["Discard Selected Cards (",ha.length,"/",oa,")"]})})]}),Il&&g.jsx("div",{className:"death-modal-overlay",children:g.jsx("div",{className:"death-modal",children:g.jsxs("div",{className:"death-modal-content",children:[g.jsx("h2",{children:"💀 You Have Been Eliminated!"}),g.jsx("p",{children:"Your character has been eliminated from the game."}),g.jsx("p",{children:"What would you like to do?"}),g.jsxs("div",{className:"death-modal-buttons",children:[g.jsx("button",{className:"spectate-button",onClick:xi,children:"👻 Spectate Game"}),g.jsx("button",{className:"exit-button",onClick:fn,children:"🚪 Exit to Main Menu"})]})]})})}),qa&&ze.length>0&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"general-store-modal",children:[g.jsx("h3",{children:"General Store"}),g.jsx("p",{children:se.length>0&&qe<se.length?`${(wu=v[se[qe]])==null?void 0:wu.character.name}'s turn to choose`:"Choose a card"}),g.jsxs("p",{className:"general-store-instruction",children:["Cards available: ",ze.length," | Players remaining: ",se.length-qe]}),g.jsx("p",{className:"general-store-rule",children:"Rule: Starting with the player who played General Store, each player chooses one card clockwise."}),g.jsx("div",{className:"general-store-cards",children:ze.map((i,c)=>{var s;return!i||!i.type?null:g.jsxs("div",{className:`general-store-card ${(s=v[se[qe]])!=null&&s.isBot?"disabled":""}`,onClick:()=>{var h;const d=se[qe];!((h=v[d])!=null&&h.isBot)&&d===se[qe]&&ze[c]&&$i(c)},onContextMenu:d=>{d.preventDefault(),ht(i)},onDoubleClick:()=>{ht(i)},title:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"general-store-card-image"}),g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.value,i.suit]})]},`general-store-${c}-${i.type}-${i.suit}-${i.value}`)})}),se.length>0&&qe<se.length&&g.jsx("div",{className:"general-store-info",children:g.jsxs("p",{children:["Turn order: ",se.map((i,c)=>{var s;return`${c===qe?"→ ":""}${(s=v[i])==null?void 0:s.character.name}${c===qe?" ←":""}`}).join(" → ")]})}),g.jsxs("div",{className:"general-store-actions",children:[g.jsx("button",{className:"close-general-store-button",onClick:()=>{nn(!1),Ye([]),fa([]),un(0),E("General Store cancelled.")},children:"Cancel General Store"}),se.length>0&&qe<se.length&&((Yu=v[se[qe]])==null?void 0:Yu.isBot)&&g.jsx("button",{className:"force-bot-selection-button",onClick:()=>Uu(),children:"Force Bot Selection"})]})]})}),as&&_n&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"ability-choice-modal",children:[g.jsx("h3",{children:"Character Ability"}),g.jsx("p",{children:_n.message}),g.jsxs("div",{className:"ability-choice-buttons",children:[g.jsx("button",{className:"ability-yes-button",onClick:()=>$n(!0),children:"Use Ability"}),g.jsx("button",{className:"ability-no-button",onClick:()=>$n(!1),children:"Don't Use"})]})]})}),Mn&&Na&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"missed-choice-modal",children:[g.jsx("h3",{children:"Defend with Missed!"}),g.jsx("p",{children:Na.message}),g.jsxs("div",{className:"missed-choice-info",children:[g.jsxs("p",{children:["You have ",Na.availableMissedCards," Missed! card",Na.availableMissedCards>1?"s":""," available."]}),g.jsxs("p",{children:["You need ",Na.missedRequired," Missed! card",Na.missedRequired>1?"s":""," to defend."]})]}),g.jsxs("div",{className:"missed-choice-buttons",children:[g.jsxs("button",{className:"missed-yes-button",onClick:()=>xu(!0),children:["Use Missed! Card",Na.missedRequired>1?"s":""]}),g.jsx("button",{className:"missed-no-button",onClick:()=>xu(!1),children:"Take Damage"})]})]})}),Hi&&Va&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"card-selection-modal",children:[g.jsx("h3",{children:"Card Selection"}),g.jsx("p",{children:Va.message}),g.jsx("div",{className:"card-selection-grid",children:(Vu=v[Va.playerIndex])==null?void 0:Vu.hand.map((i,c)=>g.jsxs("div",{className:"selectable-card",onClick:()=>Zi(c),children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"card-selection-image",onError:s=>{s.target.style.display="none",s.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-selection-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},c))})]})}),rl&&g.jsx("div",{className:"modal-overlay",onClick:()=>an(!1),children:g.jsxs("div",{className:"game-menu-modal",onClick:i=>i.stopPropagation(),children:[g.jsx("h3",{children:"Game Menu"}),g.jsxs("div",{className:"game-menu-buttons",children:[g.jsx("button",{className:"menu-button",onClick:ps,children:Ue?"🔊 Sound: ON":"🔇 Sound: OFF"}),g.jsx("button",{className:"menu-button",onClick:Es,children:"🔄 Restart Game"}),g.jsx("button",{className:"menu-button",onClick:tc,children:"🏠 Main Menu"}),g.jsx("button",{className:"menu-button menu-close",onClick:()=>an(!1),children:"✕ Close"})]})]})}),Gi.map(i=>{if(!i||!i.card||!i.card.type)return null;const c=document.querySelector(`[data-player-index="${i.fromPlayerIndex}"]`),s=i.toPlayerIndex>=0?document.querySelector(`[data-player-index="${i.toPlayerIndex}"]`):document.querySelector(".discard-pile"),d=document.querySelector(".discard-pile");if(!c||!s&&i.phase==="toTarget"||!d&&i.phase==="toDiscard")return null;const h=c.getBoundingClientRect(),m=i.phase==="toTarget"?s.getBoundingClientRect():d.getBoundingClientRect(),y=h.left+h.width/2,T=h.top+h.height/2,C=m.left+m.width/2,j=m.top+m.height/2,Q=y+(C-y)*i.progress,F=T+(j-T)*i.progress,K=i.phase==="toTarget"?1+i.progress*.2:1.2-i.progress*.2,ue=i.progress*360;return g.jsx("div",{className:`animating-card ${i.card.type==="BANG!"?"bang-card":""}`,style:{left:Q-30,top:F-42,transform:`scale(${K}) rotate(${ue}deg)`,opacity:1-i.progress*.1},children:g.jsxs("div",{className:`card ${i.card.suit==="♥"||i.card.suit==="♦"?"red":"black"}`,children:[g.jsx("div",{children:i.card.type||"Unknown"}),g.jsxs("div",{children:[i.card.value||"",i.card.suit||""]})]})},i.id)}),Au&&ye&&g.jsx("div",{className:"card-zoom-overlay",onClick:rn,children:g.jsx("div",{className:"card-zoom-modal",onClick:i=>i.stopPropagation(),children:g.jsxs("div",{className:"card-zoom-content",children:[g.jsxs("div",{className:"card-zoom-image",children:[g.jsx("img",{src:ye.isCharacter?_i[ye.type]:Rt[ye.type],alt:ye.type,className:"zoomed-card-image",onError:i=>{i.target.style.display="none",i.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-zoom-fallback",style:{display:"none"},children:[g.jsx("div",{className:"zoomed-card-name",children:ye.type}),g.jsxs("div",{className:"zoomed-card-suit",children:[ye.suit," ",ye.value]})]})]}),g.jsx("div",{className:"card-description",children:ye.isCharacter?ye.character.ability:Yi(ye.type)})]})})}),Mt&&g.jsx("div",{className:"card-zoom-overlay",onClick:()=>pa(null),children:g.jsx("div",{className:"card-zoom-modal",onClick:i=>i.stopPropagation(),children:g.jsxs("div",{className:"card-zoom-content",children:[g.jsxs("div",{className:"card-zoom-image",children:[g.jsx("img",{src:Mt.isCharacter?_i[Mt.type]:Rt[Mt.type],alt:Mt.type,className:"zoomed-card-image",onError:i=>{i.target.style.display="none",i.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-zoom-fallback",style:{display:"none"},children:[g.jsx("div",{className:"zoomed-card-name",children:Mt.type}),g.jsxs("div",{className:"zoomed-card-suit",children:[Mt.suit," ",Mt.value]})]})]}),g.jsxs("div",{className:"card-description",children:[Mt.isCharacter?(Qn=Mt.character)==null?void 0:Qn.ability:Yi(Mt.type),g.jsxs("div",{className:"long-press-hint",children:["💡 ",g.jsx("strong",{children:"Mobile Tip:"})," Hold cards for 2s to zoom, tap quickly to play"]})]})]})})}),ls&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard with Tequila"}),g.jsx("div",{className:"card-selection-grid",children:(ac=v[B])==null?void 0:ac.hand.filter((i,c)=>c!==ot).map((i,c)=>{const s=c>=ot?c+1:c;return g.jsxs("div",{className:"selectable-card",onClick:()=>vs(s),children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"card-image",onError:d=>{d.target.style.display="none",d.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},s)})}),g.jsx("button",{className:"cancel-button",onClick:()=>{Ln(!1),xn(null),ol(null),E("Tequila cancelled.")},children:"Cancel"})]})}),Ui&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard with Whisky"}),g.jsx("div",{className:"card-selection-grid",children:(ka=v[B])==null?void 0:ka.hand.filter((i,c)=>c!==qn).map((i,c)=>{const s=c>=qn?c+1:c;return g.jsxs("div",{className:"selectable-card",onClick:()=>Ji(s),children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"card-image",onError:d=>{d.target.style.display="none",d.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},s)})}),g.jsx("button",{className:"cancel-button",onClick:()=>{hl(!1),Et(null),E("Whisky cancelled.")},children:"Cancel"})]})}),Cu&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard with Springfield"}),g.jsx("div",{className:"card-selection-grid",children:(kn=v[B])==null?void 0:kn.hand.filter((i,c)=>c!==ml).map((i,c)=>{const s=c>=ml?c+1:c;return g.jsxs("div",{className:"selectable-card",onClick:()=>Wi(s),children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"card-image",onError:d=>{d.target.style.display="none",d.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},s)})}),g.jsx("button",{className:"cancel-button",onClick:()=>{wn(!1),Ra(null),Yn(null),E("Springfield cancelled.")},children:"Cancel"})]})}),yl&&Yt!==null&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard (Can Can)"}),g.jsxs("p",{children:[(_l=v[Yt])==null?void 0:_l.character.name," must discard a card"]}),((Zn=v[Yt])==null?void 0:Zn.hand.length)>0&&g.jsxs("div",{children:[g.jsx("h4",{children:"Hand Cards:"}),g.jsx("div",{className:"card-selection-grid",children:(Xu=v[Yt])==null?void 0:Xu.hand.map((i,c)=>g.jsxs("div",{className:"selectable-card",onClick:()=>Ol(c,!0),children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"card-image",onError:s=>{s.target.style.display="none",s.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},`hand-${c}`))})]}),((Ma=v[Yt])==null?void 0:Ma.inPlay.length)>0&&g.jsxs("div",{children:[g.jsx("h4",{children:"Equipment Cards:"}),g.jsx("div",{className:"card-selection-grid",children:(Za=v[Yt])==null?void 0:Za.inPlay.map((i,c)=>g.jsxs("div",{className:"selectable-card",onClick:()=>Ol(c,!1),children:[g.jsx("img",{src:Rt[i.type],alt:i.type,className:"card-image",onError:s=>{s.target.style.display="none",s.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},`equipment-${c}`))})]}),g.jsx("button",{className:"cancel-button",onClick:()=>{Ru(!1),Vn(null),Ou(null),E("Can Can cancelled.")},children:"Cancel"})]})}),Dt.map(i=>g.jsx("div",{className:"moving-card",style:{left:i.startX,top:i.startY,transform:"translate(-50%, -50%)","--end-x":`${i.endX-i.startX}px`,"--end-y":`${i.endY-i.startY}px`},children:g.jsx("img",{src:Rt[i.card.type]||"/images/cards/card-back.png",alt:i.card.type,onError:c=>{c.target.src="/images/cards/card-back.png"}})},i.id))]})}$m.createRoot(document.getElementById("root")).render(g.jsx(Y.StrictMode,{children:g.jsx(Km,{})}));
