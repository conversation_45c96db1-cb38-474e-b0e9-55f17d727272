import { useState, useEffect, useCallback, useRef } from 'react'
import './App.css'

// Card types
const CARD_TYPES = {
  BANG: 'BANG',
  MISSED: 'MISSED',
  BEER: 'BEER',
  PANIC: 'PANIC',
  CAT_BALOU: 'CAT_BALOU',
  STAGECOACH: 'STAGECOACH',
  WELLS_FARGO: 'WELLS_FARGO',
  GATLING: 'GATLING',
  INDIANS: 'INDIANS',
  DUEL: 'DUEL',
  GENERAL_STORE: 'GENERAL_STORE',
  SALOON: 'SALOON',
  BARREL: 'BARREL',
  SCOPE: 'SCOPE',
  MUSTANG: 'MUSTANG',
  JAIL: 'JAIL',
  DYNAMITE: 'DYNAMITE',
  VOLCANIC: 'VOLCANIC',
  SCHOFIELD: 'SCHOFIELD',
  REMINGTON: 'REMINGTON',
  REV_CARABINE: 'REV_CARABINE',
  WINCHESTER: 'WINCHESTER',
  // Dodge City expansion cards
  BINOCULAR: 'BINOCULAR',
  HIDEOUT: 'HIDEOUT',
  BRAWL: 'BRAWL',
  DODGE: 'DODGE',
  PUNCH: 'PUNCH',
  RAG_TIME: 'RAG_TIME',
  SPRINGFIELD: 'SPRINGFIELD',
  TEQUILA: 'TEQUILA',
  WHISKY: 'WHISKY',
  BIBLE: 'BIBLE',
  BUFFALO_RIFLE: 'BUFFALO_RIFLE',
  CAN_CAN: 'CAN_CAN',
  CANTEEN: 'CANTEEN',
  CONESTOGA: 'CONESTOGA',
  DERRINGER: 'DERRINGER',
  HOWITZER: 'HOWITZER',
  IRON_PLATE: 'IRON_PLATE',
  KNIFE: 'KNIFE',
  PEPPERBOX: 'PEPPERBOX',
  PONY_EXPRESS: 'PONY_EXPRESS',
  SOMBRERO: 'SOMBRERO',
  TEN_GALLON_HAT: 'TEN_GALLON_HAT'
}

// Card suits
const SUITS = {
  HEARTS: 'HEARTS',
  DIAMONDS: 'DIAMONDS',
  CLUBS: 'CLUBS',
  SPADES: 'SPADES',
}

// Weapon ranges
const WEAPON_RANGES = {
  [CARD_TYPES.VOLCANIC]: 1,
  [CARD_TYPES.SCHOFIELD]: 2,
  [CARD_TYPES.REMINGTON]: 3,
  [CARD_TYPES.REV_CARABINE]: 4,
  [CARD_TYPES.WINCHESTER]: 5,
  // Dodge City weapons
}

// Equipment types
const EQUIPMENT_TYPES = {
  WEAPON: 'WEAPON',
  DEFENSIVE: 'DEFENSIVE',
  SPECIAL: 'SPECIAL',
  GREEN: 'GREEN', // Green-bordered cards that can be activated
}

// Local card images mapping
const CARD_IMAGES = {
  [CARD_TYPES.BANG]: '/images/cards/bang.png',
  [CARD_TYPES.MISSED]: '/images/cards/missed.png',
  [CARD_TYPES.BEER]: '/images/cards/beer.png',
  [CARD_TYPES.PANIC]: '/images/cards/panic.png',
  [CARD_TYPES.CAT_BALOU]: '/images/cards/cat_balou.png',
  [CARD_TYPES.STAGECOACH]: '/images/cards/stagecoach.png',
  [CARD_TYPES.WELLS_FARGO]: '/images/cards/wells_fargo.png',
  [CARD_TYPES.GATLING]: '/images/cards/gatling.png',
  [CARD_TYPES.INDIANS]: '/images/cards/indians.png',
  [CARD_TYPES.DUEL]: '/images/cards/duel.png',
  [CARD_TYPES.GENERAL_STORE]: '/images/cards/general_store.png',
  [CARD_TYPES.SALOON]: '/images/cards/saloon.png',
  [CARD_TYPES.BARREL]: '/images/cards/barrel.png',
  [CARD_TYPES.SCOPE]: '/images/cards/scope.png',
  [CARD_TYPES.MUSTANG]: '/images/cards/mustang.png',
  [CARD_TYPES.JAIL]: '/images/cards/jail.png',
  [CARD_TYPES.DYNAMITE]: '/images/cards/dynamite.png',
  [CARD_TYPES.VOLCANIC]: '/images/cards/volcanic.png',
  [CARD_TYPES.SCHOFIELD]: '/images/cards/schofield.png',
  [CARD_TYPES.REMINGTON]: '/images/cards/remington.png',
  [CARD_TYPES.REV_CARABINE]: '/images/cards/rev_carabine.png',
  [CARD_TYPES.WINCHESTER]: '/images/cards/winchester.png',
  // Dodge City cards
  [CARD_TYPES.BINOCULAR]: '/src/assets/cards/dodge_city/binocular.png',
  [CARD_TYPES.HIDEOUT]: '/src/assets/cards/dodge_city/hideout.png',
  [CARD_TYPES.BRAWL]: '/src/assets/cards/dodge_city/brawl.png',
  [CARD_TYPES.DODGE]: '/src/assets/cards/dodge_city/dodge.png',
  [CARD_TYPES.PUNCH]: '/src/assets/cards/dodge_city/punch.png',
  [CARD_TYPES.RAG_TIME]: '/src/assets/cards/dodge_city/rag_time.png',
  [CARD_TYPES.SPRINGFIELD]: '/src/assets/cards/dodge_city/springfield.png',
  [CARD_TYPES.TEQUILA]: '/src/assets/cards/dodge_city/tequila.png',
  [CARD_TYPES.WHISKY]: '/src/assets/cards/dodge_city/whisky.png',
  [CARD_TYPES.BIBLE]: '/src/assets/cards/dodge_city/bible.png',
  [CARD_TYPES.BUFFALO_RIFLE]: '/src/assets/cards/dodge_city/buffalo_rifle.png',
  [CARD_TYPES.CAN_CAN]: '/src/assets/cards/dodge_city/can_can.png',
  [CARD_TYPES.CANTEEN]: '/src/assets/cards/dodge_city/canteen.png',
  [CARD_TYPES.CONESTOGA]: '/src/assets/cards/dodge_city/conestoga.png',
  [CARD_TYPES.DERRINGER]: '/src/assets/cards/dodge_city/derringer.png',
  [CARD_TYPES.HOWITZER]: '/src/assets/cards/dodge_city/howitzer.png',
  [CARD_TYPES.IRON_PLATE]: '/src/assets/cards/dodge_city/iron_plate.png',
  [CARD_TYPES.KNIFE]: '/src/assets/cards/dodge_city/knife.png',
  [CARD_TYPES.PEPPERBOX]: '/src/assets/cards/dodge_city/pepperbox.png',
  [CARD_TYPES.PONY_EXPRESS]: '/src/assets/cards/dodge_city/pony_express.png',
  [CARD_TYPES.SOMBRERO]: '/src/assets/cards/dodge_city/sombrero.png',
  [CARD_TYPES.TEN_GALLON_HAT]: '/src/assets/cards/dodge_city/ten_gallon_hat.png',
}

// Local character images mapping
const CHARACTER_IMAGES = {
  'Bart Cassidy': '/images/characters/bart_cassidy.png',
  'Black Jack': '/images/characters/black_jack.png',
  'Calamity Janet': '/images/characters/calamity_janet.png',
  'El Gringo': '/images/characters/el_gringo.png',
  'Jesse Jones': '/images/characters/jesse_jones.png',
  'Jourdonnais': '/images/characters/jourdonnais.png',
  'Kit Carlson': '/images/characters/kit_carlson.png',
  'Lucky Duke': '/images/characters/lucky_duke.png',
  'Paul Regret': '/images/characters/paul_regret.png',
  'Pedro Ramirez': '/images/characters/pedro_ramirez.png',
  'Rose Doolan': '/images/characters/rose_doolan.png',
  'Sid Ketchum': '/images/characters/sid_ketchum.png',
  'Slab the Killer': '/images/characters/slab_the_killer.png',
  'Suzy Lafayette': '/images/characters/suzy_lafayette.png',
  'Vulture Sam': '/images/characters/vulture_sam.png',
  'Willy the Kid': '/images/characters/willy_the_kid.png',
  // Dodge City characters
  'Apache Kid': '/src/assets/characters/dodge_city/apache_kid.png',
  'Belle Star': '/src/assets/characters/dodge_city/belle_star.png',
  'Bill Noface': '/src/assets/characters/dodge_city/bill_noface.png',
  'Chuck Wengam': '/src/assets/characters/dodge_city/chuck_wengam.png',
  'Doc Holyday': '/src/assets/characters/dodge_city/doc_holyday.png',
  'Elena Fuente': '/src/assets/characters/dodge_city/elena_fuente.png',
  'Greg Digger': '/src/assets/characters/dodge_city/greg_digger.png',
  'Herb Hunter': '/src/assets/characters/dodge_city/herb_hunter.png',
  'José Delgado': '/src/assets/characters/dodge_city/jose_delgado.png',
  'Molly Stark': '/src/assets/characters/dodge_city/molly_stark.png',
  'Pat Brennan': '/src/assets/characters/dodge_city/pat_brennan.png',
  'Pixie Pete': '/src/assets/characters/dodge_city/pixie_pete.png',
  'Sean Mallory': '/src/assets/characters/dodge_city/sean_mallory.png',
  'Tequila Joe': '/src/assets/characters/dodge_city/tequila_joe.png',
  'Vera Custer': '/src/assets/characters/dodge_city/vera_custer.png',
}

// Local card back image
const CARD_BACK_IMAGE = '/images/box/card_back.png';

// Roles
const ROLES = {
  SHERIFF: 'SHERIFF',
  DEPUTY: 'DEPUTY',
  OUTLAW: 'OUTLAW',
  RENEGADE: 'RENEGADE',
}

// Local role images mapping
const ROLE_IMAGES = {
  [ROLES.SHERIFF]: '/images/roles/sheriff.png',
  [ROLES.DEPUTY]: '/images/roles/deputy.png',
  [ROLES.OUTLAW]: '/images/roles/outlaw.png',
  [ROLES.RENEGADE]: '/images/roles/renegade.png',
}

// Characters with life points and abilities
const CHARACTERS = [
  {
    name: 'Bart Cassidy',
    life: 4,
    ability: 'Draws a card when he loses a life point',
    abilityType: 'ON_DAMAGE_TAKEN'
  },
  {
    name: 'Black Jack',
    life: 4,
    ability: 'Shows second card when drawing; draws again if heart/diamond',
    abilityType: 'ON_DRAW'
  },
  {
    name: 'Calamity Janet',
    life: 4,
    ability: 'Can play BANG! as Missed! and vice versa',
    abilityType: 'CARD_SUBSTITUTION'
  },
  {
    name: 'El Gringo',
    life: 3,
    ability: 'Draws from attacker when hit',
    abilityType: 'ON_DAMAGE_TAKEN'
  },
  {
    name: 'Jesse Jones',
    life: 4,
    ability: 'Can draw first card from another player',
    abilityType: 'DRAW_CHOICE'
  },
  {
    name: 'Jourdonnais',
    life: 4,
    ability: 'Has built-in Barrel effect',
    abilityType: 'BUILT_IN_BARREL'
  },
  {
    name: 'Kit Carlson',
    life: 4,
    ability: 'Sees top 3 cards when drawing, chooses 2',
    abilityType: 'DRAW_CHOICE'
  },
  {
    name: 'Lucky Duke',
    life: 4,
    ability: 'Flips 2 cards for checks, chooses one',
    abilityType: 'LUCKY_DRAW'
  },
  {
    name: 'Paul Regret',
    life: 3,
    ability: 'Has built-in Mustang effect',
    abilityType: 'BUILT_IN_MUSTANG'
  },
  {
    name: 'Pedro Ramirez',
    life: 4,
    ability: 'Can draw first card from discard pile',
    abilityType: 'DRAW_CHOICE'
  },
  {
    name: 'Rose Doolan',
    life: 4,
    ability: 'Has built-in Scope effect',
    abilityType: 'BUILT_IN_SCOPE'
  },
  {
    name: 'Sid Ketchum',
    life: 4,
    ability: 'Can discard 2 cards to regain 1 life point',
    abilityType: 'ACTIVE_ABILITY'
  },
  {
    name: 'Slab the Killer',
    life: 4,
    ability: 'Players need 2 Missed! to avoid his BANG!',
    abilityType: 'ATTACK_MODIFIER'
  },
  {
    name: 'Suzy Lafayette',
    life: 4,
    ability: 'Draws a card when she has no cards in hand',
    abilityType: 'AUTO_DRAW'
  },
  {
    name: 'Vulture Sam',
    life: 4,
    ability: 'Takes cards of eliminated players',
    abilityType: 'ON_ELIMINATION'
  },
  {
    name: 'Willy the Kid',
    life: 4,
    ability: 'Can play any number of BANG! cards',
    abilityType: 'UNLIMITED_BANG'
  },
  // Dodge City characters
  {
    name: 'Apache Kid',
    life: 3,
    ability: 'Unaffected by Diamond cards played by other players (except during Duels)',
    abilityType: 'DIAMOND_IMMUNITY'
  },
  {
    name: 'Belle Star',
    life: 4,
    ability: 'During her turn, no card in front of any other player has any effect',
    abilityType: 'DISABLE_EQUIPMENT'
  },
  {
    name: 'Bill Noface',
    life: 4,
    ability: 'Draws 1 card plus 1 for each injury (lost life point) during phase 1',
    abilityType: 'INJURY_DRAW'
  },
  {
    name: 'Chuck Wengam',
    life: 4,
    ability: 'Can lose 1 life to draw 2 cards (multiple times per turn, not last life)',
    abilityType: 'LIFE_FOR_CARDS'
  },
  {
    name: 'Doc Holyday',
    life: 4,
    ability: 'Once per turn, discard 2 cards for BANG! effect (doesn\'t count toward limit)',
    abilityType: 'DISCARD_FOR_BANG'
  },
  {
    name: 'Elena Fuente',
    life: 3,
    ability: 'Can use any card in her hand as a Missed!',
    abilityType: 'ANY_AS_MISSED'
  },
  {
    name: 'Greg Digger',
    life: 4,
    ability: 'Regains 2 life when another character is eliminated',
    abilityType: 'HEAL_ON_ELIMINATION'
  },
  {
    name: 'Herb Hunter',
    life: 4,
    ability: 'Draws 2 extra cards when another character is eliminated',
    abilityType: 'DRAW_ON_ELIMINATION'
  },
  {
    name: 'José Delgado',
    life: 4,
    ability: 'Can discard a blue card to draw 2 cards (twice per turn)',
    abilityType: 'BLUE_FOR_CARDS'
  },
  {
    name: 'Molly Stark',
    life: 4,
    ability: 'Draws 1 card when voluntarily discarding Missed!/Beer/BANG! on others\' turns',
    abilityType: 'VOLUNTARY_DISCARD_DRAW'
  },
  {
    name: 'Pat Brennan',
    life: 4,
    ability: 'Can draw 1 card from in play instead of 2 from deck during phase 1',
    abilityType: 'STEAL_OR_DRAW'
  },
  {
    name: 'Pixie Pete',
    life: 3,
    ability: 'Draws 3 cards instead of 2 during phase 1',
    abilityType: 'ENHANCED_DRAW'
  },
  {
    name: 'Sean Mallory',
    life: 3,
    ability: 'Can hold up to 10 cards in hand during phase 3',
    abilityType: 'EXTENDED_HAND_LIMIT'
  },
  {
    name: 'Tequila Joe',
    life: 4,
    ability: 'Regains 2 life from Beer instead of 1 (only 1 from other healing cards)',
    abilityType: 'ENHANCED_BEER'
  },
  {
    name: 'Vera Custer',
    life: 3,
    ability: 'At turn start, copies another character\'s ability until next turn',
    abilityType: 'COPY_ABILITY'
  },
]

function App() {
  const [gameState, setGameState] = useState('setup'); // setup, playing, ended
  const [gameResult, setGameResult] = useState(null); // 'won', 'lost', or null
  const [players, setPlayers] = useState([]);
  const [currentPlayerIndex, setCurrentPlayerIndex] = useState(0);
  const [deck, setDeck] = useState([]);
  const [discardPile, setDiscardPile] = useState([]);
  const [numPlayers, setNumPlayers] = useState(4);
  const [message, setMessage] = useState('Welcome to BANG!');
  const [messageTimeout, setMessageTimeout] = useState(null);
  // eslint-disable-next-line no-unused-vars
  const [visualEffects, setVisualEffects] = useState({});
  const [cardAnimations, setCardAnimations] = useState({});
  const [movingCards, setMovingCards] = useState([]);

  // Enhanced message function with persistence for important messages
  const setMessageWithPersistence = (newMessage, isPersistent = false, duration = 3000) => {
    // Clear any existing timeout
    if (messageTimeout) {
      clearTimeout(messageTimeout);
      setMessageTimeout(null);
    }

    setMessage(newMessage);

    // For persistent messages (like errors/warnings), keep them visible longer
    if (isPersistent) {
      const timeout = setTimeout(() => {
        setMessage('');
        setMessageTimeout(null);
      }, duration);
      setMessageTimeout(timeout);
    }
  };

  // Start turn timer
  const startTurnTimer = useCallback(() => {
    setTurnTimeLeft(120); // Reset to 2 minutes
    setTurnTimerActive(true);
  }, []);

  // Stop turn timer
  const stopTurnTimer = useCallback(() => {
    setTurnTimerActive(false);
    setTurnTimeLeft(120);
  }, []);

  // Format time for display (MM:SS)
  const formatTime = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };



  // Sound system state
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [audioContext, setAudioContext] = useState(null);

  // Sound system functions
  const initAudioContext = useCallback(() => {
    if (!audioContext && soundEnabled) {
      const ctx = new (window.AudioContext || window.webkitAudioContext)();
      setAudioContext(ctx);
      return ctx;
    }
    return audioContext;
  }, [audioContext, soundEnabled]);

  const playSound = useCallback((type) => {
    if (!soundEnabled) return;

    // Special handling for dominating sound - use audio file
    if (type === 'dominating') {
      try {
        const audio = new Audio('/src//assets/audio/Dominating.mp3');
        audio.volume = 0.7; // Adjust volume as needed
        audio.play().catch(error => {
          console.log('Could not play dominating sound:', error);
          // Fallback to generated killingSpree sound if file fails
          playSound('killingSpree');
        });
        return;
      } catch (error) {
        console.log('Error creating dominating audio:', error);
        // Fallback to generated sound
        playSound('killingSpree');
        return;
      }
    }

    // Special handling for unstoppable sound - use audio file
    if (type === 'unstoppable') {
      try {
        const audio = new Audio('/src//assets/audio//Unstoppable.mp3');
        audio.volume = 0.8; // Slightly louder for victory sound
        audio.play().catch(error => {
          console.log('Could not play unstoppable sound:', error);
          // Fallback to generated victory sound if file fails
          playSound('victory');
        });
        return;
      } catch (error) {
        console.log('Error creating unstoppable audio:', error);
        // Fallback to generated sound
        playSound('victory');
        return;
      }
    }

    // Special handling for godlike sound - use audio file
    if (type === 'godlike') {
      try {
        const audio = new Audio('/src/assets/audio/Godlike.mp3');
        audio.volume = 0.7; // Moderate volume for damage sound
        audio.play().catch(error => {
          console.log('Could not play godlike sound:', error);
          // Fallback to generated damage sound if file fails
          playSound('damage');
        });
        return;
      } catch (error) {
        console.log('Error creating godlike audio:', error);
        // Fallback to generated sound
        playSound('damage');
        return;
      }
    }

    const ctx = initAudioContext();
    if (!ctx) return;

    try {
      const oscillator = ctx.createOscillator();
      const gainNode = ctx.createGain();

      oscillator.connect(gainNode);
      gainNode.connect(ctx.destination);

      switch (type) {
        case 'turnStart':
          // Bell sound - multiple frequencies for rich tone
          oscillator.frequency.setValueAtTime(800, ctx.currentTime);
          oscillator.frequency.setValueAtTime(600, ctx.currentTime + 0.1);
          gainNode.gain.setValueAtTime(0.3, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.8);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.8);
          break;

        case 'damage':
          // Sharp impact sound
          oscillator.frequency.setValueAtTime(200, ctx.currentTime);
          oscillator.frequency.exponentialRampToValueAtTime(100, ctx.currentTime + 0.3);
          gainNode.gain.setValueAtTime(0.4, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.3);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.3);
          break;

        case 'cardPlay':
          // Card flip sound
          oscillator.frequency.setValueAtTime(400, ctx.currentTime);
          oscillator.frequency.setValueAtTime(300, ctx.currentTime + 0.05);
          gainNode.gain.setValueAtTime(0.2, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.15);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.15);
          break;

        case 'cardDraw':
          // Soft card draw sound
          oscillator.frequency.setValueAtTime(500, ctx.currentTime);
          oscillator.frequency.setValueAtTime(450, ctx.currentTime + 0.1);
          gainNode.gain.setValueAtTime(0.15, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.2);
          break;

        case 'elimination':
          // Dramatic elimination sound
          oscillator.frequency.setValueAtTime(150, ctx.currentTime);
          oscillator.frequency.exponentialRampToValueAtTime(50, ctx.currentTime + 1.0);
          gainNode.gain.setValueAtTime(0.5, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 1.0);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 1.0);
          break;

        case 'defense':
          // Shield/block sound
          oscillator.frequency.setValueAtTime(600, ctx.currentTime);
          oscillator.frequency.setValueAtTime(800, ctx.currentTime + 0.1);
          gainNode.gain.setValueAtTime(0.25, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.4);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.4);
          break;

        case 'heal':
          // Positive healing sound
          oscillator.frequency.setValueAtTime(400, ctx.currentTime);
          oscillator.frequency.setValueAtTime(600, ctx.currentTime + 0.2);
          oscillator.frequency.setValueAtTime(800, ctx.currentTime + 0.4);
          gainNode.gain.setValueAtTime(0.2, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.6);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.6);
          break;

        case 'killingSpree': {
          // Epic elimination sound - dramatic sequence like "KILLING SPREE!"
          // First impact - deep bass hit
          oscillator.type = 'sawtooth';
          oscillator.frequency.setValueAtTime(80, ctx.currentTime);
          oscillator.frequency.exponentialRampToValueAtTime(40, ctx.currentTime + 0.3);
          gainNode.gain.setValueAtTime(0.6, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.1, ctx.currentTime + 0.3);
          oscillator.start(ctx.currentTime);

          // Create additional oscillators for layered epic effect
          const osc2 = ctx.createOscillator();
          const gain2 = ctx.createGain();
          osc2.connect(gain2);
          gain2.connect(ctx.destination);
          osc2.type = 'sawtooth';
          osc2.frequency.setValueAtTime(160, ctx.currentTime + 0.1);
          osc2.frequency.exponentialRampToValueAtTime(80, ctx.currentTime + 0.4);
          gain2.gain.setValueAtTime(0.4, ctx.currentTime + 0.1);
          gain2.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.4);
          osc2.start(ctx.currentTime + 0.1);
          osc2.stop(ctx.currentTime + 0.4);

          // Epic rise - triumphant sound
          const osc3 = ctx.createOscillator();
          const gain3 = ctx.createGain();
          osc3.connect(gain3);
          gain3.connect(ctx.destination);
          osc3.type = 'triangle';
          osc3.frequency.setValueAtTime(200, ctx.currentTime + 0.5);
          osc3.frequency.exponentialRampToValueAtTime(400, ctx.currentTime + 1.2);
          gain3.gain.setValueAtTime(0.5, ctx.currentTime + 0.5);
          gain3.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 1.2);
          osc3.start(ctx.currentTime + 0.5);
          osc3.stop(ctx.currentTime + 1.2);

          // Final dramatic chord
          const osc4 = ctx.createOscillator();
          const gain4 = ctx.createGain();
          osc4.connect(gain4);
          gain4.connect(ctx.destination);
          osc4.type = 'square';
          osc4.frequency.setValueAtTime(300, ctx.currentTime + 1.0);
          osc4.frequency.setValueAtTime(350, ctx.currentTime + 1.5);
          gain4.gain.setValueAtTime(0.3, ctx.currentTime + 1.0);
          gain4.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 2.0);
          osc4.start(ctx.currentTime + 1.0);
          osc4.stop(ctx.currentTime + 2.0);

          oscillator.stop(ctx.currentTime + 0.3);
          break;
        }

        case 'victory': {
          // Epic victory sound - triumphant fanfare
          oscillator.type = 'triangle';
          oscillator.frequency.setValueAtTime(440, ctx.currentTime); // A4
          oscillator.frequency.setValueAtTime(554, ctx.currentTime + 0.3); // C#5
          oscillator.frequency.setValueAtTime(659, ctx.currentTime + 0.6); // E5
          oscillator.frequency.setValueAtTime(880, ctx.currentTime + 0.9); // A5
          gainNode.gain.setValueAtTime(0.5, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 2.0);
          oscillator.start(ctx.currentTime);

          // Create harmony oscillators for rich victory sound
          const victoryOsc2 = ctx.createOscillator();
          const victoryGain2 = ctx.createGain();
          victoryOsc2.connect(victoryGain2);
          victoryGain2.connect(ctx.destination);
          victoryOsc2.type = 'triangle';
          victoryOsc2.frequency.setValueAtTime(330, ctx.currentTime); // E4
          victoryOsc2.frequency.setValueAtTime(415, ctx.currentTime + 0.3); // G#4
          victoryOsc2.frequency.setValueAtTime(494, ctx.currentTime + 0.6); // B4
          victoryOsc2.frequency.setValueAtTime(659, ctx.currentTime + 0.9); // E5
          victoryGain2.gain.setValueAtTime(0.3, ctx.currentTime);
          victoryGain2.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 2.0);
          victoryOsc2.start(ctx.currentTime);
          victoryOsc2.stop(ctx.currentTime + 2.0);

          oscillator.stop(ctx.currentTime + 2.0);
          break;
        }

        case 'warning':
          // Timer warning sound
          oscillator.frequency.setValueAtTime(1000, ctx.currentTime);
          oscillator.frequency.setValueAtTime(800, ctx.currentTime + 0.1);
          gainNode.gain.setValueAtTime(0.3, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.2);
          break;

        default:
          // Default beep
          oscillator.frequency.setValueAtTime(440, ctx.currentTime);
          gainNode.gain.setValueAtTime(0.2, ctx.currentTime);
          gainNode.gain.exponentialRampToValueAtTime(0.01, ctx.currentTime + 0.2);
          oscillator.start(ctx.currentTime);
          oscillator.stop(ctx.currentTime + 0.2);
      }
    } catch (error) {
      console.log('Audio playback failed:', error);
    }
  }, [soundEnabled, initAudioContext]);

  // Card animation functions
  const animateCardToTarget = (card, fromPlayerIndex, toPlayerIndex, onComplete) => {
    const animId = `${Date.now()}-${Math.random()}-${cardAnimationId + 1}`;
    setCardAnimationId(prev => prev + 1);

    const animationData = {
      id: animId,
      card: card,
      fromPlayerIndex: fromPlayerIndex,
      toPlayerIndex: toPlayerIndex,
      phase: 'toTarget', // 'toTarget' or 'toDiscard'
      progress: 0,
      startTime: Date.now()
    };

    setAnimatingCards(prev => [...prev, animationData]);

    // Animate to target first
    const animateToTarget = () => {
      const elapsed = Date.now() - animationData.startTime;
      const duration = 800; // 800ms to reach target
      const progress = Math.min(elapsed / duration, 1);

      setAnimatingCards(prev =>
        prev.map(anim =>
          anim.id === animId
            ? { ...anim, progress: progress }
            : anim
        )
      );

      if (progress < 1) {
        requestAnimationFrame(animateToTarget);
      } else {
        // Start second phase - animate to discard pile
        setTimeout(() => {
          animationData.phase = 'toDiscard';
          animationData.startTime = Date.now();
          animationData.progress = 0;

          const animateToDiscard = () => {
            const elapsed = Date.now() - animationData.startTime;
            const duration = 600; // 600ms to reach discard pile
            const progress = Math.min(elapsed / duration, 1);

            setAnimatingCards(prev =>
              prev.map(anim =>
                anim.id === animId
                  ? { ...anim, progress: progress }
                  : anim
              )
            );

            if (progress < 1) {
              requestAnimationFrame(animateToDiscard);
            } else {
              // Animation complete, remove from animating cards
              setAnimatingCards(prev => prev.filter(anim => anim.id !== animId));
              if (onComplete) onComplete();
            }
          };

          requestAnimationFrame(animateToDiscard);
        }, 300); // Brief pause at target
      }
    };

    requestAnimationFrame(animateToTarget);
  };

  // eslint-disable-next-line no-unused-vars
  const animateCardToDiscard = (card, fromPlayerIndex, onComplete) => {
    const animId = `${Date.now()}-${Math.random()}-${cardAnimationId + 1}`;
    setCardAnimationId(prev => prev + 1);

    const animationData = {
      id: animId,
      card: card,
      fromPlayerIndex: fromPlayerIndex,
      toPlayerIndex: -1, // -1 indicates discard pile
      phase: 'toDiscard',
      progress: 0,
      startTime: Date.now()
    };

    setAnimatingCards(prev => [...prev, animationData]);

    const animate = () => {
      const elapsed = Date.now() - animationData.startTime;
      const duration = 600; // 600ms to reach discard pile
      const progress = Math.min(elapsed / duration, 1);

      setAnimatingCards(prev =>
        prev.map(anim =>
          anim.id === animId
            ? { ...anim, progress: progress }
            : anim
        )
      );

      if (progress < 1) {
        requestAnimationFrame(animate);
      } else {
        // Animation complete
        setAnimatingCards(prev => prev.filter(anim => anim.id !== animId));
        if (onComplete) onComplete();
      }
    };

    requestAnimationFrame(animate);
  };

  // Visual effects system
  const triggerVisualEffect = useCallback((playerIndex, effectType, duration = 2000) => {
    const effectId = `${playerIndex}-${effectType}-${Date.now()}`;

    setVisualEffects(prev => ({
      ...prev,
      [effectId]: { playerIndex, effectType, timestamp: Date.now() }
    }));

    // Remove effect after duration
    setTimeout(() => {
      setVisualEffects(prev => {
        const newEffects = { ...prev };
        delete newEffects[effectId];
        return newEffects;
      });
    }, duration);
  }, [setVisualEffects]);

  // Create floating damage/heal numbers
  const createFloatingNumber = (playerIndex, value, type = 'damage') => {
    const playerElement = document.querySelector(`[data-player-index="${playerIndex}"]`);
    if (!playerElement) return;

    const floatingElement = document.createElement('div');
    floatingElement.className = type === 'damage' ? 'floating-damage' : 'floating-heal';
    floatingElement.textContent = type === 'damage' ? `-${value}` : `+${value}`;

    playerElement.style.position = 'relative';
    playerElement.appendChild(floatingElement);

    // Remove element after animation
    setTimeout(() => {
      if (floatingElement.parentNode) {
        floatingElement.parentNode.removeChild(floatingElement);
      }
    }, 2000);
  };

  // Screen shake effect for major events
  const triggerScreenShake = () => {
    const gameBoard = document.querySelector('.game-board');
    if (gameBoard) {
      gameBoard.classList.add('screen-shake');
      setTimeout(() => {
        gameBoard.classList.remove('screen-shake');
      }, 800);
    }
  };
  const [targetingCard, setTargetingCard] = useState(null);
  const [showTargetSelection, setShowTargetSelection] = useState(false);
  // eslint-disable-next-line no-unused-vars
  const [hoveredCard, setHoveredCard] = useState(null);

  // Card preview/zoom functionality
  const [previewCard, setPreviewCard] = useState(null);
  const [showCardPreview, setShowCardPreview] = useState(false);

  // Touch and hold functionality for card preview
  const [touchTimer, setTouchTimer] = useState(null);
  const [isLongPressing, setIsLongPressing] = useState(false);
  const [longPressTimer, setLongPressTimer] = useState(null);
  const [pressedCard, setPressedCard] = useState(null);
  const [zoomedCard, setZoomedCard] = useState(null);

  // Player death and spectate mode
  const [showDeathModal, setShowDeathModal] = useState(false);
  const [isSpectating, setIsSpectating] = useState(false);

  // Character ability choice system
  const [showAbilityChoice, setShowAbilityChoice] = useState(false);
  const [abilityChoice, setAbilityChoice] = useState(null);
  const [pendingAttack, setPendingAttack] = useState(null);

  // Missed card choice system
  const [showMissedChoice, setShowMissedChoice] = useState(false);
  const [missedChoice, setMissedChoice] = useState(null);
  const [pendingMissedAttack, setPendingMissedAttack] = useState(null);

  // Game menu system
  const [showGameMenu, setShowGameMenu] = useState(false);



  // Card animation system
  const [animatingCards, setAnimatingCards] = useState([]);
  const [cardAnimationId, setCardAnimationId] = useState(0);

  // General Store selection system
  const [showGeneralStore, setShowGeneralStore] = useState(false);
  const [generalStoreCards, setGeneralStoreCards] = useState([]);
  const [generalStorePlayerOrder, setGeneralStorePlayerOrder] = useState([]);
  const [currentGeneralStorePlayer, setCurrentGeneralStorePlayer] = useState(0);

  // Turn phase management - BANG! has 3 phases: draw, play, discard
  const [turnPhase, setTurnPhase] = useState('draw'); // 'draw', 'play', 'discard'
  const [hasDrawnCards, setHasDrawnCards] = useState(false);
  const [isDrawingCards, setIsDrawingCards] = useState(false);
  const [turnId, setTurnId] = useState(0); // Unique identifier for each turn

  // Use ref to track which turn we've already processed drawing for
  const lastDrawnTurnRef = useRef(-1);

  // Bot management
  const [isProcessingBotTurn, setIsProcessingBotTurn] = useState(false);

  // Discard phase management
  const [showDiscardSelection, setShowDiscardSelection] = useState(false);
  const [cardsToDiscard, setCardsToDiscard] = useState(0);
  const [selectedDiscardCards, setSelectedDiscardCards] = useState([]);

  // BANG! card limit tracking
  const [bangCardsPlayedThisTurn, setBangCardsPlayedThisTurn] = useState(0);

  // Green-bordered card tracking (can't use same turn they're played)
  const [greenCardsPlayedThisTurn, setGreenCardsPlayedThisTurn] = useState(new Set());

  // Card selection for special effects (like Brawl additional discard)
  const [showCardSelection, setShowCardSelection] = useState(false);
  const [cardSelectionData, setCardSelectionData] = useState(null);

  // Tequila card selection system
  const [showTequilaCardSelection, setShowTequilaCardSelection] = useState(false);
  const [tequilaCardIndex, setTequilaCardIndex] = useState(null);
  const [tequilaTargetIndex, setTequilaTargetIndex] = useState(null);

  // Turn timer - 2 minutes per turn
  const [turnTimeLeft, setTurnTimeLeft] = useState(120); // 120 seconds = 2 minutes
  const [turnTimerActive, setTurnTimerActive] = useState(false);

  // Multiplayer setup
  const [numHumanPlayers, setNumHumanPlayers] = useState(1);
  const [humanPlayerNames, setHumanPlayerNames] = useState(['']);

  // Handle when time runs out
  const handleTimeUp = useCallback(() => {
    setTurnTimerActive(false);
    setMessage(`Time's up! ${players[currentPlayerIndex]?.name}'s turn is over.`);

    // Force discard if over hand limit
    const currentPlayer = players[currentPlayerIndex];
    if (currentPlayer && currentPlayer.hand.length > currentPlayer.health) {
      // Auto-discard random cards to meet hand limit
      const cardsToDiscard = currentPlayer.hand.length - currentPlayer.health;
      const updatedPlayers = [...players];
      const discardedCards = [];

      for (let i = 0; i < cardsToDiscard; i++) {
        const randomIndex = Math.floor(Math.random() * updatedPlayers[currentPlayerIndex].hand.length);
        const discardedCard = updatedPlayers[currentPlayerIndex].hand.splice(randomIndex, 1)[0];
        discardedCards.push(discardedCard);
      }

      setPlayers(updatedPlayers);
      setDiscardPile(prev => [...prev, ...discardedCards]);
      setMessage(`${currentPlayer.name} auto-discarded ${cardsToDiscard} cards due to time limit.`);
    }

    // Move to next player - use a flag to trigger endTurn
    setTurnPhase('timeUp');
  }, [players, currentPlayerIndex, setTurnTimerActive, setMessage, setDiscardPile, setPlayers, setTurnPhase]);

  // Handle timeUp phase - triggers endTurn after timeout
  useEffect(() => {
    if (turnPhase === 'timeUp') {
      setTimeout(() => {
        // Find the endTurn function and call it
        const nextPlayerIndex = (currentPlayerIndex + 1) % players.length;
        let nextPlayer = nextPlayerIndex;

        // Find next alive player
        for (let i = 0; i < players.length; i++) {
          if (players[nextPlayer].isAlive) break;
          nextPlayer = (nextPlayer + 1) % players.length;
        }

        setCurrentPlayerIndex(nextPlayer);
        setTurnPhase('draw');
        setHasDrawnCards(false);
        setBangCardsPlayedThisTurn(0);
        setMessage(`${players[nextPlayer].name}'s turn!`);
      }, 1500);
    }
  }, [turnPhase, currentPlayerIndex, players, setCurrentPlayerIndex, setTurnPhase, setHasDrawnCards, setBangCardsPlayedThisTurn, setMessage]);

  // Turn timer effect - runs continuously during active turns
  useEffect(() => {
    let interval = null;

    if (turnTimerActive && turnTimeLeft > 0 && gameState === 'playing') {
      interval = setInterval(() => {
        setTurnTimeLeft(prevTime => {
          if (prevTime <= 1) {
            // Time's up! Force end turn
            handleTimeUp();
            return 0;
          }
          return prevTime - 1;
        });
      }, 1000);
    } else if (!turnTimerActive || turnTimeLeft <= 0) {
      clearInterval(interval);
    }

    return () => clearInterval(interval);
  }, [turnTimerActive, gameState, turnTimeLeft, players, currentPlayerIndex, handleTimeUp]);





  // Initialize game
  const startGame = () => {
    if (numPlayers < 4 || numPlayers > 7) {
      setMessage('Please select 4-7 players');
      return;
    }

    // Create deck
    let newDeck = createDeck();

    // Assign roles
    const roles = assignRoles(numPlayers);

    // Create players with characters
    const shuffledCharacters = [...CHARACTERS].sort(() => Math.random() - 0.5);
    const newPlayers = [];

    for (let i = 0; i < numPlayers; i++) {
      const character = shuffledCharacters[i];
      const role = roles[i];

      // Sheriff gets +1 health point
      const maxHealth = role === ROLES.SHERIFF ? character.life + 1 : character.life;
      const drawResult = drawCards(newDeck, maxHealth);
      const initialCards = drawResult.drawnCards || [];

      // Remove dealt cards from deck
      newDeck = drawResult.updatedDeck;

      // Determine player name and type
      let name;
      let isBot;
      if (i < numHumanPlayers) {
        // Human player
        name = humanPlayerNames[i]?.trim() || `Player ${i + 1}`;
        isBot = false;
      } else {
        // Bot players
        const botNumber = i - numHumanPlayers + 1;
        name = `Bot ${botNumber}`;
        isBot = true;
      }

      newPlayers.push({
        id: i,
        character,
        role,
        health: maxHealth,
        maxHealth: maxHealth, // Store max health for reference
        hand: Array.isArray(initialCards) ? initialCards : [],
        inPlay: [],
        isAlive: true,
        isBot: isBot,
        name: name,
      });
    }
    
    // Find sheriff
    const sheriffIndex = newPlayers.findIndex(p => p.role === ROLES.SHERIFF);
    setCurrentPlayerIndex(sheriffIndex);
    
    setPlayers(newPlayers);
    setDeck(newDeck);
    setGameState('playing');

    // Initialize turn state
    setTurnPhase('draw');
    setHasDrawnCards(false);
    setBangCardsPlayedThisTurn(0); // Reset BANG! counter for new game

    setMessage(`Game started! ${newPlayers[sheriffIndex].name} is the Sheriff and goes first. Cards will be drawn automatically.`);
  };

  // Create deck with all cards including suits and values
  const createDeck = () => {
    // Define card data with suits and values (based on official Bang! deck)
    const cardData = [
      // BANG! cards
      { type: CARD_TYPES.BANG, suit: SUITS.SPADES, value: 'A' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '2' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '3' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '4' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '5' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '6' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '7' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '8' },
      { type: CARD_TYPES.BANG, suit: SUITS.CLUBS, value: '9' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '2' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '3' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '4' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '5' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '6' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '7' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '8' },
      { type: CARD_TYPES.BANG, suit: SUITS.DIAMONDS, value: '9' },
      { type: CARD_TYPES.BANG, suit: SUITS.HEARTS, value: '12' },
      { type: CARD_TYPES.BANG, suit: SUITS.HEARTS, value: '13' },
      { type: CARD_TYPES.BANG, suit: SUITS.HEARTS, value: 'A' },
      { type: CARD_TYPES.BANG, suit: SUITS.SPADES, value: 'K' },
      { type: CARD_TYPES.BANG, suit: SUITS.SPADES, value: 'Q' },
      { type: CARD_TYPES.BANG, suit: SUITS.SPADES, value: 'J' },
      { type: CARD_TYPES.BANG, suit: SUITS.SPADES, value: '10' },
      { type: CARD_TYPES.BANG, suit: SUITS.SPADES, value: '9' },

      // MISSED! cards
      { type: CARD_TYPES.MISSED, suit: SUITS.SPADES, value: '8' },
      { type: CARD_TYPES.MISSED, suit: SUITS.SPADES, value: '7' },
      { type: CARD_TYPES.MISSED, suit: SUITS.SPADES, value: '6' },
      { type: CARD_TYPES.MISSED, suit: SUITS.SPADES, value: '5' },
      { type: CARD_TYPES.MISSED, suit: SUITS.SPADES, value: '4' },
      { type: CARD_TYPES.MISSED, suit: SUITS.SPADES, value: '3' },
      { type: CARD_TYPES.MISSED, suit: SUITS.SPADES, value: '2' },
      { type: CARD_TYPES.MISSED, suit: SUITS.CLUBS, value: 'A' },
      { type: CARD_TYPES.MISSED, suit: SUITS.CLUBS, value: 'K' },
      { type: CARD_TYPES.MISSED, suit: SUITS.CLUBS, value: 'Q' },
      { type: CARD_TYPES.MISSED, suit: SUITS.CLUBS, value: 'J' },
      { type: CARD_TYPES.MISSED, suit: SUITS.CLUBS, value: '10' },

      // BEER cards
      { type: CARD_TYPES.BEER, suit: SUITS.HEARTS, value: '6' },
      { type: CARD_TYPES.BEER, suit: SUITS.HEARTS, value: '7' },
      { type: CARD_TYPES.BEER, suit: SUITS.HEARTS, value: '8' },
      { type: CARD_TYPES.BEER, suit: SUITS.HEARTS, value: '9' },
      { type: CARD_TYPES.BEER, suit: SUITS.HEARTS, value: '10' },
      { type: CARD_TYPES.BEER, suit: SUITS.HEARTS, value: 'J' },

      // Other action cards
      { type: CARD_TYPES.PANIC, suit: SUITS.HEARTS, value: '4' },
      { type: CARD_TYPES.PANIC, suit: SUITS.DIAMONDS, value: 'J' },
      { type: CARD_TYPES.PANIC, suit: SUITS.DIAMONDS, value: 'Q' },
      { type: CARD_TYPES.PANIC, suit: SUITS.DIAMONDS, value: 'A' },

      { type: CARD_TYPES.CAT_BALOU, suit: SUITS.DIAMONDS, value: 'K' },
      { type: CARD_TYPES.CAT_BALOU, suit: SUITS.DIAMONDS, value: '10' },
      { type: CARD_TYPES.CAT_BALOU, suit: SUITS.HEARTS, value: 'K' },
      { type: CARD_TYPES.CAT_BALOU, suit: SUITS.HEARTS, value: 'Q' },

      { type: CARD_TYPES.STAGECOACH, suit: SUITS.SPADES, value: '9' },
      { type: CARD_TYPES.STAGECOACH, suit: SUITS.SPADES, value: '9' },

      { type: CARD_TYPES.WELLS_FARGO, suit: SUITS.HEARTS, value: '3' },

      { type: CARD_TYPES.GATLING, suit: SUITS.HEARTS, value: '10' },

      { type: CARD_TYPES.INDIANS, suit: SUITS.DIAMONDS, value: 'K' },
      { type: CARD_TYPES.INDIANS, suit: SUITS.DIAMONDS, value: 'A' },

      { type: CARD_TYPES.DUEL, suit: SUITS.CLUBS, value: '8' },
      { type: CARD_TYPES.DUEL, suit: SUITS.DIAMONDS, value: 'J' },
      { type: CARD_TYPES.DUEL, suit: SUITS.SPADES, value: 'J' },

      { type: CARD_TYPES.GENERAL_STORE, suit: SUITS.CLUBS, value: '9' },
      { type: CARD_TYPES.GENERAL_STORE, suit: SUITS.SPADES, value: 'Q' },

      { type: CARD_TYPES.SALOON, suit: SUITS.HEARTS, value: '5' },

      // Equipment cards
      { type: CARD_TYPES.BARREL, suit: SUITS.SPADES, value: 'K' },
      { type: CARD_TYPES.BARREL, suit: SUITS.SPADES, value: 'Q' },

      { type: CARD_TYPES.SCOPE, suit: SUITS.SPADES, value: 'A' },

      { type: CARD_TYPES.MUSTANG, suit: SUITS.HEARTS, value: '8' },
      { type: CARD_TYPES.MUSTANG, suit: SUITS.HEARTS, value: '9' },

      { type: CARD_TYPES.JAIL, suit: SUITS.SPADES, value: '10' },
      { type: CARD_TYPES.JAIL, suit: SUITS.SPADES, value: 'J' },
      { type: CARD_TYPES.JAIL, suit: SUITS.HEARTS, value: '4' },

      { type: CARD_TYPES.DYNAMITE, suit: SUITS.HEARTS, value: '2' },

      // Weapons
      { type: CARD_TYPES.VOLCANIC, suit: SUITS.SPADES, value: '10' },
      { type: CARD_TYPES.VOLCANIC, suit: SUITS.CLUBS, value: '10' },

      { type: CARD_TYPES.SCHOFIELD, suit: SUITS.CLUBS, value: 'K' },
      { type: CARD_TYPES.SCHOFIELD, suit: SUITS.CLUBS, value: 'Q' },
      { type: CARD_TYPES.SCHOFIELD, suit: SUITS.SPADES, value: 'K' },

      { type: CARD_TYPES.REMINGTON, suit: SUITS.CLUBS, value: 'K' },

      { type: CARD_TYPES.REV_CARABINE, suit: SUITS.CLUBS, value: 'A' },

      { type: CARD_TYPES.WINCHESTER, suit: SUITS.SPADES, value: '8' },

      // Dodge City expansion cards
      // New action cards
      { type: CARD_TYPES.DODGE, suit: SUITS.CLUBS, value: 'A' },
      { type: CARD_TYPES.DODGE, suit: SUITS.SPADES, value: 'A' },
      { type: CARD_TYPES.PUNCH, suit: SUITS.HEARTS, value: 'A' },
      { type: CARD_TYPES.PUNCH, suit: SUITS.DIAMONDS, value: 'A' },
      { type: CARD_TYPES.BRAWL, suit: SUITS.CLUBS, value: 'J' },
      { type: CARD_TYPES.RAG_TIME, suit: SUITS.HEARTS, value: 'Q' },
      { type: CARD_TYPES.TEQUILA, suit: SUITS.HEARTS, value: '4' },
      { type: CARD_TYPES.WHISKY, suit: SUITS.HEARTS, value: '3' },

      // New equipment cards
      { type: CARD_TYPES.BINOCULAR, suit: SUITS.CLUBS, value: 'Q' },
      { type: CARD_TYPES.HIDEOUT, suit: SUITS.SPADES, value: 'Q' },
      { type: CARD_TYPES.BIBLE, suit: SUITS.HEARTS, value: 'K' },
      { type: CARD_TYPES.CAN_CAN, suit: SUITS.CLUBS, value: 'K' },
      { type: CARD_TYPES.CANTEEN, suit: SUITS.HEARTS, value: '8' },
      { type: CARD_TYPES.CONESTOGA, suit: SUITS.HEARTS, value: '9' },
      { type: CARD_TYPES.IRON_PLATE, suit: SUITS.SPADES, value: 'K' },
      { type: CARD_TYPES.PONY_EXPRESS, suit: SUITS.SPADES, value: 'J' },
      { type: CARD_TYPES.SOMBRERO, suit: SUITS.CLUBS, value: '9' },
      { type: CARD_TYPES.TEN_GALLON_HAT, suit: SUITS.HEARTS, value: '10' },

      // New weapons
      { type: CARD_TYPES.DERRINGER, suit: SUITS.CLUBS, value: '4' },
      { type: CARD_TYPES.PEPPERBOX, suit: SUITS.DIAMONDS, value: '8' },
      { type: CARD_TYPES.KNIFE, suit: SUITS.SPADES, value: '9' },
      { type: CARD_TYPES.SPRINGFIELD, suit: SUITS.CLUBS, value: '8' },
      { type: CARD_TYPES.BUFFALO_RIFLE, suit: SUITS.SPADES, value: 'A' },
      { type: CARD_TYPES.HOWITZER, suit: SUITS.HEARTS, value: '2' },
    ];

    // Add equipment type to cards
    cardData.forEach(card => {
      if ([CARD_TYPES.VOLCANIC, CARD_TYPES.SCHOFIELD, CARD_TYPES.REMINGTON,
           CARD_TYPES.REV_CARABINE, CARD_TYPES.WINCHESTER,
           // Dodge City weapons
           CARD_TYPES.PEPPERBOX, CARD_TYPES.KNIFE,
           CARD_TYPES.SPRINGFIELD, CARD_TYPES.BUFFALO_RIFLE].includes(card.type)) {
        card.equipmentType = EQUIPMENT_TYPES.WEAPON;
        card.range = WEAPON_RANGES[card.type];
      } else if ([CARD_TYPES.BARREL, CARD_TYPES.MUSTANG,
                  // Dodge City defensive equipment
                  CARD_TYPES.SOMBRERO].includes(card.type)) {
        card.equipmentType = EQUIPMENT_TYPES.DEFENSIVE;
      } else if ([CARD_TYPES.SCOPE, CARD_TYPES.JAIL, CARD_TYPES.DYNAMITE].includes(card.type)) {
        card.equipmentType = EQUIPMENT_TYPES.SPECIAL;
      } else if ([
                  // Dodge City green-bordered cards (can be activated by discarding)
                  CARD_TYPES.BINOCULAR, CARD_TYPES.HIDEOUT, CARD_TYPES.BIBLE,
                  CARD_TYPES.CAN_CAN, CARD_TYPES.CANTEEN, CARD_TYPES.CONESTOGA,
                  CARD_TYPES.PONY_EXPRESS, CARD_TYPES.DERRINGER, CARD_TYPES.TEN_GALLON_HAT, CARD_TYPES.HOWITZER].includes(card.type)) {
        card.equipmentType = EQUIPMENT_TYPES.GREEN;
      }
    });

    // Shuffle deck
    return cardData.sort(() => Math.random() - 0.5);
  };

  // Assign roles based on number of players
  const assignRoles = (numPlayers) => {
    const roles = [];
    
    // Always 1 Sheriff
    roles.push(ROLES.SHERIFF);
    
    // Always 2 Outlaws for 4 players, 3 for 5-6, 4 for 7
    const numOutlaws = numPlayers <= 4 ? 2 : numPlayers <= 6 ? 3 : 4;
    for (let i = 0; i < numOutlaws; i++) {
      roles.push(ROLES.OUTLAW);
    }
    
    // 1 Renegade for 4-6 players, 2 for 7
    const numRenegades = numPlayers <= 6 ? 1 : 2;
    for (let i = 0; i < numRenegades; i++) {
      roles.push(ROLES.RENEGADE);
    }
    
    // Deputies: 0 for 4, 1 for 5, 2 for 6-7
    const numDeputies = numPlayers <= 4 ? 0 : numPlayers === 5 ? 1 : 2;
    for (let i = 0; i < numDeputies; i++) {
      roles.push(ROLES.DEPUTY);
    }
    
    // Shuffle roles
    return roles.sort(() => Math.random() - 0.5);
  };

  // Draw cards from deck with automatic reshuffling
  const drawCards = useCallback((deckToDrawFrom, count) => {
    let currentDeck = [...deckToDrawFrom];
    let currentDiscardPile = [...discardPile];
    const drawnCards = [];

    for (let i = 0; i < count; i++) {
      // If deck is empty, reshuffle
      if (currentDeck.length === 0) {
        if (currentDiscardPile.length === 0) {
          console.warn('Cannot reshuffle: both deck and discard pile are empty!');
          break;
        }

        // Keep the top card of discard pile (most recently played)
        const topCard = currentDiscardPile[currentDiscardPile.length - 1];
        const cardsToShuffle = currentDiscardPile.slice(0, -1);

        // Shuffle the cards randomly
        const shuffledCards = [...cardsToShuffle];
        for (let j = shuffledCards.length - 1; j > 0; j--) {
          const k = Math.floor(Math.random() * (j + 1));
          [shuffledCards[j], shuffledCards[k]] = [shuffledCards[k], shuffledCards[j]];
        }

        currentDeck = shuffledCards;
        currentDiscardPile = [topCard];

        // Update the actual state
        setDeck(currentDeck);
        setDiscardPile(currentDiscardPile);

        setMessage('Deck reshuffled from discard pile!');
      }

      if (currentDeck.length > 0) {
        drawnCards.push(currentDeck[0]);
        currentDeck = currentDeck.slice(1);
      }
    }

    return { drawnCards, updatedDeck: currentDeck, updatedDiscardPile: currentDiscardPile };
  }, [discardPile, setDeck, setDiscardPile, setMessage]);

  // Special draw phase for characters with draw abilities
  // eslint-disable-next-line no-unused-vars
  const handleSpecialDraw = (player) => {
    switch (player.character.abilityType) {
      case 'ON_DRAW':
        if (player.character.name === 'Black Jack') {
          // Black Jack shows second card when drawing; draws again if heart/diamond
          if (deck.length >= 2) {
            const firstCard = deck[0];
            const secondCard = deck[1];

            setMessage(`${player.name} (Black Jack) draws ${firstCard.type} and shows ${secondCard.suit} ${secondCard.value}`);

            let cardsDrawn = [firstCard, secondCard];
            let newDeck = deck.slice(2);

            // If second card is red (heart/diamond), draw another card
            if (secondCard.suit === SUITS.HEARTS || secondCard.suit === SUITS.DIAMONDS) {
              if (newDeck.length > 0) {
                const bonusCard = newDeck[0];
                cardsDrawn.push(bonusCard);
                newDeck = newDeck.slice(1);
                setMessage(`${player.name} (Black Jack) draws bonus card for red suit!`);
              }
            }

            setDeck(newDeck);
            return cardsDrawn;
          }
        }
        break;

      case 'DRAW_CHOICE':
        if (player.character.name === 'Jesse Jones') {
          // Jesse Jones can draw first card from another player
          // For now, implement as normal draw (would need UI for choice)
          return drawCards(deck, 2);
        } else if (player.character.name === 'Kit Carlson') {
          // Kit Carlson sees top 3 cards when drawing, chooses 2
          if (deck.length >= 3) {
            const topThree = deck.slice(0, 3);
            // For AI/auto-play, choose first 2 cards
            const chosen = topThree.slice(0, 2);
            const discarded = topThree[2];

            setDeck([...deck.slice(3), discarded]); // Put discarded card at bottom
            setMessage(`${player.name} (Kit Carlson) chooses 2 cards from top 3`);
            return chosen;
          }
        } else if (player.character.name === 'Pedro Ramirez') {
          // Pedro Ramirez can draw first card from discard pile
          if (discardPile.length > 0) {
            const topDiscard = discardPile[discardPile.length - 1];
            const secondCard = deck.length > 0 ? deck[0] : null;

            setDiscardPile(discardPile.slice(0, -1)); // Remove top discard
            if (secondCard) {
              setDeck(deck.slice(1)); // Remove second card from deck
              setMessage(`${player.name} (Pedro Ramirez) draws from discard pile and deck`);
              return [topDiscard, secondCard];
            } else {
              setMessage(`${player.name} (Pedro Ramirez) draws from discard pile only`);
              return [topDiscard];
            }
          }
        }
        break;

      default:
        return drawCards(deck, 2);
    }

    // Fallback to normal draw
    return drawCards(deck, 2);
  };

  // Calculate distance between players
  const calculateDistance = (fromIndex, toIndex, players) => {
    const totalPlayers = players.filter(p => p.isAlive).length;
    const alivePlayers = players.map((p, i) => ({ ...p, originalIndex: i })).filter(p => p.isAlive);

    const fromPos = alivePlayers.findIndex(p => p.originalIndex === fromIndex);
    const toPos = alivePlayers.findIndex(p => p.originalIndex === toIndex);

    if (fromPos === -1 || toPos === -1) return Infinity;

    const clockwise = (toPos - fromPos + totalPlayers) % totalPlayers;
    const counterclockwise = (fromPos - toPos + totalPlayers) % totalPlayers;

    let baseDistance = Math.min(clockwise, counterclockwise);

    // Apply Paul Regret's ability - all other players see him at +1 distance
    const targetPlayer = players[toIndex];
    if (targetPlayer && targetPlayer.character.name === 'Paul Regret') {
      baseDistance += 1;
    }

    // Apply Rose Doolan's ability - she sees all other players at -1 distance
    const attackerPlayer = players[fromIndex];
    if (attackerPlayer && attackerPlayer.character.name === 'Rose Doolan') {
      baseDistance = Math.max(1, baseDistance - 1); // Minimum distance is 1
    }

    return baseDistance;
  };

  // Calculate weapon range for player
  const getPlayerRange = (player) => {
    const weapon = player.inPlay.find(card => card.equipmentType === EQUIPMENT_TYPES.WEAPON);
    const baseRange = weapon ? weapon.range : 1;

    // Rose Doolan has built-in Scope effect
    const scopeBonus = (player.character.abilityType === 'BUILT_IN_SCOPE' ||
                       player.inPlay.some(card => card.type === CARD_TYPES.SCOPE)) ? 1 : 0;

    return baseRange + scopeBonus;
  };

  // Calculate defensive distance for player
  const getPlayerDefensiveDistance = (player) => {
    // Paul Regret has built-in Mustang effect
    const mustangBonus = (player.character.abilityType === 'BUILT_IN_MUSTANG' ||
                         player.inPlay.some(card => card.type === CARD_TYPES.MUSTANG)) ? 1 : 0;

    return 1 + mustangBonus;
  };

  // Check if target is in range
  const isInRange = (attackerIndex, targetIndex, players) => {
    const attacker = players[attackerIndex];
    const target = players[targetIndex];

    if (!attacker.isAlive || !target.isAlive) return false;

    const distance = calculateDistance(attackerIndex, targetIndex, players);
    const attackRange = getPlayerRange(attacker);
    const defensiveDistance = getPlayerDefensiveDistance(target);

    return distance <= attackRange && distance >= defensiveDistance;
  };

  // Perform barrel check (flip card to see if it's a heart)
  const performBarrelCheck = (player) => {
    if (deck.length === 0) return false;

    const flippedCard = deck[0];
    setDeck(deck.slice(1));
    setDiscardPile([...discardPile, flippedCard]);

    // Lucky Duke flips 2 cards and chooses one
    if (player.character.name === 'Lucky Duke') {
      if (deck.length > 0) {
        const secondCard = deck[0];
        setDeck(deck.slice(1));
        setDiscardPile([...discardPile, secondCard]);

        // Choose the better card (heart wins)
        const chosenCard = flippedCard.suit === SUITS.HEARTS ? flippedCard :
                          secondCard.suit === SUITS.HEARTS ? secondCard : flippedCard;

        setMessage(`${player.character.name} flipped ${flippedCard.suit} ${flippedCard.value} and ${secondCard.suit} ${secondCard.value}, chose ${chosenCard.suit} ${chosenCard.value}`);
        return chosenCard.suit === SUITS.HEARTS;
      }
    }

    setMessage(`${player.character.name} flipped ${flippedCard.suit} ${flippedCard.value} for barrel check`);
    return flippedCard.suit === SUITS.HEARTS;
  };

  // Check if player can use barrel defense
  const canUseBarrel = (player) => {
    return player.character.name === 'Jourdonnais' ||
           player.inPlay.some(card => card.type === CARD_TYPES.BARREL);
  };

  // Check if player can play another BANG! card this turn
  const canPlayBang = (player) => {
    // Willy the Kid can play unlimited BANG! cards
    if (player.character.name === 'Willy the Kid') {
      return true;
    }

    // Volcanic weapon allows unlimited BANG! cards
    if (player.inPlay.some(card => card.type === CARD_TYPES.VOLCANIC)) {
      return true;
    }

    // Otherwise, can only play one BANG! per turn
    return bangCardsPlayedThisTurn === 0;
  };

  // Check if a player's role should be visible to all players
  const isRoleVisible = (player) => {
    // Sheriff's role is always visible to all players
    return player.role === ROLES.SHERIFF;
  };

  // Helper function to check for green defensive cards
  const canUseGreenDefensiveCard = (player, cardType) => {
    // Check if player has the green card in play and it wasn't played this turn
    const hasCard = player.inPlay.some(card => card.type === cardType);
    if (!hasCard) return false;

    // Check if this green card was played this turn (can't use same turn)
    const cardKey = `${players.findIndex(p => p.id === player.id)}-${cardType}`;
    return !greenCardsPlayedThisTurn.has(cardKey);
  };

  // Helper function to activate green defensive card
  const activateGreenDefensiveCard = (playerIndex, cardType) => {
    const updatedPlayers = [...players];
    const cardIndex = updatedPlayers[playerIndex].inPlay.findIndex(card => card.type === cardType);

    if (cardIndex >= 0) {
      const usedCard = updatedPlayers[playerIndex].inPlay.splice(cardIndex, 1)[0];
      setDiscardPile(prev => [...prev, usedCard]);
      setPlayers(updatedPlayers);
      return true;
    }
    return false;
  };

  // Helper function to trigger all elimination abilities
  const triggerEliminationAbilities = (updatedPlayers, eliminatedPlayerIndex) => {
    const eliminatedPlayer = updatedPlayers[eliminatedPlayerIndex];

    // Trigger Vulture Sam's ability
    const vultureSam = updatedPlayers.find(p => p.character.name === 'Vulture Sam' && p.isAlive);
    if (vultureSam && vultureSam !== eliminatedPlayer) {
      triggerCharacterAbility(vultureSam, 'ON_ELIMINATION', { eliminatedPlayer });
      // Clear eliminated player's cards since Vulture Sam took them
      updatedPlayers[eliminatedPlayerIndex].hand = [];
      updatedPlayers[eliminatedPlayerIndex].inPlay = [];
    }

    // Trigger Greg Digger's ability (heal on elimination)
    const gregDigger = updatedPlayers.find(p => p.character.name === 'Greg Digger' && p.isAlive);
    if (gregDigger && gregDigger !== eliminatedPlayer) {
      triggerCharacterAbility(gregDigger, 'HEAL_ON_ELIMINATION', { eliminatedPlayer });
    }

    // Trigger Herb Hunter's ability (draw on elimination)
    const herbHunter = updatedPlayers.find(p => p.character.name === 'Herb Hunter' && p.isAlive);
    if (herbHunter && herbHunter !== eliminatedPlayer) {
      triggerCharacterAbility(herbHunter, 'DRAW_ON_ELIMINATION', { eliminatedPlayer });
    }
  };

  // Character ability implementations
  const triggerCharacterAbility = (player, abilityType, context = {}) => {
    switch (abilityType) {
      case 'ON_DAMAGE_TAKEN':
        if (player.character.name === 'Bart Cassidy') {
          // Bart Cassidy draws a card when he loses a life point
          if (deck.length > 0) {
            const drawnCard = deck[0];
            setDeck(deck.slice(1));
            setPlayers(prevPlayers =>
              prevPlayers.map(p =>
                p.id === player.id
                  ? { ...p, hand: [...p.hand, drawnCard] }
                  : p
              )
            );
            setMessage(`${player.name} (Bart Cassidy) draws a card from taking damage!`);
          }
        } else if (player.character.name === 'El Gringo') {
          // El Gringo draws from attacker when hit
          const { attackerIndex } = context;
          if (attackerIndex !== undefined && players[attackerIndex] && players[attackerIndex].hand.length > 0) {
            const attacker = players[attackerIndex];
            const randomCardIndex = Math.floor(Math.random() * attacker.hand.length);
            const stolenCard = attacker.hand[randomCardIndex];

            setPlayers(prevPlayers =>
              prevPlayers.map((p, index) => {
                if (index === attackerIndex) {
                  // Remove card from attacker
                  const newHand = [...p.hand];
                  newHand.splice(randomCardIndex, 1);
                  return { ...p, hand: newHand };
                } else if (p.id === player.id) {
                  // Add card to El Gringo
                  return { ...p, hand: [...p.hand, stolenCard] };
                }
                return p;
              })
            );
            setMessage(`${player.name} (El Gringo) draws a card from ${attacker.name}!`);
          }
        }
        break;

      case 'AUTO_DRAW':
        if (player.character.name === 'Suzy Lafayette' && player.hand.length === 0) {
          // Suzy Lafayette draws a card when she has no cards in hand
          if (deck.length > 0) {
            const drawnCard = deck[0];
            setDeck(deck.slice(1));
            setPlayers(prevPlayers =>
              prevPlayers.map(p =>
                p.id === player.id
                  ? { ...p, hand: [...p.hand, drawnCard] }
                  : p
              )
            );
            setMessage(`${player.name} (Suzy Lafayette) draws a card for having no cards!`);
          }
        }
        break;

      case 'ON_ELIMINATION':
        if (player.character.name === 'Vulture Sam') {
          // Vulture Sam takes cards of eliminated players
          const { eliminatedPlayer } = context;
          if (eliminatedPlayer && eliminatedPlayer.hand.length > 0) {
            setPlayers(prevPlayers =>
              prevPlayers.map(p =>
                p.id === player.id
                  ? { ...p, hand: [...p.hand, ...eliminatedPlayer.hand] }
                  : p
              )
            );
            setMessage(`${player.name} (Vulture Sam) takes all cards from eliminated ${eliminatedPlayer.name}!`);
          }
        }
        break;

      // Dodge City character abilities
      case 'HEAL_ON_ELIMINATION':
        if (player.character.name === 'Greg Digger') {
          // Greg Digger regains 2 life when another character is eliminated
          const { eliminatedPlayer } = context;
          if (eliminatedPlayer && eliminatedPlayer.id !== player.id) {
            setPlayers(prevPlayers =>
              prevPlayers.map(p =>
                p.id === player.id
                  ? { ...p, health: Math.min(p.health + 2, p.character.life) }
                  : p
              )
            );
            setMessage(`${player.name} (Greg Digger) regained 2 life from elimination!`);
          }
        }
        break;

      case 'DRAW_ON_ELIMINATION':
        if (player.character.name === 'Herb Hunter') {
          // Herb Hunter draws 2 extra cards when another character is eliminated
          const { eliminatedPlayer } = context;
          if (eliminatedPlayer && eliminatedPlayer.id !== player.id && deck.length >= 2) {
            const drawResult = drawCards(deck, 2);
            setPlayers(prevPlayers =>
              prevPlayers.map(p =>
                p.id === player.id
                  ? { ...p, hand: [...p.hand, ...drawResult.drawnCards] }
                  : p
              )
            );
            setDeck(drawResult.updatedDeck);
            setMessage(`${player.name} (Herb Hunter) drew 2 cards from elimination!`);
          }
        }
        break;

      default:
        break;
    }
  };

  // Handle card preview
  const handleCardPreview = (card) => {
    setPreviewCard(card);
    setShowCardPreview(true);
  };

  const handleCharacterPreview = (character) => {
    // Create a character object that looks like a card for the preview system
    const characterAsCard = {
      type: character.name,
      suit: '',
      value: '',
      isCharacter: true,
      character: character
    };
    setPreviewCard(characterAsCard);
    setShowCardPreview(true);
  };

  const closeCardPreview = () => {
    setShowCardPreview(false);
    setPreviewCard(null);
  };

  // Handle player death modal choices
  const handleExitGame = () => {
    setShowDeathModal(false);
    setGameState('setup');
    setPlayers([]);
    setCurrentPlayerIndex(0);
    setDeck([]);
    setDiscardPile([]);
    setTurnPhase('draw');
    setHasDrawnCards(false);
    setBangCardsPlayedThisTurn(0);
    setNumHumanPlayers(1);
    setHumanPlayerNames(['']);
    setMessage('Welcome to BANG!');
    setIsSpectating(false);
  };

  const handleSpectateGame = () => {
    setShowDeathModal(false);
    setIsSpectating(true);
    setMessage('You are now spectating the game. You can watch until the game ends.');
  };

  // Touch and hold handlers for card preview
  const handleTouchStart = (card) => {
    const timer = setTimeout(() => {
      setIsLongPressing(true);
      handleCardPreview(card);
    }, 2000); // 2000ms long press duration
    setTouchTimer(timer);
  };

  const handleTouchEnd = () => {
    if (touchTimer) {
      clearTimeout(touchTimer);
      setTouchTimer(null);
    }
    setIsLongPressing(false);
  };

  const handleTouchMove = () => {
    // Cancel long press if finger moves
    if (touchTimer) {
      clearTimeout(touchTimer);
      setTouchTimer(null);
    }
    setIsLongPressing(false);
  };

  // Prevent context menu and drag on images for mobile
  // eslint-disable-next-line no-unused-vars
  const handleImageInteraction = (e) => {
    e.preventDefault();
    e.stopPropagation();
    return false;
  };

  // Helper function to check if card needs target
  const needsTarget = useCallback((card) => {
    return [
      CARD_TYPES.BANG, CARD_TYPES.PANIC, CARD_TYPES.CAT_BALOU,
      CARD_TYPES.DUEL, CARD_TYPES.JAIL, CARD_TYPES.PUNCH, CARD_TYPES.TEQUILA
    ].includes(card.type);
  }, []);

  // Long press handling for mobile card zoom
  const handleCardTouchStart = useCallback((e, card, cardIndex) => {
    e.preventDefault();

    // Clear any existing timers
    if (longPressTimer) {
      clearTimeout(longPressTimer);
    }

    setPressedCard({ card, cardIndex });
    setIsLongPressing(false);

    // Add visual feedback class to the card element
    const cardElement = e.currentTarget;

    // Set timer for long press (800ms delay)
    const timer = setTimeout(() => {
      setIsLongPressing(true);
      setZoomedCard(card);
      cardElement.classList.add('long-pressing');
      // Add haptic feedback if available
      if (navigator.vibrate) {
        navigator.vibrate(50);
      }
    }, 800);

    setLongPressTimer(timer);
  }, [longPressTimer]);

  const handleCardTouchEnd = useCallback((e) => {
    e.preventDefault();

    // Remove visual feedback class
    const cardElement = e.currentTarget;
    cardElement.classList.remove('long-pressing');

    // Clear the long press timer
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    // Reset states
    setIsLongPressing(false);
    setPressedCard(null);
  }, [longPressTimer]);

  const handleCardTouchMove = useCallback((e) => {
    // Cancel long press if finger moves too much
    if (longPressTimer) {
      clearTimeout(longPressTimer);
      setLongPressTimer(null);
    }

    // Remove visual feedback class
    const cardElement = e.currentTarget;
    cardElement.classList.remove('long-pressing');

    setIsLongPressing(false);
  }, [longPressTimer]);



  // Card animation functions
  const triggerCardAnimation = (cardId, animationType, duration = 500) => {
    setCardAnimations(prev => ({
      ...prev,
      [cardId]: { type: animationType, timestamp: Date.now() }
    }));

    // Remove animation after duration
    setTimeout(() => {
      setCardAnimations(prev => {
        const newAnimations = { ...prev };
        delete newAnimations[cardId];
        return newAnimations;
      });
    }, duration);
  };

  const createMovingCard = (card, fromElement, toElement, onComplete) => {
    const fromRect = fromElement.getBoundingClientRect();
    const toRect = toElement.getBoundingClientRect();

    const movingCard = {
      id: `moving-${Date.now()}-${Math.random()}`,
      card,
      startX: fromRect.left + fromRect.width / 2,
      startY: fromRect.top + fromRect.height / 2,
      endX: toRect.left + toRect.width / 2,
      endY: toRect.top + toRect.height / 2,
      onComplete
    };

    setMovingCards(prev => [...prev, movingCard]);

    // Remove moving card after animation
    setTimeout(() => {
      setMovingCards(prev => prev.filter(mc => mc.id !== movingCard.id));
      if (onComplete) onComplete();
    }, 800);
  };

  // eslint-disable-next-line no-unused-vars
  const animateCardPlay = (cardElement, targetElement) => {
    if (cardElement && targetElement) {
      // Add played card animation
      triggerCardAnimation(`play-${Date.now()}`, 'cardPlay', 600);

      // Create flying card effect
      createMovingCard(
        { type: 'BANG', suit: '♠', value: 'A' }, // Placeholder card
        cardElement,
        targetElement,
        () => {
          // Animation complete
          triggerCardAnimation(`impact-${Date.now()}`, 'impact', 300);
        }
      );
    }
  };

  const animateCardDraw = () => {
    triggerCardAnimation(`draw-${Date.now()}`, 'cardDraw', 400);
  };

  // eslint-disable-next-line no-unused-vars
  const animateCardDiscard = (cardElement) => {
    if (cardElement) {
      triggerCardAnimation(`discard-${Date.now()}`, 'cardDiscard', 500);
    }
  };

  // Get card description for preview
  const getCardDescription = (cardType) => {
    const descriptions = {
      [CARD_TYPES.BANG]: "Deal 1 damage to a player within range. Target must play a Missed! card or lose 1 life point.",
      [CARD_TYPES.MISSED]: "Play in response to a BANG! card to avoid taking damage.",
      [CARD_TYPES.BEER]: "Regain 1 life point (up to your maximum). Cannot be played if you're already at maximum health. Requires 3+ players alive.",
      [CARD_TYPES.SALOON]: "All players regain 1 life point (up to their maximum).",
      [CARD_TYPES.PANIC]: "Draw a random card from a player at distance 1.",
      [CARD_TYPES.CAT_BALOU]: "Force a player to discard a random card from their hand or remove a card from play.",
      [CARD_TYPES.DUEL]: "Challenge another player to a duel. You and target alternate playing BANG! cards. First player who cannot play a BANG! loses 1 life point.",
      [CARD_TYPES.JAIL]: "Place on another player. They must draw a Heart to escape or skip their turn. Cannot target the Sheriff or players already in jail.",
      [CARD_TYPES.DYNAMITE]: "Place in front of you. Each turn, draw a card - if it's Spades 2-9, take 3 damage!",
      [CARD_TYPES.BARREL]: "When targeted by BANG!, draw a card. If it's a Heart, the BANG! misses.",
      [CARD_TYPES.MUSTANG]: "Increases distance from you to other players by 1.",
      [CARD_TYPES.SCOPE]: "Decreases distance from other players to you by 1.",
      [CARD_TYPES.VOLCANIC]: "Allows you to play unlimited BANG! cards per turn.",
      [CARD_TYPES.SCHOFIELD]: "Range 2 weapon. Can target players at distance 1-2.",
      [CARD_TYPES.REMINGTON]: "Range 3 weapon. Can target players at distance 1-3.",
      [CARD_TYPES.REV_CARABINE]: "Range 4 weapon. Can target players at distance 1-4.",
      [CARD_TYPES.WINCHESTER]: "Range 5 weapon. Can target players at distance 1-5.",
      [CARD_TYPES.STAGECOACH]: "Draw 2 cards from the deck.",
      [CARD_TYPES.WELLS_FARGO]: "Draw 3 cards from the deck.",
      [CARD_TYPES.GENERAL_STORE]: "All players draw 1 card from a shared selection.",
      [CARD_TYPES.INDIANS]: "All other players must play a BANG! card or lose 1 life point.",
      [CARD_TYPES.GATLING]: "Deal 1 damage to all other players (they can play Missed! to avoid).",
      // Dodge City cards
      [CARD_TYPES.DODGE]: "Play in response to a BANG! card to avoid taking damage (like Missed!) and draw 1 card.",
      [CARD_TYPES.PUNCH]: "Deal 1 damage to a player at distance 1 (like BANG! but only range 1).",
      [CARD_TYPES.BRAWL]: "Discard another card + Brawl: Force all other players to discard a card from hand or play.",
      [CARD_TYPES.RAG_TIME]: "All players draw 1 card, then discard 1 card.",
      [CARD_TYPES.TEQUILA]: "Discard another card + Tequila: Choose any player to regain 1 life point.",
      [CARD_TYPES.WHISKY]: "Regain 2 life points (enhanced Beer effect).",
      [CARD_TYPES.BINOCULAR]: "GREEN: Discard to look at top 3 cards of deck, put them back in any order.",
      [CARD_TYPES.HIDEOUT]: "GREEN: Discard when targeted by BANG! - draw a card, if Spade the BANG! misses.",
      [CARD_TYPES.BIBLE]: "GREEN: Discard to avoid BANG! (like Missed!) and draw 1 card.",
      [CARD_TYPES.CAN_CAN]: "GREEN: Discard when playing Missed! to draw 1 card.",
      [CARD_TYPES.CANTEEN]: "GREEN: Discard when playing Beer to regain 2 life instead of 1.",
      [CARD_TYPES.CONESTOGA]: "GREEN: Discard during hand limit phase to discard 1 less card.",
      [CARD_TYPES.IRON_PLATE]: "GREEN: Discard to avoid BANG! (like Missed!).",
      [CARD_TYPES.PONY_EXPRESS]: "GREEN: Discard to draw 3 cards from deck.",
      [CARD_TYPES.SOMBRERO]: "GREEN: Discard to avoid BANG! (like Missed!).",
      [CARD_TYPES.TEN_GALLON_HAT]: "GREEN: Discard to avoid BANG! (like Missed!).",
      [CARD_TYPES.DERRINGER]: "GREEN: Discard to play as BANG! at range 1 and draw 1 card.",
      [CARD_TYPES.PEPPERBOX]: "Range 1 weapon. Can play unlimited BANG! cards per turn.",
      [CARD_TYPES.KNIFE]: "Range 1 weapon. BANG! cards played cannot be avoided with Missed!",
      [CARD_TYPES.SPRINGFIELD]: "Range 2 weapon. Can target players at distance 1-2.",
      [CARD_TYPES.BUFFALO_RIFLE]: "Range 5 weapon. Can target players at distance 1-5.",
      [CARD_TYPES.HOWITZER]: "GREEN: Discard to play as BANG! against all other players. Doesn't count toward BANG! limit."
    };
    return descriptions[cardType] || "No description available.";
  };

  // Handle draw phase - can only be called once per turn
  const handlePlayerTurn = useCallback(() => {
    // Check if we've already drawn for this turn
    if (lastDrawnTurnRef.current === turnId) {
      return;
    }

    // Only allow drawing during draw phase and if cards haven't been drawn yet
    if (turnPhase !== 'draw' || hasDrawnCards || isDrawingCards) {
      if (!isDrawingCards) {
        setMessage("You have already drawn cards this turn!");
      }
      return;
    }

    // Mark this turn as processed and set drawing flag
    lastDrawnTurnRef.current = turnId;
    setIsDrawingCards(true);

    const currentPlayer = players[currentPlayerIndex];
    const updatedPlayers = [...players];
    let newDeck = [...deck];
    let cardsDrawn = 0;

    // Handle start-of-turn effects (Jail, Dynamite)
    const jailIndex = currentPlayer.inPlay.findIndex(card => card.type === CARD_TYPES.JAIL);
    if (jailIndex >= 0) {
      // Player is in jail - flip card to see if they escape
      if (newDeck.length > 0) {
        let escaped = false;

        if (currentPlayer.character.name === 'Lucky Duke') {
          // Lucky Duke draws 2 cards and chooses the better one
          if (newDeck.length >= 2) {
            const firstCard = newDeck[0];
            const secondCard = newDeck[1];
            newDeck = newDeck.slice(2);
            setDiscardPile([...discardPile, firstCard, secondCard]);

            // Choose the better card (Hearts are best for jail escape)
            const chosenCard = firstCard.suit === SUITS.HEARTS ? firstCard :
                              secondCard.suit === SUITS.HEARTS ? secondCard : firstCard;

            escaped = chosenCard.suit === SUITS.HEARTS;
            setMessage(`${currentPlayer.character.name} (Lucky Duke) drew 2 cards for jail escape and chose ${chosenCard.suit} ${chosenCard.value}`);
          }
        } else {
          // Normal jail escape attempt
          const flippedCard = newDeck[0];
          newDeck = newDeck.slice(1);
          setDiscardPile([...discardPile, flippedCard]);

          escaped = flippedCard.suit === SUITS.HEARTS;
          setMessage(`${currentPlayer.character.name} flipped ${flippedCard.suit} ${flippedCard.value} for jail escape`);
        }

        if (escaped) {
          // Escaped jail
          const jailCard = updatedPlayers[currentPlayerIndex].inPlay.splice(jailIndex, 1)[0];
          setDiscardPile([...discardPile, jailCard]);
          setMessage(`${currentPlayer.character.name} escaped from jail!`);
        } else {
          // Stay in jail, skip turn
          setMessage(`${currentPlayer.character.name} remains in jail and skips their turn.`);
          setPlayers(updatedPlayers);
          setDeck(newDeck);
          endTurn();
          return;
        }
      }
    }

    const dynamiteIndex = currentPlayer.inPlay.findIndex(card => card.type === CARD_TYPES.DYNAMITE);
    if (dynamiteIndex >= 0) {
      // Check dynamite
      if (newDeck.length > 0) {
        const flippedCard = newDeck[0];
        newDeck = newDeck.slice(1);
        setDiscardPile([...discardPile, flippedCard]);

        if (flippedCard.suit === SUITS.SPADES && ['2', '3', '4', '5', '6', '7', '8', '9'].includes(flippedCard.value)) {
          // Dynamite explodes
          const dynamiteCard = updatedPlayers[currentPlayerIndex].inPlay.splice(dynamiteIndex, 1)[0];
          setDiscardPile([...discardPile, dynamiteCard]);
          updatedPlayers[currentPlayerIndex].health -= 3;

          if (updatedPlayers[currentPlayerIndex].health <= 0) {
            updatedPlayers[currentPlayerIndex].isAlive = false;

            // Trigger elimination abilities
            triggerEliminationAbilities(updatedPlayers, currentPlayerIndex);

            setMessage(`${currentPlayer.character.name} (${currentPlayer.role}) was killed by dynamite!`);

            // Play epic elimination sound - DOMINATING!
            playSound('dominating');

            // Check if the eliminated player is the human player (index 0)
            if (currentPlayerIndex === 0 && !updatedPlayers[0].isBot) {
              setShowDeathModal(true);
            }

            setPlayers(updatedPlayers);
            setDeck(newDeck);
            checkGameEnd(updatedPlayers);
            return;
          } else {
            // Play epic damage sound - GODLIKE! (only for non-fatal damage)
            playSound('godlike');
            setMessage(`${currentPlayer.character.name} was hurt by dynamite! Health: ${updatedPlayers[currentPlayerIndex].health}`);
          }
        } else {
          // Pass dynamite to next player
          const dynamiteCard = updatedPlayers[currentPlayerIndex].inPlay.splice(dynamiteIndex, 1)[0];
          let nextPlayerIndex = (currentPlayerIndex + 1) % players.length;
          while (!updatedPlayers[nextPlayerIndex].isAlive) {
            nextPlayerIndex = (nextPlayerIndex + 1) % players.length;
          }
          updatedPlayers[nextPlayerIndex].inPlay.push(dynamiteCard);
          setMessage(`${currentPlayer.character.name} passed dynamite to ${updatedPlayers[nextPlayerIndex].character.name}`);
        }
      }
    }

    // Draw phase with character abilities
    if (currentPlayer.character.abilityType === 'DRAW_CHOICE') {
      if (currentPlayer.character.name === 'Jesse Jones') {
        // Jesse Jones can draw first card from another player
        // For simplicity, draw from deck for now
        const drawResult = drawCards(newDeck, 2);
        updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
        newDeck = drawResult.updatedDeck;
        cardsDrawn = drawResult.drawnCards.length;
      } else if (currentPlayer.character.name === 'Kit Carlson') {
        // Kit Carlson sees top 3 cards, chooses 2
        const drawResult = drawCards(newDeck, 3);
        if (drawResult.drawnCards.length >= 3) {
          // For simplicity, take first 2
          updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards.slice(0, 2));
          setDiscardPile([...discardPile, drawResult.drawnCards[2]]);
          cardsDrawn = 2;
        } else {
          // If less than 3 cards available, take what we can
          updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
          cardsDrawn = drawResult.drawnCards.length;
        }
        newDeck = drawResult.updatedDeck;
      } else if (currentPlayer.character.name === 'Pedro Ramirez') {
        // Pedro Ramirez can draw first card from discard pile
        if (discardPile.length > 0) {
          const topDiscard = discardPile[discardPile.length - 1];
          updatedPlayers[currentPlayerIndex].hand.push(topDiscard);
          setDiscardPile(discardPile.slice(0, -1));
          cardsDrawn = 1;

          // Draw second card from deck
          const drawResult = drawCards(newDeck, 1);
          if (drawResult.drawnCards.length > 0) {
            updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
            newDeck = drawResult.updatedDeck;
            cardsDrawn = 2;
          }
        } else {
          // No discard pile, draw normally
          const drawResult = drawCards(newDeck, 2);
          updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
          newDeck = drawResult.updatedDeck;
          cardsDrawn = drawResult.drawnCards.length;
        }
      }
    } else if (currentPlayer.character.abilityType === 'ON_DRAW') {
      // Black Jack shows second card when drawing
      const drawResult = drawCards(newDeck, 2);
      updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
      newDeck = drawResult.updatedDeck;
      cardsDrawn = drawResult.drawnCards.length;

      if (drawResult.drawnCards.length >= 2 && (drawResult.drawnCards[1].suit === SUITS.HEARTS || drawResult.drawnCards[1].suit === SUITS.DIAMONDS)) {
        // Draw additional card
        const bonusDrawResult = drawCards(newDeck, 1);
        if (bonusDrawResult.drawnCards.length > 0) {
          updatedPlayers[currentPlayerIndex].hand.push(...bonusDrawResult.drawnCards);
          newDeck = bonusDrawResult.updatedDeck;
          cardsDrawn = drawResult.drawnCards.length + bonusDrawResult.drawnCards.length;
          setMessage(`${currentPlayer.character.name} drew an extra card (Black Jack ability)!`);
        }
      }
    } else if (currentPlayer.character.abilityType === 'INJURY_DRAW') {
      // Bill Noface draws 1 + injury cards
      const injuries = currentPlayer.character.life - currentPlayer.health;
      const cardsToDraw = 1 + injuries;
      const drawResult = drawCards(newDeck, cardsToDraw);
      updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
      newDeck = drawResult.updatedDeck;
      cardsDrawn = drawResult.drawnCards.length;
      setMessage(`${currentPlayer.character.name} drew ${cardsToDraw} cards (1 + ${injuries} injuries)!`);
    } else if (currentPlayer.character.abilityType === 'ENHANCED_DRAW') {
      // Pixie Pete draws 3 cards instead of 2
      const drawResult = drawCards(newDeck, 3);
      updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
      newDeck = drawResult.updatedDeck;
      cardsDrawn = drawResult.drawnCards.length;
      setMessage(`${currentPlayer.character.name} drew 3 cards!`);
    } else {
      // Normal draw
      const drawResult = drawCards(newDeck, 2);
      updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
      newDeck = drawResult.updatedDeck;
      cardsDrawn = drawResult.drawnCards.length;
    }

    // Suzy Lafayette draws a card if she has no cards
    if (currentPlayer.character.name === 'Suzy Lafayette' && updatedPlayers[currentPlayerIndex].hand.length === 0) {
      const drawResult = drawCards(newDeck, 1);
      if (drawResult.drawnCards.length > 0) {
        updatedPlayers[currentPlayerIndex].hand.push(...drawResult.drawnCards);
        newDeck = drawResult.updatedDeck;
        setMessage(`${currentPlayer.character.name} drew a card (Suzy Lafayette ability)!`);
      }
    }

    // Play card draw sound
    playSound('cardDraw');

    // Trigger card draw visual effect
    triggerVisualEffect(currentPlayerIndex, 'card-draw-effect', 1000);

    // Trigger card draw animation
    animateCardDraw();

    setPlayers(updatedPlayers);
    setDeck(newDeck);
    setHasDrawnCards(true);
    setTurnPhase('play');
    setMessage(`${currentPlayer.name} drew ${cardsDrawn} cards. Now play cards or end turn.`);

    // Reset drawing flag
    setIsDrawingCards(false);
  }, [turnPhase, hasDrawnCards, isDrawingCards, players, currentPlayerIndex, deck, discardPile, setPlayers, setDeck, setHasDrawnCards, setTurnPhase, setMessage, setDiscardPile, playSound, triggerVisualEffect, createFloatingNumber, animateCardDraw, drawCards, setIsDrawingCards]);

  // Play a card
  const playCard = (playerIndex, cardIndex, targetPlayerIndex = null) => {
    if (playerIndex !== currentPlayerIndex) {
      setMessage("It's not your turn!");
      return;
    }

    // Only allow playing cards during play phase
    if (turnPhase !== 'play') {
      if (turnPhase === 'draw') {
        setMessage("You must draw cards first!");
      } else {
        setMessage("You can only play cards during the play phase!");
      }
      return;
    }

    const player = players[playerIndex];
    const card = player.hand[cardIndex];

    // Play card play sound
    playSound('cardPlay');

    // Trigger card play animation
    triggerCardAnimation(`play-${playerIndex}-${cardIndex}`, 'cardPlay', 600);

    // Handle Calamity Janet's card substitution ability
    if (player.character.name === 'Calamity Janet') {
      if (card.type === CARD_TYPES.MISSED && targetPlayerIndex !== null) {
        // Calamity Janet can play Missed! as BANG!
        handleBangCard(playerIndex, cardIndex, targetPlayerIndex, true); // true indicates substitution
        return;
      } else if (card.type === CARD_TYPES.BANG && targetPlayerIndex === null) {
        // This might be intended as a defensive play, but we'll handle it normally
      }
    }

    // Handle different card types
    switch (card.type) {
      case CARD_TYPES.BANG:
        if (targetPlayerIndex === null) {
          setMessage("Select a target for BANG!");
          return;
        }
        handleBangCard(playerIndex, cardIndex, targetPlayerIndex);
        break;
      case CARD_TYPES.MISSED:
        // Missed! cards can't normally be played offensively except by Calamity Janet
        if (player.character.name === 'Calamity Janet' && targetPlayerIndex !== null) {
          handleBangCard(playerIndex, cardIndex, targetPlayerIndex, true);
        } else {
          setMessage("Missed! cards can only be played defensively!");
        }
        break;
      case CARD_TYPES.BEER:
        handleBeerCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.PANIC:
        if (targetPlayerIndex === null) {
          setMessage("Select a target for Panic!");
          return;
        }
        handlePanicCard(playerIndex, cardIndex, targetPlayerIndex);
        break;
      case CARD_TYPES.CAT_BALOU:
        if (targetPlayerIndex === null) {
          setMessage("Select a target for Cat Balou!");
          return;
        }
        handleCatBalouCard(playerIndex, cardIndex, targetPlayerIndex);
        break;
      case CARD_TYPES.STAGECOACH:
        handleStagecoachCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.WELLS_FARGO:
        handleWellsFargoCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.GATLING:
        handleGatlingCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.INDIANS:
        handleIndiansCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.DUEL:
        if (targetPlayerIndex === null) {
          setMessage("Select a target for Duel!");
          return;
        }
        handleDuelCard(playerIndex, cardIndex, targetPlayerIndex);
        break;
      case CARD_TYPES.GENERAL_STORE:
        handleGeneralStoreCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.SALOON:
        handleSaloonCard(playerIndex, cardIndex);
        break;
      // Equipment cards
      case CARD_TYPES.BARREL:
      case CARD_TYPES.SCOPE:
      case CARD_TYPES.MUSTANG:
      case CARD_TYPES.VOLCANIC:
      case CARD_TYPES.SCHOFIELD:
      case CARD_TYPES.REMINGTON:
      case CARD_TYPES.REV_CARABINE:
      case CARD_TYPES.WINCHESTER:
        handleEquipmentCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.JAIL:
        if (targetPlayerIndex === null) {
          setMessage("Select a target for Jail!");
          return;
        }
        handleJailCard(playerIndex, cardIndex, targetPlayerIndex);
        break;
      case CARD_TYPES.DYNAMITE:
        handleDynamiteCard(playerIndex, cardIndex);
        break;
      // Dodge City cards
      case CARD_TYPES.DODGE:
        // Dodge is now a green-bordered equipment card
        handleEquipmentCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.PUNCH:
        if (targetPlayerIndex === null) {
          setMessage("Select a target for Punch!");
          return;
        }
        handlePunchCard(playerIndex, cardIndex, targetPlayerIndex);
        break;
      case CARD_TYPES.BRAWL:
        handleBrawlCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.RAG_TIME:
        handleRagTimeCard(playerIndex, cardIndex);
        break;
      case CARD_TYPES.TEQUILA:
        // Tequila can be played without a target (targets self) or with a target
        handleTequilaCard(playerIndex, cardIndex, targetPlayerIndex);
        break;
      case CARD_TYPES.WHISKY:
        handleWhiskyCard(playerIndex, cardIndex);
        break;

      // Dodge City equipment
      case CARD_TYPES.BINOCULAR:
      case CARD_TYPES.HIDEOUT:
      case CARD_TYPES.BIBLE:
      case CARD_TYPES.CAN_CAN:
      case CARD_TYPES.CANTEEN:
      case CARD_TYPES.CONESTOGA:
      case CARD_TYPES.IRON_PLATE:
      case CARD_TYPES.PONY_EXPRESS:
      case CARD_TYPES.SOMBRERO:
      case CARD_TYPES.TEN_GALLON_HAT:
      case CARD_TYPES.DERRINGER:
      case CARD_TYPES.HOWITZER:
      case CARD_TYPES.PEPPERBOX:
      case CARD_TYPES.KNIFE:
      case CARD_TYPES.SPRINGFIELD:
      case CARD_TYPES.BUFFALO_RIFLE:
        handleEquipmentCard(playerIndex, cardIndex);
        break;
      default:
        setMessage(`Card ${card.type} not implemented yet`);
    }
  };

  // Handle BANG! card
  const handleBangCard = (playerIndex, cardIndex, targetPlayerIndex, isSubstitution = false, cardType = null) => {
    const player = players[playerIndex];
    const targetPlayer = players[targetPlayerIndex];

    // Get the card to check its suit for Apache Kid immunity
    const cardToPlay = player.hand[cardIndex];

    // Check Apache Kid's diamond immunity
    if (targetPlayer.character.name === 'Apache Kid' && cardToPlay.suit === SUITS.DIAMONDS) {
      setMessage(`${cardToPlay.type} (Diamond) cannot affect Apache Kid!`);
      return;
    }

    // Check if player can play another BANG! card this turn (unless it's a substitution)
    if (!isSubstitution && !canPlayBang(player)) {
      setMessage(`${player.name} has already played a BANG! card this turn!`);
      return;
    }

    // Check if target is in range using proper range calculation
    if (!isInRange(playerIndex, targetPlayerIndex, players)) {
      setMessage("Target is out of range!");
      return;
    }

    // Get the card before removing it
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand[cardIndex];

    // Start card animation to target
    animateCardToTarget(card, playerIndex, targetPlayerIndex);

    // Remove card from hand and add to discard pile immediately for game logic
    updatedPlayers[playerIndex].hand.splice(cardIndex, 1);
    setDiscardPile(prev => [...prev, card]);

    // Increment BANG! cards played this turn (only for actual BANG! cards, not substitutions)
    if (!isSubstitution) {
      setBangCardsPlayedThisTurn(bangCardsPlayedThisTurn + 1);
    }

    // Target player must play a Missed! or lose a life point
    let cardUsed;
    if (cardType === CARD_TYPES.PUNCH) {
      cardUsed = "Punch";
    } else if (isSubstitution) {
      cardUsed = "Missed! (as BANG!)";
    } else {
      cardUsed = "BANG!";
    }
    setMessage(`${player.name} played ${cardUsed} on ${targetPlayer.name}!`);

    // Check for barrel defense first - give player choice
    let defended = false;
    if (canUseBarrel(targetPlayer) && !targetPlayer.isBot) {
      // For human players, ask if they want to use barrel
      setPendingAttack({
        type: 'BANG',
        attackerIndex: playerIndex,
        targetIndex: targetPlayerIndex,
        cardIndex: cardIndex,
        isSubstitution: isSubstitution
      });
      setAbilityChoice({
        type: 'BARREL_DEFENSE',
        playerIndex: targetPlayerIndex,
        message: `${targetPlayer.name}, do you want to use your Barrel defense?`
      });
      setShowAbilityChoice(true);
      return; // Wait for player choice
    } else if (canUseBarrel(targetPlayer) && targetPlayer.isBot) {
      // Bots automatically use barrel
      if (performBarrelCheck(targetPlayer)) {
        defended = true;
        setMessage(`${targetPlayer.character.name} defended with Barrel!`);
      }
    }

    // If not defended by barrel, check for Missed! cards and green defensive cards
    if (!defended) {
      const missedRequired = player.character.name === 'Slab the Killer' ? 2 : 1;
      let missedCount = 0;

      // First check for green defensive cards

      // Check for Bible card (acts like Missed! + draw 1)
      if (canUseGreenDefensiveCard(updatedPlayers[targetPlayerIndex], CARD_TYPES.BIBLE) && missedCount < missedRequired) {
        if (activateGreenDefensiveCard(targetPlayerIndex, CARD_TYPES.BIBLE)) {
          missedCount++;
          // Bible also draws 1 card when used
          const drawResult = drawCards(deck, 1);
          if (drawResult.drawnCards.length > 0) {
            updatedPlayers[targetPlayerIndex].hand.push(...drawResult.drawnCards);
            setDeck(drawResult.updatedDeck);
          }
          setMessage(`${targetPlayer.name} used Bible to defend and drew 1 card!`);
        }
      }

      // Check for Hideout card (draw card, if Spade then BANG! misses)
      if (canUseGreenDefensiveCard(updatedPlayers[targetPlayerIndex], CARD_TYPES.HIDEOUT) && missedCount < missedRequired) {
        if (activateGreenDefensiveCard(targetPlayerIndex, CARD_TYPES.HIDEOUT)) {
          // Draw a card first
          const drawResult = drawCards(deck, 1);
          if (drawResult.drawnCards.length > 0) {
            const drawnCard = drawResult.drawnCards[0];
            updatedPlayers[targetPlayerIndex].hand.push(drawnCard);
            setDeck(drawResult.updatedDeck);

            // If it's a Spade, the BANG! misses
            if (drawnCard.suit === SUITS.SPADES) {
              missedCount++;
              setMessage(`${targetPlayer.name} used Hideout, drew ${drawnCard.type} of Spades - BANG! missed!`);
            } else {
              setMessage(`${targetPlayer.name} used Hideout, drew ${drawnCard.type} of ${drawnCard.suit} - BANG! still hits!`);
            }
          }
        }
      }

      // Check for Sombrero card (acts like Missed!)
      if (canUseGreenDefensiveCard(updatedPlayers[targetPlayerIndex], CARD_TYPES.SOMBRERO) && missedCount < missedRequired) {
        if (activateGreenDefensiveCard(targetPlayerIndex, CARD_TYPES.SOMBRERO)) {
          missedCount++;
          setMessage(`${targetPlayer.name} used Sombrero to defend against BANG!!`);
        }
      }

      // Check for Iron Plate card (acts like Missed!)
      if (canUseGreenDefensiveCard(updatedPlayers[targetPlayerIndex], CARD_TYPES.IRON_PLATE) && missedCount < missedRequired) {
        if (activateGreenDefensiveCard(targetPlayerIndex, CARD_TYPES.IRON_PLATE)) {
          missedCount++;
          setMessage(`${targetPlayer.name} used Iron Plate to defend against BANG!!`);
        }
      }

      // Check for Ten Gallon Hat card (acts like Missed!)
      if (canUseGreenDefensiveCard(updatedPlayers[targetPlayerIndex], CARD_TYPES.TEN_GALLON_HAT) && missedCount < missedRequired) {
        if (activateGreenDefensiveCard(targetPlayerIndex, CARD_TYPES.TEN_GALLON_HAT)) {
          missedCount++;
          setMessage(`${targetPlayer.name} used Ten Gallon Hat to defend against BANG!!`);
        }
      }



      // Then check for regular Missed! and Dodge cards
      for (let i = missedCount; i < missedRequired; i++) {
        const missedIndex = updatedPlayers[targetPlayerIndex].hand.findIndex(c =>
          c.type === CARD_TYPES.MISSED ||
          c.type === CARD_TYPES.DODGE ||
          (updatedPlayers[targetPlayerIndex].character.name === 'Calamity Janet' && c.type === CARD_TYPES.BANG)
        );

        if (missedIndex >= 0) {
          const missedCard = updatedPlayers[targetPlayerIndex].hand.splice(missedIndex, 1)[0];
          setDiscardPile([...discardPile, missedCard]);
          missedCount++;

          if (missedCard.type === CARD_TYPES.DODGE) {
            // Dodge draws 1 card when used
            const drawResult = drawCards(deck, 1);
            if (drawResult.drawnCards.length > 0) {
              updatedPlayers[targetPlayerIndex].hand.push(...drawResult.drawnCards);
              setDeck(drawResult.updatedDeck);
            }
            setMessage(`${targetPlayer.name} used Dodge to defend and drew 1 card!`);
          } else if (updatedPlayers[targetPlayerIndex].character.name === 'Calamity Janet' && missedCard.type === CARD_TYPES.BANG) {
            setMessage(`${targetPlayer.name} (Calamity Janet) used BANG! as Missed!`);
          }
        } else {
          break;
        }
      }

      if (missedCount >= missedRequired) {
        defended = true;
        setMessage(`${targetPlayer.character.name} defended with ${missedCount} defensive card(s)`);
      }
    }

    if (!defended) {
      // Target loses a life point
      updatedPlayers[targetPlayerIndex].health -= 1;

      // Trigger visual effects
      triggerVisualEffect(targetPlayerIndex, 'damage-effect', 1500);
      createFloatingNumber(targetPlayerIndex, 1, 'damage');

      // Trigger character abilities on damage
      triggerCharacterAbility(updatedPlayers[targetPlayerIndex], 'ON_DAMAGE_TAKEN', { attackerIndex: playerIndex });

      if (updatedPlayers[targetPlayerIndex].health <= 0) {
        updatedPlayers[targetPlayerIndex].isAlive = false;

        // Trigger elimination visual effects
        triggerVisualEffect(targetPlayerIndex, 'elimination-effect', 2000);
        triggerScreenShake();

        // Play epic elimination sound - DOMINATING!
        playSound('dominating');

        // Trigger all elimination abilities
        triggerEliminationAbilities(updatedPlayers, targetPlayerIndex);

        setMessage(`${targetPlayer.character.name} (${targetPlayer.role}) was eliminated!`);

        // Check if the eliminated player is the human player (index 0)
        if (targetPlayerIndex === 0 && !updatedPlayers[0].isBot) {
          setShowDeathModal(true);
        }

        checkGameEnd(updatedPlayers);
      } else {
        // Play epic damage sound - GODLIKE! (only for non-fatal damage)
        playSound('godlike');
        setMessage(`${targetPlayer.character.name} lost a life point! Health: ${updatedPlayers[targetPlayerIndex].health}`);
      }
    } else {
      // Trigger defense visual effects
      if (canUseBarrel(targetPlayer)) {
        triggerVisualEffect(targetPlayerIndex, 'barrel-defense-effect', 1000);
      } else {
        triggerVisualEffect(targetPlayerIndex, 'dodge-effect', 800);
      }
    }

    setPlayers(updatedPlayers);
  };

  // Handle Beer card
  const handleBeerCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];

    // Beer can only be used if there are 3+ players alive
    const alivePlayers = players.filter(p => p.isAlive);
    if (alivePlayers.length < 3) {
      setMessageWithPersistence("Beer can only be used when there are 3 or more players alive!", true, 4000);
      return;
    }

    // Check if player can benefit from Beer (not at maximum health)
    if (player.health >= player.maxHealth) {
      setMessageWithPersistence(`${player.name} is already at maximum health! Cannot play Beer.`, true, 4000);
      return;
    }

    // Remove Beer card from player's hand and discard it
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // Player gains life points (Tequila Joe gets 2 from Beer, others get 1)
    const healAmount = player.character.name === 'Tequila Joe' ? 2 : 1;
    const actualHeal = Math.min(healAmount, player.character.life - player.health);
    updatedPlayers[playerIndex].health += actualHeal;

    // Play healing sound
    playSound('heal');

    // Trigger healing visual effects
    triggerVisualEffect(playerIndex, 'healing-effect', 2000);
    createFloatingNumber(playerIndex, actualHeal, 'heal');

    const healMessage = player.character.name === 'Tequila Joe' ?
      `${player.name} (Tequila Joe) gained ${actualHeal} life points from Beer! Health: ${updatedPlayers[playerIndex].health}` :
      `${player.name} gained ${actualHeal} life point! Health: ${updatedPlayers[playerIndex].health}`;
    setMessage(healMessage);

    setPlayers(updatedPlayers);
  };

  // Handle Panic card
  const handlePanicCard = (playerIndex, cardIndex, targetPlayerIndex) => {
    const player = players[playerIndex];
    const targetPlayer = players[targetPlayerIndex];

    // Check if target is at distance 1
    if (calculateDistance(playerIndex, targetPlayerIndex, players) !== 1) {
      setMessage("Panic can only target players at distance 1!");
      return;
    }

    if (targetPlayer.hand.length === 0 && targetPlayer.inPlay.length === 0) {
      setMessage("Target has no cards to steal!");
      return;
    }

    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // Steal a random card from target's hand or in-play cards
    const allTargetCards = [...targetPlayer.hand, ...targetPlayer.inPlay];
    const randomIndex = Math.floor(Math.random() * allTargetCards.length);
    const stolenCard = allTargetCards[randomIndex];

    // Remove from target and add to player
    if (randomIndex < targetPlayer.hand.length) {
      updatedPlayers[targetPlayerIndex].hand.splice(randomIndex, 1);
    } else {
      updatedPlayers[targetPlayerIndex].inPlay.splice(randomIndex - targetPlayer.hand.length, 1);
    }

    updatedPlayers[playerIndex].hand.push(stolenCard);
    setMessage(`${player.character.name} stole ${stolenCard.type} from ${targetPlayer.character.name}!`);
    setPlayers(updatedPlayers);
  };

  // Handle Cat Balou card
  const handleCatBalouCard = (playerIndex, cardIndex, targetPlayerIndex) => {
    const player = players[playerIndex];
    const targetPlayer = players[targetPlayerIndex];

    // Filter out Jail cards from in-play cards (cannot be discarded by Cat Balou)
    const discardableInPlayCards = targetPlayer.inPlay.filter(card => card.type !== CARD_TYPES.JAIL);
    const allTargetCards = [...targetPlayer.hand, ...discardableInPlayCards];

    if (allTargetCards.length === 0) {
      setMessage("Target has no cards that can be discarded!");
      return;
    }

    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // Force target to discard a random card (excluding Jail)
    const randomIndex = Math.floor(Math.random() * allTargetCards.length);
    const discardedCard = allTargetCards[randomIndex];

    // Remove from target
    if (randomIndex < targetPlayer.hand.length) {
      // Card is from hand
      const handIndex = targetPlayer.hand.findIndex(c => c === discardedCard);
      updatedPlayers[targetPlayerIndex].hand.splice(handIndex, 1);
    } else {
      // Card is from in-play (but not Jail)
      const inPlayIndex = targetPlayer.inPlay.findIndex(c => c === discardedCard);
      updatedPlayers[targetPlayerIndex].inPlay.splice(inPlayIndex, 1);
    }

    setDiscardPile([...discardPile, discardedCard]);
    setMessage(`${player.character.name} forced ${targetPlayer.character.name} to discard ${discardedCard.type}!`);
    setPlayers(updatedPlayers);
  };

  // Handle Stagecoach card
  const handleStagecoachCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // Draw 2 cards
    const drawResult = drawCards(deck, 2);
    updatedPlayers[playerIndex].hand.push(...drawResult.drawnCards);
    setDeck(drawResult.updatedDeck);

    setMessage(`${player.character.name} drew ${drawResult.drawnCards.length} cards with Stagecoach!`);
    setPlayers(updatedPlayers);
  };

  // Handle Wells Fargo card
  const handleWellsFargoCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // Draw 3 cards
    const drawResult = drawCards(deck, 3);
    updatedPlayers[playerIndex].hand.push(...drawResult.drawnCards);
    setDeck(drawResult.updatedDeck);

    setMessage(`${player.character.name} drew ${drawResult.drawnCards.length} cards with Wells Fargo!`);
    setPlayers(updatedPlayers);
  };

  // Handle Gatling card
  const handleGatlingCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // All other players must play a Missed! or lose 1 life point
    for (let i = 0; i < updatedPlayers.length; i++) {
      if (i !== playerIndex && updatedPlayers[i].isAlive) {
        const targetPlayer = updatedPlayers[i];
        let defended = false;

        // Check for barrel defense
        if (canUseBarrel(targetPlayer)) {
          if (performBarrelCheck(targetPlayer)) {
            defended = true;
            setMessage(`${targetPlayer.character.name} defended against Gatling with Barrel!`);
          }
        }

        // Check for Missed! card
        if (!defended) {
          const missedIndex = targetPlayer.hand.findIndex(c =>
            c.type === CARD_TYPES.MISSED ||
            (targetPlayer.character.abilityType === 'CARD_SUBSTITUTION' && c.type === CARD_TYPES.BANG)
          );

          if (missedIndex >= 0) {
            const missedCard = updatedPlayers[i].hand.splice(missedIndex, 1)[0];
            setDiscardPile([...discardPile, missedCard]);
            defended = true;
          }
        }

        if (!defended) {
          updatedPlayers[i].health -= 1;

          if (updatedPlayers[i].health <= 0) {
            updatedPlayers[i].isAlive = false;

            // Trigger elimination abilities
            triggerEliminationAbilities(updatedPlayers, i);

            // Play epic elimination sound - DOMINATING!
            playSound('dominating');
            setMessage(`${targetPlayer.character.name} (${targetPlayer.role}) was eliminated by Gatling!`);
          } else {
            // Play epic damage sound - GODLIKE! (only for non-fatal damage)
            playSound('godlike');
          }
        }
      }
    }

    setMessage(`${player.character.name} played Gatling!`);
    setPlayers(updatedPlayers);
    checkGameEnd(updatedPlayers);
  };

  // Handle Indians card
  const handleIndiansCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // All other players must play a BANG! or lose 1 life point
    for (let i = 0; i < updatedPlayers.length; i++) {
      if (i !== playerIndex && updatedPlayers[i].isAlive) {
        const targetPlayer = updatedPlayers[i];
        const bangIndex = targetPlayer.hand.findIndex(c =>
          c.type === CARD_TYPES.BANG ||
          (targetPlayer.character.name === 'Calamity Janet' && c.type === CARD_TYPES.MISSED)
        );

        if (bangIndex >= 0) {
          const bangCard = updatedPlayers[i].hand.splice(bangIndex, 1)[0];
          setDiscardPile([...discardPile, bangCard]);
        } else {
          updatedPlayers[i].health -= 1;

          if (updatedPlayers[i].health <= 0) {
            updatedPlayers[i].isAlive = false;

            // Trigger elimination abilities
            triggerEliminationAbilities(updatedPlayers, i);

            // Play epic elimination sound - DOMINATING!
            playSound('dominating');
            setMessage(`${targetPlayer.character.name} (${targetPlayer.role}) was eliminated by Indians!`);
          } else {
            // Play epic damage sound - GODLIKE! (only for non-fatal damage)
            playSound('godlike');
          }
        }
      }
    }

    setMessage(`${player.character.name} played Indians!`);
    setPlayers(updatedPlayers);
    checkGameEnd(updatedPlayers);
  };

  // Handle Duel card - Official BANG! rules implementation
  const handleDuelCard = (playerIndex, cardIndex, targetPlayerIndex) => {
    const player = players[playerIndex];
    const targetPlayer = players[targetPlayerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    setMessage(`${player.character.name} challenged ${targetPlayer.character.name} to a duel!`);

    // Start the duel sequence - challenger goes first
    processDuel(playerIndex, targetPlayerIndex, true, updatedPlayers);
  };

  // Process the duel sequence with alternating BANG! cards
  const processDuel = (challengerIndex, targetIndex, challengerTurn, updatedPlayers) => {
    const currentDuelist = challengerTurn ? challengerIndex : targetIndex;
    const currentPlayer = updatedPlayers[currentDuelist];
    const opponentIndex = challengerTurn ? targetIndex : challengerIndex;

    // Check if current duelist can play a BANG! card
    const bangIndex = currentPlayer.hand.findIndex(c =>
      c.type === CARD_TYPES.BANG ||
      (currentPlayer.character.name === 'Calamity Janet' && c.type === CARD_TYPES.MISSED)
    );

    if (bangIndex >= 0) {
      // Player can respond - play the BANG! card
      const bangCard = updatedPlayers[currentDuelist].hand.splice(bangIndex, 1)[0];
      setDiscardPile(prev => [...prev, bangCard]);

      const cardUsed = bangCard.type === CARD_TYPES.MISSED ? "Missed! (as BANG!)" : "BANG!";
      setMessage(`${currentPlayer.character.name} played ${cardUsed} in the duel!`);

      // Update players state immediately
      setPlayers([...updatedPlayers]);

      // Continue duel - switch to opponent's turn
      const delay = currentPlayer.isBot ? 1000 : 1500; // Shorter delay for bots
      setTimeout(() => {
        processDuel(challengerIndex, targetIndex, !challengerTurn, updatedPlayers);
      }, delay);
    } else {
      // Player cannot respond - loses the duel
      updatedPlayers[currentDuelist].health -= 1;

      // Trigger character abilities on damage
      triggerCharacterAbility(updatedPlayers[currentDuelist], 'ON_DAMAGE_TAKEN', {
        attackerIndex: opponentIndex
      });

      if (updatedPlayers[currentDuelist].health <= 0) {
        updatedPlayers[currentDuelist].isAlive = false;

        // Play epic elimination sound - DOMINATING!
        playSound('dominating');

        // Trigger all elimination abilities
        triggerEliminationAbilities(updatedPlayers, currentDuelist);

        setMessage(`${currentPlayer.character.name} (${currentPlayer.role}) was eliminated in the duel!`);
        checkGameEnd(updatedPlayers);
      } else {
        // Play epic damage sound - GODLIKE! (only for non-fatal damage)
        playSound('godlike');
        setMessage(`${currentPlayer.character.name} lost the duel and 1 life point! Health: ${updatedPlayers[currentDuelist].health}`);
      }

      setPlayers(updatedPlayers);
    }
  };

  // Handle General Store card
  const handleGeneralStoreCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // Reveal cards equal to number of alive players
    const alivePlayers = updatedPlayers.filter(p => p.isAlive);
    const drawResult = drawCards(deck, alivePlayers.length);
    const revealedCards = drawResult.drawnCards;
    setDeck(drawResult.updatedDeck);

    // Create player order starting from the player who played General Store and proceeding clockwise
    const playerOrder = [];
    for (let i = 0; i < players.length; i++) {
      const nextPlayerIndex = (playerIndex + i) % players.length;
      if (players[nextPlayerIndex].isAlive) {
        playerOrder.push(nextPlayerIndex);
      }
    }

    // Set up General Store selection
    setGeneralStoreCards(revealedCards);
    setGeneralStorePlayerOrder(playerOrder);
    setCurrentGeneralStorePlayer(0);
    setShowGeneralStore(true);
    setPlayers(updatedPlayers);

    setMessage(`${player.character.name} played General Store! ${alivePlayers.length} cards revealed. Starting with ${player.character.name}, each player chooses one card clockwise.`);

    // Bot selection will be handled automatically by useEffect
  };

  // Handle General Store card selection
  const handleGeneralStoreSelection = (cardIndex) => {
    // Prevent multiple selections
    if (!generalStoreCards[cardIndex]) return;

    const currentPlayerIndex = generalStorePlayerOrder[currentGeneralStorePlayer];
    const selectedCard = generalStoreCards[cardIndex];
    const currentPlayer = players[currentPlayerIndex];

    // Validate that it's the correct player's turn
    if (currentPlayerIndex !== generalStorePlayerOrder[currentGeneralStorePlayer]) {
      return;
    }

    // Remove selected card from available cards first to prevent double selection
    const remainingCards = generalStoreCards.filter((_, index) => index !== cardIndex);
    setGeneralStoreCards(remainingCards);

    // Add card to current player's hand
    const updatedPlayers = [...players];
    updatedPlayers[currentPlayerIndex].hand.push(selectedCard);
    setPlayers(updatedPlayers);

    setMessage(`${currentPlayer.character.name} chose ${selectedCard.type}!`);

    // Move to next player
    const nextPlayer = currentGeneralStorePlayer + 1;
    setCurrentGeneralStorePlayer(nextPlayer);

    // Check if all players have selected or no cards left
    if (nextPlayer >= generalStorePlayerOrder.length || remainingCards.length === 0) {
      // General Store selection complete
      setTimeout(() => {
        setShowGeneralStore(false);
        setGeneralStoreCards([]);
        setGeneralStorePlayerOrder([]);
        setCurrentGeneralStorePlayer(0);

        // Discard any remaining cards
        if (remainingCards.length > 0) {
          setDiscardPile(prev => [...prev, ...remainingCards]);
          setMessage(`General Store complete! All players chose their cards. ${remainingCards.length} card(s) discarded.`);
        } else {
          setMessage(`General Store complete! All cards were chosen by players.`);
        }
      }, 1000);
    } else {
      // Update message for next player after a short delay
      setTimeout(() => {
        const nextPlayerIndex = generalStorePlayerOrder[nextPlayer];
        const nextPlayerName = players[nextPlayerIndex].character.name;
        setMessage(`${nextPlayerName}'s turn to choose from General Store (${remainingCards.length} cards left).`);

        // Bot selection will be handled by useEffect
      }, 800);
    }
  };

  // Handle bot selection for General Store
  const handleBotGeneralStoreSelection = () => {
    // Validate state before proceeding
    if (!showGeneralStore ||
        generalStoreCards.length === 0 ||
        generalStorePlayerOrder.length === 0 ||
        currentGeneralStorePlayer >= generalStorePlayerOrder.length) {
      return;
    }

    const currentPlayerIndex = generalStorePlayerOrder[currentGeneralStorePlayer];
    const botPlayer = players[currentPlayerIndex];

    // Ensure it's actually a bot's turn
    if (!botPlayer || !botPlayer.isBot) {
      return;
    }

    // Bot chooses the highest priority card
    let bestCardIndex = 0;
    let bestPriority = getBotCardPriority(generalStoreCards[0], botPlayer, []);

    for (let i = 1; i < generalStoreCards.length; i++) {
      const priority = getBotCardPriority(generalStoreCards[i], botPlayer, []);
      if (priority > bestPriority) {
        bestPriority = priority;
        bestCardIndex = i;
      }
    }

    // Add a small delay to make the selection visible
    setTimeout(() => {
      handleGeneralStoreSelection(bestCardIndex);
    }, 300);
  };

  // Handle Saloon card
  const handleSaloonCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // All players regain 1 life point
    updatedPlayers.forEach(p => {
      if (p.isAlive && p.health < p.maxHealth) {
        p.health += 1;
      }
    });

    setMessage(`${player.name} played Saloon! All players regained 1 life point.`);
    setPlayers(updatedPlayers);
  };

  // Handle Equipment cards
  const handleEquipmentCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];

    // Check for duplicate equipment - cannot have same card type
    const existingCard = updatedPlayers[playerIndex].inPlay.findIndex(c => c.type === card.type);
    if (existingCard >= 0) {
      // Return card to hand and show error message
      updatedPlayers[playerIndex].hand.push(card);
      setPlayers(updatedPlayers);
      setMessage(`${player.name} already has ${card.type} equipped! Cannot have duplicate equipment.`);
      return;
    }

    // Remove existing equipment of same category for weapons only
    if (card.equipmentType === EQUIPMENT_TYPES.WEAPON) {
      const existingWeapon = updatedPlayers[playerIndex].inPlay.findIndex(c => c.equipmentType === EQUIPMENT_TYPES.WEAPON);
      if (existingWeapon >= 0) {
        const oldWeapon = updatedPlayers[playerIndex].inPlay.splice(existingWeapon, 1)[0];
        setDiscardPile([...discardPile, oldWeapon]);
        setMessage(`${player.name} replaced ${oldWeapon.type} with ${card.type}!`);
      } else {
        setMessage(`${player.name} equipped ${card.type}!`);
      }
    } else {
      // For non-weapon equipment, just add it
      if (card.equipmentType === EQUIPMENT_TYPES.GREEN) {
        setMessage(`${player.name} equipped ${card.type}! (Can be used starting next turn)`);
      } else {
        setMessage(`${player.name} equipped ${card.type}!`);
      }
    }

    // Track green-bordered cards played this turn (can't be used until next turn)
    if (card.equipmentType === EQUIPMENT_TYPES.GREEN) {
      setGreenCardsPlayedThisTurn(prev => new Set([...prev, `${playerIndex}-${card.type}`]));
    }

    // Add to in-play
    updatedPlayers[playerIndex].inPlay.push(card);
    setPlayers(updatedPlayers);
  };

  // Handle Jail card
  const handleJailCard = (playerIndex, cardIndex, targetPlayerIndex) => {
    const player = players[playerIndex];
    const targetPlayer = players[targetPlayerIndex];

    // Cannot jail the Sheriff
    if (targetPlayer.role === ROLES.SHERIFF) {
      setMessageWithPersistence("Cannot jail the Sheriff!", true, 4000);
      return;
    }

    // Cannot jail a player who is already in jail
    const isAlreadyInJail = targetPlayer.inPlay.some(card => card.type === CARD_TYPES.JAIL);
    if (isAlreadyInJail) {
      setMessageWithPersistence(`${targetPlayer.character.name} is already in jail! Cannot jail the same player twice.`, true, 4000);
      return;
    }

    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];

    // Add jail to target's in-play area
    updatedPlayers[targetPlayerIndex].inPlay.push(card);

    // Trigger jail visual effect
    triggerVisualEffect(targetPlayerIndex, 'jail-effect', 500);

    setMessage(`${player.character.name} put ${targetPlayer.character.name} in jail!`);
    setPlayers(updatedPlayers);
  };

  // Handle Dynamite card
  const handleDynamiteCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];

    // Add dynamite to player's in-play area
    updatedPlayers[playerIndex].inPlay.push(card);
    setMessage(`${player.character.name} placed Dynamite!`);
    setPlayers(updatedPlayers);
  };

  // Dodge City card handlers

  // Handle Punch card (like BANG! but only range 1 and doesn't count toward BANG! limit)
  const handlePunchCard = (playerIndex, cardIndex, targetPlayerIndex) => {
    // Check if target is at distance 1
    if (calculateDistance(playerIndex, targetPlayerIndex, players) !== 1) {
      setMessage("Punch can only target players at distance 1!");
      return;
    }

    // Use the same logic as BANG! card but don't count toward BANG! limit
    // Pass true for isSubstitution to prevent incrementing BANG! counter, and pass PUNCH card type for correct message
    handleBangCard(playerIndex, cardIndex, targetPlayerIndex, true, CARD_TYPES.PUNCH);
  };

  // Handle Brawl card (requires discarding another card)
  const handleBrawlCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];

    // Check if player has another card to discard
    if (player.hand.length < 2) {
      setMessage("You need at least 2 cards to play Brawl (Brawl + another card)!");
      return;
    }

    // If it's a bot, handle automatically
    if (player.isBot) {
      const updatedPlayers = [...players];
      const brawlCard = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];

      // Bot automatically discards lowest priority card
      const additionalCard = updatedPlayers[playerIndex].hand.splice(0, 1)[0];
      setDiscardPile([...discardPile, brawlCard, additionalCard]);

      // Force all OTHER players to discard a card from hand or play
      let totalDiscarded = 0;
      updatedPlayers.forEach((p, index) => {
        if (index !== playerIndex && p.isAlive) {
          // For bots, prioritize discarding from hand first, then equipment
          if (p.hand.length > 0) {
            const cardToDiscard = p.hand.splice(0, 1)[0];
            setDiscardPile(prev => [...prev, cardToDiscard]);
            totalDiscarded++;
          } else if (p.inPlay.length > 0) {
            const cardToDiscard = p.inPlay.splice(0, 1)[0];
            setDiscardPile(prev => [...prev, cardToDiscard]);
            totalDiscarded++;
          }
        }
      });

      setMessage(`${player.character.name} played Brawl! All other players discarded ${totalDiscarded} cards.`);
      setPlayers(updatedPlayers);
    } else {
      // For human players, show card selection modal
      const updatedPlayers = [...players];
      const brawlCard = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
      setPlayers(updatedPlayers);
      setDiscardPile([...discardPile, brawlCard]);

      // Show card selection for additional discard
      setCardSelectionData({
        type: 'BRAWL_ADDITIONAL_DISCARD',
        playerIndex: playerIndex,
        brawlCard: brawlCard,
        message: 'Choose an additional card to discard for Brawl:'
      });
      setShowCardSelection(true);
    }
  };

  // Handle BANG! attack from green cards (doesn't use a card from hand)
  const handleGreenCardBangAttack = (playerIndex, targetPlayerIndex, cardType) => {
    const player = players[playerIndex];
    const targetPlayer = players[targetPlayerIndex];

    // Check if target is in range (for Derringer, only range 1)
    if (cardType === CARD_TYPES.DERRINGER && calculateDistance(playerIndex, targetPlayerIndex, players) !== 1) {
      setMessage("Derringer can only target players at range 1!");
      return;
    }

    const updatedPlayers = [...players];

    // Target player must play a Missed! or lose a life point
    setMessage(`${player.name} used ${cardType} on ${targetPlayer.name}!`);

    // Check for barrel defense first
    let defended = false;
    if (canUseBarrel(targetPlayer) && targetPlayer.isBot) {
      // Bots automatically use barrel
      if (performBarrelCheck(targetPlayer)) {
        defended = true;
        setMessage(`${targetPlayer.character.name} defended with Barrel!`);
      }
    }

    // If not defended by barrel, check for defensive cards
    if (!defended) {
      const missedRequired = player.character.name === 'Slab the Killer' ? 2 : 1;
      let missedCount = 0;

      // Check for green defensive cards (same logic as regular BANG!)
      // Check for Bible card (acts like Missed! + draw 1)
      if (canUseGreenDefensiveCard(updatedPlayers[targetPlayerIndex], CARD_TYPES.BIBLE) && missedCount < missedRequired) {
        if (activateGreenDefensiveCard(targetPlayerIndex, CARD_TYPES.BIBLE)) {
          missedCount++;
          const drawResult = drawCards(deck, 1);
          if (drawResult.drawnCards.length > 0) {
            updatedPlayers[targetPlayerIndex].hand.push(...drawResult.drawnCards);
            setDeck(drawResult.updatedDeck);
          }
          setMessage(`${targetPlayer.name} used Bible to defend and drew 1 card!`);
        }
      }

      // Check for other green defensive cards...
      if (canUseGreenDefensiveCard(updatedPlayers[targetPlayerIndex], CARD_TYPES.SOMBRERO) && missedCount < missedRequired) {
        if (activateGreenDefensiveCard(targetPlayerIndex, CARD_TYPES.SOMBRERO)) {
          missedCount++;
          setMessage(`${targetPlayer.name} used Sombrero to defend!`);
        }
      }

      // Check for regular Missed! cards
      for (let i = missedCount; i < missedRequired; i++) {
        const missedIndex = updatedPlayers[targetPlayerIndex].hand.findIndex(c =>
          c.type === CARD_TYPES.MISSED ||
          c.type === CARD_TYPES.DODGE ||
          (updatedPlayers[targetPlayerIndex].character.name === 'Calamity Janet' && c.type === CARD_TYPES.BANG)
        );

        if (missedIndex >= 0) {
          const missedCard = updatedPlayers[targetPlayerIndex].hand.splice(missedIndex, 1)[0];
          setDiscardPile(prev => [...prev, missedCard]);
          missedCount++;

          if (missedCard.type === CARD_TYPES.DODGE) {
            const drawResult = drawCards(deck, 1);
            if (drawResult.drawnCards.length > 0) {
              updatedPlayers[targetPlayerIndex].hand.push(...drawResult.drawnCards);
              setDeck(drawResult.updatedDeck);
            }
            setMessage(`${targetPlayer.name} used Dodge to defend and drew 1 card!`);
          }
        } else {
          break;
        }
      }

      if (missedCount >= missedRequired) {
        defended = true;
        setMessage(`${targetPlayer.character.name} defended with ${missedCount} defensive card(s)`);
      }
    }

    if (!defended) {
      // Target loses a life point
      updatedPlayers[targetPlayerIndex].health -= 1;

      // Trigger damage abilities
      triggerCharacterAbility(updatedPlayers[targetPlayerIndex], 'ON_DAMAGE_TAKEN', { attackerIndex: playerIndex });

      if (updatedPlayers[targetPlayerIndex].health <= 0) {
        updatedPlayers[targetPlayerIndex].isAlive = false;
        triggerEliminationAbilities(updatedPlayers, targetPlayerIndex);
        playSound('dominating');
        setMessage(`${targetPlayer.character.name} was eliminated by ${cardType}!`);

        if (targetPlayerIndex === 0 && !updatedPlayers[0].isBot) {
          setShowDeathModal(true);
        }
        checkGameEnd(updatedPlayers);
      } else {
        playSound('godlike');
        setMessage(`${targetPlayer.character.name} lost a life point! Health: ${updatedPlayers[targetPlayerIndex].health}`);
      }
    }

    setPlayers(updatedPlayers);
  };

  // Handle green card activation with target selection
  const handleGreenCardWithTarget = (playerIndex, cardType, targetPlayerIndex) => {
    const player = players[playerIndex];

    // Check if it's the player's turn
    if (currentPlayerIndex !== playerIndex) {
      setMessage("You can only activate green cards on your turn!");
      return;
    }

    // Check if the card can be used
    if (!canUseGreenDefensiveCard(player, cardType)) {
      setMessage("This green card cannot be used right now!");
      return;
    }

    // Handle specific green card abilities with targets
    switch (cardType) {
      case CARD_TYPES.DERRINGER: {
        // Check if target is in range (for Derringer, only range 1)
        if (calculateDistance(playerIndex, targetPlayerIndex, players) !== 1) {
          setMessage("Derringer can only target players at range 1!");
          return;
        }

        // Activate the green card (remove from equipment)
        activateGreenDefensiveCard(playerIndex, cardType);

        // Perform BANG! attack
        handleGreenCardBangAttack(playerIndex, targetPlayerIndex, cardType);

        // Draw 1 card
        const drawResult = drawCards(deck, 1);
        if (drawResult.drawnCards.length > 0) {
          const updatedPlayers = [...players];
          updatedPlayers[playerIndex].hand.push(...drawResult.drawnCards);
          setPlayers(updatedPlayers);
          setDeck(drawResult.updatedDeck);
          setMessage(`${player.name} used Derringer and drew 1 card!`);
        }
        break;
      }

      default:
        setMessage(`${cardType} targeting not implemented yet!`);
        break;
    }
  };

  // Handle green card activation
  const handleGreenCardActivation = (playerIndex, cardType) => {
    const player = players[playerIndex];

    // Check if it's the player's turn
    if (currentPlayerIndex !== playerIndex) {
      setMessage("You can only activate green cards on your turn!");
      return;
    }

    // Check if the card can be used
    if (!canUseGreenDefensiveCard(player, cardType)) {
      setMessage("This green card cannot be used right now!");
      return;
    }

    // Handle specific green card abilities
    switch (cardType) {
      case CARD_TYPES.DERRINGER: {
        // Derringer: BANG! at range 1 + draw 1 card
        const targets = players
          .map((p, index) => ({ player: p, index }))
          .filter(({ player, index }) =>
            player.isAlive &&
            index !== playerIndex &&
            calculateDistance(playerIndex, index, players) === 1
          );

        if (targets.length === 0) {
          setMessage("No targets at range 1 for Derringer!");
          return;
        }

        // If only one target, attack directly
        if (targets.length === 1) {
          activateGreenDefensiveCard(playerIndex, cardType);

          // Perform BANG! attack without using a card from hand
          handleGreenCardBangAttack(playerIndex, targets[0].index, cardType);

          // Draw 1 card
          const drawResult = drawCards(deck, 1);
          if (drawResult.drawnCards.length > 0) {
            const updatedPlayers = [...players];
            updatedPlayers[playerIndex].hand.push(...drawResult.drawnCards);
            setPlayers(updatedPlayers);
            setDeck(drawResult.updatedDeck);
            setMessage(`${player.name} used Derringer and drew 1 card!`);
          }
        } else {
          // Multiple targets - show target selection
          setTargetingCard({ playerIndex, cardIndex: -1, card: { type: cardType } });
          setShowTargetSelection(true);
        }
        break;
      }

      case CARD_TYPES.HOWITZER: {
        // Howitzer: BANG! to all other players, doesn't count toward BANG! limit
        activateGreenDefensiveCard(playerIndex, cardType);

        // Use the same logic as regular BANG! attacks but for all players
        const targets = players
          .map((p, index) => ({ player: p, index }))
          .filter(({ player, index }) => index !== playerIndex && player.isAlive);

        // Process each target with full BANG! defense logic
        let totalDamage = 0;
        const updatedPlayers = [...players];

        for (const { player: targetPlayer, index: targetIndex } of targets) {
          // Use the green card BANG! attack logic for each target
          let defended = false;

          // Check for barrel defense first
          if (canUseBarrel(targetPlayer) && targetPlayer.isBot) {
            // Bots automatically use barrel
            if (performBarrelCheck(targetPlayer)) {
              defended = true;
              setMessage(`${targetPlayer.character.name} defended with Barrel against Howitzer!`);
              continue;
            }
          }

          // If not defended by barrel, check for defensive cards
          if (!defended) {
            const missedRequired = 1; // Howitzer is like regular BANG!, not Slab the Killer
            let missedCount = 0;

            // Check for green defensive cards
            if (canUseGreenDefensiveCard(updatedPlayers[targetIndex], CARD_TYPES.BIBLE) && missedCount < missedRequired) {
              if (activateGreenDefensiveCard(targetIndex, CARD_TYPES.BIBLE)) {
                missedCount++;
                const drawResult = drawCards(deck, 1);
                if (drawResult.drawnCards.length > 0) {
                  updatedPlayers[targetIndex].hand.push(...drawResult.drawnCards);
                  setDeck(drawResult.updatedDeck);
                }
                setMessage(`${targetPlayer.character.name} used Bible to defend against Howitzer and drew 1 card!`);
              }
            }

            // Check for other green defensive cards
            if (canUseGreenDefensiveCard(updatedPlayers[targetIndex], CARD_TYPES.SOMBRERO) && missedCount < missedRequired) {
              if (activateGreenDefensiveCard(targetIndex, CARD_TYPES.SOMBRERO)) {
                missedCount++;
                setMessage(`${targetPlayer.character.name} used Sombrero to defend against Howitzer!`);
              }
            }

            if (canUseGreenDefensiveCard(updatedPlayers[targetIndex], CARD_TYPES.IRON_PLATE) && missedCount < missedRequired) {
              if (activateGreenDefensiveCard(targetIndex, CARD_TYPES.IRON_PLATE)) {
                missedCount++;
                setMessage(`${targetPlayer.character.name} used Iron Plate to defend against Howitzer!`);
              }
            }

            if (canUseGreenDefensiveCard(updatedPlayers[targetIndex], CARD_TYPES.TEN_GALLON_HAT) && missedCount < missedRequired) {
              if (activateGreenDefensiveCard(targetIndex, CARD_TYPES.TEN_GALLON_HAT)) {
                missedCount++;
                setMessage(`${targetPlayer.character.name} used Ten Gallon Hat to defend against Howitzer!`);
              }
            }

            // Check for regular Missed! cards (bots only for now - human interaction would need more complex logic)
            if (targetPlayer.isBot && missedCount < missedRequired) {
              const missedIndex = updatedPlayers[targetIndex].hand.findIndex(c =>
                c.type === CARD_TYPES.MISSED ||
                c.type === CARD_TYPES.DODGE ||
                (updatedPlayers[targetIndex].character.name === 'Calamity Janet' && c.type === CARD_TYPES.BANG)
              );

              if (missedIndex >= 0) {
                const missedCard = updatedPlayers[targetIndex].hand.splice(missedIndex, 1)[0];
                setDiscardPile(prev => [...prev, missedCard]);
                missedCount++;

                if (missedCard.type === CARD_TYPES.DODGE) {
                  const drawResult = drawCards(deck, 1);
                  if (drawResult.drawnCards.length > 0) {
                    updatedPlayers[targetIndex].hand.push(...drawResult.drawnCards);
                    setDeck(drawResult.updatedDeck);
                  }
                  setMessage(`${targetPlayer.character.name} used Dodge to defend against Howitzer and drew 1 card!`);
                } else {
                  setMessage(`${targetPlayer.character.name} used Missed! to defend against Howitzer!`);
                }
              }
            }

            if (missedCount >= missedRequired) {
              defended = true;
            }
          }

          // If not defended, take damage
          if (!defended) {
            updatedPlayers[targetIndex].health -= 1;
            totalDamage++;

            // Trigger damage abilities
            triggerCharacterAbility(updatedPlayers[targetIndex], 'ON_DAMAGE_TAKEN', { attackerIndex: playerIndex });

            if (updatedPlayers[targetIndex].health <= 0) {
              updatedPlayers[targetIndex].isAlive = false;
              triggerEliminationAbilities(updatedPlayers, targetIndex);
              playSound('dominating');
              setMessage(`${targetPlayer.character.name} was eliminated by Howitzer!`);

              if (targetIndex === 0 && !updatedPlayers[0].isBot) {
                setShowDeathModal(true);
              }
              checkGameEnd(updatedPlayers);
            } else {
              playSound('godlike');
              setMessage(`${targetPlayer.character.name} lost a life point to Howitzer! Health: ${updatedPlayers[targetIndex].health}`);
            }
          }
        }

        setPlayers(updatedPlayers);
        setMessage(`${player.name} used Howitzer! ${totalDamage} players took damage.`);
        break;
      }

      case CARD_TYPES.PONY_EXPRESS: {
        // Pony Express: Draw 3 cards from deck
        activateGreenDefensiveCard(playerIndex, cardType);

        const drawResult = drawCards(deck, 3);
        if (drawResult.drawnCards.length > 0) {
          const updatedPlayers = [...players];
          updatedPlayers[playerIndex].hand.push(...drawResult.drawnCards);
          setPlayers(updatedPlayers);
          setDeck(drawResult.updatedDeck);
          setMessage(`${player.name} used Pony Express and drew ${drawResult.drawnCards.length} cards!`);
        } else {
          setMessage(`${player.name} used Pony Express but no cards were available to draw!`);
        }
        break;
      }

      default:
        setMessage(`${cardType} activation not implemented yet!`);
        break;
    }
  };

  // Handle card selection for special effects
  const handleCardSelectionChoice = (selectedCardIndex) => {
    if (!cardSelectionData) return;

    const { type, playerIndex } = cardSelectionData;
    const updatedPlayers = [...players];

    if (type === 'BRAWL_ADDITIONAL_DISCARD') {
      // Remove the selected additional card
      const additionalCard = updatedPlayers[playerIndex].hand.splice(selectedCardIndex, 1)[0];
      setDiscardPile(prev => [...prev, additionalCard]);

      // Force all OTHER players to discard a card from hand or play
      let totalDiscarded = 0;
      updatedPlayers.forEach((p, index) => {
        if (index !== playerIndex && p.isAlive) {
          // For bots, prioritize discarding from hand first, then equipment
          if (p.hand.length > 0) {
            const cardToDiscard = p.hand.splice(0, 1)[0];
            setDiscardPile(prev => [...prev, cardToDiscard]);
            totalDiscarded++;
          } else if (p.inPlay.length > 0) {
            const cardToDiscard = p.inPlay.splice(0, 1)[0];
            setDiscardPile(prev => [...prev, cardToDiscard]);
            totalDiscarded++;
          }
        }
      });

      setMessage(`${players[playerIndex].character.name} played Brawl! All other players discarded ${totalDiscarded} cards.`);
    }

    setPlayers(updatedPlayers);
    setShowCardSelection(false);
    setCardSelectionData(null);
  };

  // Handle Rag Time card
  const handleRagTimeCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];
    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // All players draw 1 card, then discard 1 card
    let newDeck = [...deck];
    updatedPlayers.forEach((p) => {
      if (p.isAlive && newDeck.length > 0) {
        const drawnCard = newDeck.shift();
        p.hand.push(drawnCard);

        // Then discard 1 card (bots discard lowest priority)
        if (p.isBot && p.hand.length > 0) {
          const cardToDiscard = p.hand.reduce((lowest, current, idx) =>
            getBotCardPriority(current, p, []) < getBotCardPriority(lowest.card, p, [])
              ? { card: current, index: idx }
              : lowest,
            { card: p.hand[0], index: 0 }
          );
          const discardedCard = p.hand.splice(cardToDiscard.index, 1)[0];
          setDiscardPile(prev => [...prev, discardedCard]);
        }
      }
    });

    setDeck(newDeck);
    setMessage(`${player.character.name} played Rag Time! All players drew and discarded 1 card.`);
    setPlayers(updatedPlayers);
  };

  // Handle Tequila card (requires discarding another card and can target any player)
  const handleTequilaCard = (playerIndex, cardIndex, targetPlayerIndex = null) => {
    const player = players[playerIndex];

    // Check if player has another card to discard
    if (player.hand.length < 2) {
      setMessage("You need at least 2 cards to play Tequila (Tequila + another card)!");
      return;
    }

    // If no target specified, target self
    const actualTargetIndex = targetPlayerIndex !== null ? targetPlayerIndex : playerIndex;
    const targetPlayer = players[actualTargetIndex];

    // Check if target is at maximum health
    if (targetPlayer.health >= targetPlayer.character.life) {
      setMessage(`${targetPlayer.character.name} is already at maximum health!`);
      return;
    }

    // If it's a bot, automatically discard the lowest priority card
    if (player.isBot) {
      const updatedPlayers = [...players];
      const tequilaCard = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];

      // Bot automatically discards lowest priority card
      const cardPriorities = updatedPlayers[playerIndex].hand.map((card, index) => ({
        card,
        index,
        priority: getBotCardPriority(card, player, [])
      }));
      cardPriorities.sort((a, b) => a.priority - b.priority);

      const additionalCard = updatedPlayers[playerIndex].hand.splice(cardPriorities[0].index, 1)[0];
      setDiscardPile([...discardPile, tequilaCard, additionalCard]);

      // Target player regains 1 life point
      updatedPlayers[actualTargetIndex].health = Math.min(updatedPlayers[actualTargetIndex].health + 1, updatedPlayers[actualTargetIndex].character.life);

      // Trigger healing visual effects
      triggerVisualEffect(actualTargetIndex, 'heal-effect', 1000);
      createFloatingNumber(actualTargetIndex, 1, 'heal');

      const targetName = actualTargetIndex === playerIndex ? "themselves" : targetPlayer.character.name;
      setMessage(`${player.character.name} played Tequila on ${targetName} who regained 1 life! Health: ${updatedPlayers[actualTargetIndex].health}`);
      setPlayers(updatedPlayers);
    } else {
      // Human player - show card selection modal
      setTequilaCardIndex(cardIndex);
      setTequilaTargetIndex(actualTargetIndex);
      setShowTequilaCardSelection(true);
      setMessage("Choose a card to discard along with Tequila:");
    }
  };

  // Handle Tequila card selection confirmation
  const confirmTequilaCardSelection = (selectedCardIndex) => {
    const player = players[currentPlayerIndex];
    const targetPlayer = players[tequilaTargetIndex];

    const updatedPlayers = [...players];

    // Remove Tequila card from hand
    const tequilaCard = updatedPlayers[currentPlayerIndex].hand.splice(tequilaCardIndex, 1)[0];

    // Remove selected additional card from hand (adjust index if needed)
    const adjustedIndex = selectedCardIndex > tequilaCardIndex ? selectedCardIndex - 1 : selectedCardIndex;
    const additionalCard = updatedPlayers[currentPlayerIndex].hand.splice(adjustedIndex, 1)[0];

    // Add both cards to discard pile
    setDiscardPile([...discardPile, tequilaCard, additionalCard]);

    // Target player regains 1 life point
    updatedPlayers[tequilaTargetIndex].health = Math.min(
      updatedPlayers[tequilaTargetIndex].health + 1,
      updatedPlayers[tequilaTargetIndex].character.life
    );

    // Trigger healing visual effects
    triggerVisualEffect(tequilaTargetIndex, 'heal-effect', 1000);
    createFloatingNumber(tequilaTargetIndex, 1, 'heal');

    const targetName = tequilaTargetIndex === currentPlayerIndex ? "themselves" : targetPlayer.character.name;
    setMessage(`${player.character.name} played Tequila on ${targetName} who regained 1 life! Health: ${updatedPlayers[tequilaTargetIndex].health}`);

    setPlayers(updatedPlayers);

    // Close the selection modal
    setShowTequilaCardSelection(false);
    setTequilaCardIndex(null);
    setTequilaTargetIndex(null);
  };

  // Handle Whisky card
  const handleWhiskyCard = (playerIndex, cardIndex) => {
    const player = players[playerIndex];

    // Check if player is at maximum health
    if (player.health >= player.character.life) {
      setMessage(`${player.character.name} is already at maximum health!`);
      return;
    }

    const updatedPlayers = [...players];
    const card = updatedPlayers[playerIndex].hand.splice(cardIndex, 1)[0];
    setDiscardPile([...discardPile, card]);

    // Regain 2 life points
    const healAmount = Math.min(2, updatedPlayers[playerIndex].character.life - updatedPlayers[playerIndex].health);
    updatedPlayers[playerIndex].health += healAmount;

    // Trigger healing visual effects
    triggerVisualEffect(playerIndex, 'heal-effect', 1000);
    createFloatingNumber(playerIndex, healAmount, 'heal');

    setMessage(`${player.character.name} drank Whisky and regained ${healAmount} life! Health: ${updatedPlayers[playerIndex].health}`);
    setPlayers(updatedPlayers);
  };

  // Complete the end turn process
  const finishEndTurn = useCallback(() => {
    // Stop the turn timer when turn actually ends
    stopTurnTimer();

    const currentPlayer = players[currentPlayerIndex];
    const updatedPlayers = [...players];

    // Suzy Lafayette draws a card if she has no cards at end of turn
    if (currentPlayer.character.name === 'Suzy Lafayette' && updatedPlayers[currentPlayerIndex].hand.length === 0) {
      if (deck.length > 0) {
        const bonusCard = deck[0];
        updatedPlayers[currentPlayerIndex].hand.push(bonusCard);
        setDeck(deck.slice(1));
        setMessage(`${currentPlayer.character.name} drew a card at end of turn (Suzy Lafayette ability)!`);
      }
    }

    setPlayers(updatedPlayers);

    let nextPlayerIndex = (currentPlayerIndex + 1) % updatedPlayers.length;

    // Skip dead players
    while (!updatedPlayers[nextPlayerIndex].isAlive) {
      nextPlayerIndex = (nextPlayerIndex + 1) % updatedPlayers.length;
    }

    setCurrentPlayerIndex(nextPlayerIndex);

    // Reset turn state for next player
    setTurnPhase('draw');
    setHasDrawnCards(false);
    setIsDrawingCards(false);
    setTurnId(prev => prev + 1); // Increment turn ID for new turn
    setBangCardsPlayedThisTurn(0); // Reset BANG! counter for new turn
    setGreenCardsPlayedThisTurn(new Set()); // Reset green cards tracking for new turn

    // Trigger turn start visual effect for the new current player
    setTimeout(() => {
      triggerVisualEffect(nextPlayerIndex, 'turn-start-effect', 1500);
    }, 100);

    setMessage(`${updatedPlayers[nextPlayerIndex].name}'s turn - Cards will be drawn automatically`);

    // Play turn start sound
    playSound('turnStart');
  }, [players, currentPlayerIndex, deck, setDeck, setPlayers, setCurrentPlayerIndex, setTurnPhase, setHasDrawnCards, setBangCardsPlayedThisTurn, setMessage, triggerVisualEffect, stopTurnTimer, playSound]);

  // Handle bot discard logic
  const handleBotDiscard = useCallback((playerIndex, numToDiscard) => {
    const updatedPlayers = [...players];
    const botPlayer = updatedPlayers[playerIndex];

    // Create priority list for discarding (lowest priority first)
    const cardPriorities = botPlayer.hand.map((card, index) => ({
      card,
      index,
      priority: getBotCardPriority(card, botPlayer, [])
    }));

    // Sort by priority (lowest first for discarding)
    cardPriorities.sort((a, b) => a.priority - b.priority);

    // Discard the lowest priority cards
    const discardedCards = [];
    for (let i = 0; i < numToDiscard; i++) {
      const cardToDiscard = cardPriorities[i];
      const discardedCard = updatedPlayers[playerIndex].hand.splice(cardToDiscard.index - i, 1)[0];
      discardedCards.push(discardedCard);
      setDiscardPile(prev => [...prev, discardedCard]);
    }

    setPlayers(updatedPlayers);
    setMessage(`${botPlayer.name} discarded ${numToDiscard} card${numToDiscard > 1 ? 's' : ''} to hand limit: ${discardedCards.map(c => c.type).join(', ')}`);

    // Continue with end turn
    setTimeout(() => finishEndTurn(), 1000);
  }, [players, setDiscardPile, setPlayers, setMessage, finishEndTurn]);

  // End turn and move to next player
  const endTurn = useCallback(() => {
    // Check if player has drawn cards first
    if (!hasDrawnCards) {
      setMessage("You must draw cards before ending your turn!");
      return;
    }

    const currentPlayer = players[currentPlayerIndex];
    const updatedPlayers = [...players];

    // Phase 3: Discard phase - check hand limit
    let handLimit = currentPlayer.health; // In BANG!, hand limit equals current life points

    // Sean Mallory can hold up to 10 cards
    if (currentPlayer.character.name === 'Sean Mallory') {
      handLimit = Math.max(handLimit, 10);
    }

    const excessCards = updatedPlayers[currentPlayerIndex].hand.length - handLimit;

    if (excessCards > 0) {
      if (currentPlayer.isBot) {
        // Bot automatically discards lowest priority cards
        handleBotDiscard(currentPlayerIndex, excessCards);
        return; // Bot discard function will continue the turn
      } else {
        // Human player must choose cards to discard
        setCardsToDiscard(excessCards);
        setSelectedDiscardCards([]);
        setShowDiscardSelection(true);
        setMessage(`You must discard ${excessCards} card${excessCards > 1 ? 's' : ''} (hand limit: ${handLimit})`);
        return; // Wait for player to select cards
      }
    }

    // Continue with end turn if no cards need to be discarded
    finishEndTurn();
  }, [hasDrawnCards, players, currentPlayerIndex, handleBotDiscard, finishEndTurn, setMessage, setCardsToDiscard, setSelectedDiscardCards, setShowDiscardSelection]);

  // Handle human player discard selection
  const handleDiscardSelection = (cardIndex) => {
    const newSelected = [...selectedDiscardCards];
    const cardIndexInSelected = newSelected.indexOf(cardIndex);

    if (cardIndexInSelected >= 0) {
      // Deselect card
      newSelected.splice(cardIndexInSelected, 1);
    } else if (newSelected.length < cardsToDiscard) {
      // Select card
      newSelected.push(cardIndex);
    }

    setSelectedDiscardCards(newSelected);
  };

  // Confirm discard selection
  const confirmDiscardSelection = () => {
    if (selectedDiscardCards.length !== cardsToDiscard) {
      setMessage(`Please select exactly ${cardsToDiscard} card${cardsToDiscard > 1 ? 's' : ''} to discard.`);
      return;
    }

    const updatedPlayers = [...players];
    const discardedCards = [];

    // Sort indices in descending order to avoid index shifting issues
    const sortedIndices = [...selectedDiscardCards].sort((a, b) => b - a);

    sortedIndices.forEach(cardIndex => {
      const discardedCard = updatedPlayers[currentPlayerIndex].hand.splice(cardIndex, 1)[0];
      discardedCards.push(discardedCard);
      setDiscardPile(prev => [...prev, discardedCard]);
    });

    setPlayers(updatedPlayers);
    setShowDiscardSelection(false);
    setSelectedDiscardCards([]);
    setCardsToDiscard(0);

    setMessage(`You discarded ${discardedCards.length} card${discardedCards.length > 1 ? 's' : ''}: ${discardedCards.map(c => c.type).join(', ')}`);

    // Continue with end turn
    setTimeout(() => finishEndTurn(), 1000);
  };

  // Check if game has ended
  const checkGameEnd = (updatedPlayers) => {
    const sheriff = updatedPlayers.find(p => p.role === ROLES.SHERIFF);
    const outlaws = updatedPlayers.filter(p => p.role === ROLES.OUTLAW && p.isAlive);
    const deputies = updatedPlayers.filter(p => p.role === ROLES.DEPUTY && p.isAlive);
    const renegades = updatedPlayers.filter(p => p.role === ROLES.RENEGADE && p.isAlive);

    // Check if the human player (index 0) is alive and what their role is
    const humanPlayer = updatedPlayers[0];
    const humanPlayerWins = humanPlayer && !humanPlayer.isBot && humanPlayer.isAlive;

    if (!sheriff.isAlive) {
      // Sheriff is dead
      if (renegades.length === 1 && outlaws.length === 0 && deputies.length === 0) {
        // Renegade wins
        setGameState('ended');
        setMessage('Game over! The Renegade wins!');

        // Determine if human player won or lost
        if (humanPlayerWins && humanPlayer.role === ROLES.RENEGADE) {
          setGameResult('won');
          playSound('unstoppable');
        } else {
          setGameResult('lost');
        }
      } else {
        // Outlaws win
        setGameState('ended');
        setMessage('Game over! The Outlaws win!');

        // Determine if human player won or lost
        if (humanPlayerWins && humanPlayer.role === ROLES.OUTLAW) {
          setGameResult('won');
          playSound('unstoppable');
        } else {
          setGameResult('lost');
        }
      }
    } else if (outlaws.length === 0 && renegades.length === 0) {
      // Sheriff and possibly Deputies win
      setGameState('ended');
      setMessage('Game over! The Sheriff and Deputies win!');

      // Determine if human player won or lost
      if (humanPlayerWins && (humanPlayer.role === ROLES.SHERIFF || humanPlayer.role === ROLES.DEPUTY)) {
        setGameResult('won');
        playSound('unstoppable');
      } else {
        setGameResult('lost');
      }
    }
  };

  // Handle ability choice responses
  const handleAbilityChoice = (useAbility) => {
    setShowAbilityChoice(false);

    if (abilityChoice.type === 'BARREL_DEFENSE') {
      const targetPlayer = players[abilityChoice.playerIndex];
      let defended = false;

      if (useAbility && performBarrelCheck(targetPlayer)) {
        defended = true;
        setMessage(`${targetPlayer.character.name} defended with Barrel!`);
        playSound('defense');
      } else if (useAbility) {
        setMessage(`${targetPlayer.character.name} tried to use Barrel but failed!`);
      }

      // Continue with the attack resolution
      continueAttackAfterBarrel(defended);
    }

    setAbilityChoice(null);
    setPendingAttack(null);
  };

  // Handle Missed! card choice responses
  const handleMissedChoice = (useMissed) => {
    setShowMissedChoice(false);

    if (missedChoice.type === 'MISSED_DEFENSE' && pendingMissedAttack) {
      const { attackerIndex, targetIndex, missedRequired, updatedPlayers } = pendingMissedAttack;
      const targetPlayer = players[targetIndex];
      let defended = false;

      if (useMissed) {
        // Player chose to use Missed! cards
        let missedCount = 0;
        for (let i = 0; i < missedRequired; i++) {
          const missedIndex = updatedPlayers[targetIndex].hand.findIndex(c =>
            c.type === CARD_TYPES.MISSED ||
            (targetPlayer.character.name === 'Calamity Janet' && c.type === CARD_TYPES.BANG)
          );

          if (missedIndex >= 0) {
            const missedCard = updatedPlayers[targetIndex].hand.splice(missedIndex, 1)[0];
            setDiscardPile(prev => [...prev, missedCard]);
            missedCount++;
          } else {
            break;
          }
        }

        if (missedCount >= missedRequired) {
          defended = true;
          setMessage(`${targetPlayer.character.name} defended with ${missedCount} Missed! card(s)`);
          playSound('defense');
        }
      } else {
        setMessage(`${targetPlayer.character.name} chose not to use Missed! cards`);
      }

      // Update players state with any discarded Missed! cards
      setPlayers(updatedPlayers);

      // Continue with attack resolution
      continueMissedAttackResolution(defended, attackerIndex, targetIndex);
    }

    setMissedChoice(null);
    setPendingMissedAttack(null);
  };

  // Game menu functions
  const handleMenuToggle = () => {
    setShowGameMenu(!showGameMenu);
  };

  const handleReturnToMenu = () => {
    setGameState('setup');
    setGameResult(null);
    setShowGameMenu(false);
    // Reset all game state
    setPlayers([]);
    setDeck([]);
    setDiscardPile([]);
    setCurrentPlayerIndex(0);
    setMessage('Welcome to BANG!');
    setShowDeathModal(false);
    setShowGeneralStore(false);
    setShowAbilityChoice(false);
    setShowMissedChoice(false);
  };

  const handleRestartGame = () => {
    setShowGameMenu(false);
    setGameResult(null);
    // Restart with same players
    const currentPlayers = players.length;
    const humanPlayers = players.filter(p => !p.isBot).length;
    const humanNames = players.filter(p => !p.isBot).map(p => p.name);

    // Reset to setup and auto-start with same configuration
    setGameState('setup');
    setTimeout(() => {
      startGame(currentPlayers, humanPlayers, humanNames);
    }, 100);
  };

  const handleToggleSound = () => {
    setSoundEnabled(!soundEnabled);
    if (!soundEnabled) {
      // Play a test sound when enabling
      playSound('click');
    }
  };

  // Continue attack resolution after Missed! card choice
  const continueMissedAttackResolution = (defended, attackerIndex, targetIndex) => {
    // eslint-disable-next-line no-unused-vars
    const player = players[attackerIndex];
    const targetPlayer = players[targetIndex];
    const updatedPlayers = [...players];

    if (!defended) {
      // Target loses a life point
      updatedPlayers[targetIndex].health -= 1;

      // Trigger visual effects
      triggerVisualEffect(targetIndex, 'damage-effect', 1500);
      createFloatingNumber(targetIndex, 1, 'damage');

      // Trigger character abilities on damage
      triggerCharacterAbility(updatedPlayers[targetIndex], 'ON_DAMAGE_TAKEN', { attackerIndex });

      if (updatedPlayers[targetIndex].health <= 0) {
        updatedPlayers[targetIndex].isAlive = false;

        // Trigger elimination abilities
        triggerEliminationAbilities(updatedPlayers, targetIndex);

        setMessage(`${targetPlayer.character.name} (${targetPlayer.role}) was eliminated!`);

        // Play epic elimination sound - DOMINATING!
        playSound('dominating');

        if (targetIndex === 0 && !updatedPlayers[0].isBot) {
          setShowDeathModal(true);
        }

        checkGameEnd(updatedPlayers);
      } else {
        // Play epic damage sound - GODLIKE! (only for non-fatal damage)
        playSound('godlike');
        setMessage(`${targetPlayer.character.name} lost a life point! Health: ${updatedPlayers[targetIndex].health}`);
      }
    } else {
      // Trigger defense visual effects
      triggerVisualEffect(targetIndex, 'dodge-effect', 800);
    }

    setPlayers(updatedPlayers);
  };

  // Continue attack resolution after barrel choice
  const continueAttackAfterBarrel = (defended) => {
    if (!pendingAttack) return;

    // eslint-disable-next-line no-unused-vars
    const { attackerIndex, targetIndex, isSubstitution } = pendingAttack;
    const player = players[attackerIndex];
    const targetPlayer = players[targetIndex];
    const updatedPlayers = [...players];

    // Note: Card has already been removed and discarded in handleBangCard
    // No need to remove it again here

    // If not defended by barrel, check for Missed! card
    if (!defended) {
      const missedRequired = player.character.name === 'Slab the Killer' ? 2 : 1;

      // Check if target has Missed! cards (or BANG! for Calamity Janet)
      const availableMissedCards = updatedPlayers[targetIndex].hand.filter(c =>
        c.type === CARD_TYPES.MISSED ||
        (targetPlayer.character.name === 'Calamity Janet' && c.type === CARD_TYPES.BANG)
      );

      if (availableMissedCards.length >= missedRequired) {
        // If target is human player, ask if they want to use Missed! cards
        if (!targetPlayer.isBot) {
          setPendingMissedAttack({
            attackerIndex,
            targetIndex,
            missedRequired,
            availableMissedCards: availableMissedCards.length,
            updatedPlayers
          });
          setMissedChoice({
            type: 'MISSED_DEFENSE',
            playerIndex: targetIndex,
            missedRequired,
            availableMissedCards: availableMissedCards.length,
            message: `${targetPlayer.character.name}, do you want to use ${missedRequired} Missed! card${missedRequired > 1 ? 's' : ''} to defend?`
          });
          setShowMissedChoice(true);
          return; // Wait for player choice
        } else {
          // Bots automatically use Missed! cards
          let missedCount = 0;
          for (let i = 0; i < missedRequired; i++) {
            const missedIndex = updatedPlayers[targetIndex].hand.findIndex(c =>
              c.type === CARD_TYPES.MISSED ||
              (targetPlayer.character.name === 'Calamity Janet' && c.type === CARD_TYPES.BANG)
            );

            if (missedIndex >= 0) {
              const missedCard = updatedPlayers[targetIndex].hand.splice(missedIndex, 1)[0];
              setDiscardPile(prev => [...prev, missedCard]);
              missedCount++;
            } else {
              break;
            }
          }

          if (missedCount >= missedRequired) {
            defended = true;
            setMessage(`${targetPlayer.character.name} defended with ${missedCount} Missed! card(s)`);
          }
        }
      }
    }

    if (!defended) {
      // Target loses a life point
      updatedPlayers[targetIndex].health -= 1;

      // Trigger visual effects
      triggerVisualEffect(targetIndex, 'damage-effect', 1500);
      createFloatingNumber(targetIndex, 1, 'damage');

      // Trigger character abilities on damage
      triggerCharacterAbility(updatedPlayers[targetIndex], 'ON_DAMAGE_TAKEN', { attackerIndex });

      if (updatedPlayers[targetIndex].health <= 0) {
        updatedPlayers[targetIndex].isAlive = false;

        // Trigger elimination abilities
        triggerEliminationAbilities(updatedPlayers, targetIndex);

        setMessage(`${targetPlayer.character.name} (${targetPlayer.role}) was eliminated!`);

        // Play epic elimination sound - DOMINATING!
        playSound('dominating');

        if (targetIndex === 0 && !updatedPlayers[0].isBot) {
          setShowDeathModal(true);
        }

        checkGameEnd(updatedPlayers);
      } else {
        // Play epic damage sound - GODLIKE! (only for non-fatal damage)
        playSound('godlike');
        setMessage(`${targetPlayer.character.name} lost a life point! Health: ${updatedPlayers[targetIndex].health}`);
      }
    } else {
      // Trigger defense visual effects
      if (canUseBarrel(targetPlayer)) {
        triggerVisualEffect(targetIndex, 'barrel-defense-effect', 1000);
      } else {
        triggerVisualEffect(targetIndex, 'dodge-effect', 800);
      }
    }

    setPlayers(updatedPlayers);
  };

  // Handle Sid Ketchum's ability
  const handleSidKetchumAbility = () => {
    const currentPlayer = players[currentPlayerIndex];
    if (currentPlayer.hand.length < 2) {
      setMessage("Need at least 2 cards to use Sid Ketchum's ability!");
      return;
    }

    if (currentPlayer.health >= currentPlayer.maxHealth) {
      setMessageWithPersistence("Already at maximum health!", true, 4000);
      return;
    }

    const updatedPlayers = [...players];

    // Discard 2 random cards
    for (let i = 0; i < 2; i++) {
      const randomIndex = Math.floor(Math.random() * updatedPlayers[currentPlayerIndex].hand.length);
      const discardedCard = updatedPlayers[currentPlayerIndex].hand.splice(randomIndex, 1)[0];
      setDiscardPile([...discardPile, discardedCard]);
    }

    // Gain 1 life point
    updatedPlayers[currentPlayerIndex].health += 1;

    // Trigger healing visual effects
    triggerVisualEffect(currentPlayerIndex, 'healing-effect', 2000);
    createFloatingNumber(currentPlayerIndex, 1, 'heal');

    setPlayers(updatedPlayers);
    setMessage(`${currentPlayer.character.name} used Sid Ketchum's ability to gain 1 life point!`);
  };

  // AI Bot Logic
  const getBotTargets = (botIndex, botRole) => {
    const alivePlayers = players.filter((p, i) => p.isAlive && i !== botIndex);

    switch (botRole) {
      case ROLES.SHERIFF:
      case ROLES.DEPUTY:
        // Target outlaws first, then renegades
        return alivePlayers.filter(p => p.role === ROLES.OUTLAW || p.role === ROLES.RENEGADE);
      case ROLES.OUTLAW:
        // Target sheriff first, then deputies
        return alivePlayers.filter(p => p.role === ROLES.SHERIFF || p.role === ROLES.DEPUTY);
      case ROLES.RENEGADE:
        // Target anyone except other renegades when multiple players alive
        if (alivePlayers.length > 2) {
          return alivePlayers.filter(p => p.role !== ROLES.RENEGADE);
        } else {
          return alivePlayers; // Target anyone when few players left
        }
      default:
        return alivePlayers;
    }
  };

  const getBotCardPriority = (card, botPlayer, targets) => {
    let priority = 0;

    // Check if bot already has this exact equipment card - if so, priority is 0
    if ([CARD_TYPES.BARREL, CARD_TYPES.MUSTANG, CARD_TYPES.SCOPE, CARD_TYPES.VOLCANIC,
         CARD_TYPES.SCHOFIELD, CARD_TYPES.REMINGTON, CARD_TYPES.REV_CARABINE,
         CARD_TYPES.WINCHESTER, CARD_TYPES.JAIL, CARD_TYPES.DYNAMITE].includes(card.type)) {
      if (botPlayer.inPlay.some(c => c.type === card.type)) {
        return 0; // Cannot play duplicate equipment
      }
    }

    switch (card.type) {
      case CARD_TYPES.BANG:
        if (targets.length > 0 && canPlayBang(botPlayer)) {
          priority = 8;
        } else if (!canPlayBang(botPlayer)) {
          priority = 0; // Cannot play BANG! if limit reached
        }
        break;
      case CARD_TYPES.BEER:
        if (botPlayer.health < botPlayer.maxHealth) priority = 9;
        break;
      case CARD_TYPES.MISSED:
        priority = 2; // Keep for defense
        break;
      case CARD_TYPES.BARREL:
      case CARD_TYPES.MUSTANG:
      case CARD_TYPES.SCOPE:
        // Already checked for duplicates above
        priority = 6;
        break;
      case CARD_TYPES.VOLCANIC:
      case CARD_TYPES.SCHOFIELD:
      case CARD_TYPES.REMINGTON:
      case CARD_TYPES.REV_CARABINE:
      case CARD_TYPES.WINCHESTER:
        // Check if bot already has any weapon (can replace)
        if (!botPlayer.inPlay.some(c => [CARD_TYPES.VOLCANIC, CARD_TYPES.SCHOFIELD, CARD_TYPES.REMINGTON, CARD_TYPES.REV_CARABINE, CARD_TYPES.WINCHESTER].includes(c.type))) {
          priority = 7; // High priority if no weapon
        } else {
          // Compare weapon ranges to decide if upgrade is worth it
          const currentWeapon = botPlayer.inPlay.find(c => [CARD_TYPES.VOLCANIC, CARD_TYPES.SCHOFIELD, CARD_TYPES.REMINGTON, CARD_TYPES.REV_CARABINE, CARD_TYPES.WINCHESTER].includes(c.type));
          const currentRange = WEAPON_RANGES[currentWeapon.type] || 1;
          const newRange = WEAPON_RANGES[card.type] || 1;
          if (newRange > currentRange) {
            priority = 6; // Medium priority for weapon upgrade
          } else {
            priority = 1; // Low priority for weapon downgrade
          }
        }
        break;
      case CARD_TYPES.GATLING:
        if (targets.length >= 2) priority = 10;
        break;
      case CARD_TYPES.INDIANS:
        if (targets.length >= 2) priority = 9;
        break;
      case CARD_TYPES.DUEL:
        if (targets.length > 0) priority = 7;
        break;
      case CARD_TYPES.PANIC:
      case CARD_TYPES.CAT_BALOU:
        if (targets.length > 0) priority = 6;
        break;
      case CARD_TYPES.STAGECOACH:
      case CARD_TYPES.WELLS_FARGO:
        priority = 6; // Increased priority for card draw
        break;
      case CARD_TYPES.SALOON:
        if (botPlayer.health < botPlayer.maxHealth) priority = 8;
        else priority = 3; // Still playable even at full health
        break;
      case CARD_TYPES.JAIL: {
        // Check if there are valid targets for Jail (not Sheriff, not already in jail)
        const validJailTargets = targets.filter(target =>
          target.role !== ROLES.SHERIFF &&
          !target.inPlay.some(card => card.type === CARD_TYPES.JAIL)
        );
        if (validJailTargets.length > 0) priority = 5;
        else priority = 0; // No valid targets
        break;
      }
      case CARD_TYPES.DYNAMITE:
        // Only place dynamite if bot doesn't already have it
        priority = 4;
        break;
      case CARD_TYPES.GENERAL_STORE:
        priority = 5; // Good card to play
        break;
      default:
        priority = 3;
    }

    return priority;
  };

  const processBotTurn = useCallback(async () => {
    if (isProcessingBotTurn) {
      return;
    }

    const currentPlayer = players[currentPlayerIndex];
    if (!currentPlayer.isBot) {
      return;
    }

    setIsProcessingBotTurn(true);

    // Phase 1: Draw cards
    if (turnPhase === 'draw' && !hasDrawnCards) {
      await new Promise(resolve => setTimeout(resolve, 1000)); // Delay for realism
      handlePlayerTurn();
      // Don't return here - continue to play phase after drawing
      await new Promise(resolve => setTimeout(resolve, 500)); // Small delay after drawing
    }

    // Phase 2: Play cards
    if (turnPhase === 'play') {
      await new Promise(resolve => setTimeout(resolve, 1500)); // Delay for realism

      const targets = getBotTargets(currentPlayerIndex, currentPlayer.role);
      const hand = [...currentPlayer.hand];

      // Calculate priorities for all cards
      const cardPriorities = hand.map((card, index) => ({
        card,
        index,
        priority: getBotCardPriority(card, currentPlayer, targets)
      }));

      // Sort by priority (highest first)
      cardPriorities.sort((a, b) => b.priority - a.priority);



      // Try to play cards (bot can play multiple cards per turn)
      const maxCardsToPlay = Math.min(3, hand.length); // Limit to prevent infinite loops

      for (let attempt = 0; attempt < maxCardsToPlay; attempt++) {
        let playedCard = false;

        // Recalculate priorities each time since hand changes
        const currentHand = [...players[currentPlayerIndex].hand];
        const currentPriorities = currentHand.map((card, index) => ({
          card,
          index,
          priority: getBotCardPriority(card, players[currentPlayerIndex], targets)
        }));

        currentPriorities.sort((a, b) => b.priority - a.priority);

        for (const cardInfo of currentPriorities) {
          if (cardInfo.priority > 2) { // Play cards with reasonable priority
            const needsTarget = [
              CARD_TYPES.BANG, CARD_TYPES.PANIC, CARD_TYPES.CAT_BALOU,
              CARD_TYPES.DUEL, CARD_TYPES.JAIL
            ].includes(cardInfo.card.type);

            if (needsTarget && targets.length > 0) {
              // Choose target based on strategy
              let targetIndex = -1;
              if (cardInfo.card.type === CARD_TYPES.BANG || cardInfo.card.type === CARD_TYPES.DUEL) {
                // Target lowest health enemy
                const sortedTargets = targets.sort((a, b) => a.health - b.health);
                targetIndex = players.findIndex(p => p === sortedTargets[0]);
              } else if (cardInfo.card.type === CARD_TYPES.JAIL) {
                // For Jail cards, filter out Sheriff and already jailed players
                const validJailTargets = targets.filter(target =>
                  target.role !== ROLES.SHERIFF &&
                  !target.inPlay.some(card => card.type === CARD_TYPES.JAIL)
                );
                if (validJailTargets.length > 0) {
                  const randomTarget = validJailTargets[Math.floor(Math.random() * validJailTargets.length)];
                  targetIndex = players.findIndex(p => p === randomTarget);
                }
              } else {
                // Random target for other cards
                const randomTarget = targets[Math.floor(Math.random() * targets.length)];
                targetIndex = players.findIndex(p => p === randomTarget);
              }

              if (targetIndex !== -1) {
                playCard(currentPlayerIndex, cardInfo.index, targetIndex);
                playedCard = true;
                break;
              }
            } else if (!needsTarget) {
              playCard(currentPlayerIndex, cardInfo.index);
              playedCard = true;
              break;
            }
          }
        }

        if (!playedCard) break; // No more good cards to play

        // Small delay between card plays
        await new Promise(resolve => setTimeout(resolve, 800));
      }

      // End turn after playing cards (or if no cards were played)
      await new Promise(resolve => setTimeout(resolve, 1000));
      endTurn();
    }

    setIsProcessingBotTurn(false);
  }, [isProcessingBotTurn, players, currentPlayerIndex, turnPhase, hasDrawnCards, handlePlayerTurn, endTurn, playCard, getBotTargets, getBotCardPriority, setIsProcessingBotTurn]);

  // Effect to trigger bot turns
  useEffect(() => {
    if (gameState === 'playing' && currentPlayerIndex !== -1 && players.length > 0) {
      const currentPlayer = players[currentPlayerIndex];
      if (currentPlayer && currentPlayer.isBot && !isProcessingBotTurn) {
        const timer = setTimeout(() => {
          processBotTurn();
        }, 1000); // Small delay before bot acts

        return () => clearTimeout(timer);
      }
    }
  }, [currentPlayerIndex, turnPhase, gameState, players, isProcessingBotTurn, processBotTurn]);

  // Effect to close General Store modal when no cards left
  useEffect(() => {
    if (showGeneralStore && generalStoreCards.length === 0) {
      setShowGeneralStore(false);
      setGeneralStoreCards([]);
      setGeneralStorePlayerOrder([]);
      setCurrentGeneralStorePlayer(0);
      setMessage('General Store complete! All cards have been taken.');
    }
  }, [showGeneralStore, generalStoreCards.length]);

  // Effect to automatically trigger bot selections in General Store
  useEffect(() => {
    if (showGeneralStore &&
        generalStorePlayerOrder.length > 0 &&
        currentGeneralStorePlayer < generalStorePlayerOrder.length &&
        generalStoreCards.length > 0) {

      const currentPlayerIndex = generalStorePlayerOrder[currentGeneralStorePlayer];
      const currentPlayer = players[currentPlayerIndex];

      if (currentPlayer && currentPlayer.isBot) {
        const timer = setTimeout(() => {
          handleBotGeneralStoreSelection();
        }, 1200);

        return () => clearTimeout(timer);
      }
    }
  }, [showGeneralStore, currentGeneralStorePlayer, generalStorePlayerOrder, generalStoreCards.length, players]);

  // Cleanup message timeout on unmount
  useEffect(() => {
    return () => {
      if (messageTimeout) {
        clearTimeout(messageTimeout);
      }
    };
  }, [messageTimeout]);



  // Auto-draw cards at turn start for human players
  useEffect(() => {
    if (gameState === 'playing' && currentPlayerIndex !== -1 && players.length > 0) {
      const currentPlayer = players[currentPlayerIndex];
      if (currentPlayer && !currentPlayer.isBot && turnPhase === 'draw' && !hasDrawnCards && !isDrawingCards && lastDrawnTurnRef.current !== turnId) {
        // Auto-draw cards for human players at the beginning of their turn
        const timeoutId = setTimeout(() => {
          handlePlayerTurn();
        }, 500); // Small delay for visual clarity

        return () => {
          clearTimeout(timeoutId);
        };
      }
    }
  }, [gameState, currentPlayerIndex, turnPhase, hasDrawnCards, isDrawingCards, turnId, handlePlayerTurn, players]);

  // Start timer when turn begins (for entire turn, not just play phase)
  // Note: 'players' is intentionally NOT in dependencies to prevent timer reset on player state changes
  useEffect(() => {
    if (gameState === 'playing' && currentPlayerIndex !== -1 && players.length > 0) {
      const currentPlayer = players[currentPlayerIndex];
      if (currentPlayer && !currentPlayer.isBot) {
        // Start timer for human players at the beginning of their turn
        startTurnTimer();
      } else {
        // Stop timer for bot players
        stopTurnTimer();
      }
    } else {
      // Stop timer when not playing
      stopTurnTimer();
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [gameState, currentPlayerIndex, startTurnTimer, stopTurnTimer]);

  return (
    <div className={`bang-game ${gameState === 'setup' ? 'setup-mode' : ''}`}>
      <div className={`game-header ${gameState !== 'setup' ? 'hidden' : ''}`}>
        <h1>🤠 BANG! The Card Game </h1>
        <p className="game-subtitle">The Wild West Card Game with Official Graphics</p>
      </div>

      {/* <div className="message-box">{message}</div> */}

      {gameState === 'setup' && (
        <div className="setup-screen">
          
          <div className="setup-form">
            <div className="game-mode">
              <label>Game Mode: </label>
              <select
                value={numHumanPlayers}
                onChange={(e) => {
                  const humans = parseInt(e.target.value);
                  setNumHumanPlayers(humans);
                  // Adjust total players if needed
                  if (numPlayers < humans) {
                    setNumPlayers(humans);
                  }
                  // Update human player names array
                  const newNames = Array(humans).fill('').map((_, i) =>
                    humanPlayerNames[i] || ''
                  );
                  setHumanPlayerNames(newNames);
                }}
              >
                <option value="1">Single Player (vs Bots)</option>
                <option value="2">2 Human Players</option>
                <option value="3">3 Human Players</option>
                <option value="4">4 Human Players</option>
                <option value="5">5 Human Players</option>
                <option value="6">6 Human Players</option>
                <option value="7">7 Human Players</option>
              </select>
            </div>

            <div className="human-players">
              {Array(numHumanPlayers).fill(0).map((_, i) => (
                <div key={`human-player-${i}`} className="player-name">
                  <label>Player {i + 1} Name: </label>
                  <input
                    type="text"
                    value={humanPlayerNames[i] || ''}
                    onChange={(e) => {
                      const newNames = [...humanPlayerNames];
                      newNames[i] = e.target.value;
                      setHumanPlayerNames(newNames);
                    }}
                    placeholder={`Enter Player ${i + 1} name`}
                    maxLength="20"
                  />
                </div>
              ))}
            </div>

            <div className="player-count">
              <label>Total Players: </label>
              <select
                value={numPlayers}
                onChange={(e) => setNumPlayers(parseInt(e.target.value))}
                disabled={numHumanPlayers === 7}
              >
                {Array.from({length: 8 - numHumanPlayers}, (_, i) => {
                  const total = numHumanPlayers + i;
                  if (total < 4) return null;
                  const bots = total - numHumanPlayers;
                  return (
                    <option key={total} value={total}>
                      {total} ({numHumanPlayers} Human{numHumanPlayers > 1 ? 's' : ''}{bots > 0 ? ` + ${bots} Bot${bots > 1 ? 's' : ''}` : ''})
                    </option>
                  );
                }).filter(Boolean)}
              </select>
            </div>
          </div>

          {numPlayers - numHumanPlayers > 0 && (
            <p className="bot-info">🤖 {numPlayers - numHumanPlayers} AI bot{numPlayers - numHumanPlayers > 1 ? 's' : ''} will join the game and make intelligent decisions!</p>
          )}

          <button className="start-button" onClick={startGame}>Start Game</button>
        </div>
      )}
      
      {gameState === 'playing' && (
        <div className="playing-container">
          {/* Turn Timer Display - At the top */}
          {turnTimerActive && (
            <div className={`turn-timer ${turnTimeLeft <= 30 && turnTimeLeft > 10 ? 'warning' : ''} ${turnTimeLeft <= 10 ? 'critical' : ''}`}>
              ⏰ Time: {formatTime(turnTimeLeft)}
            </div>
          )}

          {/* Game Menu Button - Always visible */}
          <button
            onClick={handleMenuToggle}
            className="game-menu-button-fixed"
            title="Game Menu"
          >
            ☰
          </button>

          {/* Top UI container with message and actions */}
          <div className="top-ui-container">
            <div className="message-box-playing">{message}</div>

            {/* Action buttons to the right of message box */}
            {currentPlayerIndex !== -1 && !players[currentPlayerIndex].isBot && (
              <div className="actions-right">
                <button
                  onClick={handlePlayerTurn}
                  disabled={turnPhase !== 'draw' || hasDrawnCards}
                  className={turnPhase !== 'draw' || hasDrawnCards ? 'disabled' : ''}
                >
                  {hasDrawnCards ? 'Cards Drawn ✓' : 'Auto-Drawing...'}
                </button>
                <button
                  onClick={endTurn}
                  disabled={!hasDrawnCards}
                  className={!hasDrawnCards ? 'disabled' : ''}
                >
                  End Turn
                </button>
                {players[currentPlayerIndex].character.name === 'Sid Ketchum' &&
                 players[currentPlayerIndex].hand.length >= 2 &&
                 players[currentPlayerIndex].health < players[currentPlayerIndex].maxHealth && (
                  <button onClick={() => handleSidKetchumAbility()}>
                    Sid Ketchum Ability
                  </button>
                )}
              </div>
            )}
          </div>

          <div className="game-board">
          {/* Human player area at top - always shows the first human player */}
          <div className={`current-player-area ${currentPlayerIndex !== 0 || (players[0] && players[0].isBot) ? 'disabled' : ''}`} data-player-index="0">
            {players.length > 0 && players[0] && !players[0].isBot && !isSpectating && (
              <div className="current-player effect-container" data-player-index={0}>
                <div className="player-status">
                  <div className="character-info">
                    <div className="character-image-container">
                      <img
                        src={CHARACTER_IMAGES[players[0].character.name]}
                        alt={players[0].character.name}
                        className="character-image"
                        onContextMenu={(e) => {
                          e.preventDefault();
                          handleCharacterPreview(players[0].character);
                        }}
                        onDoubleClick={() => {
                          handleCharacterPreview(players[0].character);
                        }}
                        onTouchStart={() => handleTouchStart({ isCharacter: true, character: players[0].character, type: players[0].character.name })}
                        onTouchEnd={handleTouchEnd}
                        onTouchMove={handleTouchMove}
                      />
                      <div className="player-name-overlay">
                        {players[0].name}
                        {players[0].isBot && <span className="bot-indicator-overlay"> 🤖</span>}
                      </div>
                      {players[0].isAlive && (
                        <div className="health-display-overlay">
                          ❤️ {players[0].health}/{players[0].maxHealth}
                        </div>
                      )}
                    </div>
                    {!players[0].isAlive && (
                      <div className="role-display-below">
                        💀 {players[0].role}
                      </div>
                    )}
                    <div className="character-details">
                      {isRoleVisible(players[0]) && (
                        <div className="sheriff-badge-current">⭐</div>
                      )}
                      {/* <div className="character-name-current">{players[0].character.name}</div> */}
                      <div className="role-display">
                        <img
                          src={ROLE_IMAGES[players[0].role]}
                          alt={players[0].role}
                          className="role-image"
                        />
                        
                      </div>
                      {/* <div className="ability-text">{players[0].character.ability}</div> */}
                    </div>
                  </div>
                </div>

                {/* Human player's hand - always show for the first human player */}
                <div className="hand-area">
                  <h4>
                    Your Hand ({players[0].hand.length} cards)
                    <span className="hand-limit-info">
                      - Hand Limit: {players[0].health}
                      
                    </span>
                  </h4>
                <div className="hand-cards">
                  {players[0] && players[0].hand && Array.isArray(players[0].hand) && players[0].hand.map((card, originalIndex) => {
                    if (!card || !card.type) return null;
                    const animationKey = `play-0-${originalIndex}`;
                    const hasAnimation = cardAnimations[animationKey];

                      return (
                        <div
                          key={`hand-${originalIndex}-${card.type}-${card.suit}-${card.value}-${Date.now()}`}
                          data-card-index={originalIndex}
                          className={`hand-card ${
                            // Check if this would be a duplicate equipment
                            [CARD_TYPES.BARREL, CARD_TYPES.MUSTANG, CARD_TYPES.SCOPE, CARD_TYPES.VOLCANIC,
                             CARD_TYPES.SCHOFIELD, CARD_TYPES.REMINGTON, CARD_TYPES.REV_CARABINE,
                             CARD_TYPES.WINCHESTER, CARD_TYPES.JAIL, CARD_TYPES.DYNAMITE].includes(card.type) &&
                            players[0].inPlay.some(c => c.type === card.type)
                              ? 'duplicate-equipment' : ''
                          } ${
                            // Check if this is a BANG! card that cannot be played due to limit
                            card.type === CARD_TYPES.BANG && !canPlayBang(players[0])
                              ? 'bang-limit-reached' : ''
                          } ${
                            // Check if this is a Beer card that cannot be played (at max health)
                            card.type === CARD_TYPES.BEER && players[0].health >= players[0].maxHealth
                              ? 'beer-unplayable' : ''
                          } ${
                            // Add animation class if card is being animated
                            hasAnimation ? `card-${hasAnimation.type}-animation` : ''
                          }`}
                        onClick={() => {
                          // Only allow card play if it's the human player's turn
                          if (currentPlayerIndex !== 0) {
                            setMessage("It's not your turn!");
                            return;
                          }

                          const needsTarget = [
                            CARD_TYPES.BANG, CARD_TYPES.PANIC, CARD_TYPES.CAT_BALOU,
                            CARD_TYPES.DUEL, CARD_TYPES.JAIL, CARD_TYPES.PUNCH, CARD_TYPES.TEQUILA
                          ].includes(card.type);

                          if (needsTarget) {
                            setTargetingCard({ playerIndex: 0, cardIndex: originalIndex, card });
                            setShowTargetSelection(true);
                          } else {
                            playCard(0, originalIndex);
                          }
                        }}
                        onContextMenu={(e) => {
                          e.preventDefault();
                          handleCardPreview(card);
                        }}
                        onDoubleClick={() => {
                          handleCardPreview(card);
                        }}
                        onMouseEnter={() => setHoveredCard(card)}
                        onMouseLeave={() => setHoveredCard(null)}
                        onTouchStart={(e) => handleCardTouchStart(e, card, originalIndex)}
                        onTouchEnd={handleCardTouchEnd}
                        onTouchMove={handleCardTouchMove}
                        title={
                          // Check for duplicate equipment
                          [CARD_TYPES.BARREL, CARD_TYPES.MUSTANG, CARD_TYPES.SCOPE, CARD_TYPES.VOLCANIC,
                           CARD_TYPES.SCHOFIELD, CARD_TYPES.REMINGTON, CARD_TYPES.REV_CARABINE,
                           CARD_TYPES.WINCHESTER, CARD_TYPES.JAIL, CARD_TYPES.DYNAMITE].includes(card.type) &&
                          players[0].inPlay.some(c => c.type === card.type)
                            ? `${card.type} - ${card.suit} ${card.value} (DUPLICATE - Cannot play!) | Right-click or double-click to preview`
                            // Check for BANG! limit
                            : card.type === CARD_TYPES.BANG && !canPlayBang(players[0])
                            ? `${card.type} - ${card.suit} ${card.value} (BANG! limit reached - Need Volcanic or Willy the Kid!) | Right-click or double-click to preview`
                            // Check for Beer at max health
                            : card.type === CARD_TYPES.BEER && players[0].health >= players[0].maxHealth
                            ? `${card.type} - ${card.suit} ${card.value} (Already at maximum health - Cannot play!) | Right-click or double-click to preview`
                            : `${card.type} - ${card.suit} ${card.value} | Right-click or double-click to preview`
                        }
                      >
                        <img
                          src={CARD_IMAGES[card.type]}
                          alt={card.type}
                          className="hand-card-image"
                          onError={(e) => {
                            e.target.style.display = 'none';
                            e.target.nextSibling.style.display = 'block';
                          }}
                        />
                        <div className="card-fallback" style={{ display: 'none' }}>
                          <div className="card-name">{card.type}</div>
                          <div className="card-suit">{card.suit} {card.value}</div>
                        </div>
                        </div>
                      );
                    })}
                  </div>
                </div>

                {/* Equipment area - moved below hand area */}
                {players[0].inPlay.length > 0 && (
                  <div className="equipment-area">
                    <h4>Equipment:</h4>
                    <div className="equipment-cards">
                      {players[0] && players[0].inPlay && Array.isArray(players[0].inPlay) && players[0].inPlay.map((card, originalIndex) => {
                        if (!card || !card.type) return null;
                        return (
                          <div
                            key={`equipment-${originalIndex}-${card.type}-${Date.now()}`}
                          className={`equipment-card ${card.equipmentType === EQUIPMENT_TYPES.GREEN ? 'green-card' : ''} ${
                            card.equipmentType === EQUIPMENT_TYPES.GREEN && canUseGreenDefensiveCard(players[0], card.type) ? 'usable-green' : ''
                          }`}
                          title={`${card.type} ${card.equipmentType === EQUIPMENT_TYPES.GREEN ? '(Green - Click to activate)' : ''}`}
                          onClick={() => {
                            if (card.equipmentType === EQUIPMENT_TYPES.GREEN && canUseGreenDefensiveCard(players[0], card.type)) {
                              handleGreenCardActivation(0, card.type);
                            }
                          }}
                          onContextMenu={(e) => {
                            e.preventDefault();
                            handleCardPreview(card);
                          }}
                          onDoubleClick={() => {
                            handleCardPreview(card);
                          }}
                          onTouchStart={() => handleTouchStart(card)}
                          onTouchEnd={handleTouchEnd}
                          onTouchMove={handleTouchMove}
                        >
                          <img
                            src={CARD_IMAGES[card.type]}
                            alt={card.type}
                            className="equipment-card-image"
                          />
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Turn controls and bot indicator */}
                {/* {!players[currentPlayerIndex].isBot && (
                  <div className="turn-controls">
                    <div className="turn-info">
                      <h4>Phase: {turnPhase.toUpperCase()}</h4>



                      {turnPhase === 'play' && (
                        <p className="bang-counter">
                          🔫 BANG!: {bangCardsPlayedThisTurn}
                          {!canPlayBang(players[currentPlayerIndex]) &&
                            <span className="bang-limit-warning"> (MAX!)</span>
                          }
                          {(players[currentPlayerIndex].character.abilityType === 'UNLIMITED_BANG' ||
                            players[currentPlayerIndex].inPlay.some(c => c.type === CARD_TYPES.VOLCANIC)) &&
                            <span className="bang-unlimited"> (∞)</span>
                          }
                        </p>
                      )}
                    </div>
                  </div>
                )}

                {/* Bot turn indicator */}
                {/* {players[currentPlayerIndex].isBot && (
                  <div className="bot-turn-indicator">
                    <h4>🤖 Bot is making their move...</h4>
                  </div>
                )} */}
              </div>
            )}

            {/* Spectator mode display */}
            {isSpectating && (
              <div className="spectator-mode">
                <div className="spectator-info">
                  <h3>👻 Spectator Mode</h3>
                  <p>You have been eliminated but are watching the game continue.</p>
                  <p>The game will end when a winning condition is met.</p>
                  <button
                    className="exit-spectator-button"
                    onClick={handleExitGame}
                  >
                    Exit to Main Menu
                  </button>
                </div>
              </div>
            )}
          </div>

          {/* Central game area */}
          <div className="central-area">
            <div className="game-info">
              <div className="deck-info">
                <h4>Deck ({deck.length})</h4>
                <div className="deck-card">
                  <img
                    src={CARD_BACK_IMAGE}
                    alt="Card Back"
                    className="deck-image"
                  />
                </div>
              </div>
              <div className="discard-info">
                <h4>Discard ({discardPile.length})</h4>
                <div className={`discard-card discard-pile ${discardPile.length === 0 ? 'empty' : ''}`}>
                  {discardPile.length > 0 && discardPile[discardPile.length - 1] && discardPile[discardPile.length - 1].type && (
                    <img
                      src={CARD_IMAGES[discardPile[discardPile.length - 1].type]}
                      alt={discardPile[discardPile.length - 1].type}
                      className="discard-image"
                      onContextMenu={(e) => {
                        e.preventDefault();
                        handleCardPreview(discardPile[discardPile.length - 1]);
                      }}
                      onDoubleClick={() => {
                        handleCardPreview(discardPile[discardPile.length - 1]);
                      }}
                      onTouchStart={() => handleTouchStart(discardPile[discardPile.length - 1])}
                      onTouchEnd={handleTouchEnd}
                      onTouchMove={handleTouchMove}
                      title={`${discardPile[discardPile.length - 1].type} - ${discardPile[discardPile.length - 1].suit} ${discardPile[discardPile.length - 1].value} | Right-click, double-click, or touch-hold to preview`}
                    />
                  )}
                </div>
              </div>
            </div>
          </div>

          {/* Other players area at bottom - shows all players except the human player */}
          <div className="other-players-area">
            {players.map((player, index) => (
              index !== 0 && (
                <div
                  key={`other-player-${index}-${player.character.name}`}
                  className={`other-player ${!player.isAlive ? 'dead-player' : ''} effect-container`}
                  data-player-index={index}
                >
                  <div className="player-info">
                    <div className="character-image-container">
                      <img
                        src={CHARACTER_IMAGES[player.character.name]}
                        alt={player.character.name}
                        className="character-image"
                        onError={(e) => {
                          e.target.style.display = 'none';
                        }}
                        onContextMenu={(e) => {
                          e.preventDefault();
                          handleCharacterPreview(player.character);
                        }}
                        onDoubleClick={() => {
                          handleCharacterPreview(player.character);
                        }}
                        onTouchStart={() => handleTouchStart({ isCharacter: true, character: player.character, type: player.character.name })}
                        onTouchEnd={handleTouchEnd}
                        onTouchMove={handleTouchMove}
                      />
                      <div className="player-name-overlay">
                        {player.name}
                        {player.isBot && <span className="bot-indicator-overlay"> 🤖</span>}
                      </div>
                      {player.isAlive && (
                        <div className="health-display-overlay">
                          ❤️ {player.health}/{player.maxHealth}
                        </div>
                      )}
                    </div>
                    {!player.isAlive && (
                      <div className="role-display-below">
                        💀 {player.role}
                      </div>
                    )}
                    <div className="player-details">
                      {isRoleVisible(player) && (
                        <div className="sheriff-badge-below">⭐</div>
                      )}
                      {/* <div className="character-name">{player.character.name}</div> */}
                      <div className="cards-count">🃏 {player.hand.length}</div>
                    </div>
                  </div>

                  {/* Show in-play cards for other players */}
                  {player.inPlay.length > 0 && (
                    <div className="other-player-equipment">
                      {player && player.inPlay && Array.isArray(player.inPlay) && player.inPlay.map((card, originalIndex) => {
                        if (!card || !card.type) return null;
                        return (
                          <div
                            key={`player-${index}-equipment-${originalIndex}-${card.type}-${Date.now()}`}
                          className="small-card"
                          title={card.type}
                          onContextMenu={(e) => {
                            e.preventDefault();
                            handleCardPreview(card);
                          }}
                          onDoubleClick={() => {
                            handleCardPreview(card);
                          }}
                          onTouchStart={() => handleTouchStart(card)}
                          onTouchEnd={handleTouchEnd}
                          onTouchMove={handleTouchMove}
                        >
                          <img
                            src={CARD_IMAGES[card.type]}
                            alt={card.type}
                            className="small-card-image"
                          />
                          </div>
                        );
                      })}
                    </div>
                  )}
                </div>
              )
            ))}
          </div>



        </div>
        </div>
      )}
      
            {gameState === 'ended' && (
        <div className="game-over">
          <div className="game-result-header">
            {gameResult === 'won' ? (
              <>
                <h2 className="victory-title">🎉 Victory! 🎉</h2>
                <p className="victory-subtitle">Congratulations! You have won the game!</p>
              </>
            ) : gameResult === 'lost' ? (
              <>
                <h2 className="defeat-title">💀 Defeat 💀</h2>
                <p className="defeat-subtitle">Better luck next time!</p>
              </>
            ) : (
              <>
                <h2>Game Over!</h2>
                <p>The game has ended.</p>
              </>
            )}
          </div>
          <div className="final-roles">
            {players.map((player, index) => (
              <div key={`final-result-${index}-${player.character.name}`} className={`player-result ${!player.isAlive ? 'dead-player' : ''}`}>
                <img
                  src={CHARACTER_IMAGES[player.character.name]}
                  alt={player.character.name}
                  className="character-image-result"
                  onError={(e) => {
                    e.target.style.display = 'none';
                  }}
                  onContextMenu={(e) => {
                    e.preventDefault();
                    handleCharacterPreview(player.character);
                  }}
                  onDoubleClick={() => {
                    handleCharacterPreview(player.character);
                  }}
                  onTouchStart={() => handleTouchStart({ isCharacter: true, character: player.character, type: player.character.name })}
                  onTouchEnd={handleTouchEnd}
                  onTouchMove={handleTouchMove}
                />
                <h3>
                  {player.name}
                  {player.isBot && <span className="bot-indicator-small"> 🤖</span>}
                </h3>
                {isRoleVisible(player) && (
                  <div className="sheriff-badge-result">⭐</div>
                )}
                <p className="character-name">{player.character.name}</p>
                <p className="role">{player.role}</p>
                <p className="status">{player.isAlive ? 'Survived' : 'Eliminated'}</p>
              </div>
            ))}
          </div>
          <button className="restart-button" onClick={() => {
            setGameState('setup');
            setPlayers([]);
            setCurrentPlayerIndex(0);
            setDeck([]);
            setDiscardPile([]);
            setTurnPhase('draw');
            setHasDrawnCards(false);
            setBangCardsPlayedThisTurn(0);
            setNumHumanPlayers(1); // Reset to single player
            setHumanPlayerNames(['']); // Reset player names
            setMessage('Welcome to BANG!');
          }}>Play Again</button>
        </div>
      )}
      
      {/* Target selection modal */}
      {showTargetSelection && targetingCard && (
        <div className="target-selection">
          <h3>Select a target for {targetingCard.card.type}</h3>
          <div className="target-options">
            {players.map((player, index) => {
              // Check if this is a valid target based on card type
              const isValidTarget = player.isAlive && (() => {
                if (targetingCard.card.type === CARD_TYPES.JAIL) {
                  // For Jail cards, cannot target Sheriff or already jailed players, and cannot target self
                  return currentPlayerIndex !== index &&
                         player.role !== ROLES.SHERIFF &&
                         !player.inPlay.some(card => card.type === CARD_TYPES.JAIL);
                }
                if (targetingCard.card.type === CARD_TYPES.TEQUILA) {
                  // Tequila can target any alive player including self
                  return true;
                }
                // For other cards, any alive player except self is valid
                return currentPlayerIndex !== index;
              })();

              return isValidTarget && (
                <div
                  key={`target-${index}-${player.character.name}`}
                  className={`target-option ${
                    targetingCard.card.type === CARD_TYPES.JAIL &&
                    (player.role === ROLES.SHERIFF || player.inPlay.some(card => card.type === CARD_TYPES.JAIL))
                      ? 'invalid-target' : ''
                  }`}
                  onClick={() => {
                    // Check if this is a green card activation (cardIndex = -1)
                    if (targetingCard.cardIndex === -1) {
                      // Handle green card activation with target
                      handleGreenCardWithTarget(targetingCard.playerIndex, targetingCard.card.type, index);
                    } else {
                      // Regular card play
                      playCard(targetingCard.playerIndex, targetingCard.cardIndex, index);
                    }
                    setShowTargetSelection(false);
                    setTargetingCard(null);
                  }}
                >
                  <div className="target-player-name">
                    {player.name}
                    {player.isBot && <span className="bot-indicator-small"> 🤖</span>}
                    {isRoleVisible(player) && <span className="sheriff-badge-target"> ⭐</span>}
                    {targetingCard.card.type === CARD_TYPES.JAIL && player.inPlay.some(card => card.type === CARD_TYPES.JAIL) &&
                      <span className="jail-indicator"> 🔒 IN JAIL</span>
                    }
                  </div>
                  <div className="target-character-name">{player.character.name}</div>
                  <div className="target-info">
                    Health: {player.health} | Distance: {calculateDistance(currentPlayerIndex, index, players)}
                  </div>
                </div>
              );
            })}
          </div>
          <button onClick={() => {
            setShowTargetSelection(false);
            setTargetingCard(null);
          }}>Cancel</button>
        </div>
      )}

      {/* Discard selection modal */}
      {showDiscardSelection && (
        <div className="discard-selection">
          <h3>Discard Cards - Hand Limit Exceeded</h3>
          <p>You must discard {cardsToDiscard} card{cardsToDiscard > 1 ? 's' : ''} (Hand limit: {players[currentPlayerIndex].health})</p>
          <div className="discard-cards">
            {players[currentPlayerIndex] && players[currentPlayerIndex].hand && Array.isArray(players[currentPlayerIndex].hand) && players[currentPlayerIndex].hand.map((card, originalIndex) => {
              if (!card || !card.type) return null;
              return (
                <div
                  key={`discard-${originalIndex}-${card.type}-${card.suit}-${card.value}-${Date.now()}`}
                className={`discard-card ${selectedDiscardCards.includes(originalIndex) ? 'selected' : ''}`}
                onClick={() => handleDiscardSelection(originalIndex)}
                onContextMenu={(e) => {
                  e.preventDefault();
                  handleCardPreview(card);
                }}
                onDoubleClick={() => {
                  handleCardPreview(card);
                }}
                title={`${card.type} - ${card.suit} ${card.value} | Right-click or double-click to preview`}
              >
                <img
                  src={CARD_IMAGES[card.type]}
                  alt={card.type}
                  className="discard-card-image"
                />
                <div className="card-name">{card.type}</div>
                {selectedDiscardCards.includes(originalIndex) && (
                  <div className="selected-indicator">✓</div>
                )}
                </div>
              );
            })}
          </div>
          <div className="discard-actions">
            <button
              onClick={confirmDiscardSelection}
              disabled={selectedDiscardCards.length !== cardsToDiscard}
              className={selectedDiscardCards.length !== cardsToDiscard ? 'disabled' : ''}
            >
              Discard Selected Cards ({selectedDiscardCards.length}/{cardsToDiscard})
            </button>
          </div>
        </div>
      )}

      {/* Player death modal */}
      {showDeathModal && (
        <div className="death-modal-overlay">
          <div className="death-modal">
            <div className="death-modal-content">
              <h2>💀 You Have Been Eliminated!</h2>
              <p>Your character has been eliminated from the game.</p>
              <p>What would you like to do?</p>
              <div className="death-modal-buttons">
                <button
                  className="spectate-button"
                  onClick={handleSpectateGame}
                >
                  👻 Spectate Game
                </button>
                <button
                  className="exit-button"
                  onClick={handleExitGame}
                >
                  🚪 Exit to Main Menu
                </button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* General Store Selection Modal */}
      {showGeneralStore && generalStoreCards.length > 0 && (
        <div className="modal-overlay">
          <div className="general-store-modal">
            <h3>General Store</h3>
            <p>
              {generalStorePlayerOrder.length > 0 && currentGeneralStorePlayer < generalStorePlayerOrder.length
                ? `${players[generalStorePlayerOrder[currentGeneralStorePlayer]]?.character.name}'s turn to choose`
                : 'Choose a card'}
            </p>
            <p className="general-store-instruction">
              Cards available: {generalStoreCards.length} | Players remaining: {generalStorePlayerOrder.length - currentGeneralStorePlayer}
            </p>
            <p className="general-store-rule">
              Rule: Starting with the player who played General Store, each player chooses one card clockwise.
            </p>
            <div className="general-store-cards">
              {generalStoreCards.map((card, index) => {
                if (!card || !card.type) return null;
                return (
                  <div
                    key={`general-store-${index}-${card.type}-${card.suit}-${card.value}`}
                    className={`general-store-card ${
                      players[generalStorePlayerOrder[currentGeneralStorePlayer]]?.isBot ? 'disabled' : ''
                    }`}
                    onClick={() => {
                      const currentPlayerIndex = generalStorePlayerOrder[currentGeneralStorePlayer];
                      // Only allow human players to click, and only when it's their turn
                      if (!players[currentPlayerIndex]?.isBot &&
                          currentPlayerIndex === generalStorePlayerOrder[currentGeneralStorePlayer] &&
                          generalStoreCards[index]) {
                        handleGeneralStoreSelection(index);
                      }
                    }}
                  onContextMenu={(e) => {
                    e.preventDefault();
                    handleCardPreview(card);
                  }}
                  onDoubleClick={() => {
                    handleCardPreview(card);
                  }}
                  title={`${card.type} - ${card.suit} ${card.value} | Right-click or double-click to preview`}
                >
                  <img
                    src={CARD_IMAGES[card.type]}
                    alt={card.type}
                    className="general-store-card-image"
                  />
                    <div className="card-name">{card.type}</div>
                    <div className="card-suit">{card.value}{card.suit}</div>
                  </div>
                );
              })}
            </div>
            {generalStorePlayerOrder.length > 0 && currentGeneralStorePlayer < generalStorePlayerOrder.length && (
              <div className="general-store-info">
                <p>Turn order: {generalStorePlayerOrder.map((playerIndex, i) =>
                  `${i === currentGeneralStorePlayer ? '→ ' : ''}${players[playerIndex]?.character.name}${i === currentGeneralStorePlayer ? ' ←' : ''}`
                ).join(' → ')}</p>
              </div>
            )}
            <div className="general-store-actions">
              <button
                className="close-general-store-button"
                onClick={() => {
                  setShowGeneralStore(false);
                  setGeneralStoreCards([]);
                  setGeneralStorePlayerOrder([]);
                  setCurrentGeneralStorePlayer(0);
                  setMessage('General Store cancelled.');
                }}
              >
                Cancel General Store
              </button>
              {/* Debug button to force bot selection */}
              {generalStorePlayerOrder.length > 0 && currentGeneralStorePlayer < generalStorePlayerOrder.length &&
               players[generalStorePlayerOrder[currentGeneralStorePlayer]]?.isBot && (
                <button
                  className="force-bot-selection-button"
                  onClick={() => handleBotGeneralStoreSelection()}
                >
                  Force Bot Selection
                </button>
              )}
            </div>
          </div>
        </div>
      )}

      {/* Ability Choice Modal */}
      {showAbilityChoice && abilityChoice && (
        <div className="modal-overlay">
          <div className="ability-choice-modal">
            <h3>Character Ability</h3>
            <p>{abilityChoice.message}</p>
            <div className="ability-choice-buttons">
              <button
                className="ability-yes-button"
                onClick={() => handleAbilityChoice(true)}
              >
                Use Ability
              </button>
              <button
                className="ability-no-button"
                onClick={() => handleAbilityChoice(false)}
              >
                Don't Use
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Missed Card Choice Modal */}
      {showMissedChoice && missedChoice && (
        <div className="modal-overlay">
          <div className="missed-choice-modal">
            <h3>Defend with Missed!</h3>
            <p>{missedChoice.message}</p>
            <div className="missed-choice-info">
              <p>You have {missedChoice.availableMissedCards} Missed! card{missedChoice.availableMissedCards > 1 ? 's' : ''} available.</p>
              <p>You need {missedChoice.missedRequired} Missed! card{missedChoice.missedRequired > 1 ? 's' : ''} to defend.</p>
            </div>
            <div className="missed-choice-buttons">
              <button
                className="missed-yes-button"
                onClick={() => handleMissedChoice(true)}
              >
                Use Missed! Card{missedChoice.missedRequired > 1 ? 's' : ''}
              </button>
              <button
                className="missed-no-button"
                onClick={() => handleMissedChoice(false)}
              >
                Take Damage
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Card Selection Modal */}
      {showCardSelection && cardSelectionData && (
        <div className="modal-overlay">
          <div className="card-selection-modal">
            <h3>Card Selection</h3>
            <p>{cardSelectionData.message}</p>
            <div className="card-selection-grid">
              {players[cardSelectionData.playerIndex]?.hand.map((card, index) => (
                <div
                  key={index}
                  className="selectable-card"
                  onClick={() => handleCardSelectionChoice(index)}
                >
                  <img
                    src={CARD_IMAGES[card.type]}
                    alt={card.type}
                    className="card-selection-image"
                    onError={(e) => {
                      e.target.style.display = 'none';
                      e.target.nextSibling.style.display = 'block';
                    }}
                  />
                  <div className="card-selection-fallback" style={{ display: 'none' }}>
                    <div className="card-name">{card.type}</div>
                    <div className="card-suit">{card.suit} {card.value}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>
      )}

      {/* Game Menu Modal */}
      {showGameMenu && (
        <div className="modal-overlay" onClick={() => setShowGameMenu(false)}>
          <div className="game-menu-modal" onClick={(e) => e.stopPropagation()}>
            <h3>Game Menu</h3>
            <div className="game-menu-buttons">
              <button
                className="menu-button"
                onClick={handleToggleSound}
              >
                {soundEnabled ? '🔊 Sound: ON' : '🔇 Sound: OFF'}
              </button>
              <button
                className="menu-button"
                onClick={handleRestartGame}
              >
                🔄 Restart Game
              </button>
              <button
                className="menu-button"
                onClick={handleReturnToMenu}
              >
                🏠 Main Menu
              </button>
              <button
                className="menu-button menu-close"
                onClick={() => setShowGameMenu(false)}
              >
                ✕ Close
              </button>
            </div>
          </div>
        </div>
      )}

      {/* Animating cards overlay */}
      {animatingCards.map(anim => {
        if (!anim || !anim.card || !anim.card.type) {
          return null;
        }

        const fromElement = document.querySelector(`[data-player-index="${anim.fromPlayerIndex}"]`);
        const toElement = anim.toPlayerIndex >= 0
          ? document.querySelector(`[data-player-index="${anim.toPlayerIndex}"]`)
          : document.querySelector('.discard-pile');
        const discardElement = document.querySelector('.discard-pile');

        if (!fromElement || (!toElement && anim.phase === 'toTarget') || (!discardElement && anim.phase === 'toDiscard')) {
          return null;
        }

        const fromRect = fromElement.getBoundingClientRect();
        const toRect = anim.phase === 'toTarget' ? toElement.getBoundingClientRect() : discardElement.getBoundingClientRect();

        const startX = fromRect.left + fromRect.width / 2;
        const startY = fromRect.top + fromRect.height / 2;
        const endX = toRect.left + toRect.width / 2;
        const endY = toRect.top + toRect.height / 2;

        const currentX = startX + (endX - startX) * anim.progress;
        const currentY = startY + (endY - startY) * anim.progress;

        const scale = anim.phase === 'toTarget' ? 1 + anim.progress * 0.2 : 1.2 - anim.progress * 0.2;
        const rotation = anim.progress * 360;

        return (
          <div
            key={anim.id}
            className={`animating-card ${anim.card.type === 'BANG!' ? 'bang-card' : ''}`}
            style={{
              left: currentX - 30,
              top: currentY - 42,
              transform: `scale(${scale}) rotate(${rotation}deg)`,
              opacity: 1 - anim.progress * 0.1
            }}
          >
            <div className={`card ${anim.card.suit === '♥' || anim.card.suit === '♦' ? 'red' : 'black'}`}>
              <div>{anim.card.type || 'Unknown'}</div>
              <div>{anim.card.value || ''}{anim.card.suit || ''}</div>
            </div>
          </div>
        );
      })}

      {/* Card zoom/preview modal */}
      {showCardPreview && previewCard && (
        <div className="card-zoom-overlay" onClick={closeCardPreview}>
          <div className="card-zoom-modal" onClick={(e) => e.stopPropagation()}>
            <div className="card-zoom-content">
              <div className="card-zoom-image">
                <img
                  src={previewCard.isCharacter ? CHARACTER_IMAGES[previewCard.type] : CARD_IMAGES[previewCard.type]}
                  alt={previewCard.type}
                  className="zoomed-card-image"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
                <div className="card-zoom-fallback" style={{ display: 'none' }}>
                  <div className="zoomed-card-name">{previewCard.type}</div>
                  <div className="zoomed-card-suit">{previewCard.suit} {previewCard.value}</div>
                </div>
              </div>
              <div className="card-description">
                {previewCard.isCharacter ? previewCard.character.ability : getCardDescription(previewCard.type)}
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Long press card zoom modal */}
      {zoomedCard && (
        <div className="card-zoom-overlay" onClick={() => setZoomedCard(null)}>
          <div className="card-zoom-modal" onClick={(e) => e.stopPropagation()}>
            <div className="card-zoom-content">
              <div className="card-zoom-image">
                <img
                  src={zoomedCard.isCharacter ? CHARACTER_IMAGES[zoomedCard.type] : CARD_IMAGES[zoomedCard.type]}
                  alt={zoomedCard.type}
                  className="zoomed-card-image"
                  onError={(e) => {
                    e.target.style.display = 'none';
                    e.target.nextSibling.style.display = 'block';
                  }}
                />
                <div className="card-zoom-fallback" style={{ display: 'none' }}>
                  <div className="zoomed-card-name">{zoomedCard.type}</div>
                  <div className="zoomed-card-suit">{zoomedCard.suit} {zoomedCard.value}</div>
                </div>
              </div>
              <div className="card-description">
                {zoomedCard.isCharacter ? zoomedCard.character?.ability : getCardDescription(zoomedCard.type)}
                <div className="long-press-hint">
                  💡 <strong>Mobile Tip:</strong> Hold cards for 0.8s to zoom, tap quickly to play
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Card preview panel */}
      {/* {hoveredCard && (
        <div className="card-preview">
          <img
            src={CARD_IMAGES[hoveredCard.type]}
            alt={hoveredCard.type}
            className="preview-image"
          />
          <div className="preview-info">
            <h4>{hoveredCard.type}</h4>
            <p>{hoveredCard.suit} {hoveredCard.value}</p>
          </div>
        </div>
      )} */}

      {/* Tequila card selection modal */}
      {showTequilaCardSelection && (
        <div className="modal-overlay">
          <div className="modal">
            <h3>Choose a card to discard with Tequila</h3>
            <div className="card-selection-grid">
              {players[currentPlayerIndex]?.hand
                .filter((_, index) => index !== tequilaCardIndex) // Don't show the Tequila card itself
                .map((card, index) => {
                  // Calculate the actual index in the original hand
                  const actualIndex = index >= tequilaCardIndex ? index + 1 : index;
                  return (
                    <div
                      key={actualIndex}
                      className="selectable-card"
                      onClick={() => confirmTequilaCardSelection(actualIndex)}
                    >
                      <img
                        src={CARD_IMAGES[card.type]}
                        alt={card.type}
                        className="card-image"
                        onError={(e) => {
                          e.target.style.display = 'none';
                          e.target.nextSibling.style.display = 'block';
                        }}
                      />
                      <div className="card-fallback" style={{ display: 'none' }}>
                        <div className="card-name">{card.type}</div>
                        <div className="card-suit">{card.suit} {card.value}</div>
                      </div>
                    </div>
                  );
                })}
            </div>
            <button
              className="cancel-button"
              onClick={() => {
                setShowTequilaCardSelection(false);
                setTequilaCardIndex(null);
                setTequilaTargetIndex(null);
                setMessage("Tequila cancelled.");
              }}
            >
              Cancel
            </button>
          </div>
        </div>
      )}

      {/* Moving cards overlay */}
      {movingCards.map(movingCard => (
        <div
          key={movingCard.id}
          className="moving-card"
          style={{
            left: movingCard.startX,
            top: movingCard.startY,
            transform: `translate(-50%, -50%)`,
            '--end-x': `${movingCard.endX - movingCard.startX}px`,
            '--end-y': `${movingCard.endY - movingCard.startY}px`
          }}
        >
          <img
            src={CARD_IMAGES[movingCard.card.type] || '/images/cards/card-back.png'}
            alt={movingCard.card.type}
            onError={(e) => {
              e.target.src = '/images/cards/card-back.png';
            }}
          />
        </div>
      ))}
    </div>
  );
}

export default App;