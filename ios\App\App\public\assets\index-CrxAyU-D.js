(function(){const Te=document.createElement("link").relList;if(Te&&Te.supports&&Te.supports("modulepreload"))return;for(const v of document.querySelectorAll('link[rel="modulepreload"]'))O(v);new MutationObserver(v=>{for(const Y of v)if(Y.type==="childList")for(const x of Y.addedNodes)x.tagName==="LINK"&&x.rel==="modulepreload"&&O(x)}).observe(document,{childList:!0,subtree:!0});function Ae(v){const Y={};return v.integrity&&(Y.integrity=v.integrity),v.referrerPolicy&&(Y.referrerPolicy=v.referrerPolicy),v.crossOrigin==="use-credentials"?Y.credentials="include":v.crossOrigin==="anonymous"?Y.credentials="omit":Y.credentials="same-origin",Y}function O(v){if(v.ep)return;v.ep=!0;const Y=Ae(v);fetch(v.href,Y)}})();var jf={exports:{}},ni={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var th;function zm(){if(th)return ni;th=1;var K=Symbol.for("react.transitional.element"),Te=Symbol.for("react.fragment");function Ae(O,v,Y){var x=null;if(Y!==void 0&&(x=""+Y),v.key!==void 0&&(x=""+v.key),"key"in v){Y={};for(var Ue in v)Ue!=="key"&&(Y[Ue]=v[Ue])}else Y=v;return v=Y.ref,{$$typeof:K,type:O,key:x,ref:v!==void 0?v:null,props:Y}}return ni.Fragment=Te,ni.jsx=Ae,ni.jsxs=Ae,ni}var ah;function Lm(){return ah||(ah=1,jf.exports=zm()),jf.exports}var S=Lm(),qf={exports:{}},ie={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var lh;function jm(){if(lh)return ie;lh=1;var K=Symbol.for("react.transitional.element"),Te=Symbol.for("react.portal"),Ae=Symbol.for("react.fragment"),O=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),Y=Symbol.for("react.consumer"),x=Symbol.for("react.context"),Ue=Symbol.for("react.forward_ref"),V=Symbol.for("react.suspense"),_=Symbol.for("react.memo"),z=Symbol.for("react.lazy"),X=Symbol.iterator;function he(A){return A===null||typeof A!="object"?null:(A=X&&A[X]||A["@@iterator"],typeof A=="function"?A:null)}var Pe={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},ut=Object.assign,N={};function Qe(A,w,Z){this.props=A,this.context=w,this.refs=N,this.updater=Z||Pe}Qe.prototype.isReactComponent={},Qe.prototype.setState=function(A,w){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,w,"setState")},Qe.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function ca(){}ca.prototype=Qe.prototype;function Ea(A,w,Z){this.props=A,this.context=w,this.refs=N,this.updater=Z||Pe}var Ze=Ea.prototype=new ca;Ze.constructor=Ea,ut(Ze,Qe.prototype),Ze.isPureReactComponent=!0;var Bt=Array.isArray,ye={H:null,A:null,T:null,S:null,V:null},At=Object.prototype.hasOwnProperty;function bt(A,w,Z,$,P,fe){return Z=fe.ref,{$$typeof:K,type:A,key:w,ref:Z!==void 0?Z:null,props:fe}}function Je(A,w){return bt(A.type,w,void 0,void 0,void 0,A.props)}function Gt(A){return typeof A=="object"&&A!==null&&A.$$typeof===K}function kt(A){var w={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(Z){return w[Z]})}var sa=/\/+/g;function He(A,w){return typeof A=="object"&&A!==null&&A.key!=null?kt(""+A.key):w.toString(36)}function za(){}function fa(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(za,za):(A.status="pending",A.then(function(w){A.status==="pending"&&(A.status="fulfilled",A.value=w)},function(w){A.status==="pending"&&(A.status="rejected",A.reason=w)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function et(A,w,Z,$,P){var fe=typeof A;(fe==="undefined"||fe==="boolean")&&(A=null);var ue=!1;if(A===null)ue=!0;else switch(fe){case"bigint":case"string":case"number":ue=!0;break;case"object":switch(A.$$typeof){case K:case Te:ue=!0;break;case z:return ue=A._init,et(ue(A._payload),w,Z,$,P)}}if(ue)return P=P(A),ue=$===""?"."+He(A,0):$,Bt(P)?(Z="",ue!=null&&(Z=ue.replace(sa,"$&/")+"/"),et(P,w,Z,"",function(Jt){return Jt})):P!=null&&(Gt(P)&&(P=Je(P,Z+(P.key==null||A&&A.key===P.key?"":(""+P.key).replace(sa,"$&/")+"/")+ue)),w.push(P)),1;ue=0;var ht=$===""?".":$+":";if(Bt(A))for(var me=0;me<A.length;me++)$=A[me],fe=ht+He($,me),ue+=et($,w,Z,fe,P);else if(me=he(A),typeof me=="function")for(A=me.call(A),me=0;!($=A.next()).done;)$=$.value,fe=ht+He($,me++),ue+=et($,w,Z,fe,P);else if(fe==="object"){if(typeof A.then=="function")return et(fa(A),w,Z,$,P);throw w=String(A),Error("Objects are not valid as a React child (found: "+(w==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":w)+"). If you meant to render a collection of children, use an array instead.")}return ue}function M(A,w,Z){if(A==null)return A;var $=[],P=0;return et(A,$,"","",function(fe){return w.call(Z,fe,P++)}),$}function q(A){if(A._status===-1){var w=A._result;w=w(),w.then(function(Z){(A._status===0||A._status===-1)&&(A._status=1,A._result=Z)},function(Z){(A._status===0||A._status===-1)&&(A._status=2,A._result=Z)}),A._status===-1&&(A._status=0,A._result=w)}if(A._status===1)return A._result.default;throw A._result}var ee=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var w=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(w))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function ne(){}return ie.Children={map:M,forEach:function(A,w,Z){M(A,function(){w.apply(this,arguments)},Z)},count:function(A){var w=0;return M(A,function(){w++}),w},toArray:function(A){return M(A,function(w){return w})||[]},only:function(A){if(!Gt(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},ie.Component=Qe,ie.Fragment=Ae,ie.Profiler=v,ie.PureComponent=Ea,ie.StrictMode=O,ie.Suspense=V,ie.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ye,ie.__COMPILER_RUNTIME={__proto__:null,c:function(A){return ye.H.useMemoCache(A)}},ie.cache=function(A){return function(){return A.apply(null,arguments)}},ie.cloneElement=function(A,w,Z){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var $=ut({},A.props),P=A.key,fe=void 0;if(w!=null)for(ue in w.ref!==void 0&&(fe=void 0),w.key!==void 0&&(P=""+w.key),w)!At.call(w,ue)||ue==="key"||ue==="__self"||ue==="__source"||ue==="ref"&&w.ref===void 0||($[ue]=w[ue]);var ue=arguments.length-2;if(ue===1)$.children=Z;else if(1<ue){for(var ht=Array(ue),me=0;me<ue;me++)ht[me]=arguments[me+2];$.children=ht}return bt(A.type,P,void 0,void 0,fe,$)},ie.createContext=function(A){return A={$$typeof:x,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:Y,_context:A},A},ie.createElement=function(A,w,Z){var $,P={},fe=null;if(w!=null)for($ in w.key!==void 0&&(fe=""+w.key),w)At.call(w,$)&&$!=="key"&&$!=="__self"&&$!=="__source"&&(P[$]=w[$]);var ue=arguments.length-2;if(ue===1)P.children=Z;else if(1<ue){for(var ht=Array(ue),me=0;me<ue;me++)ht[me]=arguments[me+2];P.children=ht}if(A&&A.defaultProps)for($ in ue=A.defaultProps,ue)P[$]===void 0&&(P[$]=ue[$]);return bt(A,fe,void 0,void 0,null,P)},ie.createRef=function(){return{current:null}},ie.forwardRef=function(A){return{$$typeof:Ue,render:A}},ie.isValidElement=Gt,ie.lazy=function(A){return{$$typeof:z,_payload:{_status:-1,_result:A},_init:q}},ie.memo=function(A,w){return{$$typeof:_,type:A,compare:w===void 0?null:w}},ie.startTransition=function(A){var w=ye.T,Z={};ye.T=Z;try{var $=A(),P=ye.S;P!==null&&P(Z,$),typeof $=="object"&&$!==null&&typeof $.then=="function"&&$.then(ne,ee)}catch(fe){ee(fe)}finally{ye.T=w}},ie.unstable_useCacheRefresh=function(){return ye.H.useCacheRefresh()},ie.use=function(A){return ye.H.use(A)},ie.useActionState=function(A,w,Z){return ye.H.useActionState(A,w,Z)},ie.useCallback=function(A,w){return ye.H.useCallback(A,w)},ie.useContext=function(A){return ye.H.useContext(A)},ie.useDebugValue=function(){},ie.useDeferredValue=function(A,w){return ye.H.useDeferredValue(A,w)},ie.useEffect=function(A,w,Z){var $=ye.H;if(typeof Z=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return $.useEffect(A,w)},ie.useId=function(){return ye.H.useId()},ie.useImperativeHandle=function(A,w,Z){return ye.H.useImperativeHandle(A,w,Z)},ie.useInsertionEffect=function(A,w){return ye.H.useInsertionEffect(A,w)},ie.useLayoutEffect=function(A,w){return ye.H.useLayoutEffect(A,w)},ie.useMemo=function(A,w){return ye.H.useMemo(A,w)},ie.useOptimistic=function(A,w){return ye.H.useOptimistic(A,w)},ie.useReducer=function(A,w,Z){return ye.H.useReducer(A,w,Z)},ie.useRef=function(A){return ye.H.useRef(A)},ie.useState=function(A){return ye.H.useState(A)},ie.useSyncExternalStore=function(A,w,Z){return ye.H.useSyncExternalStore(A,w,Z)},ie.useTransition=function(){return ye.H.useTransition()},ie.version="19.1.0",ie}var nh;function Qf(){return nh||(nh=1,qf.exports=jm()),qf.exports}var Q=Qf(),xf={exports:{}},ui={},wf={exports:{}},Yf={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var uh;function qm(){return uh||(uh=1,function(K){function Te(M,q){var ee=M.length;M.push(q);e:for(;0<ee;){var ne=ee-1>>>1,A=M[ne];if(0<v(A,q))M[ne]=q,M[ee]=A,ee=ne;else break e}}function Ae(M){return M.length===0?null:M[0]}function O(M){if(M.length===0)return null;var q=M[0],ee=M.pop();if(ee!==q){M[0]=ee;e:for(var ne=0,A=M.length,w=A>>>1;ne<w;){var Z=2*(ne+1)-1,$=M[Z],P=Z+1,fe=M[P];if(0>v($,ee))P<A&&0>v(fe,$)?(M[ne]=fe,M[P]=ee,ne=P):(M[ne]=$,M[Z]=ee,ne=Z);else if(P<A&&0>v(fe,ee))M[ne]=fe,M[P]=ee,ne=P;else break e}}return q}function v(M,q){var ee=M.sortIndex-q.sortIndex;return ee!==0?ee:M.id-q.id}if(K.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var Y=performance;K.unstable_now=function(){return Y.now()}}else{var x=Date,Ue=x.now();K.unstable_now=function(){return x.now()-Ue}}var V=[],_=[],z=1,X=null,he=3,Pe=!1,ut=!1,N=!1,Qe=!1,ca=typeof setTimeout=="function"?setTimeout:null,Ea=typeof clearTimeout=="function"?clearTimeout:null,Ze=typeof setImmediate<"u"?setImmediate:null;function Bt(M){for(var q=Ae(_);q!==null;){if(q.callback===null)O(_);else if(q.startTime<=M)O(_),q.sortIndex=q.expirationTime,Te(V,q);else break;q=Ae(_)}}function ye(M){if(N=!1,Bt(M),!ut)if(Ae(V)!==null)ut=!0,At||(At=!0,He());else{var q=Ae(_);q!==null&&et(ye,q.startTime-M)}}var At=!1,bt=-1,Je=5,Gt=-1;function kt(){return Qe?!0:!(K.unstable_now()-Gt<Je)}function sa(){if(Qe=!1,At){var M=K.unstable_now();Gt=M;var q=!0;try{e:{ut=!1,N&&(N=!1,Ea(bt),bt=-1),Pe=!0;var ee=he;try{t:{for(Bt(M),X=Ae(V);X!==null&&!(X.expirationTime>M&&kt());){var ne=X.callback;if(typeof ne=="function"){X.callback=null,he=X.priorityLevel;var A=ne(X.expirationTime<=M);if(M=K.unstable_now(),typeof A=="function"){X.callback=A,Bt(M),q=!0;break t}X===Ae(V)&&O(V),Bt(M)}else O(V);X=Ae(V)}if(X!==null)q=!0;else{var w=Ae(_);w!==null&&et(ye,w.startTime-M),q=!1}}break e}finally{X=null,he=ee,Pe=!1}q=void 0}}finally{q?He():At=!1}}}var He;if(typeof Ze=="function")He=function(){Ze(sa)};else if(typeof MessageChannel<"u"){var za=new MessageChannel,fa=za.port2;za.port1.onmessage=sa,He=function(){fa.postMessage(null)}}else He=function(){ca(sa,0)};function et(M,q){bt=ca(function(){M(K.unstable_now())},q)}K.unstable_IdlePriority=5,K.unstable_ImmediatePriority=1,K.unstable_LowPriority=4,K.unstable_NormalPriority=3,K.unstable_Profiling=null,K.unstable_UserBlockingPriority=2,K.unstable_cancelCallback=function(M){M.callback=null},K.unstable_forceFrameRate=function(M){0>M||125<M?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Je=0<M?Math.floor(1e3/M):5},K.unstable_getCurrentPriorityLevel=function(){return he},K.unstable_next=function(M){switch(he){case 1:case 2:case 3:var q=3;break;default:q=he}var ee=he;he=q;try{return M()}finally{he=ee}},K.unstable_requestPaint=function(){Qe=!0},K.unstable_runWithPriority=function(M,q){switch(M){case 1:case 2:case 3:case 4:case 5:break;default:M=3}var ee=he;he=M;try{return q()}finally{he=ee}},K.unstable_scheduleCallback=function(M,q,ee){var ne=K.unstable_now();switch(typeof ee=="object"&&ee!==null?(ee=ee.delay,ee=typeof ee=="number"&&0<ee?ne+ee:ne):ee=ne,M){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=ee+A,M={id:z++,callback:q,priorityLevel:M,startTime:ee,expirationTime:A,sortIndex:-1},ee>ne?(M.sortIndex=ee,Te(_,M),Ae(V)===null&&M===Ae(_)&&(N?(Ea(bt),bt=-1):N=!0,et(ye,ee-ne))):(M.sortIndex=A,Te(V,M),ut||Pe||(ut=!0,At||(At=!0,He()))),M},K.unstable_shouldYield=kt,K.unstable_wrapCallback=function(M){var q=he;return function(){var ee=he;he=q;try{return M.apply(this,arguments)}finally{he=ee}}}}(Yf)),Yf}var ih;function xm(){return ih||(ih=1,wf.exports=qm()),wf.exports}var Vf={exports:{}},St={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var ch;function wm(){if(ch)return St;ch=1;var K=Qf();function Te(V){var _="https://react.dev/errors/"+V;if(1<arguments.length){_+="?args[]="+encodeURIComponent(arguments[1]);for(var z=2;z<arguments.length;z++)_+="&args[]="+encodeURIComponent(arguments[z])}return"Minified React error #"+V+"; visit "+_+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Ae(){}var O={d:{f:Ae,r:function(){throw Error(Te(522))},D:Ae,C:Ae,L:Ae,m:Ae,X:Ae,S:Ae,M:Ae},p:0,findDOMNode:null},v=Symbol.for("react.portal");function Y(V,_,z){var X=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:v,key:X==null?null:""+X,children:V,containerInfo:_,implementation:z}}var x=K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function Ue(V,_){if(V==="font")return"";if(typeof _=="string")return _==="use-credentials"?_:""}return St.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=O,St.createPortal=function(V,_){var z=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!_||_.nodeType!==1&&_.nodeType!==9&&_.nodeType!==11)throw Error(Te(299));return Y(V,_,null,z)},St.flushSync=function(V){var _=x.T,z=O.p;try{if(x.T=null,O.p=2,V)return V()}finally{x.T=_,O.p=z,O.d.f()}},St.preconnect=function(V,_){typeof V=="string"&&(_?(_=_.crossOrigin,_=typeof _=="string"?_==="use-credentials"?_:"":void 0):_=null,O.d.C(V,_))},St.prefetchDNS=function(V){typeof V=="string"&&O.d.D(V)},St.preinit=function(V,_){if(typeof V=="string"&&_&&typeof _.as=="string"){var z=_.as,X=Ue(z,_.crossOrigin),he=typeof _.integrity=="string"?_.integrity:void 0,Pe=typeof _.fetchPriority=="string"?_.fetchPriority:void 0;z==="style"?O.d.S(V,typeof _.precedence=="string"?_.precedence:void 0,{crossOrigin:X,integrity:he,fetchPriority:Pe}):z==="script"&&O.d.X(V,{crossOrigin:X,integrity:he,fetchPriority:Pe,nonce:typeof _.nonce=="string"?_.nonce:void 0})}},St.preinitModule=function(V,_){if(typeof V=="string")if(typeof _=="object"&&_!==null){if(_.as==null||_.as==="script"){var z=Ue(_.as,_.crossOrigin);O.d.M(V,{crossOrigin:z,integrity:typeof _.integrity=="string"?_.integrity:void 0,nonce:typeof _.nonce=="string"?_.nonce:void 0})}}else _==null&&O.d.M(V)},St.preload=function(V,_){if(typeof V=="string"&&typeof _=="object"&&_!==null&&typeof _.as=="string"){var z=_.as,X=Ue(z,_.crossOrigin);O.d.L(V,z,{crossOrigin:X,integrity:typeof _.integrity=="string"?_.integrity:void 0,nonce:typeof _.nonce=="string"?_.nonce:void 0,type:typeof _.type=="string"?_.type:void 0,fetchPriority:typeof _.fetchPriority=="string"?_.fetchPriority:void 0,referrerPolicy:typeof _.referrerPolicy=="string"?_.referrerPolicy:void 0,imageSrcSet:typeof _.imageSrcSet=="string"?_.imageSrcSet:void 0,imageSizes:typeof _.imageSizes=="string"?_.imageSizes:void 0,media:typeof _.media=="string"?_.media:void 0})}},St.preloadModule=function(V,_){if(typeof V=="string")if(_){var z=Ue(_.as,_.crossOrigin);O.d.m(V,{as:typeof _.as=="string"&&_.as!=="script"?_.as:void 0,crossOrigin:z,integrity:typeof _.integrity=="string"?_.integrity:void 0})}else O.d.m(V)},St.requestFormReset=function(V){O.d.r(V)},St.unstable_batchedUpdates=function(V,_){return V(_)},St.useFormState=function(V,_,z){return x.H.useFormState(V,_,z)},St.useFormStatus=function(){return x.H.useHostTransitionStatus()},St.version="19.1.0",St}var sh;function Ym(){if(sh)return Vf.exports;sh=1;function K(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(K)}catch(Te){console.error(Te)}}return K(),Vf.exports=wm(),Vf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fh;function Vm(){if(fh)return ui;fh=1;var K=xm(),Te=Qf(),Ae=Ym();function O(e){var t="https://react.dev/errors/"+e;if(1<arguments.length){t+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)t+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+e+"; visit "+t+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function v(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function Y(e){var t=e,a=e;if(e.alternate)for(;t.return;)t=t.return;else{e=t;do t=e,(t.flags&4098)!==0&&(a=t.return),e=t.return;while(e)}return t.tag===3?a:null}function x(e){if(e.tag===13){var t=e.memoizedState;if(t===null&&(e=e.alternate,e!==null&&(t=e.memoizedState)),t!==null)return t.dehydrated}return null}function Ue(e){if(Y(e)!==e)throw Error(O(188))}function V(e){var t=e.alternate;if(!t){if(t=Y(e),t===null)throw Error(O(188));return t!==e?null:e}for(var a=e,l=t;;){var n=a.return;if(n===null)break;var u=n.alternate;if(u===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===a)return Ue(n),e;if(u===l)return Ue(n),t;u=u.sibling}throw Error(O(188))}if(a.return!==l.return)a=n,l=u;else{for(var f=!1,d=n.child;d;){if(d===a){f=!0,a=n,l=u;break}if(d===l){f=!0,l=n,a=u;break}d=d.sibling}if(!f){for(d=u.child;d;){if(d===a){f=!0,a=u,l=n;break}if(d===l){f=!0,l=u,a=n;break}d=d.sibling}if(!f)throw Error(O(189))}}if(a.alternate!==l)throw Error(O(190))}if(a.tag!==3)throw Error(O(188));return a.stateNode.current===a?e:t}function _(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e;for(e=e.child;e!==null;){if(t=_(e),t!==null)return t;e=e.sibling}return null}var z=Object.assign,X=Symbol.for("react.element"),he=Symbol.for("react.transitional.element"),Pe=Symbol.for("react.portal"),ut=Symbol.for("react.fragment"),N=Symbol.for("react.strict_mode"),Qe=Symbol.for("react.profiler"),ca=Symbol.for("react.provider"),Ea=Symbol.for("react.consumer"),Ze=Symbol.for("react.context"),Bt=Symbol.for("react.forward_ref"),ye=Symbol.for("react.suspense"),At=Symbol.for("react.suspense_list"),bt=Symbol.for("react.memo"),Je=Symbol.for("react.lazy"),Gt=Symbol.for("react.activity"),kt=Symbol.for("react.memo_cache_sentinel"),sa=Symbol.iterator;function He(e){return e===null||typeof e!="object"?null:(e=sa&&e[sa]||e["@@iterator"],typeof e=="function"?e:null)}var za=Symbol.for("react.client.reference");function fa(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===za?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case ut:return"Fragment";case Qe:return"Profiler";case N:return"StrictMode";case ye:return"Suspense";case At:return"SuspenseList";case Gt:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case Pe:return"Portal";case Ze:return(e.displayName||"Context")+".Provider";case Ea:return(e._context.displayName||"Context")+".Consumer";case Bt:var t=e.render;return e=e.displayName,e||(e=t.displayName||t.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case bt:return t=e.displayName||null,t!==null?t:fa(e.type)||"Memo";case Je:t=e._payload,e=e._init;try{return fa(e(t))}catch{}}return null}var et=Array.isArray,M=Te.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=Ae.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ee={pending:!1,data:null,method:null,action:null},ne=[],A=-1;function w(e){return{current:e}}function Z(e){0>A||(e.current=ne[A],ne[A]=null,A--)}function $(e,t){A++,ne[A]=e.current,e.current=t}var P=w(null),fe=w(null),ue=w(null),ht=w(null);function me(e,t){switch($(ue,t),$(fe,e),$(P,null),t.nodeType){case 9:case 11:e=(e=t.documentElement)&&(e=e.namespaceURI)?_d(e):0;break;default:if(e=t.tagName,t=t.namespaceURI)t=_d(t),e=Md(t,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}Z(P),$(P,e)}function Jt(){Z(P),Z(fe),Z(ue)}function iu(e){e.memoizedState!==null&&$(ht,e);var t=P.current,a=Md(t,e.type);t!==a&&($(fe,e),$(P,a))}function ll(e){fe.current===e&&(Z(P),Z(fe)),ht.current===e&&(Z(ht),Pu._currentValue=ee)}var nl=Object.prototype.hasOwnProperty,xl=K.unstable_scheduleCallback,ci=K.unstable_cancelCallback,La=K.unstable_shouldYield,ra=K.unstable_requestPaint,Dt=K.unstable_now,Zf=K.unstable_getCurrentPriorityLevel,cu=K.unstable_ImmediatePriority,Rt=K.unstable_UserBlockingPriority,wl=K.unstable_NormalPriority,_c=K.unstable_LowPriority,oa=K.unstable_IdlePriority,si=K.log,fi=K.unstable_setDisableYieldValue,Yl=null,it=null;function Ut(e){if(typeof si=="function"&&fi(e),it&&typeof it.setStrictMode=="function")try{it.setStrictMode(Yl,e)}catch{}}var mt=Math.clz32?Math.clz32:Mc,ri=Math.log,oi=Math.LN2;function Mc(e){return e>>>=0,e===0?32:31-(ri(e)/oi|0)|0}var ul=256,Ht=4194304;function Ta(e){var t=e&42;if(t!==0)return t;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function Vl(e,t,a){var l=e.pendingLanes;if(l===0)return 0;var n=0,u=e.suspendedLanes,f=e.pingedLanes;e=e.warmLanes;var d=l&134217727;return d!==0?(l=d&~u,l!==0?n=Ta(l):(f&=d,f!==0?n=Ta(f):a||(a=d&~e,a!==0&&(n=Ta(a))))):(d=l&~u,d!==0?n=Ta(d):f!==0?n=Ta(f):a||(a=l&~e,a!==0&&(n=Ta(a)))),n===0?0:t!==0&&t!==n&&(t&u)===0&&(u=n&-n,a=t&-t,u>=a||u===32&&(a&4194048)!==0)?t:n}function il(e,t){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&t)===0}function di(e,t){switch(e){case 1:case 2:case 4:case 8:case 64:return t+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function cl(){var e=ul;return ul<<=1,(ul&4194048)===0&&(ul=256),e}function hi(){var e=Ht;return Ht<<=1,(Ht&62914560)===0&&(Ht=4194304),e}function sl(e){for(var t=[],a=0;31>a;a++)t.push(e);return t}function Xl(e,t){e.pendingLanes|=t,t!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function Bc(e,t,a,l,n,u){var f=e.pendingLanes;e.pendingLanes=a,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=a,e.entangledLanes&=a,e.errorRecoveryDisabledLanes&=a,e.shellSuspendCounter=0;var d=e.entanglements,g=e.expirationTimes,D=e.hiddenUpdates;for(a=f&~a;0<a;){var B=31-mt(a),L=1<<B;d[B]=0,g[B]=-1;var R=D[B];if(R!==null)for(D[B]=null,B=0;B<R.length;B++){var C=R[B];C!==null&&(C.lane&=-536870913)}a&=~L}l!==0&&ja(e,l,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(f&~t))}function ja(e,t,a){e.pendingLanes|=t,e.suspendedLanes&=~t;var l=31-mt(t);e.entangledLanes|=t,e.entanglements[l]=e.entanglements[l]|1073741824|a&4194090}function fl(e,t){var a=e.entangledLanes|=t;for(e=e.entanglements;a;){var l=31-mt(a),n=1<<l;n&t|e[l]&t&&(e[l]|=t),a&=~n}}function We(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function qa(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function _e(){var e=q.p;return e!==0?e:(e=window.event,e===void 0?32:Jd(e.type))}function yn(e,t){var a=q.p;try{return q.p=e,t()}finally{q.p=a}}var De=Math.random().toString(36).slice(2),je="__reactFiber$"+De,pe="__reactProps$"+De,tt="__reactContainer$"+De,ct="__reactEvents$"+De,da="__reactListeners$"+De,Ql="__reactHandles$"+De,Zl="__reactResources$"+De,pa="__reactMarker$"+De;function su(e){delete e[je],delete e[pe],delete e[ct],delete e[da],delete e[Ql]}function Na(e){var t=e[je];if(t)return t;for(var a=e.parentNode;a;){if(t=a[tt]||a[je]){if(a=t.alternate,t.child!==null||a!==null&&a.child!==null)for(e=Hd(e);e!==null;){if(a=e[je])return a;e=Hd(e)}return t}e=a,a=e.parentNode}return null}function ha(e){if(e=e[je]||e[tt]){var t=e.tag;if(t===5||t===6||t===13||t===26||t===27||t===3)return e}return null}function xa(e){var t=e.tag;if(t===5||t===26||t===27||t===6)return e.stateNode;throw Error(O(33))}function rl(e){var t=e[Zl];return t||(t=e[Zl]={hoistableStyles:new Map,hoistableScripts:new Map}),t}function qe(e){e[pa]=!0}var Wt=new Set,gn={};function yt(e,t){ma(e,t),ma(e+"Capture",t)}function ma(e,t){for(gn[e]=t,e=0;e<t.length;e++)Wt.add(t[e])}var mi=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),ya={},yi={};function gi(e){return nl.call(yi,e)?!0:nl.call(ya,e)?!1:mi.test(e)?yi[e]=!0:(ya[e]=!0,!1)}function vn(e,t,a){if(gi(t))if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":e.removeAttribute(t);return;case"boolean":var l=t.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(t);return}}e.setAttribute(t,""+a)}}function $l(e,t,a){if(a===null)e.removeAttribute(t);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttribute(t,""+a)}}function Et(e,t,a,l){if(l===null)e.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttributeNS(t,a,""+l)}}var Sn,ba;function Da(e){if(Sn===void 0)try{throw Error()}catch(a){var t=a.stack.trim().match(/\n( *(at )?)/);Sn=t&&t[1]||"",ba=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Sn+e+ba}var ol=!1;function dl(e,t){if(!e||ol)return"";ol=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(t){var L=function(){throw Error()};if(Object.defineProperty(L.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(L,[])}catch(C){var R=C}Reflect.construct(e,[],L)}else{try{L.call()}catch(C){R=C}e.call(L.prototype)}}else{try{throw Error()}catch(C){R=C}(L=e())&&typeof L.catch=="function"&&L.catch(function(){})}}catch(C){if(C&&R&&typeof C.stack=="string")return[C.stack,R.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),f=u[0],d=u[1];if(f&&d){var g=f.split(`
`),D=d.split(`
`);for(n=l=0;l<g.length&&!g[l].includes("DetermineComponentFrameRoot");)l++;for(;n<D.length&&!D[n].includes("DetermineComponentFrameRoot");)n++;if(l===g.length||n===D.length)for(l=g.length-1,n=D.length-1;1<=l&&0<=n&&g[l]!==D[n];)n--;for(;1<=l&&0<=n;l--,n--)if(g[l]!==D[n]){if(l!==1||n!==1)do if(l--,n--,0>n||g[l]!==D[n]){var B=`
`+g[l].replace(" at new "," at ");return e.displayName&&B.includes("<anonymous>")&&(B=B.replace("<anonymous>",e.displayName)),B}while(1<=l&&0<=n);break}}}finally{ol=!1,Error.prepareStackTrace=a}return(a=e?e.displayName||e.name:"")?Da(a):""}function Tt(e){switch(e.tag){case 26:case 27:case 5:return Da(e.type);case 16:return Da("Lazy");case 13:return Da("Suspense");case 19:return Da("SuspenseList");case 0:case 15:return dl(e.type,!1);case 11:return dl(e.type.render,!1);case 1:return dl(e.type,!0);case 31:return Da("Activity");default:return""}}function An(e){try{var t="";do t+=Tt(e),e=e.return;while(e);return t}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function st(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function Kl(e){var t=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(t==="checkbox"||t==="radio")}function vi(e){var t=Kl(e)?"checked":"value",a=Object.getOwnPropertyDescriptor(e.constructor.prototype,t),l=""+e[t];if(!e.hasOwnProperty(t)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,u=a.set;return Object.defineProperty(e,t,{configurable:!0,get:function(){return n.call(this)},set:function(f){l=""+f,u.call(this,f)}}),Object.defineProperty(e,t,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(f){l=""+f},stopTracking:function(){e._valueTracker=null,delete e[t]}}}}function kl(e){e._valueTracker||(e._valueTracker=vi(e))}function Si(e){if(!e)return!1;var t=e._valueTracker;if(!t)return!0;var a=t.getValue(),l="";return e&&(l=Kl(e)?e.checked?"true":"false":e.value),e=l,e!==a?(t.setValue(e),!0):!1}function En(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var Ge=/[\n"\\]/g;function $e(e){return e.replace(Ge,function(t){return"\\"+t.charCodeAt(0).toString(16)+" "})}function fu(e,t,a,l,n,u,f,d){e.name="",f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"?e.type=f:e.removeAttribute("type"),t!=null?f==="number"?(t===0&&e.value===""||e.value!=t)&&(e.value=""+st(t)):e.value!==""+st(t)&&(e.value=""+st(t)):f!=="submit"&&f!=="reset"||e.removeAttribute("value"),t!=null?ru(e,f,st(t)):a!=null?ru(e,f,st(a)):l!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?e.name=""+st(d):e.removeAttribute("name")}function Ai(e,t,a,l,n,u,f,d){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),t!=null||a!=null){if(!(u!=="submit"&&u!=="reset"||t!=null))return;a=a!=null?""+st(a):"",t=t!=null?""+st(t):a,d||t===e.value||(e.value=t),e.defaultValue=t}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=d?e.checked:!!l,e.defaultChecked=!!l,f!=null&&typeof f!="function"&&typeof f!="symbol"&&typeof f!="boolean"&&(e.name=f)}function ru(e,t,a){t==="number"&&En(e.ownerDocument)===e||e.defaultValue===""+a||(e.defaultValue=""+a)}function Ft(e,t,a,l){if(e=e.options,t){t={};for(var n=0;n<a.length;n++)t["$"+a[n]]=!0;for(a=0;a<e.length;a++)n=t.hasOwnProperty("$"+e[a].value),e[a].selected!==n&&(e[a].selected=n),n&&l&&(e[a].defaultSelected=!0)}else{for(a=""+st(a),t=null,n=0;n<e.length;n++){if(e[n].value===a){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}t!==null||e[n].disabled||(t=e[n])}t!==null&&(t.selected=!0)}}function Ra(e,t,a){if(t!=null&&(t=""+st(t),t!==e.value&&(e.value=t),a==null)){e.defaultValue!==t&&(e.defaultValue=t);return}e.defaultValue=a!=null?""+st(a):""}function hl(e,t,a,l){if(t==null){if(l!=null){if(a!=null)throw Error(O(92));if(et(l)){if(1<l.length)throw Error(O(93));l=l[0]}a=l}a==null&&(a=""),t=a}a=st(t),e.defaultValue=a,l=e.textContent,l===a&&l!==""&&l!==null&&(e.value=l)}function ga(e,t){if(t){var a=e.firstChild;if(a&&a===e.lastChild&&a.nodeType===3){a.nodeValue=t;return}}e.textContent=t}var zt=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Lt(e,t,a){var l=t.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?e.setProperty(t,""):t==="float"?e.cssFloat="":e[t]="":l?e.setProperty(t,a):typeof a!="number"||a===0||zt.has(t)?t==="float"?e.cssFloat=a:e[t]=(""+a).trim():e[t]=a+"px"}function It(e,t,a){if(t!=null&&typeof t!="object")throw Error(O(62));if(e=e.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||t!=null&&t.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in t)l=t[n],t.hasOwnProperty(n)&&a[n]!==l&&Lt(e,n,l)}else for(var u in t)t.hasOwnProperty(u)&&Lt(e,u,t[u])}function jt(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var pt=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),ml=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Tn(e){return ml.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var pn=null;function ou(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var qt=null,xt=null;function wa(e){var t=ha(e);if(t&&(e=t.stateNode)){var a=e[pe]||null;e:switch(e=t.stateNode,t.type){case"input":if(fu(e,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),t=a.name,a.type==="radio"&&t!=null){for(a=e;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+$e(""+t)+'"][type="radio"]'),t=0;t<a.length;t++){var l=a[t];if(l!==e&&l.form===e.form){var n=l[pe]||null;if(!n)throw Error(O(90));fu(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(t=0;t<a.length;t++)l=a[t],l.form===e.form&&Si(l)}break e;case"textarea":Ra(e,a.value,a.defaultValue);break e;case"select":t=a.value,t!=null&&Ft(e,!!a.multiple,t,!1)}}}var du=!1;function Ei(e,t,a){if(du)return e(t,a);du=!0;try{var l=e(t);return l}finally{if(du=!1,(qt!==null||xt!==null)&&(sc(),qt&&(t=qt,e=xt,xt=qt=null,wa(t),e)))for(t=0;t<e.length;t++)wa(e[t])}}function Jl(e,t){var a=e.stateNode;if(a===null)return null;var l=a[pe]||null;if(l===null)return null;a=l[t];e:switch(t){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(a&&typeof a!="function")throw Error(O(231,t,typeof a));return a}var Pt=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Nn=!1;if(Pt)try{var yl={};Object.defineProperty(yl,"passive",{get:function(){Nn=!0}}),window.addEventListener("test",yl,yl),window.removeEventListener("test",yl,yl)}catch{Nn=!1}var Ct=null,Ya=null,Va=null;function Ti(){if(Va)return Va;var e,t=Ya,a=t.length,l,n="value"in Ct?Ct.value:Ct.textContent,u=n.length;for(e=0;e<a&&t[e]===n[e];e++);var f=a-e;for(l=1;l<=f&&t[a-l]===n[u-l];l++);return Va=n.slice(e,1<l?1-l:void 0)}function bn(e){var t=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&t===13&&(e=13)):e=t,e===10&&(e=13),32<=e||e===13?e:0}function Dn(){return!0}function pi(){return!1}function gt(e){function t(a,l,n,u,f){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=u,this.target=f,this.currentTarget=null;for(var d in e)e.hasOwnProperty(d)&&(a=e[d],this[d]=a?a(u):u[d]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?Dn:pi,this.isPropagationStopped=pi,this}return z(t.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=Dn)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=Dn)},persist:function(){},isPersistent:Dn}),t}var Xa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Rn=gt(Xa),Wl=z({},Xa,{view:0,detail:0}),Ni=gt(Wl),hu,Cn,gl,On=z({},Wl,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:yu,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==gl&&(gl&&e.type==="mousemove"?(hu=e.screenX-gl.screenX,Cn=e.screenY-gl.screenY):Cn=hu=0,gl=e),hu)},movementY:function(e){return"movementY"in e?e.movementY:Cn}}),_n=gt(On),Gc=z({},On,{dataTransfer:0}),Uc=gt(Gc),Hc=z({},Wl,{relatedTarget:0}),mu=gt(Hc),bi=z({},Xa,{animationName:0,elapsedTime:0,pseudoElement:0}),zc=gt(bi),Lc=z({},Xa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),jc=gt(Lc),qc=z({},Xa,{data:0}),Di=gt(qc),xc={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},Fl={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},Ri={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function Mn(e){var t=this.nativeEvent;return t.getModifierState?t.getModifierState(e):(e=Ri[e])?!!t[e]:!1}function yu(){return Mn}var wc=z({},Wl,{key:function(e){if(e.key){var t=xc[e.key]||e.key;if(t!=="Unidentified")return t}return e.type==="keypress"?(e=bn(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?Fl[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:yu,charCode:function(e){return e.type==="keypress"?bn(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?bn(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),Ca=gt(wc),Ci=z({},On,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),gu=gt(Ci),Yc=z({},Wl,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:yu}),Vc=gt(Yc),Xc=z({},Xa,{propertyName:0,elapsedTime:0,pseudoElement:0}),Qc=gt(Xc),Zc=z({},On,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),$c=gt(Zc),Kc=z({},Xa,{newState:0,oldState:0}),Oi=gt(Kc),Oa=[9,13,27,32],Bn=Pt&&"CompositionEvent"in window,vl=null;Pt&&"documentMode"in document&&(vl=document.documentMode);var _i=Pt&&"TextEvent"in window&&!vl,vu=Pt&&(!Bn||vl&&8<vl&&11>=vl),Su=" ",i=!1;function c(e,t){switch(e){case"keyup":return Oa.indexOf(t.keyCode)!==-1;case"keydown":return t.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function s(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var o=!1;function h(e,t){switch(e){case"compositionend":return s(t);case"keypress":return t.which!==32?null:(i=!0,Su);case"textInput":return e=t.data,e===Su&&i?null:e;default:return null}}function m(e,t){if(o)return e==="compositionend"||!Bn&&c(e,t)?(e=Ti(),Va=Ya=Ct=null,o=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(t.ctrlKey||t.altKey||t.metaKey)||t.ctrlKey&&t.altKey){if(t.char&&1<t.char.length)return t.char;if(t.which)return String.fromCharCode(t.which)}return null;case"compositionend":return vu&&t.locale!=="ko"?null:t.data;default:return null}}var y={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function p(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t==="input"?!!y[e.type]:t==="textarea"}function H(e,t,a,l){qt?xt?xt.push(l):xt=[l]:qt=l,t=mc(t,"onChange"),0<t.length&&(a=new Rn("onChange","change",null,a,l),e.push({event:a,listeners:t}))}var j=null,k=null;function I(e){bd(e,0)}function W(e){var t=xa(e);if(Si(t))return e}function re(e,t){if(e==="change")return t}var xe=!1;if(Pt){var ft;if(Pt){var Il="oninput"in document;if(!Il){var $f=document.createElement("div");$f.setAttribute("oninput","return;"),Il=typeof $f.oninput=="function"}ft=Il}else ft=!1;xe=ft&&(!document.documentMode||9<document.documentMode)}function Kf(){j&&(j.detachEvent("onpropertychange",kf),k=j=null)}function kf(e){if(e.propertyName==="value"&&W(k)){var t=[];H(t,k,e,ou(e)),Ei(I,t)}}function oh(e,t,a){e==="focusin"?(Kf(),j=t,k=a,j.attachEvent("onpropertychange",kf)):e==="focusout"&&Kf()}function dh(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return W(k)}function hh(e,t){if(e==="click")return W(t)}function mh(e,t){if(e==="input"||e==="change")return W(t)}function yh(e,t){return e===t&&(e!==0||1/e===1/t)||e!==e&&t!==t}var wt=typeof Object.is=="function"?Object.is:yh;function Au(e,t){if(wt(e,t))return!0;if(typeof e!="object"||e===null||typeof t!="object"||t===null)return!1;var a=Object.keys(e),l=Object.keys(t);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!nl.call(t,n)||!wt(e[n],t[n]))return!1}return!0}function Jf(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function Wf(e,t){var a=Jf(e);e=0;for(var l;a;){if(a.nodeType===3){if(l=e+a.textContent.length,e<=t&&l>=t)return{node:a,offset:t-e};e=l}e:{for(;a;){if(a.nextSibling){a=a.nextSibling;break e}a=a.parentNode}a=void 0}a=Jf(a)}}function Ff(e,t){return e&&t?e===t?!0:e&&e.nodeType===3?!1:t&&t.nodeType===3?Ff(e,t.parentNode):"contains"in e?e.contains(t):e.compareDocumentPosition?!!(e.compareDocumentPosition(t)&16):!1:!1}function If(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var t=En(e.document);t instanceof e.HTMLIFrameElement;){try{var a=typeof t.contentWindow.location.href=="string"}catch{a=!1}if(a)e=t.contentWindow;else break;t=En(e.document)}return t}function kc(e){var t=e&&e.nodeName&&e.nodeName.toLowerCase();return t&&(t==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||t==="textarea"||e.contentEditable==="true")}var gh=Pt&&"documentMode"in document&&11>=document.documentMode,Gn=null,Jc=null,Eu=null,Wc=!1;function Pf(e,t,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;Wc||Gn==null||Gn!==En(l)||(l=Gn,"selectionStart"in l&&kc(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Eu&&Au(Eu,l)||(Eu=l,l=mc(Jc,"onSelect"),0<l.length&&(t=new Rn("onSelect","select",null,t,a),e.push({event:t,listeners:l}),t.target=Gn)))}function Pl(e,t){var a={};return a[e.toLowerCase()]=t.toLowerCase(),a["Webkit"+e]="webkit"+t,a["Moz"+e]="moz"+t,a}var Un={animationend:Pl("Animation","AnimationEnd"),animationiteration:Pl("Animation","AnimationIteration"),animationstart:Pl("Animation","AnimationStart"),transitionrun:Pl("Transition","TransitionRun"),transitionstart:Pl("Transition","TransitionStart"),transitioncancel:Pl("Transition","TransitionCancel"),transitionend:Pl("Transition","TransitionEnd")},Fc={},er={};Pt&&(er=document.createElement("div").style,"AnimationEvent"in window||(delete Un.animationend.animation,delete Un.animationiteration.animation,delete Un.animationstart.animation),"TransitionEvent"in window||delete Un.transitionend.transition);function en(e){if(Fc[e])return Fc[e];if(!Un[e])return e;var t=Un[e],a;for(a in t)if(t.hasOwnProperty(a)&&a in er)return Fc[e]=t[a];return e}var tr=en("animationend"),ar=en("animationiteration"),lr=en("animationstart"),vh=en("transitionrun"),Sh=en("transitionstart"),Ah=en("transitioncancel"),nr=en("transitionend"),ur=new Map,Ic="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ic.push("scrollEnd");function va(e,t){ur.set(e,t),yt(t,[e])}var ir=new WeakMap;function ea(e,t){if(typeof e=="object"&&e!==null){var a=ir.get(e);return a!==void 0?a:(t={value:e,source:t,stack:An(t)},ir.set(e,t),t)}return{value:e,source:t,stack:An(t)}}var ta=[],Hn=0,Pc=0;function Mi(){for(var e=Hn,t=Pc=Hn=0;t<e;){var a=ta[t];ta[t++]=null;var l=ta[t];ta[t++]=null;var n=ta[t];ta[t++]=null;var u=ta[t];if(ta[t++]=null,l!==null&&n!==null){var f=l.pending;f===null?n.next=n:(n.next=f.next,f.next=n),l.pending=n}u!==0&&cr(a,n,u)}}function Bi(e,t,a,l){ta[Hn++]=e,ta[Hn++]=t,ta[Hn++]=a,ta[Hn++]=l,Pc|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function es(e,t,a,l){return Bi(e,t,a,l),Gi(e)}function zn(e,t){return Bi(e,null,null,t),Gi(e)}function cr(e,t,a){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a);for(var n=!1,u=e.return;u!==null;)u.childLanes|=a,l=u.alternate,l!==null&&(l.childLanes|=a),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&t!==null&&(n=31-mt(a),e=u.hiddenUpdates,l=e[n],l===null?e[n]=[t]:l.push(t),t.lane=a|536870912),u):null}function Gi(e){if(50<Zu)throw Zu=0,cf=null,Error(O(185));for(var t=e.return;t!==null;)e=t,t=e.return;return e.tag===3?e.stateNode:null}var Ln={};function Eh(e,t,a,l){this.tag=e,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=t,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function Yt(e,t,a,l){return new Eh(e,t,a,l)}function ts(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Qa(e,t){var a=e.alternate;return a===null?(a=Yt(e.tag,t,e.key,e.mode),a.elementType=e.elementType,a.type=e.type,a.stateNode=e.stateNode,a.alternate=e,e.alternate=a):(a.pendingProps=t,a.type=e.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=e.flags&65011712,a.childLanes=e.childLanes,a.lanes=e.lanes,a.child=e.child,a.memoizedProps=e.memoizedProps,a.memoizedState=e.memoizedState,a.updateQueue=e.updateQueue,t=e.dependencies,a.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext},a.sibling=e.sibling,a.index=e.index,a.ref=e.ref,a.refCleanup=e.refCleanup,a}function sr(e,t){e.flags&=65011714;var a=e.alternate;return a===null?(e.childLanes=0,e.lanes=t,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=a.childLanes,e.lanes=a.lanes,e.child=a.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=a.memoizedProps,e.memoizedState=a.memoizedState,e.updateQueue=a.updateQueue,e.type=a.type,t=a.dependencies,e.dependencies=t===null?null:{lanes:t.lanes,firstContext:t.firstContext}),e}function Ui(e,t,a,l,n,u){var f=0;if(l=e,typeof e=="function")ts(e)&&(f=1);else if(typeof e=="string")f=pm(e,a,P.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case Gt:return e=Yt(31,a,t,n),e.elementType=Gt,e.lanes=u,e;case ut:return tn(a.children,n,u,t);case N:f=8,n|=24;break;case Qe:return e=Yt(12,a,t,n|2),e.elementType=Qe,e.lanes=u,e;case ye:return e=Yt(13,a,t,n),e.elementType=ye,e.lanes=u,e;case At:return e=Yt(19,a,t,n),e.elementType=At,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ca:case Ze:f=10;break e;case Ea:f=9;break e;case Bt:f=11;break e;case bt:f=14;break e;case Je:f=16,l=null;break e}f=29,a=Error(O(130,e===null?"null":typeof e,"")),l=null}return t=Yt(f,a,t,n),t.elementType=e,t.type=l,t.lanes=u,t}function tn(e,t,a,l){return e=Yt(7,e,l,t),e.lanes=a,e}function as(e,t,a){return e=Yt(6,e,null,t),e.lanes=a,e}function ls(e,t,a){return t=Yt(4,e.children!==null?e.children:[],e.key,t),t.lanes=a,t.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},t}var jn=[],qn=0,Hi=null,zi=0,aa=[],la=0,an=null,Za=1,$a="";function ln(e,t){jn[qn++]=zi,jn[qn++]=Hi,Hi=e,zi=t}function fr(e,t,a){aa[la++]=Za,aa[la++]=$a,aa[la++]=an,an=e;var l=Za;e=$a;var n=32-mt(l)-1;l&=~(1<<n),a+=1;var u=32-mt(t)+n;if(30<u){var f=n-n%5;u=(l&(1<<f)-1).toString(32),l>>=f,n-=f,Za=1<<32-mt(t)+n|a<<n|l,$a=u+e}else Za=1<<u|a<<n|l,$a=e}function ns(e){e.return!==null&&(ln(e,1),fr(e,1,0))}function us(e){for(;e===Hi;)Hi=jn[--qn],jn[qn]=null,zi=jn[--qn],jn[qn]=null;for(;e===an;)an=aa[--la],aa[la]=null,$a=aa[--la],aa[la]=null,Za=aa[--la],aa[la]=null}var Nt=null,we=null,Ee=!1,nn=null,_a=!1,is=Error(O(519));function un(e){var t=Error(O(418,""));throw Nu(ea(t,e)),is}function rr(e){var t=e.stateNode,a=e.type,l=e.memoizedProps;switch(t[je]=e,t[pe]=l,a){case"dialog":de("cancel",t),de("close",t);break;case"iframe":case"object":case"embed":de("load",t);break;case"video":case"audio":for(a=0;a<Ku.length;a++)de(Ku[a],t);break;case"source":de("error",t);break;case"img":case"image":case"link":de("error",t),de("load",t);break;case"details":de("toggle",t);break;case"input":de("invalid",t),Ai(t,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),kl(t);break;case"select":de("invalid",t);break;case"textarea":de("invalid",t),hl(t,l.value,l.defaultValue,l.children),kl(t)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||t.textContent===""+a||l.suppressHydrationWarning===!0||Od(t.textContent,a)?(l.popover!=null&&(de("beforetoggle",t),de("toggle",t)),l.onScroll!=null&&de("scroll",t),l.onScrollEnd!=null&&de("scrollend",t),l.onClick!=null&&(t.onclick=yc),t=!0):t=!1,t||un(e)}function or(e){for(Nt=e.return;Nt;)switch(Nt.tag){case 5:case 13:_a=!1;return;case 27:case 3:_a=!0;return;default:Nt=Nt.return}}function Tu(e){if(e!==Nt)return!1;if(!Ee)return or(e),Ee=!0,!1;var t=e.tag,a;if((a=t!==3&&t!==27)&&((a=t===5)&&(a=e.type,a=!(a!=="form"&&a!=="button")||Nf(e.type,e.memoizedProps)),a=!a),a&&we&&un(e),or(e),t===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(O(317));e:{for(e=e.nextSibling,t=0;e;){if(e.nodeType===8)if(a=e.data,a==="/$"){if(t===0){we=Aa(e.nextSibling);break e}t--}else a!=="$"&&a!=="$!"&&a!=="$?"||t++;e=e.nextSibling}we=null}}else t===27?(t=we,Ul(e.type)?(e=Cf,Cf=null,we=e):we=t):we=Nt?Aa(e.stateNode.nextSibling):null;return!0}function pu(){we=Nt=null,Ee=!1}function dr(){var e=nn;return e!==null&&(Mt===null?Mt=e:Mt.push.apply(Mt,e),nn=null),e}function Nu(e){nn===null?nn=[e]:nn.push(e)}var cs=w(null),cn=null,Ka=null;function Sl(e,t,a){$(cs,t._currentValue),t._currentValue=a}function ka(e){e._currentValue=cs.current,Z(cs)}function ss(e,t,a){for(;e!==null;){var l=e.alternate;if((e.childLanes&t)!==t?(e.childLanes|=t,l!==null&&(l.childLanes|=t)):l!==null&&(l.childLanes&t)!==t&&(l.childLanes|=t),e===a)break;e=e.return}}function fs(e,t,a,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var f=n.child;u=u.firstContext;e:for(;u!==null;){var d=u;u=n;for(var g=0;g<t.length;g++)if(d.context===t[g]){u.lanes|=a,d=u.alternate,d!==null&&(d.lanes|=a),ss(u.return,a,e),l||(f=null);break e}u=d.next}}else if(n.tag===18){if(f=n.return,f===null)throw Error(O(341));f.lanes|=a,u=f.alternate,u!==null&&(u.lanes|=a),ss(f,a,e),f=null}else f=n.child;if(f!==null)f.return=n;else for(f=n;f!==null;){if(f===e){f=null;break}if(n=f.sibling,n!==null){n.return=f.return,f=n;break}f=f.return}n=f}}function bu(e,t,a,l){e=null;for(var n=t,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var f=n.alternate;if(f===null)throw Error(O(387));if(f=f.memoizedProps,f!==null){var d=n.type;wt(n.pendingProps.value,f.value)||(e!==null?e.push(d):e=[d])}}else if(n===ht.current){if(f=n.alternate,f===null)throw Error(O(387));f.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(Pu):e=[Pu])}n=n.return}e!==null&&fs(t,e,a,l),t.flags|=262144}function Li(e){for(e=e.firstContext;e!==null;){if(!wt(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function sn(e){cn=e,Ka=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function vt(e){return hr(cn,e)}function ji(e,t){return cn===null&&sn(e),hr(e,t)}function hr(e,t){var a=t._currentValue;if(t={context:t,memoizedValue:a,next:null},Ka===null){if(e===null)throw Error(O(308));Ka=t,e.dependencies={lanes:0,firstContext:t},e.flags|=524288}else Ka=Ka.next=t;return a}var Th=typeof AbortController<"u"?AbortController:function(){var e=[],t=this.signal={aborted:!1,addEventListener:function(a,l){e.push(l)}};this.abort=function(){t.aborted=!0,e.forEach(function(a){return a()})}},ph=K.unstable_scheduleCallback,Nh=K.unstable_NormalPriority,Fe={$$typeof:Ze,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function rs(){return{controller:new Th,data:new Map,refCount:0}}function Du(e){e.refCount--,e.refCount===0&&ph(Nh,function(){e.controller.abort()})}var Ru=null,os=0,xn=0,wn=null;function bh(e,t){if(Ru===null){var a=Ru=[];os=0,xn=mf(),wn={status:"pending",value:void 0,then:function(l){a.push(l)}}}return os++,t.then(mr,mr),t}function mr(){if(--os===0&&Ru!==null){wn!==null&&(wn.status="fulfilled");var e=Ru;Ru=null,xn=0,wn=null;for(var t=0;t<e.length;t++)(0,e[t])()}}function Dh(e,t){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return e.then(function(){l.status="fulfilled",l.value=t;for(var n=0;n<a.length;n++)(0,a[n])(t)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var yr=M.S;M.S=function(e,t){typeof t=="object"&&t!==null&&typeof t.then=="function"&&bh(e,t),yr!==null&&yr(e,t)};var fn=w(null);function ds(){var e=fn.current;return e!==null?e:Be.pooledCache}function qi(e,t){t===null?$(fn,fn.current):$(fn,t.pool)}function gr(){var e=ds();return e===null?null:{parent:Fe._currentValue,pool:e}}var Cu=Error(O(460)),vr=Error(O(474)),xi=Error(O(542)),hs={then:function(){}};function Sr(e){return e=e.status,e==="fulfilled"||e==="rejected"}function wi(){}function Ar(e,t,a){switch(a=e[a],a===void 0?e.push(t):a!==t&&(t.then(wi,wi),t=a),t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Tr(e),e;default:if(typeof t.status=="string")t.then(wi,wi);else{if(e=Be,e!==null&&100<e.shellSuspendCounter)throw Error(O(482));e=t,e.status="pending",e.then(function(l){if(t.status==="pending"){var n=t;n.status="fulfilled",n.value=l}},function(l){if(t.status==="pending"){var n=t;n.status="rejected",n.reason=l}})}switch(t.status){case"fulfilled":return t.value;case"rejected":throw e=t.reason,Tr(e),e}throw Ou=t,Cu}}var Ou=null;function Er(){if(Ou===null)throw Error(O(459));var e=Ou;return Ou=null,e}function Tr(e){if(e===Cu||e===xi)throw Error(O(483))}var Al=!1;function ms(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function ys(e,t){e=e.updateQueue,t.updateQueue===e&&(t.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function El(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function Tl(e,t,a){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(Ne&2)!==0){var n=l.pending;return n===null?t.next=t:(t.next=n.next,n.next=t),l.pending=t,t=Gi(e),cr(e,null,a),t}return Bi(e,l,t,a),Gi(e)}function _u(e,t,a){if(t=t.updateQueue,t!==null&&(t=t.shared,(a&4194048)!==0)){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,fl(e,a)}}function gs(e,t){var a=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,u=null;if(a=a.firstBaseUpdate,a!==null){do{var f={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};u===null?n=u=f:u=u.next=f,a=a.next}while(a!==null);u===null?n=u=t:u=u.next=t}else n=u=t;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},e.updateQueue=a;return}e=a.lastBaseUpdate,e===null?a.firstBaseUpdate=t:e.next=t,a.lastBaseUpdate=t}var vs=!1;function Mu(){if(vs){var e=wn;if(e!==null)throw e}}function Bu(e,t,a,l){vs=!1;var n=e.updateQueue;Al=!1;var u=n.firstBaseUpdate,f=n.lastBaseUpdate,d=n.shared.pending;if(d!==null){n.shared.pending=null;var g=d,D=g.next;g.next=null,f===null?u=D:f.next=D,f=g;var B=e.alternate;B!==null&&(B=B.updateQueue,d=B.lastBaseUpdate,d!==f&&(d===null?B.firstBaseUpdate=D:d.next=D,B.lastBaseUpdate=g))}if(u!==null){var L=n.baseState;f=0,B=D=g=null,d=u;do{var R=d.lane&-536870913,C=R!==d.lane;if(C?(ge&R)===R:(l&R)===R){R!==0&&R===xn&&(vs=!0),B!==null&&(B=B.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});e:{var le=e,te=d;R=t;var Oe=a;switch(te.tag){case 1:if(le=te.payload,typeof le=="function"){L=le.call(Oe,L,R);break e}L=le;break e;case 3:le.flags=le.flags&-65537|128;case 0:if(le=te.payload,R=typeof le=="function"?le.call(Oe,L,R):le,R==null)break e;L=z({},L,R);break e;case 2:Al=!0}}R=d.callback,R!==null&&(e.flags|=64,C&&(e.flags|=8192),C=n.callbacks,C===null?n.callbacks=[R]:C.push(R))}else C={lane:R,tag:d.tag,payload:d.payload,callback:d.callback,next:null},B===null?(D=B=C,g=L):B=B.next=C,f|=R;if(d=d.next,d===null){if(d=n.shared.pending,d===null)break;C=d,d=C.next,C.next=null,n.lastBaseUpdate=C,n.shared.pending=null}}while(!0);B===null&&(g=L),n.baseState=g,n.firstBaseUpdate=D,n.lastBaseUpdate=B,u===null&&(n.shared.lanes=0),_l|=f,e.lanes=f,e.memoizedState=L}}function pr(e,t){if(typeof e!="function")throw Error(O(191,e));e.call(t)}function Nr(e,t){var a=e.callbacks;if(a!==null)for(e.callbacks=null,e=0;e<a.length;e++)pr(a[e],t)}var Yn=w(null),Yi=w(0);function br(e,t){e=tl,$(Yi,e),$(Yn,t),tl=e|t.baseLanes}function Ss(){$(Yi,tl),$(Yn,Yn.current)}function As(){tl=Yi.current,Z(Yn),Z(Yi)}var pl=0,ce=null,Re=null,Ke=null,Vi=!1,Vn=!1,rn=!1,Xi=0,Gu=0,Xn=null,Rh=0;function Ve(){throw Error(O(321))}function Es(e,t){if(t===null)return!1;for(var a=0;a<t.length&&a<e.length;a++)if(!wt(e[a],t[a]))return!1;return!0}function Ts(e,t,a,l,n,u){return pl=u,ce=t,t.memoizedState=null,t.updateQueue=null,t.lanes=0,M.H=e===null||e.memoizedState===null?co:so,rn=!1,u=a(l,n),rn=!1,Vn&&(u=Rr(t,a,l,n)),Dr(e),u}function Dr(e){M.H=Ji;var t=Re!==null&&Re.next!==null;if(pl=0,Ke=Re=ce=null,Vi=!1,Gu=0,Xn=null,t)throw Error(O(300));e===null||at||(e=e.dependencies,e!==null&&Li(e)&&(at=!0))}function Rr(e,t,a,l){ce=e;var n=0;do{if(Vn&&(Xn=null),Gu=0,Vn=!1,25<=n)throw Error(O(301));if(n+=1,Ke=Re=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}M.H=Uh,u=t(a,l)}while(Vn);return u}function Ch(){var e=M.H,t=e.useState()[0];return t=typeof t.then=="function"?Uu(t):t,e=e.useState()[0],(Re!==null?Re.memoizedState:null)!==e&&(ce.flags|=1024),t}function ps(){var e=Xi!==0;return Xi=0,e}function Ns(e,t,a){t.updateQueue=e.updateQueue,t.flags&=-2053,e.lanes&=~a}function bs(e){if(Vi){for(e=e.memoizedState;e!==null;){var t=e.queue;t!==null&&(t.pending=null),e=e.next}Vi=!1}pl=0,Ke=Re=ce=null,Vn=!1,Gu=Xi=0,Xn=null}function Ot(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ke===null?ce.memoizedState=Ke=e:Ke=Ke.next=e,Ke}function ke(){if(Re===null){var e=ce.alternate;e=e!==null?e.memoizedState:null}else e=Re.next;var t=Ke===null?ce.memoizedState:Ke.next;if(t!==null)Ke=t,Re=e;else{if(e===null)throw ce.alternate===null?Error(O(467)):Error(O(310));Re=e,e={memoizedState:Re.memoizedState,baseState:Re.baseState,baseQueue:Re.baseQueue,queue:Re.queue,next:null},Ke===null?ce.memoizedState=Ke=e:Ke=Ke.next=e}return Ke}function Ds(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Uu(e){var t=Gu;return Gu+=1,Xn===null&&(Xn=[]),e=Ar(Xn,e,t),t=ce,(Ke===null?t.memoizedState:Ke.next)===null&&(t=t.alternate,M.H=t===null||t.memoizedState===null?co:so),e}function Qi(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return Uu(e);if(e.$$typeof===Ze)return vt(e)}throw Error(O(438,String(e)))}function Rs(e){var t=null,a=ce.updateQueue;if(a!==null&&(t=a.memoCache),t==null){var l=ce.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(t={data:l.data.map(function(n){return n.slice()}),index:0})))}if(t==null&&(t={data:[],index:0}),a===null&&(a=Ds(),ce.updateQueue=a),a.memoCache=t,a=t.data[t.index],a===void 0)for(a=t.data[t.index]=Array(e),l=0;l<e;l++)a[l]=kt;return t.index++,a}function Ja(e,t){return typeof t=="function"?t(e):t}function Zi(e){var t=ke();return Cs(t,Re,e)}function Cs(e,t,a){var l=e.queue;if(l===null)throw Error(O(311));l.lastRenderedReducer=a;var n=e.baseQueue,u=l.pending;if(u!==null){if(n!==null){var f=n.next;n.next=u.next,u.next=f}t.baseQueue=n=u,l.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{t=n.next;var d=f=null,g=null,D=t,B=!1;do{var L=D.lane&-536870913;if(L!==D.lane?(ge&L)===L:(pl&L)===L){var R=D.revertLane;if(R===0)g!==null&&(g=g.next={lane:0,revertLane:0,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null}),L===xn&&(B=!0);else if((pl&R)===R){D=D.next,R===xn&&(B=!0);continue}else L={lane:0,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},g===null?(d=g=L,f=u):g=g.next=L,ce.lanes|=R,_l|=R;L=D.action,rn&&a(u,L),u=D.hasEagerState?D.eagerState:a(u,L)}else R={lane:L,revertLane:D.revertLane,action:D.action,hasEagerState:D.hasEagerState,eagerState:D.eagerState,next:null},g===null?(d=g=R,f=u):g=g.next=R,ce.lanes|=L,_l|=L;D=D.next}while(D!==null&&D!==t);if(g===null?f=u:g.next=d,!wt(u,e.memoizedState)&&(at=!0,B&&(a=wn,a!==null)))throw a;e.memoizedState=u,e.baseState=f,e.baseQueue=g,l.lastRenderedState=u}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Os(e){var t=ke(),a=t.queue;if(a===null)throw Error(O(311));a.lastRenderedReducer=e;var l=a.dispatch,n=a.pending,u=t.memoizedState;if(n!==null){a.pending=null;var f=n=n.next;do u=e(u,f.action),f=f.next;while(f!==n);wt(u,t.memoizedState)||(at=!0),t.memoizedState=u,t.baseQueue===null&&(t.baseState=u),a.lastRenderedState=u}return[u,l]}function Cr(e,t,a){var l=ce,n=ke(),u=Ee;if(u){if(a===void 0)throw Error(O(407));a=a()}else a=t();var f=!wt((Re||n).memoizedState,a);f&&(n.memoizedState=a,at=!0),n=n.queue;var d=Mr.bind(null,l,n,e);if(Hu(2048,8,d,[e]),n.getSnapshot!==t||f||Ke!==null&&Ke.memoizedState.tag&1){if(l.flags|=2048,Qn(9,$i(),_r.bind(null,l,n,a,t),null),Be===null)throw Error(O(349));u||(pl&124)!==0||Or(l,t,a)}return a}function Or(e,t,a){e.flags|=16384,e={getSnapshot:t,value:a},t=ce.updateQueue,t===null?(t=Ds(),ce.updateQueue=t,t.stores=[e]):(a=t.stores,a===null?t.stores=[e]:a.push(e))}function _r(e,t,a,l){t.value=a,t.getSnapshot=l,Br(t)&&Gr(e)}function Mr(e,t,a){return a(function(){Br(t)&&Gr(e)})}function Br(e){var t=e.getSnapshot;e=e.value;try{var a=t();return!wt(e,a)}catch{return!0}}function Gr(e){var t=zn(e,2);t!==null&&$t(t,e,2)}function _s(e){var t=Ot();if(typeof e=="function"){var a=e;if(e=a(),rn){Ut(!0);try{a()}finally{Ut(!1)}}}return t.memoizedState=t.baseState=e,t.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ja,lastRenderedState:e},t}function Ur(e,t,a,l){return e.baseState=a,Cs(e,Re,typeof l=="function"?l:Ja)}function Oh(e,t,a,l,n){if(ki(e))throw Error(O(485));if(e=t.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(f){u.listeners.push(f)}};M.T!==null?a(!0):u.isTransition=!1,l(u),a=t.pending,a===null?(u.next=t.pending=u,Hr(t,u)):(u.next=a.next,t.pending=a.next=u)}}function Hr(e,t){var a=t.action,l=t.payload,n=e.state;if(t.isTransition){var u=M.T,f={};M.T=f;try{var d=a(n,l),g=M.S;g!==null&&g(f,d),zr(e,t,d)}catch(D){Ms(e,t,D)}finally{M.T=u}}else try{u=a(n,l),zr(e,t,u)}catch(D){Ms(e,t,D)}}function zr(e,t,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){Lr(e,t,l)},function(l){return Ms(e,t,l)}):Lr(e,t,a)}function Lr(e,t,a){t.status="fulfilled",t.value=a,jr(t),e.state=a,t=e.pending,t!==null&&(a=t.next,a===t?e.pending=null:(a=a.next,t.next=a,Hr(e,a)))}function Ms(e,t,a){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do t.status="rejected",t.reason=a,jr(t),t=t.next;while(t!==l)}e.action=null}function jr(e){e=e.listeners;for(var t=0;t<e.length;t++)(0,e[t])()}function qr(e,t){return t}function xr(e,t){if(Ee){var a=Be.formState;if(a!==null){e:{var l=ce;if(Ee){if(we){t:{for(var n=we,u=_a;n.nodeType!==8;){if(!u){n=null;break t}if(n=Aa(n.nextSibling),n===null){n=null;break t}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){we=Aa(n.nextSibling),l=n.data==="F!";break e}}un(l)}l=!1}l&&(t=a[0])}}return a=Ot(),a.memoizedState=a.baseState=t,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:qr,lastRenderedState:t},a.queue=l,a=no.bind(null,ce,l),l.dispatch=a,l=_s(!1),u=zs.bind(null,ce,!1,l.queue),l=Ot(),n={state:t,dispatch:null,action:e,pending:null},l.queue=n,a=Oh.bind(null,ce,n,u,a),n.dispatch=a,l.memoizedState=e,[t,a,!1]}function wr(e){var t=ke();return Yr(t,Re,e)}function Yr(e,t,a){if(t=Cs(e,t,qr)[0],e=Zi(Ja)[0],typeof t=="object"&&t!==null&&typeof t.then=="function")try{var l=Uu(t)}catch(f){throw f===Cu?xi:f}else l=t;t=ke();var n=t.queue,u=n.dispatch;return a!==t.memoizedState&&(ce.flags|=2048,Qn(9,$i(),_h.bind(null,n,a),null)),[l,u,e]}function _h(e,t){e.action=t}function Vr(e){var t=ke(),a=Re;if(a!==null)return Yr(t,a,e);ke(),t=t.memoizedState,a=ke();var l=a.queue.dispatch;return a.memoizedState=e,[t,l,!1]}function Qn(e,t,a,l){return e={tag:e,create:a,deps:l,inst:t,next:null},t=ce.updateQueue,t===null&&(t=Ds(),ce.updateQueue=t),a=t.lastEffect,a===null?t.lastEffect=e.next=e:(l=a.next,a.next=e,e.next=l,t.lastEffect=e),e}function $i(){return{destroy:void 0,resource:void 0}}function Xr(){return ke().memoizedState}function Ki(e,t,a,l){var n=Ot();l=l===void 0?null:l,ce.flags|=e,n.memoizedState=Qn(1|t,$i(),a,l)}function Hu(e,t,a,l){var n=ke();l=l===void 0?null:l;var u=n.memoizedState.inst;Re!==null&&l!==null&&Es(l,Re.memoizedState.deps)?n.memoizedState=Qn(t,u,a,l):(ce.flags|=e,n.memoizedState=Qn(1|t,u,a,l))}function Qr(e,t){Ki(8390656,8,e,t)}function Zr(e,t){Hu(2048,8,e,t)}function $r(e,t){return Hu(4,2,e,t)}function Kr(e,t){return Hu(4,4,e,t)}function kr(e,t){if(typeof t=="function"){e=e();var a=t(e);return function(){typeof a=="function"?a():t(null)}}if(t!=null)return e=e(),t.current=e,function(){t.current=null}}function Jr(e,t,a){a=a!=null?a.concat([e]):null,Hu(4,4,kr.bind(null,t,e),a)}function Bs(){}function Wr(e,t){var a=ke();t=t===void 0?null:t;var l=a.memoizedState;return t!==null&&Es(t,l[1])?l[0]:(a.memoizedState=[e,t],e)}function Fr(e,t){var a=ke();t=t===void 0?null:t;var l=a.memoizedState;if(t!==null&&Es(t,l[1]))return l[0];if(l=e(),rn){Ut(!0);try{e()}finally{Ut(!1)}}return a.memoizedState=[l,t],l}function Gs(e,t,a){return a===void 0||(pl&1073741824)!==0?e.memoizedState=t:(e.memoizedState=a,e=td(),ce.lanes|=e,_l|=e,a)}function Ir(e,t,a,l){return wt(a,t)?a:Yn.current!==null?(e=Gs(e,a,l),wt(e,t)||(at=!0),e):(pl&42)===0?(at=!0,e.memoizedState=a):(e=td(),ce.lanes|=e,_l|=e,t)}function Pr(e,t,a,l,n){var u=q.p;q.p=u!==0&&8>u?u:8;var f=M.T,d={};M.T=d,zs(e,!1,t,a);try{var g=n(),D=M.S;if(D!==null&&D(d,g),g!==null&&typeof g=="object"&&typeof g.then=="function"){var B=Dh(g,l);zu(e,t,B,Zt(e))}else zu(e,t,l,Zt(e))}catch(L){zu(e,t,{then:function(){},status:"rejected",reason:L},Zt())}finally{q.p=u,M.T=f}}function Mh(){}function Us(e,t,a,l){if(e.tag!==5)throw Error(O(476));var n=eo(e).queue;Pr(e,n,t,ee,a===null?Mh:function(){return to(e),a(l)})}function eo(e){var t=e.memoizedState;if(t!==null)return t;t={memoizedState:ee,baseState:ee,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ja,lastRenderedState:ee},next:null};var a={};return t.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Ja,lastRenderedState:a},next:null},e.memoizedState=t,e=e.alternate,e!==null&&(e.memoizedState=t),t}function to(e){var t=eo(e).next.queue;zu(e,t,{},Zt())}function Hs(){return vt(Pu)}function ao(){return ke().memoizedState}function lo(){return ke().memoizedState}function Bh(e){for(var t=e.return;t!==null;){switch(t.tag){case 24:case 3:var a=Zt();e=El(a);var l=Tl(t,e,a);l!==null&&($t(l,t,a),_u(l,t,a)),t={cache:rs()},e.payload=t;return}t=t.return}}function Gh(e,t,a){var l=Zt();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},ki(e)?uo(t,a):(a=es(e,t,a,l),a!==null&&($t(a,e,l),io(a,t,l)))}function no(e,t,a){var l=Zt();zu(e,t,a,l)}function zu(e,t,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(ki(e))uo(t,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=t.lastRenderedReducer,u!==null))try{var f=t.lastRenderedState,d=u(f,a);if(n.hasEagerState=!0,n.eagerState=d,wt(d,f))return Bi(e,t,n,0),Be===null&&Mi(),!1}catch{}finally{}if(a=es(e,t,n,l),a!==null)return $t(a,e,l),io(a,t,l),!0}return!1}function zs(e,t,a,l){if(l={lane:2,revertLane:mf(),action:l,hasEagerState:!1,eagerState:null,next:null},ki(e)){if(t)throw Error(O(479))}else t=es(e,a,l,2),t!==null&&$t(t,e,2)}function ki(e){var t=e.alternate;return e===ce||t!==null&&t===ce}function uo(e,t){Vn=Vi=!0;var a=e.pending;a===null?t.next=t:(t.next=a.next,a.next=t),e.pending=t}function io(e,t,a){if((a&4194048)!==0){var l=t.lanes;l&=e.pendingLanes,a|=l,t.lanes=a,fl(e,a)}}var Ji={readContext:vt,use:Qi,useCallback:Ve,useContext:Ve,useEffect:Ve,useImperativeHandle:Ve,useLayoutEffect:Ve,useInsertionEffect:Ve,useMemo:Ve,useReducer:Ve,useRef:Ve,useState:Ve,useDebugValue:Ve,useDeferredValue:Ve,useTransition:Ve,useSyncExternalStore:Ve,useId:Ve,useHostTransitionStatus:Ve,useFormState:Ve,useActionState:Ve,useOptimistic:Ve,useMemoCache:Ve,useCacheRefresh:Ve},co={readContext:vt,use:Qi,useCallback:function(e,t){return Ot().memoizedState=[e,t===void 0?null:t],e},useContext:vt,useEffect:Qr,useImperativeHandle:function(e,t,a){a=a!=null?a.concat([e]):null,Ki(4194308,4,kr.bind(null,t,e),a)},useLayoutEffect:function(e,t){return Ki(4194308,4,e,t)},useInsertionEffect:function(e,t){Ki(4,2,e,t)},useMemo:function(e,t){var a=Ot();t=t===void 0?null:t;var l=e();if(rn){Ut(!0);try{e()}finally{Ut(!1)}}return a.memoizedState=[l,t],l},useReducer:function(e,t,a){var l=Ot();if(a!==void 0){var n=a(t);if(rn){Ut(!0);try{a(t)}finally{Ut(!1)}}}else n=t;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=Gh.bind(null,ce,e),[l.memoizedState,e]},useRef:function(e){var t=Ot();return e={current:e},t.memoizedState=e},useState:function(e){e=_s(e);var t=e.queue,a=no.bind(null,ce,t);return t.dispatch=a,[e.memoizedState,a]},useDebugValue:Bs,useDeferredValue:function(e,t){var a=Ot();return Gs(a,e,t)},useTransition:function(){var e=_s(!1);return e=Pr.bind(null,ce,e.queue,!0,!1),Ot().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,t,a){var l=ce,n=Ot();if(Ee){if(a===void 0)throw Error(O(407));a=a()}else{if(a=t(),Be===null)throw Error(O(349));(ge&124)!==0||Or(l,t,a)}n.memoizedState=a;var u={value:a,getSnapshot:t};return n.queue=u,Qr(Mr.bind(null,l,u,e),[e]),l.flags|=2048,Qn(9,$i(),_r.bind(null,l,u,a,t),null),a},useId:function(){var e=Ot(),t=Be.identifierPrefix;if(Ee){var a=$a,l=Za;a=(l&~(1<<32-mt(l)-1)).toString(32)+a,t="«"+t+"R"+a,a=Xi++,0<a&&(t+="H"+a.toString(32)),t+="»"}else a=Rh++,t="«"+t+"r"+a.toString(32)+"»";return e.memoizedState=t},useHostTransitionStatus:Hs,useFormState:xr,useActionState:xr,useOptimistic:function(e){var t=Ot();t.memoizedState=t.baseState=e;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return t.queue=a,t=zs.bind(null,ce,!0,a),a.dispatch=t,[e,t]},useMemoCache:Rs,useCacheRefresh:function(){return Ot().memoizedState=Bh.bind(null,ce)}},so={readContext:vt,use:Qi,useCallback:Wr,useContext:vt,useEffect:Zr,useImperativeHandle:Jr,useInsertionEffect:$r,useLayoutEffect:Kr,useMemo:Fr,useReducer:Zi,useRef:Xr,useState:function(){return Zi(Ja)},useDebugValue:Bs,useDeferredValue:function(e,t){var a=ke();return Ir(a,Re.memoizedState,e,t)},useTransition:function(){var e=Zi(Ja)[0],t=ke().memoizedState;return[typeof e=="boolean"?e:Uu(e),t]},useSyncExternalStore:Cr,useId:ao,useHostTransitionStatus:Hs,useFormState:wr,useActionState:wr,useOptimistic:function(e,t){var a=ke();return Ur(a,Re,e,t)},useMemoCache:Rs,useCacheRefresh:lo},Uh={readContext:vt,use:Qi,useCallback:Wr,useContext:vt,useEffect:Zr,useImperativeHandle:Jr,useInsertionEffect:$r,useLayoutEffect:Kr,useMemo:Fr,useReducer:Os,useRef:Xr,useState:function(){return Os(Ja)},useDebugValue:Bs,useDeferredValue:function(e,t){var a=ke();return Re===null?Gs(a,e,t):Ir(a,Re.memoizedState,e,t)},useTransition:function(){var e=Os(Ja)[0],t=ke().memoizedState;return[typeof e=="boolean"?e:Uu(e),t]},useSyncExternalStore:Cr,useId:ao,useHostTransitionStatus:Hs,useFormState:Vr,useActionState:Vr,useOptimistic:function(e,t){var a=ke();return Re!==null?Ur(a,Re,e,t):(a.baseState=e,[e,a.queue.dispatch])},useMemoCache:Rs,useCacheRefresh:lo},Zn=null,Lu=0;function Wi(e){var t=Lu;return Lu+=1,Zn===null&&(Zn=[]),Ar(Zn,e,t)}function ju(e,t){t=t.props.ref,e.ref=t!==void 0?t:null}function Fi(e,t){throw t.$$typeof===X?Error(O(525)):(e=Object.prototype.toString.call(t),Error(O(31,e==="[object Object]"?"object with keys {"+Object.keys(t).join(", ")+"}":e)))}function fo(e){var t=e._init;return t(e._payload)}function ro(e){function t(T,E){if(e){var b=T.deletions;b===null?(T.deletions=[E],T.flags|=16):b.push(E)}}function a(T,E){if(!e)return null;for(;E!==null;)t(T,E),E=E.sibling;return null}function l(T){for(var E=new Map;T!==null;)T.key!==null?E.set(T.key,T):E.set(T.index,T),T=T.sibling;return E}function n(T,E){return T=Qa(T,E),T.index=0,T.sibling=null,T}function u(T,E,b){return T.index=b,e?(b=T.alternate,b!==null?(b=b.index,b<E?(T.flags|=67108866,E):b):(T.flags|=67108866,E)):(T.flags|=1048576,E)}function f(T){return e&&T.alternate===null&&(T.flags|=67108866),T}function d(T,E,b,G){return E===null||E.tag!==6?(E=as(b,T.mode,G),E.return=T,E):(E=n(E,b),E.return=T,E)}function g(T,E,b,G){var J=b.type;return J===ut?B(T,E,b.props.children,G,b.key):E!==null&&(E.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===Je&&fo(J)===E.type)?(E=n(E,b.props),ju(E,b),E.return=T,E):(E=Ui(b.type,b.key,b.props,null,T.mode,G),ju(E,b),E.return=T,E)}function D(T,E,b,G){return E===null||E.tag!==4||E.stateNode.containerInfo!==b.containerInfo||E.stateNode.implementation!==b.implementation?(E=ls(b,T.mode,G),E.return=T,E):(E=n(E,b.children||[]),E.return=T,E)}function B(T,E,b,G,J){return E===null||E.tag!==7?(E=tn(b,T.mode,G,J),E.return=T,E):(E=n(E,b),E.return=T,E)}function L(T,E,b){if(typeof E=="string"&&E!==""||typeof E=="number"||typeof E=="bigint")return E=as(""+E,T.mode,b),E.return=T,E;if(typeof E=="object"&&E!==null){switch(E.$$typeof){case he:return b=Ui(E.type,E.key,E.props,null,T.mode,b),ju(b,E),b.return=T,b;case Pe:return E=ls(E,T.mode,b),E.return=T,E;case Je:var G=E._init;return E=G(E._payload),L(T,E,b)}if(et(E)||He(E))return E=tn(E,T.mode,b,null),E.return=T,E;if(typeof E.then=="function")return L(T,Wi(E),b);if(E.$$typeof===Ze)return L(T,ji(T,E),b);Fi(T,E)}return null}function R(T,E,b,G){var J=E!==null?E.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return J!==null?null:d(T,E,""+b,G);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case he:return b.key===J?g(T,E,b,G):null;case Pe:return b.key===J?D(T,E,b,G):null;case Je:return J=b._init,b=J(b._payload),R(T,E,b,G)}if(et(b)||He(b))return J!==null?null:B(T,E,b,G,null);if(typeof b.then=="function")return R(T,E,Wi(b),G);if(b.$$typeof===Ze)return R(T,E,ji(T,b),G);Fi(T,b)}return null}function C(T,E,b,G,J){if(typeof G=="string"&&G!==""||typeof G=="number"||typeof G=="bigint")return T=T.get(b)||null,d(E,T,""+G,J);if(typeof G=="object"&&G!==null){switch(G.$$typeof){case he:return T=T.get(G.key===null?b:G.key)||null,g(E,T,G,J);case Pe:return T=T.get(G.key===null?b:G.key)||null,D(E,T,G,J);case Je:var se=G._init;return G=se(G._payload),C(T,E,b,G,J)}if(et(G)||He(G))return T=T.get(b)||null,B(E,T,G,J,null);if(typeof G.then=="function")return C(T,E,b,Wi(G),J);if(G.$$typeof===Ze)return C(T,E,b,ji(E,G),J);Fi(E,G)}return null}function le(T,E,b,G){for(var J=null,se=null,F=E,ae=E=0,nt=null;F!==null&&ae<b.length;ae++){F.index>ae?(nt=F,F=null):nt=F.sibling;var Se=R(T,F,b[ae],G);if(Se===null){F===null&&(F=nt);break}e&&F&&Se.alternate===null&&t(T,F),E=u(Se,E,ae),se===null?J=Se:se.sibling=Se,se=Se,F=nt}if(ae===b.length)return a(T,F),Ee&&ln(T,ae),J;if(F===null){for(;ae<b.length;ae++)F=L(T,b[ae],G),F!==null&&(E=u(F,E,ae),se===null?J=F:se.sibling=F,se=F);return Ee&&ln(T,ae),J}for(F=l(F);ae<b.length;ae++)nt=C(F,T,ae,b[ae],G),nt!==null&&(e&&nt.alternate!==null&&F.delete(nt.key===null?ae:nt.key),E=u(nt,E,ae),se===null?J=nt:se.sibling=nt,se=nt);return e&&F.forEach(function(ql){return t(T,ql)}),Ee&&ln(T,ae),J}function te(T,E,b,G){if(b==null)throw Error(O(151));for(var J=null,se=null,F=E,ae=E=0,nt=null,Se=b.next();F!==null&&!Se.done;ae++,Se=b.next()){F.index>ae?(nt=F,F=null):nt=F.sibling;var ql=R(T,F,Se.value,G);if(ql===null){F===null&&(F=nt);break}e&&F&&ql.alternate===null&&t(T,F),E=u(ql,E,ae),se===null?J=ql:se.sibling=ql,se=ql,F=nt}if(Se.done)return a(T,F),Ee&&ln(T,ae),J;if(F===null){for(;!Se.done;ae++,Se=b.next())Se=L(T,Se.value,G),Se!==null&&(E=u(Se,E,ae),se===null?J=Se:se.sibling=Se,se=Se);return Ee&&ln(T,ae),J}for(F=l(F);!Se.done;ae++,Se=b.next())Se=C(F,T,ae,Se.value,G),Se!==null&&(e&&Se.alternate!==null&&F.delete(Se.key===null?ae:Se.key),E=u(Se,E,ae),se===null?J=Se:se.sibling=Se,se=Se);return e&&F.forEach(function(Hm){return t(T,Hm)}),Ee&&ln(T,ae),J}function Oe(T,E,b,G){if(typeof b=="object"&&b!==null&&b.type===ut&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case he:e:{for(var J=b.key;E!==null;){if(E.key===J){if(J=b.type,J===ut){if(E.tag===7){a(T,E.sibling),G=n(E,b.props.children),G.return=T,T=G;break e}}else if(E.elementType===J||typeof J=="object"&&J!==null&&J.$$typeof===Je&&fo(J)===E.type){a(T,E.sibling),G=n(E,b.props),ju(G,b),G.return=T,T=G;break e}a(T,E);break}else t(T,E);E=E.sibling}b.type===ut?(G=tn(b.props.children,T.mode,G,b.key),G.return=T,T=G):(G=Ui(b.type,b.key,b.props,null,T.mode,G),ju(G,b),G.return=T,T=G)}return f(T);case Pe:e:{for(J=b.key;E!==null;){if(E.key===J)if(E.tag===4&&E.stateNode.containerInfo===b.containerInfo&&E.stateNode.implementation===b.implementation){a(T,E.sibling),G=n(E,b.children||[]),G.return=T,T=G;break e}else{a(T,E);break}else t(T,E);E=E.sibling}G=ls(b,T.mode,G),G.return=T,T=G}return f(T);case Je:return J=b._init,b=J(b._payload),Oe(T,E,b,G)}if(et(b))return le(T,E,b,G);if(He(b)){if(J=He(b),typeof J!="function")throw Error(O(150));return b=J.call(b),te(T,E,b,G)}if(typeof b.then=="function")return Oe(T,E,Wi(b),G);if(b.$$typeof===Ze)return Oe(T,E,ji(T,b),G);Fi(T,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,E!==null&&E.tag===6?(a(T,E.sibling),G=n(E,b),G.return=T,T=G):(a(T,E),G=as(b,T.mode,G),G.return=T,T=G),f(T)):a(T,E)}return function(T,E,b,G){try{Lu=0;var J=Oe(T,E,b,G);return Zn=null,J}catch(F){if(F===Cu||F===xi)throw F;var se=Yt(29,F,null,T.mode);return se.lanes=G,se.return=T,se}finally{}}}var $n=ro(!0),oo=ro(!1),na=w(null),Ma=null;function Nl(e){var t=e.alternate;$(Ie,Ie.current&1),$(na,e),Ma===null&&(t===null||Yn.current!==null||t.memoizedState!==null)&&(Ma=e)}function ho(e){if(e.tag===22){if($(Ie,Ie.current),$(na,e),Ma===null){var t=e.alternate;t!==null&&t.memoizedState!==null&&(Ma=e)}}else bl()}function bl(){$(Ie,Ie.current),$(na,na.current)}function Wa(e){Z(na),Ma===e&&(Ma=null),Z(Ie)}var Ie=w(0);function Ii(e){for(var t=e;t!==null;){if(t.tag===13){var a=t.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Rf(a)))return t}else if(t.tag===19&&t.memoizedProps.revealOrder!==void 0){if((t.flags&128)!==0)return t}else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return null;t=t.return}t.sibling.return=t.return,t=t.sibling}return null}function Ls(e,t,a,l){t=e.memoizedState,a=a(l,t),a=a==null?t:z({},t,a),e.memoizedState=a,e.lanes===0&&(e.updateQueue.baseState=a)}var js={enqueueSetState:function(e,t,a){e=e._reactInternals;var l=Zt(),n=El(l);n.payload=t,a!=null&&(n.callback=a),t=Tl(e,n,l),t!==null&&($t(t,e,l),_u(t,e,l))},enqueueReplaceState:function(e,t,a){e=e._reactInternals;var l=Zt(),n=El(l);n.tag=1,n.payload=t,a!=null&&(n.callback=a),t=Tl(e,n,l),t!==null&&($t(t,e,l),_u(t,e,l))},enqueueForceUpdate:function(e,t){e=e._reactInternals;var a=Zt(),l=El(a);l.tag=2,t!=null&&(l.callback=t),t=Tl(e,l,a),t!==null&&($t(t,e,a),_u(t,e,a))}};function mo(e,t,a,l,n,u,f){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,u,f):t.prototype&&t.prototype.isPureReactComponent?!Au(a,l)||!Au(n,u):!0}function yo(e,t,a,l){e=t.state,typeof t.componentWillReceiveProps=="function"&&t.componentWillReceiveProps(a,l),typeof t.UNSAFE_componentWillReceiveProps=="function"&&t.UNSAFE_componentWillReceiveProps(a,l),t.state!==e&&js.enqueueReplaceState(t,t.state,null)}function on(e,t){var a=t;if("ref"in t){a={};for(var l in t)l!=="ref"&&(a[l]=t[l])}if(e=e.defaultProps){a===t&&(a=z({},a));for(var n in e)a[n]===void 0&&(a[n]=e[n])}return a}var Pi=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var t=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(t))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function go(e){Pi(e)}function vo(e){console.error(e)}function So(e){Pi(e)}function ec(e,t){try{var a=e.onUncaughtError;a(t.value,{componentStack:t.stack})}catch(l){setTimeout(function(){throw l})}}function Ao(e,t,a){try{var l=e.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:t.tag===1?t.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function qs(e,t,a){return a=El(a),a.tag=3,a.payload={element:null},a.callback=function(){ec(e,t)},a}function Eo(e){return e=El(e),e.tag=3,e}function To(e,t,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var u=l.value;e.payload=function(){return n(u)},e.callback=function(){Ao(t,a,l)}}var f=a.stateNode;f!==null&&typeof f.componentDidCatch=="function"&&(e.callback=function(){Ao(t,a,l),typeof n!="function"&&(Ml===null?Ml=new Set([this]):Ml.add(this));var d=l.stack;this.componentDidCatch(l.value,{componentStack:d!==null?d:""})})}function Hh(e,t,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(t=a.alternate,t!==null&&bu(t,a,n,!0),a=na.current,a!==null){switch(a.tag){case 13:return Ma===null?ff():a.alternate===null&&Ye===0&&(Ye=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===hs?a.flags|=16384:(t=a.updateQueue,t===null?a.updateQueue=new Set([l]):t.add(l),of(e,l,n)),!1;case 22:return a.flags|=65536,l===hs?a.flags|=16384:(t=a.updateQueue,t===null?(t={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=t):(a=t.retryQueue,a===null?t.retryQueue=new Set([l]):a.add(l)),of(e,l,n)),!1}throw Error(O(435,a.tag))}return of(e,l,n),ff(),!1}if(Ee)return t=na.current,t!==null?((t.flags&65536)===0&&(t.flags|=256),t.flags|=65536,t.lanes=n,l!==is&&(e=Error(O(422),{cause:l}),Nu(ea(e,a)))):(l!==is&&(t=Error(O(423),{cause:l}),Nu(ea(t,a))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=ea(l,a),n=qs(e.stateNode,l,n),gs(e,n),Ye!==4&&(Ye=2)),!1;var u=Error(O(520),{cause:l});if(u=ea(u,a),Qu===null?Qu=[u]:Qu.push(u),Ye!==4&&(Ye=2),t===null)return!0;l=ea(l,a),a=t;do{switch(a.tag){case 3:return a.flags|=65536,e=n&-n,a.lanes|=e,e=qs(a.stateNode,l,e),gs(a,e),!1;case 1:if(t=a.type,u=a.stateNode,(a.flags&128)===0&&(typeof t.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(Ml===null||!Ml.has(u))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Eo(n),To(n,e,a,l),gs(a,n),!1}a=a.return}while(a!==null);return!1}var po=Error(O(461)),at=!1;function rt(e,t,a,l){t.child=e===null?oo(t,null,a,l):$n(t,e.child,a,l)}function No(e,t,a,l,n){a=a.render;var u=t.ref;if("ref"in l){var f={};for(var d in l)d!=="ref"&&(f[d]=l[d])}else f=l;return sn(t),l=Ts(e,t,a,f,u,n),d=ps(),e!==null&&!at?(Ns(e,t,n),Fa(e,t,n)):(Ee&&d&&ns(t),t.flags|=1,rt(e,t,l,n),t.child)}function bo(e,t,a,l,n){if(e===null){var u=a.type;return typeof u=="function"&&!ts(u)&&u.defaultProps===void 0&&a.compare===null?(t.tag=15,t.type=u,Do(e,t,u,l,n)):(e=Ui(a.type,null,l,t,t.mode,n),e.ref=t.ref,e.return=t,t.child=e)}if(u=e.child,!$s(e,n)){var f=u.memoizedProps;if(a=a.compare,a=a!==null?a:Au,a(f,l)&&e.ref===t.ref)return Fa(e,t,n)}return t.flags|=1,e=Qa(u,l),e.ref=t.ref,e.return=t,t.child=e}function Do(e,t,a,l,n){if(e!==null){var u=e.memoizedProps;if(Au(u,l)&&e.ref===t.ref)if(at=!1,t.pendingProps=l=u,$s(e,n))(e.flags&131072)!==0&&(at=!0);else return t.lanes=e.lanes,Fa(e,t,n)}return xs(e,t,a,l,n)}function Ro(e,t,a){var l=t.pendingProps,n=l.children,u=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((t.flags&128)!==0){if(l=u!==null?u.baseLanes|a:a,e!==null){for(n=t.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;t.childLanes=u&~l}else t.childLanes=0,t.child=null;return Co(e,t,l,a)}if((a&536870912)!==0)t.memoizedState={baseLanes:0,cachePool:null},e!==null&&qi(t,u!==null?u.cachePool:null),u!==null?br(t,u):Ss(),ho(t);else return t.lanes=t.childLanes=536870912,Co(e,t,u!==null?u.baseLanes|a:a,a)}else u!==null?(qi(t,u.cachePool),br(t,u),bl(),t.memoizedState=null):(e!==null&&qi(t,null),Ss(),bl());return rt(e,t,n,a),t.child}function Co(e,t,a,l){var n=ds();return n=n===null?null:{parent:Fe._currentValue,pool:n},t.memoizedState={baseLanes:a,cachePool:n},e!==null&&qi(t,null),Ss(),ho(t),e!==null&&bu(e,t,l,!0),null}function tc(e,t){var a=t.ref;if(a===null)e!==null&&e.ref!==null&&(t.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(O(284));(e===null||e.ref!==a)&&(t.flags|=4194816)}}function xs(e,t,a,l,n){return sn(t),a=Ts(e,t,a,l,void 0,n),l=ps(),e!==null&&!at?(Ns(e,t,n),Fa(e,t,n)):(Ee&&l&&ns(t),t.flags|=1,rt(e,t,a,n),t.child)}function Oo(e,t,a,l,n,u){return sn(t),t.updateQueue=null,a=Rr(t,l,a,n),Dr(e),l=ps(),e!==null&&!at?(Ns(e,t,u),Fa(e,t,u)):(Ee&&l&&ns(t),t.flags|=1,rt(e,t,a,u),t.child)}function _o(e,t,a,l,n){if(sn(t),t.stateNode===null){var u=Ln,f=a.contextType;typeof f=="object"&&f!==null&&(u=vt(f)),u=new a(l,u),t.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=js,t.stateNode=u,u._reactInternals=t,u=t.stateNode,u.props=l,u.state=t.memoizedState,u.refs={},ms(t),f=a.contextType,u.context=typeof f=="object"&&f!==null?vt(f):Ln,u.state=t.memoizedState,f=a.getDerivedStateFromProps,typeof f=="function"&&(Ls(t,a,f,l),u.state=t.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(f=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),f!==u.state&&js.enqueueReplaceState(u,u.state,null),Bu(t,l,u,n),Mu(),u.state=t.memoizedState),typeof u.componentDidMount=="function"&&(t.flags|=4194308),l=!0}else if(e===null){u=t.stateNode;var d=t.memoizedProps,g=on(a,d);u.props=g;var D=u.context,B=a.contextType;f=Ln,typeof B=="object"&&B!==null&&(f=vt(B));var L=a.getDerivedStateFromProps;B=typeof L=="function"||typeof u.getSnapshotBeforeUpdate=="function",d=t.pendingProps!==d,B||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(d||D!==f)&&yo(t,u,l,f),Al=!1;var R=t.memoizedState;u.state=R,Bu(t,l,u,n),Mu(),D=t.memoizedState,d||R!==D||Al?(typeof L=="function"&&(Ls(t,a,L,l),D=t.memoizedState),(g=Al||mo(t,a,g,l,R,D,f))?(B||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(t.flags|=4194308)):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),t.memoizedProps=l,t.memoizedState=D),u.props=l,u.state=D,u.context=f,l=g):(typeof u.componentDidMount=="function"&&(t.flags|=4194308),l=!1)}else{u=t.stateNode,ys(e,t),f=t.memoizedProps,B=on(a,f),u.props=B,L=t.pendingProps,R=u.context,D=a.contextType,g=Ln,typeof D=="object"&&D!==null&&(g=vt(D)),d=a.getDerivedStateFromProps,(D=typeof d=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(f!==L||R!==g)&&yo(t,u,l,g),Al=!1,R=t.memoizedState,u.state=R,Bu(t,l,u,n),Mu();var C=t.memoizedState;f!==L||R!==C||Al||e!==null&&e.dependencies!==null&&Li(e.dependencies)?(typeof d=="function"&&(Ls(t,a,d,l),C=t.memoizedState),(B=Al||mo(t,a,B,l,R,C,g)||e!==null&&e.dependencies!==null&&Li(e.dependencies))?(D||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,C,g),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,C,g)),typeof u.componentDidUpdate=="function"&&(t.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(t.flags|=1024)):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),t.memoizedProps=l,t.memoizedState=C),u.props=l,u.state=C,u.context=g,l=B):(typeof u.componentDidUpdate!="function"||f===e.memoizedProps&&R===e.memoizedState||(t.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||f===e.memoizedProps&&R===e.memoizedState||(t.flags|=1024),l=!1)}return u=l,tc(e,t),l=(t.flags&128)!==0,u||l?(u=t.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:u.render(),t.flags|=1,e!==null&&l?(t.child=$n(t,e.child,null,n),t.child=$n(t,null,a,n)):rt(e,t,a,n),t.memoizedState=u.state,e=t.child):e=Fa(e,t,n),e}function Mo(e,t,a,l){return pu(),t.flags|=256,rt(e,t,a,l),t.child}var ws={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function Ys(e){return{baseLanes:e,cachePool:gr()}}function Vs(e,t,a){return e=e!==null?e.childLanes&~a:0,t&&(e|=ua),e}function Bo(e,t,a){var l=t.pendingProps,n=!1,u=(t.flags&128)!==0,f;if((f=u)||(f=e!==null&&e.memoizedState===null?!1:(Ie.current&2)!==0),f&&(n=!0,t.flags&=-129),f=(t.flags&32)!==0,t.flags&=-33,e===null){if(Ee){if(n?Nl(t):bl(),Ee){var d=we,g;if(g=d){e:{for(g=d,d=_a;g.nodeType!==8;){if(!d){d=null;break e}if(g=Aa(g.nextSibling),g===null){d=null;break e}}d=g}d!==null?(t.memoizedState={dehydrated:d,treeContext:an!==null?{id:Za,overflow:$a}:null,retryLane:536870912,hydrationErrors:null},g=Yt(18,null,null,0),g.stateNode=d,g.return=t,t.child=g,Nt=t,we=null,g=!0):g=!1}g||un(t)}if(d=t.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return Rf(d)?t.lanes=32:t.lanes=536870912,null;Wa(t)}return d=l.children,l=l.fallback,n?(bl(),n=t.mode,d=ac({mode:"hidden",children:d},n),l=tn(l,n,a,null),d.return=t,l.return=t,d.sibling=l,t.child=d,n=t.child,n.memoizedState=Ys(a),n.childLanes=Vs(e,f,a),t.memoizedState=ws,l):(Nl(t),Xs(t,d))}if(g=e.memoizedState,g!==null&&(d=g.dehydrated,d!==null)){if(u)t.flags&256?(Nl(t),t.flags&=-257,t=Qs(e,t,a)):t.memoizedState!==null?(bl(),t.child=e.child,t.flags|=128,t=null):(bl(),n=l.fallback,d=t.mode,l=ac({mode:"visible",children:l.children},d),n=tn(n,d,a,null),n.flags|=2,l.return=t,n.return=t,l.sibling=n,t.child=l,$n(t,e.child,null,a),l=t.child,l.memoizedState=Ys(a),l.childLanes=Vs(e,f,a),t.memoizedState=ws,t=n);else if(Nl(t),Rf(d)){if(f=d.nextSibling&&d.nextSibling.dataset,f)var D=f.dgst;f=D,l=Error(O(419)),l.stack="",l.digest=f,Nu({value:l,source:null,stack:null}),t=Qs(e,t,a)}else if(at||bu(e,t,a,!1),f=(a&e.childLanes)!==0,at||f){if(f=Be,f!==null&&(l=a&-a,l=(l&42)!==0?1:We(l),l=(l&(f.suspendedLanes|a))!==0?0:l,l!==0&&l!==g.retryLane))throw g.retryLane=l,zn(e,l),$t(f,e,l),po;d.data==="$?"||ff(),t=Qs(e,t,a)}else d.data==="$?"?(t.flags|=192,t.child=e.child,t=null):(e=g.treeContext,we=Aa(d.nextSibling),Nt=t,Ee=!0,nn=null,_a=!1,e!==null&&(aa[la++]=Za,aa[la++]=$a,aa[la++]=an,Za=e.id,$a=e.overflow,an=t),t=Xs(t,l.children),t.flags|=4096);return t}return n?(bl(),n=l.fallback,d=t.mode,g=e.child,D=g.sibling,l=Qa(g,{mode:"hidden",children:l.children}),l.subtreeFlags=g.subtreeFlags&65011712,D!==null?n=Qa(D,n):(n=tn(n,d,a,null),n.flags|=2),n.return=t,l.return=t,l.sibling=n,t.child=l,l=n,n=t.child,d=e.child.memoizedState,d===null?d=Ys(a):(g=d.cachePool,g!==null?(D=Fe._currentValue,g=g.parent!==D?{parent:D,pool:D}:g):g=gr(),d={baseLanes:d.baseLanes|a,cachePool:g}),n.memoizedState=d,n.childLanes=Vs(e,f,a),t.memoizedState=ws,l):(Nl(t),a=e.child,e=a.sibling,a=Qa(a,{mode:"visible",children:l.children}),a.return=t,a.sibling=null,e!==null&&(f=t.deletions,f===null?(t.deletions=[e],t.flags|=16):f.push(e)),t.child=a,t.memoizedState=null,a)}function Xs(e,t){return t=ac({mode:"visible",children:t},e.mode),t.return=e,e.child=t}function ac(e,t){return e=Yt(22,e,null,t),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function Qs(e,t,a){return $n(t,e.child,null,a),e=Xs(t,t.pendingProps.children),e.flags|=2,t.memoizedState=null,e}function Go(e,t,a){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t),ss(e.return,t,a)}function Zs(e,t,a,l,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:t,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(u.isBackwards=t,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=a,u.tailMode=n)}function Uo(e,t,a){var l=t.pendingProps,n=l.revealOrder,u=l.tail;if(rt(e,t,l.children,a),l=Ie.current,(l&2)!==0)l=l&1|2,t.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=t.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&Go(e,a,t);else if(e.tag===19)Go(e,a,t);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break e;for(;e.sibling===null;){if(e.return===null||e.return===t)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch($(Ie,l),n){case"forwards":for(a=t.child,n=null;a!==null;)e=a.alternate,e!==null&&Ii(e)===null&&(n=a),a=a.sibling;a=n,a===null?(n=t.child,t.child=null):(n=a.sibling,a.sibling=null),Zs(t,!1,n,a,u);break;case"backwards":for(a=null,n=t.child,t.child=null;n!==null;){if(e=n.alternate,e!==null&&Ii(e)===null){t.child=n;break}e=n.sibling,n.sibling=a,a=n,n=e}Zs(t,!0,a,null,u);break;case"together":Zs(t,!1,null,null,void 0);break;default:t.memoizedState=null}return t.child}function Fa(e,t,a){if(e!==null&&(t.dependencies=e.dependencies),_l|=t.lanes,(a&t.childLanes)===0)if(e!==null){if(bu(e,t,a,!1),(a&t.childLanes)===0)return null}else return null;if(e!==null&&t.child!==e.child)throw Error(O(153));if(t.child!==null){for(e=t.child,a=Qa(e,e.pendingProps),t.child=a,a.return=t;e.sibling!==null;)e=e.sibling,a=a.sibling=Qa(e,e.pendingProps),a.return=t;a.sibling=null}return t.child}function $s(e,t){return(e.lanes&t)!==0?!0:(e=e.dependencies,!!(e!==null&&Li(e)))}function zh(e,t,a){switch(t.tag){case 3:me(t,t.stateNode.containerInfo),Sl(t,Fe,e.memoizedState.cache),pu();break;case 27:case 5:iu(t);break;case 4:me(t,t.stateNode.containerInfo);break;case 10:Sl(t,t.type,t.memoizedProps.value);break;case 13:var l=t.memoizedState;if(l!==null)return l.dehydrated!==null?(Nl(t),t.flags|=128,null):(a&t.child.childLanes)!==0?Bo(e,t,a):(Nl(t),e=Fa(e,t,a),e!==null?e.sibling:null);Nl(t);break;case 19:var n=(e.flags&128)!==0;if(l=(a&t.childLanes)!==0,l||(bu(e,t,a,!1),l=(a&t.childLanes)!==0),n){if(l)return Uo(e,t,a);t.flags|=128}if(n=t.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),$(Ie,Ie.current),l)break;return null;case 22:case 23:return t.lanes=0,Ro(e,t,a);case 24:Sl(t,Fe,e.memoizedState.cache)}return Fa(e,t,a)}function Ho(e,t,a){if(e!==null)if(e.memoizedProps!==t.pendingProps)at=!0;else{if(!$s(e,a)&&(t.flags&128)===0)return at=!1,zh(e,t,a);at=(e.flags&131072)!==0}else at=!1,Ee&&(t.flags&1048576)!==0&&fr(t,zi,t.index);switch(t.lanes=0,t.tag){case 16:e:{e=t.pendingProps;var l=t.elementType,n=l._init;if(l=n(l._payload),t.type=l,typeof l=="function")ts(l)?(e=on(l,e),t.tag=1,t=_o(null,t,l,e,a)):(t.tag=0,t=xs(null,t,l,e,a));else{if(l!=null){if(n=l.$$typeof,n===Bt){t.tag=11,t=No(null,t,l,e,a);break e}else if(n===bt){t.tag=14,t=bo(null,t,l,e,a);break e}}throw t=fa(l)||l,Error(O(306,t,""))}}return t;case 0:return xs(e,t,t.type,t.pendingProps,a);case 1:return l=t.type,n=on(l,t.pendingProps),_o(e,t,l,n,a);case 3:e:{if(me(t,t.stateNode.containerInfo),e===null)throw Error(O(387));l=t.pendingProps;var u=t.memoizedState;n=u.element,ys(e,t),Bu(t,l,null,a);var f=t.memoizedState;if(l=f.cache,Sl(t,Fe,l),l!==u.cache&&fs(t,[Fe],a,!0),Mu(),l=f.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:f.cache},t.updateQueue.baseState=u,t.memoizedState=u,t.flags&256){t=Mo(e,t,l,a);break e}else if(l!==n){n=ea(Error(O(424)),t),Nu(n),t=Mo(e,t,l,a);break e}else{switch(e=t.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(we=Aa(e.firstChild),Nt=t,Ee=!0,nn=null,_a=!0,a=oo(t,null,l,a),t.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(pu(),l===n){t=Fa(e,t,a);break e}rt(e,t,l,a)}t=t.child}return t;case 26:return tc(e,t),e===null?(a=qd(t.type,null,t.pendingProps,null))?t.memoizedState=a:Ee||(a=t.type,e=t.pendingProps,l=gc(ue.current).createElement(a),l[je]=t,l[pe]=e,dt(l,a,e),qe(l),t.stateNode=l):t.memoizedState=qd(t.type,e.memoizedProps,t.pendingProps,e.memoizedState),null;case 27:return iu(t),e===null&&Ee&&(l=t.stateNode=zd(t.type,t.pendingProps,ue.current),Nt=t,_a=!0,n=we,Ul(t.type)?(Cf=n,we=Aa(l.firstChild)):we=n),rt(e,t,t.pendingProps.children,a),tc(e,t),e===null&&(t.flags|=4194304),t.child;case 5:return e===null&&Ee&&((n=l=we)&&(l=fm(l,t.type,t.pendingProps,_a),l!==null?(t.stateNode=l,Nt=t,we=Aa(l.firstChild),_a=!1,n=!0):n=!1),n||un(t)),iu(t),n=t.type,u=t.pendingProps,f=e!==null?e.memoizedProps:null,l=u.children,Nf(n,u)?l=null:f!==null&&Nf(n,f)&&(t.flags|=32),t.memoizedState!==null&&(n=Ts(e,t,Ch,null,null,a),Pu._currentValue=n),tc(e,t),rt(e,t,l,a),t.child;case 6:return e===null&&Ee&&((e=a=we)&&(a=rm(a,t.pendingProps,_a),a!==null?(t.stateNode=a,Nt=t,we=null,e=!0):e=!1),e||un(t)),null;case 13:return Bo(e,t,a);case 4:return me(t,t.stateNode.containerInfo),l=t.pendingProps,e===null?t.child=$n(t,null,l,a):rt(e,t,l,a),t.child;case 11:return No(e,t,t.type,t.pendingProps,a);case 7:return rt(e,t,t.pendingProps,a),t.child;case 8:return rt(e,t,t.pendingProps.children,a),t.child;case 12:return rt(e,t,t.pendingProps.children,a),t.child;case 10:return l=t.pendingProps,Sl(t,t.type,l.value),rt(e,t,l.children,a),t.child;case 9:return n=t.type._context,l=t.pendingProps.children,sn(t),n=vt(n),l=l(n),t.flags|=1,rt(e,t,l,a),t.child;case 14:return bo(e,t,t.type,t.pendingProps,a);case 15:return Do(e,t,t.type,t.pendingProps,a);case 19:return Uo(e,t,a);case 31:return l=t.pendingProps,a=t.mode,l={mode:l.mode,children:l.children},e===null?(a=ac(l,a),a.ref=t.ref,t.child=a,a.return=t,t=a):(a=Qa(e.child,l),a.ref=t.ref,t.child=a,a.return=t,t=a),t;case 22:return Ro(e,t,a);case 24:return sn(t),l=vt(Fe),e===null?(n=ds(),n===null&&(n=Be,u=rs(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=a),n=u),t.memoizedState={parent:l,cache:n},ms(t),Sl(t,Fe,n)):((e.lanes&a)!==0&&(ys(e,t),Bu(t,null,null,a),Mu()),n=e.memoizedState,u=t.memoizedState,n.parent!==l?(n={parent:l,cache:l},t.memoizedState=n,t.lanes===0&&(t.memoizedState=t.updateQueue.baseState=n),Sl(t,Fe,l)):(l=u.cache,Sl(t,Fe,l),l!==n.cache&&fs(t,[Fe],a,!0))),rt(e,t,t.pendingProps.children,a),t.child;case 29:throw t.pendingProps}throw Error(O(156,t.tag))}function Ia(e){e.flags|=4}function zo(e,t){if(t.type!=="stylesheet"||(t.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Xd(t)){if(t=na.current,t!==null&&((ge&4194048)===ge?Ma!==null:(ge&62914560)!==ge&&(ge&536870912)===0||t!==Ma))throw Ou=hs,vr;e.flags|=8192}}function lc(e,t){t!==null&&(e.flags|=4),e.flags&16384&&(t=e.tag!==22?hi():536870912,e.lanes|=t,Wn|=t)}function qu(e,t){if(!Ee)switch(e.tailMode){case"hidden":t=e.tail;for(var a=null;t!==null;)t.alternate!==null&&(a=t),t=t.sibling;a===null?e.tail=null:a.sibling=null;break;case"collapsed":a=e.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?t||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function Le(e){var t=e.alternate!==null&&e.alternate.child===e.child,a=0,l=0;if(t)for(var n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=a,t}function Lh(e,t,a){var l=t.pendingProps;switch(us(t),t.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Le(t),null;case 1:return Le(t),null;case 3:return a=t.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),t.memoizedState.cache!==l&&(t.flags|=2048),ka(Fe),Jt(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(e===null||e.child===null)&&(Tu(t)?Ia(t):e===null||e.memoizedState.isDehydrated&&(t.flags&256)===0||(t.flags|=1024,dr())),Le(t),null;case 26:return a=t.memoizedState,e===null?(Ia(t),a!==null?(Le(t),zo(t,a)):(Le(t),t.flags&=-16777217)):a?a!==e.memoizedState?(Ia(t),Le(t),zo(t,a)):(Le(t),t.flags&=-16777217):(e.memoizedProps!==l&&Ia(t),Le(t),t.flags&=-16777217),null;case 27:ll(t),a=ue.current;var n=t.type;if(e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Ia(t);else{if(!l){if(t.stateNode===null)throw Error(O(166));return Le(t),null}e=P.current,Tu(t)?rr(t):(e=zd(n,l,a),t.stateNode=e,Ia(t))}return Le(t),null;case 5:if(ll(t),a=t.type,e!==null&&t.stateNode!=null)e.memoizedProps!==l&&Ia(t);else{if(!l){if(t.stateNode===null)throw Error(O(166));return Le(t),null}if(e=P.current,Tu(t))rr(t);else{switch(n=gc(ue.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}e[je]=t,e[pe]=l;e:for(n=t.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===t)break e;for(;n.sibling===null;){if(n.return===null||n.return===t)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}t.stateNode=e;e:switch(dt(e,a,l),a){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&Ia(t)}}return Le(t),t.flags&=-16777217,null;case 6:if(e&&t.stateNode!=null)e.memoizedProps!==l&&Ia(t);else{if(typeof l!="string"&&t.stateNode===null)throw Error(O(166));if(e=ue.current,Tu(t)){if(e=t.stateNode,a=t.memoizedProps,l=null,n=Nt,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[je]=t,e=!!(e.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||Od(e.nodeValue,a)),e||un(t)}else e=gc(e).createTextNode(l),e[je]=t,t.stateNode=e}return Le(t),null;case 13:if(l=t.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Tu(t),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(O(318));if(n=t.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(O(317));n[je]=t}else pu(),(t.flags&128)===0&&(t.memoizedState=null),t.flags|=4;Le(t),n=!1}else n=dr(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return t.flags&256?(Wa(t),t):(Wa(t),null)}if(Wa(t),(t.flags&128)!==0)return t.lanes=a,t;if(a=l!==null,e=e!==null&&e.memoizedState!==null,a){l=t.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==n&&(l.flags|=2048)}return a!==e&&a&&(t.child.flags|=8192),lc(t,t.updateQueue),Le(t),null;case 4:return Jt(),e===null&&Sf(t.stateNode.containerInfo),Le(t),null;case 10:return ka(t.type),Le(t),null;case 19:if(Z(Ie),n=t.memoizedState,n===null)return Le(t),null;if(l=(t.flags&128)!==0,u=n.rendering,u===null)if(l)qu(n,!1);else{if(Ye!==0||e!==null&&(e.flags&128)!==0)for(e=t.child;e!==null;){if(u=Ii(e),u!==null){for(t.flags|=128,qu(n,!1),e=u.updateQueue,t.updateQueue=e,lc(t,e),t.subtreeFlags=0,e=a,a=t.child;a!==null;)sr(a,e),a=a.sibling;return $(Ie,Ie.current&1|2),t.child}e=e.sibling}n.tail!==null&&Dt()>ic&&(t.flags|=128,l=!0,qu(n,!1),t.lanes=4194304)}else{if(!l)if(e=Ii(u),e!==null){if(t.flags|=128,l=!0,e=e.updateQueue,t.updateQueue=e,lc(t,e),qu(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!Ee)return Le(t),null}else 2*Dt()-n.renderingStartTime>ic&&a!==536870912&&(t.flags|=128,l=!0,qu(n,!1),t.lanes=4194304);n.isBackwards?(u.sibling=t.child,t.child=u):(e=n.last,e!==null?e.sibling=u:t.child=u,n.last=u)}return n.tail!==null?(t=n.tail,n.rendering=t,n.tail=t.sibling,n.renderingStartTime=Dt(),t.sibling=null,e=Ie.current,$(Ie,l?e&1|2:e&1),t):(Le(t),null);case 22:case 23:return Wa(t),As(),l=t.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(t.flags|=8192):l&&(t.flags|=8192),l?(a&536870912)!==0&&(t.flags&128)===0&&(Le(t),t.subtreeFlags&6&&(t.flags|=8192)):Le(t),a=t.updateQueue,a!==null&&lc(t,a.retryQueue),a=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),l=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(l=t.memoizedState.cachePool.pool),l!==a&&(t.flags|=2048),e!==null&&Z(fn),null;case 24:return a=null,e!==null&&(a=e.memoizedState.cache),t.memoizedState.cache!==a&&(t.flags|=2048),ka(Fe),Le(t),null;case 25:return null;case 30:return null}throw Error(O(156,t.tag))}function jh(e,t){switch(us(t),t.tag){case 1:return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 3:return ka(Fe),Jt(),e=t.flags,(e&65536)!==0&&(e&128)===0?(t.flags=e&-65537|128,t):null;case 26:case 27:case 5:return ll(t),null;case 13:if(Wa(t),e=t.memoizedState,e!==null&&e.dehydrated!==null){if(t.alternate===null)throw Error(O(340));pu()}return e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 19:return Z(Ie),null;case 4:return Jt(),null;case 10:return ka(t.type),null;case 22:case 23:return Wa(t),As(),e!==null&&Z(fn),e=t.flags,e&65536?(t.flags=e&-65537|128,t):null;case 24:return ka(Fe),null;case 25:return null;default:return null}}function Lo(e,t){switch(us(t),t.tag){case 3:ka(Fe),Jt();break;case 26:case 27:case 5:ll(t);break;case 4:Jt();break;case 13:Wa(t);break;case 19:Z(Ie);break;case 10:ka(t.type);break;case 22:case 23:Wa(t),As(),e!==null&&Z(fn);break;case 24:ka(Fe)}}function xu(e,t){try{var a=t.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&e)===e){l=void 0;var u=a.create,f=a.inst;l=u(),f.destroy=l}a=a.next}while(a!==n)}}catch(d){Me(t,t.return,d)}}function Dl(e,t,a){try{var l=t.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var u=n.next;l=u;do{if((l.tag&e)===e){var f=l.inst,d=f.destroy;if(d!==void 0){f.destroy=void 0,n=t;var g=a,D=d;try{D()}catch(B){Me(n,g,B)}}}l=l.next}while(l!==u)}}catch(B){Me(t,t.return,B)}}function jo(e){var t=e.updateQueue;if(t!==null){var a=e.stateNode;try{Nr(t,a)}catch(l){Me(e,e.return,l)}}}function qo(e,t,a){a.props=on(e.type,e.memoizedProps),a.state=e.memoizedState;try{a.componentWillUnmount()}catch(l){Me(e,t,l)}}function wu(e,t){try{var a=e.ref;if(a!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof a=="function"?e.refCleanup=a(l):a.current=l}}catch(n){Me(e,t,n)}}function Ba(e,t){var a=e.ref,l=e.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){Me(e,t,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){Me(e,t,n)}else a.current=null}function xo(e){var t=e.type,a=e.memoizedProps,l=e.stateNode;try{e:switch(t){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break e;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){Me(e,e.return,n)}}function Ks(e,t,a){try{var l=e.stateNode;nm(l,e.type,a,t),l[pe]=t}catch(n){Me(e,e.return,n)}}function wo(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Ul(e.type)||e.tag===4}function ks(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||wo(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Ul(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Js(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(e,t):(t=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,t.appendChild(e),a=a._reactRootContainer,a!=null||t.onclick!==null||(t.onclick=yc));else if(l!==4&&(l===27&&Ul(e.type)&&(a=e.stateNode,t=null),e=e.child,e!==null))for(Js(e,t,a),e=e.sibling;e!==null;)Js(e,t,a),e=e.sibling}function nc(e,t,a){var l=e.tag;if(l===5||l===6)e=e.stateNode,t?a.insertBefore(e,t):a.appendChild(e);else if(l!==4&&(l===27&&Ul(e.type)&&(a=e.stateNode),e=e.child,e!==null))for(nc(e,t,a),e=e.sibling;e!==null;)nc(e,t,a),e=e.sibling}function Yo(e){var t=e.stateNode,a=e.memoizedProps;try{for(var l=e.type,n=t.attributes;n.length;)t.removeAttributeNode(n[0]);dt(t,l,a),t[je]=e,t[pe]=a}catch(u){Me(e,e.return,u)}}var Pa=!1,Xe=!1,Ws=!1,Vo=typeof WeakSet=="function"?WeakSet:Set,lt=null;function qh(e,t){if(e=e.containerInfo,Tf=pc,e=If(e),kc(e)){if("selectionStart"in e)var a={start:e.selectionStart,end:e.selectionEnd};else e:{a=(a=e.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{a.nodeType,u.nodeType}catch{a=null;break e}var f=0,d=-1,g=-1,D=0,B=0,L=e,R=null;t:for(;;){for(var C;L!==a||n!==0&&L.nodeType!==3||(d=f+n),L!==u||l!==0&&L.nodeType!==3||(g=f+l),L.nodeType===3&&(f+=L.nodeValue.length),(C=L.firstChild)!==null;)R=L,L=C;for(;;){if(L===e)break t;if(R===a&&++D===n&&(d=f),R===u&&++B===l&&(g=f),(C=L.nextSibling)!==null)break;L=R,R=L.parentNode}L=C}a=d===-1||g===-1?null:{start:d,end:g}}else a=null}a=a||{start:0,end:0}}else a=null;for(pf={focusedElem:e,selectionRange:a},pc=!1,lt=t;lt!==null;)if(t=lt,e=t.child,(t.subtreeFlags&1024)!==0&&e!==null)e.return=t,lt=e;else for(;lt!==null;){switch(t=lt,u=t.alternate,e=t.flags,t.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,a=t,n=u.memoizedProps,u=u.memoizedState,l=a.stateNode;try{var le=on(a.type,n,a.elementType===a.type);e=l.getSnapshotBeforeUpdate(le,u),l.__reactInternalSnapshotBeforeUpdate=e}catch(te){Me(a,a.return,te)}}break;case 3:if((e&1024)!==0){if(e=t.stateNode.containerInfo,a=e.nodeType,a===9)Df(e);else if(a===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Df(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(O(163))}if(e=t.sibling,e!==null){e.return=t.return,lt=e;break}lt=t.return}}function Xo(e,t,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:Rl(e,a),l&4&&xu(5,a);break;case 1:if(Rl(e,a),l&4)if(e=a.stateNode,t===null)try{e.componentDidMount()}catch(f){Me(a,a.return,f)}else{var n=on(a.type,t.memoizedProps);t=t.memoizedState;try{e.componentDidUpdate(n,t,e.__reactInternalSnapshotBeforeUpdate)}catch(f){Me(a,a.return,f)}}l&64&&jo(a),l&512&&wu(a,a.return);break;case 3:if(Rl(e,a),l&64&&(e=a.updateQueue,e!==null)){if(t=null,a.child!==null)switch(a.child.tag){case 27:case 5:t=a.child.stateNode;break;case 1:t=a.child.stateNode}try{Nr(e,t)}catch(f){Me(a,a.return,f)}}break;case 27:t===null&&l&4&&Yo(a);case 26:case 5:Rl(e,a),t===null&&l&4&&xo(a),l&512&&wu(a,a.return);break;case 12:Rl(e,a);break;case 13:Rl(e,a),l&4&&$o(e,a),l&64&&(e=a.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(a=Kh.bind(null,a),om(e,a))));break;case 22:if(l=a.memoizedState!==null||Pa,!l){t=t!==null&&t.memoizedState!==null||Xe,n=Pa;var u=Xe;Pa=l,(Xe=t)&&!u?Cl(e,a,(a.subtreeFlags&8772)!==0):Rl(e,a),Pa=n,Xe=u}break;case 30:break;default:Rl(e,a)}}function Qo(e){var t=e.alternate;t!==null&&(e.alternate=null,Qo(t)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(t=e.stateNode,t!==null&&su(t)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var ze=null,_t=!1;function el(e,t,a){for(a=a.child;a!==null;)Zo(e,t,a),a=a.sibling}function Zo(e,t,a){if(it&&typeof it.onCommitFiberUnmount=="function")try{it.onCommitFiberUnmount(Yl,a)}catch{}switch(a.tag){case 26:Xe||Ba(a,t),el(e,t,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Xe||Ba(a,t);var l=ze,n=_t;Ul(a.type)&&(ze=a.stateNode,_t=!1),el(e,t,a),Ju(a.stateNode),ze=l,_t=n;break;case 5:Xe||Ba(a,t);case 6:if(l=ze,n=_t,ze=null,el(e,t,a),ze=l,_t=n,ze!==null)if(_t)try{(ze.nodeType===9?ze.body:ze.nodeName==="HTML"?ze.ownerDocument.body:ze).removeChild(a.stateNode)}catch(u){Me(a,t,u)}else try{ze.removeChild(a.stateNode)}catch(u){Me(a,t,u)}break;case 18:ze!==null&&(_t?(e=ze,Ud(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,a.stateNode),li(e)):Ud(ze,a.stateNode));break;case 4:l=ze,n=_t,ze=a.stateNode.containerInfo,_t=!0,el(e,t,a),ze=l,_t=n;break;case 0:case 11:case 14:case 15:Xe||Dl(2,a,t),Xe||Dl(4,a,t),el(e,t,a);break;case 1:Xe||(Ba(a,t),l=a.stateNode,typeof l.componentWillUnmount=="function"&&qo(a,t,l)),el(e,t,a);break;case 21:el(e,t,a);break;case 22:Xe=(l=Xe)||a.memoizedState!==null,el(e,t,a),Xe=l;break;default:el(e,t,a)}}function $o(e,t){if(t.memoizedState===null&&(e=t.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{li(e)}catch(a){Me(t,t.return,a)}}function xh(e){switch(e.tag){case 13:case 19:var t=e.stateNode;return t===null&&(t=e.stateNode=new Vo),t;case 22:return e=e.stateNode,t=e._retryCache,t===null&&(t=e._retryCache=new Vo),t;default:throw Error(O(435,e.tag))}}function Fs(e,t){var a=xh(e);t.forEach(function(l){var n=kh.bind(null,e,l);a.has(l)||(a.add(l),l.then(n,n))})}function Vt(e,t){var a=t.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],u=e,f=t,d=f;e:for(;d!==null;){switch(d.tag){case 27:if(Ul(d.type)){ze=d.stateNode,_t=!1;break e}break;case 5:ze=d.stateNode,_t=!1;break e;case 3:case 4:ze=d.stateNode.containerInfo,_t=!0;break e}d=d.return}if(ze===null)throw Error(O(160));Zo(u,f,n),ze=null,_t=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(t.subtreeFlags&13878)for(t=t.child;t!==null;)Ko(t,e),t=t.sibling}var Sa=null;function Ko(e,t){var a=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Vt(t,e),Xt(e),l&4&&(Dl(3,e,e.return),xu(3,e),Dl(5,e,e.return));break;case 1:Vt(t,e),Xt(e),l&512&&(Xe||a===null||Ba(a,a.return)),l&64&&Pa&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(a=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Sa;if(Vt(t,e),Xt(e),l&512&&(Xe||a===null||Ba(a,a.return)),l&4){var u=a!==null?a.memoizedState:null;if(l=e.memoizedState,a===null)if(l===null)if(e.stateNode===null){e:{l=e.type,a=e.memoizedProps,n=n.ownerDocument||n;t:switch(l){case"title":u=n.getElementsByTagName("title")[0],(!u||u[pa]||u[je]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(l),n.head.insertBefore(u,n.querySelector("head > title"))),dt(u,l,a),u[je]=e,qe(u),l=u;break e;case"link":var f=Yd("link","href",n).get(l+(a.href||""));if(f){for(var d=0;d<f.length;d++)if(u=f[d],u.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&u.getAttribute("rel")===(a.rel==null?null:a.rel)&&u.getAttribute("title")===(a.title==null?null:a.title)&&u.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){f.splice(d,1);break t}}u=n.createElement(l),dt(u,l,a),n.head.appendChild(u);break;case"meta":if(f=Yd("meta","content",n).get(l+(a.content||""))){for(d=0;d<f.length;d++)if(u=f[d],u.getAttribute("content")===(a.content==null?null:""+a.content)&&u.getAttribute("name")===(a.name==null?null:a.name)&&u.getAttribute("property")===(a.property==null?null:a.property)&&u.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&u.getAttribute("charset")===(a.charSet==null?null:a.charSet)){f.splice(d,1);break t}}u=n.createElement(l),dt(u,l,a),n.head.appendChild(u);break;default:throw Error(O(468,l))}u[je]=e,qe(u),l=u}e.stateNode=l}else Vd(n,e.type,e.stateNode);else e.stateNode=wd(n,l,e.memoizedProps);else u!==l?(u===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):u.count--,l===null?Vd(n,e.type,e.stateNode):wd(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&Ks(e,e.memoizedProps,a.memoizedProps)}break;case 27:Vt(t,e),Xt(e),l&512&&(Xe||a===null||Ba(a,a.return)),a!==null&&l&4&&Ks(e,e.memoizedProps,a.memoizedProps);break;case 5:if(Vt(t,e),Xt(e),l&512&&(Xe||a===null||Ba(a,a.return)),e.flags&32){n=e.stateNode;try{ga(n,"")}catch(C){Me(e,e.return,C)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,Ks(e,n,a!==null?a.memoizedProps:n)),l&1024&&(Ws=!0);break;case 6:if(Vt(t,e),Xt(e),l&4){if(e.stateNode===null)throw Error(O(162));l=e.memoizedProps,a=e.stateNode;try{a.nodeValue=l}catch(C){Me(e,e.return,C)}}break;case 3:if(Ac=null,n=Sa,Sa=vc(t.containerInfo),Vt(t,e),Sa=n,Xt(e),l&4&&a!==null&&a.memoizedState.isDehydrated)try{li(t.containerInfo)}catch(C){Me(e,e.return,C)}Ws&&(Ws=!1,ko(e));break;case 4:l=Sa,Sa=vc(e.stateNode.containerInfo),Vt(t,e),Xt(e),Sa=l;break;case 12:Vt(t,e),Xt(e);break;case 13:Vt(t,e),Xt(e),e.child.flags&8192&&e.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(lf=Dt()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Fs(e,l)));break;case 22:n=e.memoizedState!==null;var g=a!==null&&a.memoizedState!==null,D=Pa,B=Xe;if(Pa=D||n,Xe=B||g,Vt(t,e),Xe=B,Pa=D,Xt(e),l&8192)e:for(t=e.stateNode,t._visibility=n?t._visibility&-2:t._visibility|1,n&&(a===null||g||Pa||Xe||dn(e)),a=null,t=e;;){if(t.tag===5||t.tag===26){if(a===null){g=a=t;try{if(u=g.stateNode,n)f=u.style,typeof f.setProperty=="function"?f.setProperty("display","none","important"):f.display="none";else{d=g.stateNode;var L=g.memoizedProps.style,R=L!=null&&L.hasOwnProperty("display")?L.display:null;d.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(C){Me(g,g.return,C)}}}else if(t.tag===6){if(a===null){g=t;try{g.stateNode.nodeValue=n?"":g.memoizedProps}catch(C){Me(g,g.return,C)}}}else if((t.tag!==22&&t.tag!==23||t.memoizedState===null||t===e)&&t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break e;for(;t.sibling===null;){if(t.return===null||t.return===e)break e;a===t&&(a=null),t=t.return}a===t&&(a=null),t.sibling.return=t.return,t=t.sibling}l&4&&(l=e.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,Fs(e,a))));break;case 19:Vt(t,e),Xt(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Fs(e,l)));break;case 30:break;case 21:break;default:Vt(t,e),Xt(e)}}function Xt(e){var t=e.flags;if(t&2){try{for(var a,l=e.return;l!==null;){if(wo(l)){a=l;break}l=l.return}if(a==null)throw Error(O(160));switch(a.tag){case 27:var n=a.stateNode,u=ks(e);nc(e,u,n);break;case 5:var f=a.stateNode;a.flags&32&&(ga(f,""),a.flags&=-33);var d=ks(e);nc(e,d,f);break;case 3:case 4:var g=a.stateNode.containerInfo,D=ks(e);Js(e,D,g);break;default:throw Error(O(161))}}catch(B){Me(e,e.return,B)}e.flags&=-3}t&4096&&(e.flags&=-4097)}function ko(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var t=e;ko(t),t.tag===5&&t.flags&1024&&t.stateNode.reset(),e=e.sibling}}function Rl(e,t){if(t.subtreeFlags&8772)for(t=t.child;t!==null;)Xo(e,t.alternate,t),t=t.sibling}function dn(e){for(e=e.child;e!==null;){var t=e;switch(t.tag){case 0:case 11:case 14:case 15:Dl(4,t,t.return),dn(t);break;case 1:Ba(t,t.return);var a=t.stateNode;typeof a.componentWillUnmount=="function"&&qo(t,t.return,a),dn(t);break;case 27:Ju(t.stateNode);case 26:case 5:Ba(t,t.return),dn(t);break;case 22:t.memoizedState===null&&dn(t);break;case 30:dn(t);break;default:dn(t)}e=e.sibling}}function Cl(e,t,a){for(a=a&&(t.subtreeFlags&8772)!==0,t=t.child;t!==null;){var l=t.alternate,n=e,u=t,f=u.flags;switch(u.tag){case 0:case 11:case 15:Cl(n,u,a),xu(4,u);break;case 1:if(Cl(n,u,a),l=u,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(D){Me(l,l.return,D)}if(l=u,n=l.updateQueue,n!==null){var d=l.stateNode;try{var g=n.shared.hiddenCallbacks;if(g!==null)for(n.shared.hiddenCallbacks=null,n=0;n<g.length;n++)pr(g[n],d)}catch(D){Me(l,l.return,D)}}a&&f&64&&jo(u),wu(u,u.return);break;case 27:Yo(u);case 26:case 5:Cl(n,u,a),a&&l===null&&f&4&&xo(u),wu(u,u.return);break;case 12:Cl(n,u,a);break;case 13:Cl(n,u,a),a&&f&4&&$o(n,u);break;case 22:u.memoizedState===null&&Cl(n,u,a),wu(u,u.return);break;case 30:break;default:Cl(n,u,a)}t=t.sibling}}function Is(e,t){var a=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(a=e.memoizedState.cachePool.pool),e=null,t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(e=t.memoizedState.cachePool.pool),e!==a&&(e!=null&&e.refCount++,a!=null&&Du(a))}function Ps(e,t){e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Du(e))}function Ga(e,t,a,l){if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Jo(e,t,a,l),t=t.sibling}function Jo(e,t,a,l){var n=t.flags;switch(t.tag){case 0:case 11:case 15:Ga(e,t,a,l),n&2048&&xu(9,t);break;case 1:Ga(e,t,a,l);break;case 3:Ga(e,t,a,l),n&2048&&(e=null,t.alternate!==null&&(e=t.alternate.memoizedState.cache),t=t.memoizedState.cache,t!==e&&(t.refCount++,e!=null&&Du(e)));break;case 12:if(n&2048){Ga(e,t,a,l),e=t.stateNode;try{var u=t.memoizedProps,f=u.id,d=u.onPostCommit;typeof d=="function"&&d(f,t.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(g){Me(t,t.return,g)}}else Ga(e,t,a,l);break;case 13:Ga(e,t,a,l);break;case 23:break;case 22:u=t.stateNode,f=t.alternate,t.memoizedState!==null?u._visibility&2?Ga(e,t,a,l):Yu(e,t):u._visibility&2?Ga(e,t,a,l):(u._visibility|=2,Kn(e,t,a,l,(t.subtreeFlags&10256)!==0)),n&2048&&Is(f,t);break;case 24:Ga(e,t,a,l),n&2048&&Ps(t.alternate,t);break;default:Ga(e,t,a,l)}}function Kn(e,t,a,l,n){for(n=n&&(t.subtreeFlags&10256)!==0,t=t.child;t!==null;){var u=e,f=t,d=a,g=l,D=f.flags;switch(f.tag){case 0:case 11:case 15:Kn(u,f,d,g,n),xu(8,f);break;case 23:break;case 22:var B=f.stateNode;f.memoizedState!==null?B._visibility&2?Kn(u,f,d,g,n):Yu(u,f):(B._visibility|=2,Kn(u,f,d,g,n)),n&&D&2048&&Is(f.alternate,f);break;case 24:Kn(u,f,d,g,n),n&&D&2048&&Ps(f.alternate,f);break;default:Kn(u,f,d,g,n)}t=t.sibling}}function Yu(e,t){if(t.subtreeFlags&10256)for(t=t.child;t!==null;){var a=e,l=t,n=l.flags;switch(l.tag){case 22:Yu(a,l),n&2048&&Is(l.alternate,l);break;case 24:Yu(a,l),n&2048&&Ps(l.alternate,l);break;default:Yu(a,l)}t=t.sibling}}var Vu=8192;function kn(e){if(e.subtreeFlags&Vu)for(e=e.child;e!==null;)Wo(e),e=e.sibling}function Wo(e){switch(e.tag){case 26:kn(e),e.flags&Vu&&e.memoizedState!==null&&bm(Sa,e.memoizedState,e.memoizedProps);break;case 5:kn(e);break;case 3:case 4:var t=Sa;Sa=vc(e.stateNode.containerInfo),kn(e),Sa=t;break;case 22:e.memoizedState===null&&(t=e.alternate,t!==null&&t.memoizedState!==null?(t=Vu,Vu=16777216,kn(e),Vu=t):kn(e));break;default:kn(e)}}function Fo(e){var t=e.alternate;if(t!==null&&(e=t.child,e!==null)){t.child=null;do t=e.sibling,e.sibling=null,e=t;while(e!==null)}}function Xu(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];lt=l,Po(l,e)}Fo(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)Io(e),e=e.sibling}function Io(e){switch(e.tag){case 0:case 11:case 15:Xu(e),e.flags&2048&&Dl(9,e,e.return);break;case 3:Xu(e);break;case 12:Xu(e);break;case 22:var t=e.stateNode;e.memoizedState!==null&&t._visibility&2&&(e.return===null||e.return.tag!==13)?(t._visibility&=-3,uc(e)):Xu(e);break;default:Xu(e)}}function uc(e){var t=e.deletions;if((e.flags&16)!==0){if(t!==null)for(var a=0;a<t.length;a++){var l=t[a];lt=l,Po(l,e)}Fo(e)}for(e=e.child;e!==null;){switch(t=e,t.tag){case 0:case 11:case 15:Dl(8,t,t.return),uc(t);break;case 22:a=t.stateNode,a._visibility&2&&(a._visibility&=-3,uc(t));break;default:uc(t)}e=e.sibling}}function Po(e,t){for(;lt!==null;){var a=lt;switch(a.tag){case 0:case 11:case 15:Dl(8,a,t);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:Du(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,lt=l;else e:for(a=e;lt!==null;){l=lt;var n=l.sibling,u=l.return;if(Qo(l),l===a){lt=null;break e}if(n!==null){n.return=u,lt=n;break e}lt=u}}}var wh={getCacheForType:function(e){var t=vt(Fe),a=t.data.get(e);return a===void 0&&(a=e(),t.data.set(e,a)),a}},Yh=typeof WeakMap=="function"?WeakMap:Map,Ne=0,Be=null,oe=null,ge=0,be=0,Qt=null,Ol=!1,Jn=!1,ef=!1,tl=0,Ye=0,_l=0,hn=0,tf=0,ua=0,Wn=0,Qu=null,Mt=null,af=!1,lf=0,ic=1/0,cc=null,Ml=null,ot=0,Bl=null,Fn=null,In=0,nf=0,uf=null,ed=null,Zu=0,cf=null;function Zt(){if((Ne&2)!==0&&ge!==0)return ge&-ge;if(M.T!==null){var e=xn;return e!==0?e:mf()}return _e()}function td(){ua===0&&(ua=(ge&536870912)===0||Ee?cl():536870912);var e=na.current;return e!==null&&(e.flags|=32),ua}function $t(e,t,a){(e===Be&&(be===2||be===9)||e.cancelPendingCommit!==null)&&(Pn(e,0),Gl(e,ge,ua,!1)),Xl(e,a),((Ne&2)===0||e!==Be)&&(e===Be&&((Ne&2)===0&&(hn|=a),Ye===4&&Gl(e,ge,ua,!1)),Ua(e))}function ad(e,t,a){if((Ne&6)!==0)throw Error(O(327));var l=!a&&(t&124)===0&&(t&e.expiredLanes)===0||il(e,t),n=l?Qh(e,t):rf(e,t,!0),u=l;do{if(n===0){Jn&&!l&&Gl(e,t,0,!1);break}else{if(a=e.current.alternate,u&&!Vh(a)){n=rf(e,t,!1),u=!1;continue}if(n===2){if(u=t,e.errorRecoveryDisabledLanes&u)var f=0;else f=e.pendingLanes&-536870913,f=f!==0?f:f&536870912?536870912:0;if(f!==0){t=f;e:{var d=e;n=Qu;var g=d.current.memoizedState.isDehydrated;if(g&&(Pn(d,f).flags|=256),f=rf(d,f,!1),f!==2){if(ef&&!g){d.errorRecoveryDisabledLanes|=u,hn|=u,n=4;break e}u=Mt,Mt=n,u!==null&&(Mt===null?Mt=u:Mt.push.apply(Mt,u))}n=f}if(u=!1,n!==2)continue}}if(n===1){Pn(e,0),Gl(e,t,0,!0);break}e:{switch(l=e,u=n,u){case 0:case 1:throw Error(O(345));case 4:if((t&4194048)!==t)break;case 6:Gl(l,t,ua,!Ol);break e;case 2:Mt=null;break;case 3:case 5:break;default:throw Error(O(329))}if((t&62914560)===t&&(n=lf+300-Dt(),10<n)){if(Gl(l,t,ua,!Ol),Vl(l,0,!0)!==0)break e;l.timeoutHandle=Bd(ld.bind(null,l,a,Mt,cc,af,t,ua,hn,Wn,Ol,u,2,-0,0),n);break e}ld(l,a,Mt,cc,af,t,ua,hn,Wn,Ol,u,0,-0,0)}}break}while(!0);Ua(e)}function ld(e,t,a,l,n,u,f,d,g,D,B,L,R,C){if(e.timeoutHandle=-1,L=t.subtreeFlags,(L&8192||(L&16785408)===16785408)&&(Iu={stylesheets:null,count:0,unsuspend:Nm},Wo(t),L=Dm(),L!==null)){e.cancelPendingCommit=L(rd.bind(null,e,t,u,a,l,n,f,d,g,B,1,R,C)),Gl(e,u,f,!D);return}rd(e,t,u,a,l,n,f,d,g)}function Vh(e){for(var t=e;;){var a=t.tag;if((a===0||a===11||a===15)&&t.flags&16384&&(a=t.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],u=n.getSnapshot;n=n.value;try{if(!wt(u(),n))return!1}catch{return!1}}if(a=t.child,t.subtreeFlags&16384&&a!==null)a.return=t,t=a;else{if(t===e)break;for(;t.sibling===null;){if(t.return===null||t.return===e)return!0;t=t.return}t.sibling.return=t.return,t=t.sibling}}return!0}function Gl(e,t,a,l){t&=~tf,t&=~hn,e.suspendedLanes|=t,e.pingedLanes&=~t,l&&(e.warmLanes|=t),l=e.expirationTimes;for(var n=t;0<n;){var u=31-mt(n),f=1<<u;l[u]=-1,n&=~f}a!==0&&ja(e,a,t)}function sc(){return(Ne&6)===0?($u(0),!1):!0}function sf(){if(oe!==null){if(be===0)var e=oe.return;else e=oe,Ka=cn=null,bs(e),Zn=null,Lu=0,e=oe;for(;e!==null;)Lo(e.alternate,e),e=e.return;oe=null}}function Pn(e,t){var a=e.timeoutHandle;a!==-1&&(e.timeoutHandle=-1,im(a)),a=e.cancelPendingCommit,a!==null&&(e.cancelPendingCommit=null,a()),sf(),Be=e,oe=a=Qa(e.current,null),ge=t,be=0,Qt=null,Ol=!1,Jn=il(e,t),ef=!1,Wn=ua=tf=hn=_l=Ye=0,Mt=Qu=null,af=!1,(t&8)!==0&&(t|=t&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=t;0<l;){var n=31-mt(l),u=1<<n;t|=e[n],l&=~u}return tl=t,Mi(),a}function nd(e,t){ce=null,M.H=Ji,t===Cu||t===xi?(t=Er(),be=3):t===vr?(t=Er(),be=4):be=t===po?8:t!==null&&typeof t=="object"&&typeof t.then=="function"?6:1,Qt=t,oe===null&&(Ye=1,ec(e,ea(t,e.current)))}function ud(){var e=M.H;return M.H=Ji,e===null?Ji:e}function id(){var e=M.A;return M.A=wh,e}function ff(){Ye=4,Ol||(ge&4194048)!==ge&&na.current!==null||(Jn=!0),(_l&134217727)===0&&(hn&134217727)===0||Be===null||Gl(Be,ge,ua,!1)}function rf(e,t,a){var l=Ne;Ne|=2;var n=ud(),u=id();(Be!==e||ge!==t)&&(cc=null,Pn(e,t)),t=!1;var f=Ye;e:do try{if(be!==0&&oe!==null){var d=oe,g=Qt;switch(be){case 8:sf(),f=6;break e;case 3:case 2:case 9:case 6:na.current===null&&(t=!0);var D=be;if(be=0,Qt=null,eu(e,d,g,D),a&&Jn){f=0;break e}break;default:D=be,be=0,Qt=null,eu(e,d,g,D)}}Xh(),f=Ye;break}catch(B){nd(e,B)}while(!0);return t&&e.shellSuspendCounter++,Ka=cn=null,Ne=l,M.H=n,M.A=u,oe===null&&(Be=null,ge=0,Mi()),f}function Xh(){for(;oe!==null;)cd(oe)}function Qh(e,t){var a=Ne;Ne|=2;var l=ud(),n=id();Be!==e||ge!==t?(cc=null,ic=Dt()+500,Pn(e,t)):Jn=il(e,t);e:do try{if(be!==0&&oe!==null){t=oe;var u=Qt;t:switch(be){case 1:be=0,Qt=null,eu(e,t,u,1);break;case 2:case 9:if(Sr(u)){be=0,Qt=null,sd(t);break}t=function(){be!==2&&be!==9||Be!==e||(be=7),Ua(e)},u.then(t,t);break e;case 3:be=7;break e;case 4:be=5;break e;case 7:Sr(u)?(be=0,Qt=null,sd(t)):(be=0,Qt=null,eu(e,t,u,7));break;case 5:var f=null;switch(oe.tag){case 26:f=oe.memoizedState;case 5:case 27:var d=oe;if(!f||Xd(f)){be=0,Qt=null;var g=d.sibling;if(g!==null)oe=g;else{var D=d.return;D!==null?(oe=D,fc(D)):oe=null}break t}}be=0,Qt=null,eu(e,t,u,5);break;case 6:be=0,Qt=null,eu(e,t,u,6);break;case 8:sf(),Ye=6;break e;default:throw Error(O(462))}}Zh();break}catch(B){nd(e,B)}while(!0);return Ka=cn=null,M.H=l,M.A=n,Ne=a,oe!==null?0:(Be=null,ge=0,Mi(),Ye)}function Zh(){for(;oe!==null&&!La();)cd(oe)}function cd(e){var t=Ho(e.alternate,e,tl);e.memoizedProps=e.pendingProps,t===null?fc(e):oe=t}function sd(e){var t=e,a=t.alternate;switch(t.tag){case 15:case 0:t=Oo(a,t,t.pendingProps,t.type,void 0,ge);break;case 11:t=Oo(a,t,t.pendingProps,t.type.render,t.ref,ge);break;case 5:bs(t);default:Lo(a,t),t=oe=sr(t,tl),t=Ho(a,t,tl)}e.memoizedProps=e.pendingProps,t===null?fc(e):oe=t}function eu(e,t,a,l){Ka=cn=null,bs(t),Zn=null,Lu=0;var n=t.return;try{if(Hh(e,n,t,a,ge)){Ye=1,ec(e,ea(a,e.current)),oe=null;return}}catch(u){if(n!==null)throw oe=n,u;Ye=1,ec(e,ea(a,e.current)),oe=null;return}t.flags&32768?(Ee||l===1?e=!0:Jn||(ge&536870912)!==0?e=!1:(Ol=e=!0,(l===2||l===9||l===3||l===6)&&(l=na.current,l!==null&&l.tag===13&&(l.flags|=16384))),fd(t,e)):fc(t)}function fc(e){var t=e;do{if((t.flags&32768)!==0){fd(t,Ol);return}e=t.return;var a=Lh(t.alternate,t,tl);if(a!==null){oe=a;return}if(t=t.sibling,t!==null){oe=t;return}oe=t=e}while(t!==null);Ye===0&&(Ye=5)}function fd(e,t){do{var a=jh(e.alternate,e);if(a!==null){a.flags&=32767,oe=a;return}if(a=e.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!t&&(e=e.sibling,e!==null)){oe=e;return}oe=e=a}while(e!==null);Ye=6,oe=null}function rd(e,t,a,l,n,u,f,d,g){e.cancelPendingCommit=null;do rc();while(ot!==0);if((Ne&6)!==0)throw Error(O(327));if(t!==null){if(t===e.current)throw Error(O(177));if(u=t.lanes|t.childLanes,u|=Pc,Bc(e,a,u,f,d,g),e===Be&&(oe=Be=null,ge=0),Fn=t,Bl=e,In=a,nf=u,uf=n,ed=l,(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Jh(wl,function(){return yd(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(t.flags&13878)!==0,(t.subtreeFlags&13878)!==0||l){l=M.T,M.T=null,n=q.p,q.p=2,f=Ne,Ne|=4;try{qh(e,t,a)}finally{Ne=f,q.p=n,M.T=l}}ot=1,od(),dd(),hd()}}function od(){if(ot===1){ot=0;var e=Bl,t=Fn,a=(t.flags&13878)!==0;if((t.subtreeFlags&13878)!==0||a){a=M.T,M.T=null;var l=q.p;q.p=2;var n=Ne;Ne|=4;try{Ko(t,e);var u=pf,f=If(e.containerInfo),d=u.focusedElem,g=u.selectionRange;if(f!==d&&d&&d.ownerDocument&&Ff(d.ownerDocument.documentElement,d)){if(g!==null&&kc(d)){var D=g.start,B=g.end;if(B===void 0&&(B=D),"selectionStart"in d)d.selectionStart=D,d.selectionEnd=Math.min(B,d.value.length);else{var L=d.ownerDocument||document,R=L&&L.defaultView||window;if(R.getSelection){var C=R.getSelection(),le=d.textContent.length,te=Math.min(g.start,le),Oe=g.end===void 0?te:Math.min(g.end,le);!C.extend&&te>Oe&&(f=Oe,Oe=te,te=f);var T=Wf(d,te),E=Wf(d,Oe);if(T&&E&&(C.rangeCount!==1||C.anchorNode!==T.node||C.anchorOffset!==T.offset||C.focusNode!==E.node||C.focusOffset!==E.offset)){var b=L.createRange();b.setStart(T.node,T.offset),C.removeAllRanges(),te>Oe?(C.addRange(b),C.extend(E.node,E.offset)):(b.setEnd(E.node,E.offset),C.addRange(b))}}}}for(L=[],C=d;C=C.parentNode;)C.nodeType===1&&L.push({element:C,left:C.scrollLeft,top:C.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<L.length;d++){var G=L[d];G.element.scrollLeft=G.left,G.element.scrollTop=G.top}}pc=!!Tf,pf=Tf=null}finally{Ne=n,q.p=l,M.T=a}}e.current=t,ot=2}}function dd(){if(ot===2){ot=0;var e=Bl,t=Fn,a=(t.flags&8772)!==0;if((t.subtreeFlags&8772)!==0||a){a=M.T,M.T=null;var l=q.p;q.p=2;var n=Ne;Ne|=4;try{Xo(e,t.alternate,t)}finally{Ne=n,q.p=l,M.T=a}}ot=3}}function hd(){if(ot===4||ot===3){ot=0,ra();var e=Bl,t=Fn,a=In,l=ed;(t.subtreeFlags&10256)!==0||(t.flags&10256)!==0?ot=5:(ot=0,Fn=Bl=null,md(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(Ml=null),qa(a),t=t.stateNode,it&&typeof it.onCommitFiberRoot=="function")try{it.onCommitFiberRoot(Yl,t,void 0,(t.current.flags&128)===128)}catch{}if(l!==null){t=M.T,n=q.p,q.p=2,M.T=null;try{for(var u=e.onRecoverableError,f=0;f<l.length;f++){var d=l[f];u(d.value,{componentStack:d.stack})}}finally{M.T=t,q.p=n}}(In&3)!==0&&rc(),Ua(e),n=e.pendingLanes,(a&4194090)!==0&&(n&42)!==0?e===cf?Zu++:(Zu=0,cf=e):Zu=0,$u(0)}}function md(e,t){(e.pooledCacheLanes&=t)===0&&(t=e.pooledCache,t!=null&&(e.pooledCache=null,Du(t)))}function rc(e){return od(),dd(),hd(),yd()}function yd(){if(ot!==5)return!1;var e=Bl,t=nf;nf=0;var a=qa(In),l=M.T,n=q.p;try{q.p=32>a?32:a,M.T=null,a=uf,uf=null;var u=Bl,f=In;if(ot=0,Fn=Bl=null,In=0,(Ne&6)!==0)throw Error(O(331));var d=Ne;if(Ne|=4,Io(u.current),Jo(u,u.current,f,a),Ne=d,$u(0,!1),it&&typeof it.onPostCommitFiberRoot=="function")try{it.onPostCommitFiberRoot(Yl,u)}catch{}return!0}finally{q.p=n,M.T=l,md(e,t)}}function gd(e,t,a){t=ea(a,t),t=qs(e.stateNode,t,2),e=Tl(e,t,2),e!==null&&(Xl(e,2),Ua(e))}function Me(e,t,a){if(e.tag===3)gd(e,e,a);else for(;t!==null;){if(t.tag===3){gd(t,e,a);break}else if(t.tag===1){var l=t.stateNode;if(typeof t.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(Ml===null||!Ml.has(l))){e=ea(a,e),a=Eo(2),l=Tl(t,a,2),l!==null&&(To(a,l,t,e),Xl(l,2),Ua(l));break}}t=t.return}}function of(e,t,a){var l=e.pingCache;if(l===null){l=e.pingCache=new Yh;var n=new Set;l.set(t,n)}else n=l.get(t),n===void 0&&(n=new Set,l.set(t,n));n.has(a)||(ef=!0,n.add(a),e=$h.bind(null,e,t,a),t.then(e,e))}function $h(e,t,a){var l=e.pingCache;l!==null&&l.delete(t),e.pingedLanes|=e.suspendedLanes&a,e.warmLanes&=~a,Be===e&&(ge&a)===a&&(Ye===4||Ye===3&&(ge&62914560)===ge&&300>Dt()-lf?(Ne&2)===0&&Pn(e,0):tf|=a,Wn===ge&&(Wn=0)),Ua(e)}function vd(e,t){t===0&&(t=hi()),e=zn(e,t),e!==null&&(Xl(e,t),Ua(e))}function Kh(e){var t=e.memoizedState,a=0;t!==null&&(a=t.retryLane),vd(e,a)}function kh(e,t){var a=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(O(314))}l!==null&&l.delete(t),vd(e,a)}function Jh(e,t){return xl(e,t)}var oc=null,tu=null,df=!1,dc=!1,hf=!1,mn=0;function Ua(e){e!==tu&&e.next===null&&(tu===null?oc=tu=e:tu=tu.next=e),dc=!0,df||(df=!0,Fh())}function $u(e,t){if(!hf&&dc){hf=!0;do for(var a=!1,l=oc;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var u=0;else{var f=l.suspendedLanes,d=l.pingedLanes;u=(1<<31-mt(42|e)+1)-1,u&=n&~(f&~d),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(a=!0,Td(l,u))}else u=ge,u=Vl(l,l===Be?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||il(l,u)||(a=!0,Td(l,u));l=l.next}while(a);hf=!1}}function Wh(){Sd()}function Sd(){dc=df=!1;var e=0;mn!==0&&(um()&&(e=mn),mn=0);for(var t=Dt(),a=null,l=oc;l!==null;){var n=l.next,u=Ad(l,t);u===0?(l.next=null,a===null?oc=n:a.next=n,n===null&&(tu=a)):(a=l,(e!==0||(u&3)!==0)&&(dc=!0)),l=n}$u(e)}function Ad(e,t){for(var a=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var f=31-mt(u),d=1<<f,g=n[f];g===-1?((d&a)===0||(d&l)!==0)&&(n[f]=di(d,t)):g<=t&&(e.expiredLanes|=d),u&=~d}if(t=Be,a=ge,a=Vl(e,e===t?a:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,a===0||e===t&&(be===2||be===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&ci(l),e.callbackNode=null,e.callbackPriority=0;if((a&3)===0||il(e,a)){if(t=a&-a,t===e.callbackPriority)return t;switch(l!==null&&ci(l),qa(a)){case 2:case 8:a=Rt;break;case 32:a=wl;break;case 268435456:a=oa;break;default:a=wl}return l=Ed.bind(null,e),a=xl(a,l),e.callbackPriority=t,e.callbackNode=a,t}return l!==null&&l!==null&&ci(l),e.callbackPriority=2,e.callbackNode=null,2}function Ed(e,t){if(ot!==0&&ot!==5)return e.callbackNode=null,e.callbackPriority=0,null;var a=e.callbackNode;if(rc()&&e.callbackNode!==a)return null;var l=ge;return l=Vl(e,e===Be?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(ad(e,l,t),Ad(e,Dt()),e.callbackNode!=null&&e.callbackNode===a?Ed.bind(null,e):null)}function Td(e,t){if(rc())return null;ad(e,t,!0)}function Fh(){cm(function(){(Ne&6)!==0?xl(cu,Wh):Sd()})}function mf(){return mn===0&&(mn=cl()),mn}function pd(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Tn(""+e)}function Nd(e,t){var a=t.ownerDocument.createElement("input");return a.name=t.name,a.value=t.value,e.id&&a.setAttribute("form",e.id),t.parentNode.insertBefore(a,t),e=new FormData(e),a.parentNode.removeChild(a),e}function Ih(e,t,a,l,n){if(t==="submit"&&a&&a.stateNode===n){var u=pd((n[pe]||null).action),f=l.submitter;f&&(t=(t=f[pe]||null)?pd(t.formAction):f.getAttribute("formAction"),t!==null&&(u=t,f=null));var d=new Rn("action","action",null,l,n);e.push({event:d,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(mn!==0){var g=f?Nd(n,f):new FormData(n);Us(a,{pending:!0,data:g,method:n.method,action:u},null,g)}}else typeof u=="function"&&(d.preventDefault(),g=f?Nd(n,f):new FormData(n),Us(a,{pending:!0,data:g,method:n.method,action:u},u,g))},currentTarget:n}]})}}for(var yf=0;yf<Ic.length;yf++){var gf=Ic[yf],Ph=gf.toLowerCase(),em=gf[0].toUpperCase()+gf.slice(1);va(Ph,"on"+em)}va(tr,"onAnimationEnd"),va(ar,"onAnimationIteration"),va(lr,"onAnimationStart"),va("dblclick","onDoubleClick"),va("focusin","onFocus"),va("focusout","onBlur"),va(vh,"onTransitionRun"),va(Sh,"onTransitionStart"),va(Ah,"onTransitionCancel"),va(nr,"onTransitionEnd"),ma("onMouseEnter",["mouseout","mouseover"]),ma("onMouseLeave",["mouseout","mouseover"]),ma("onPointerEnter",["pointerout","pointerover"]),ma("onPointerLeave",["pointerout","pointerover"]),yt("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),yt("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),yt("onBeforeInput",["compositionend","keypress","textInput","paste"]),yt("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),yt("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),yt("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Ku="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),tm=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Ku));function bd(e,t){t=(t&4)!==0;for(var a=0;a<e.length;a++){var l=e[a],n=l.event;l=l.listeners;e:{var u=void 0;if(t)for(var f=l.length-1;0<=f;f--){var d=l[f],g=d.instance,D=d.currentTarget;if(d=d.listener,g!==u&&n.isPropagationStopped())break e;u=d,n.currentTarget=D;try{u(n)}catch(B){Pi(B)}n.currentTarget=null,u=g}else for(f=0;f<l.length;f++){if(d=l[f],g=d.instance,D=d.currentTarget,d=d.listener,g!==u&&n.isPropagationStopped())break e;u=d,n.currentTarget=D;try{u(n)}catch(B){Pi(B)}n.currentTarget=null,u=g}}}}function de(e,t){var a=t[ct];a===void 0&&(a=t[ct]=new Set);var l=e+"__bubble";a.has(l)||(Dd(t,e,2,!1),a.add(l))}function vf(e,t,a){var l=0;t&&(l|=4),Dd(a,e,l,t)}var hc="_reactListening"+Math.random().toString(36).slice(2);function Sf(e){if(!e[hc]){e[hc]=!0,Wt.forEach(function(a){a!=="selectionchange"&&(tm.has(a)||vf(a,!1,e),vf(a,!0,e))});var t=e.nodeType===9?e:e.ownerDocument;t===null||t[hc]||(t[hc]=!0,vf("selectionchange",!1,t))}}function Dd(e,t,a,l){switch(Jd(t)){case 2:var n=Om;break;case 8:n=_m;break;default:n=Gf}a=n.bind(null,t,a,e),n=void 0,!Nn||t!=="touchstart"&&t!=="touchmove"&&t!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(t,a,{capture:!0,passive:n}):e.addEventListener(t,a,!0):n!==void 0?e.addEventListener(t,a,{passive:n}):e.addEventListener(t,a,!1)}function Af(e,t,a,l,n){var u=l;if((t&1)===0&&(t&2)===0&&l!==null)e:for(;;){if(l===null)return;var f=l.tag;if(f===3||f===4){var d=l.stateNode.containerInfo;if(d===n)break;if(f===4)for(f=l.return;f!==null;){var g=f.tag;if((g===3||g===4)&&f.stateNode.containerInfo===n)return;f=f.return}for(;d!==null;){if(f=Na(d),f===null)return;if(g=f.tag,g===5||g===6||g===26||g===27){l=u=f;continue e}d=d.parentNode}}l=l.return}Ei(function(){var D=u,B=ou(a),L=[];e:{var R=ur.get(e);if(R!==void 0){var C=Rn,le=e;switch(e){case"keypress":if(bn(a)===0)break e;case"keydown":case"keyup":C=Ca;break;case"focusin":le="focus",C=mu;break;case"focusout":le="blur",C=mu;break;case"beforeblur":case"afterblur":C=mu;break;case"click":if(a.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":C=_n;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":C=Uc;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":C=Vc;break;case tr:case ar:case lr:C=zc;break;case nr:C=Qc;break;case"scroll":case"scrollend":C=Ni;break;case"wheel":C=$c;break;case"copy":case"cut":case"paste":C=jc;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":C=gu;break;case"toggle":case"beforetoggle":C=Oi}var te=(t&4)!==0,Oe=!te&&(e==="scroll"||e==="scrollend"),T=te?R!==null?R+"Capture":null:R;te=[];for(var E=D,b;E!==null;){var G=E;if(b=G.stateNode,G=G.tag,G!==5&&G!==26&&G!==27||b===null||T===null||(G=Jl(E,T),G!=null&&te.push(ku(E,G,b))),Oe)break;E=E.return}0<te.length&&(R=new C(R,le,null,a,B),L.push({event:R,listeners:te}))}}if((t&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",C=e==="mouseout"||e==="pointerout",R&&a!==pn&&(le=a.relatedTarget||a.fromElement)&&(Na(le)||le[tt]))break e;if((C||R)&&(R=B.window===B?B:(R=B.ownerDocument)?R.defaultView||R.parentWindow:window,C?(le=a.relatedTarget||a.toElement,C=D,le=le?Na(le):null,le!==null&&(Oe=Y(le),te=le.tag,le!==Oe||te!==5&&te!==27&&te!==6)&&(le=null)):(C=null,le=D),C!==le)){if(te=_n,G="onMouseLeave",T="onMouseEnter",E="mouse",(e==="pointerout"||e==="pointerover")&&(te=gu,G="onPointerLeave",T="onPointerEnter",E="pointer"),Oe=C==null?R:xa(C),b=le==null?R:xa(le),R=new te(G,E+"leave",C,a,B),R.target=Oe,R.relatedTarget=b,G=null,Na(B)===D&&(te=new te(T,E+"enter",le,a,B),te.target=b,te.relatedTarget=Oe,G=te),Oe=G,C&&le)t:{for(te=C,T=le,E=0,b=te;b;b=au(b))E++;for(b=0,G=T;G;G=au(G))b++;for(;0<E-b;)te=au(te),E--;for(;0<b-E;)T=au(T),b--;for(;E--;){if(te===T||T!==null&&te===T.alternate)break t;te=au(te),T=au(T)}te=null}else te=null;C!==null&&Rd(L,R,C,te,!1),le!==null&&Oe!==null&&Rd(L,Oe,le,te,!0)}}e:{if(R=D?xa(D):window,C=R.nodeName&&R.nodeName.toLowerCase(),C==="select"||C==="input"&&R.type==="file")var J=re;else if(p(R))if(xe)J=mh;else{J=dh;var se=oh}else C=R.nodeName,!C||C.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?D&&jt(D.elementType)&&(J=re):J=hh;if(J&&(J=J(e,D))){H(L,J,a,B);break e}se&&se(e,R,D),e==="focusout"&&D&&R.type==="number"&&D.memoizedProps.value!=null&&ru(R,"number",R.value)}switch(se=D?xa(D):window,e){case"focusin":(p(se)||se.contentEditable==="true")&&(Gn=se,Jc=D,Eu=null);break;case"focusout":Eu=Jc=Gn=null;break;case"mousedown":Wc=!0;break;case"contextmenu":case"mouseup":case"dragend":Wc=!1,Pf(L,a,B);break;case"selectionchange":if(gh)break;case"keydown":case"keyup":Pf(L,a,B)}var F;if(Bn)e:{switch(e){case"compositionstart":var ae="onCompositionStart";break e;case"compositionend":ae="onCompositionEnd";break e;case"compositionupdate":ae="onCompositionUpdate";break e}ae=void 0}else o?c(e,a)&&(ae="onCompositionEnd"):e==="keydown"&&a.keyCode===229&&(ae="onCompositionStart");ae&&(vu&&a.locale!=="ko"&&(o||ae!=="onCompositionStart"?ae==="onCompositionEnd"&&o&&(F=Ti()):(Ct=B,Ya="value"in Ct?Ct.value:Ct.textContent,o=!0)),se=mc(D,ae),0<se.length&&(ae=new Di(ae,e,null,a,B),L.push({event:ae,listeners:se}),F?ae.data=F:(F=s(a),F!==null&&(ae.data=F)))),(F=_i?h(e,a):m(e,a))&&(ae=mc(D,"onBeforeInput"),0<ae.length&&(se=new Di("onBeforeInput","beforeinput",null,a,B),L.push({event:se,listeners:ae}),se.data=F)),Ih(L,e,D,a,B)}bd(L,t)})}function ku(e,t,a){return{instance:e,listener:t,currentTarget:a}}function mc(e,t){for(var a=t+"Capture",l=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Jl(e,a),n!=null&&l.unshift(ku(e,n,u)),n=Jl(e,t),n!=null&&l.push(ku(e,n,u))),e.tag===3)return l;e=e.return}return[]}function au(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function Rd(e,t,a,l,n){for(var u=t._reactName,f=[];a!==null&&a!==l;){var d=a,g=d.alternate,D=d.stateNode;if(d=d.tag,g!==null&&g===l)break;d!==5&&d!==26&&d!==27||D===null||(g=D,n?(D=Jl(a,u),D!=null&&f.unshift(ku(a,D,g))):n||(D=Jl(a,u),D!=null&&f.push(ku(a,D,g)))),a=a.return}f.length!==0&&e.push({event:t,listeners:f})}var am=/\r\n?/g,lm=/\u0000|\uFFFD/g;function Cd(e){return(typeof e=="string"?e:""+e).replace(am,`
`).replace(lm,"")}function Od(e,t){return t=Cd(t),Cd(e)===t}function yc(){}function Ce(e,t,a,l,n,u){switch(a){case"children":typeof l=="string"?t==="body"||t==="textarea"&&l===""||ga(e,l):(typeof l=="number"||typeof l=="bigint")&&t!=="body"&&ga(e,""+l);break;case"className":$l(e,"class",l);break;case"tabIndex":$l(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":$l(e,a,l);break;case"style":It(e,l,u);break;case"data":if(t!=="object"){$l(e,"data",l);break}case"src":case"href":if(l===""&&(t!=="a"||a!=="href")){e.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=Tn(""+l),e.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(a==="formAction"?(t!=="input"&&Ce(e,t,"name",n.name,n,null),Ce(e,t,"formEncType",n.formEncType,n,null),Ce(e,t,"formMethod",n.formMethod,n,null),Ce(e,t,"formTarget",n.formTarget,n,null)):(Ce(e,t,"encType",n.encType,n,null),Ce(e,t,"method",n.method,n,null),Ce(e,t,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(a);break}l=Tn(""+l),e.setAttribute(a,l);break;case"onClick":l!=null&&(e.onclick=yc);break;case"onScroll":l!=null&&de("scroll",e);break;case"onScrollEnd":l!=null&&de("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(O(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(O(60));e.innerHTML=a}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}a=Tn(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""+l):e.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,""):e.removeAttribute(a);break;case"capture":case"download":l===!0?e.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(a,l):e.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(a,l):e.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(a):e.setAttribute(a,l);break;case"popover":de("beforetoggle",e),de("toggle",e),vn(e,"popover",l);break;case"xlinkActuate":Et(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Et(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Et(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Et(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Et(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Et(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Et(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Et(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Et(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":vn(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=pt.get(a)||a,vn(e,a,l))}}function Ef(e,t,a,l,n,u){switch(a){case"style":It(e,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(O(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(O(60));e.innerHTML=a}}break;case"children":typeof l=="string"?ga(e,l):(typeof l=="number"||typeof l=="bigint")&&ga(e,""+l);break;case"onScroll":l!=null&&de("scroll",e);break;case"onScrollEnd":l!=null&&de("scrollend",e);break;case"onClick":l!=null&&(e.onclick=yc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!gn.hasOwnProperty(a))e:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),t=a.slice(2,n?a.length-7:void 0),u=e[pe]||null,u=u!=null?u[a]:null,typeof u=="function"&&e.removeEventListener(t,u,n),typeof l=="function")){typeof u!="function"&&u!==null&&(a in e?e[a]=null:e.hasAttribute(a)&&e.removeAttribute(a)),e.addEventListener(t,l,n);break e}a in e?e[a]=l:l===!0?e.setAttribute(a,""):vn(e,a,l)}}}function dt(e,t,a){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":de("error",e),de("load",e);var l=!1,n=!1,u;for(u in a)if(a.hasOwnProperty(u)){var f=a[u];if(f!=null)switch(u){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(O(137,t));default:Ce(e,t,u,f,a,null)}}n&&Ce(e,t,"srcSet",a.srcSet,a,null),l&&Ce(e,t,"src",a.src,a,null);return;case"input":de("invalid",e);var d=u=f=n=null,g=null,D=null;for(l in a)if(a.hasOwnProperty(l)){var B=a[l];if(B!=null)switch(l){case"name":n=B;break;case"type":f=B;break;case"checked":g=B;break;case"defaultChecked":D=B;break;case"value":u=B;break;case"defaultValue":d=B;break;case"children":case"dangerouslySetInnerHTML":if(B!=null)throw Error(O(137,t));break;default:Ce(e,t,l,B,a,null)}}Ai(e,u,d,g,D,f,n,!1),kl(e);return;case"select":de("invalid",e),l=f=u=null;for(n in a)if(a.hasOwnProperty(n)&&(d=a[n],d!=null))switch(n){case"value":u=d;break;case"defaultValue":f=d;break;case"multiple":l=d;default:Ce(e,t,n,d,a,null)}t=u,a=f,e.multiple=!!l,t!=null?Ft(e,!!l,t,!1):a!=null&&Ft(e,!!l,a,!0);return;case"textarea":de("invalid",e),u=n=l=null;for(f in a)if(a.hasOwnProperty(f)&&(d=a[f],d!=null))switch(f){case"value":l=d;break;case"defaultValue":n=d;break;case"children":u=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(O(91));break;default:Ce(e,t,f,d,a,null)}hl(e,l,n,u),kl(e);return;case"option":for(g in a)if(a.hasOwnProperty(g)&&(l=a[g],l!=null))switch(g){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:Ce(e,t,g,l,a,null)}return;case"dialog":de("beforetoggle",e),de("toggle",e),de("cancel",e),de("close",e);break;case"iframe":case"object":de("load",e);break;case"video":case"audio":for(l=0;l<Ku.length;l++)de(Ku[l],e);break;case"image":de("error",e),de("load",e);break;case"details":de("toggle",e);break;case"embed":case"source":case"link":de("error",e),de("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(D in a)if(a.hasOwnProperty(D)&&(l=a[D],l!=null))switch(D){case"children":case"dangerouslySetInnerHTML":throw Error(O(137,t));default:Ce(e,t,D,l,a,null)}return;default:if(jt(t)){for(B in a)a.hasOwnProperty(B)&&(l=a[B],l!==void 0&&Ef(e,t,B,l,a,void 0));return}}for(d in a)a.hasOwnProperty(d)&&(l=a[d],l!=null&&Ce(e,t,d,l,a,null))}function nm(e,t,a,l){switch(t){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,f=null,d=null,g=null,D=null,B=null;for(C in a){var L=a[C];if(a.hasOwnProperty(C)&&L!=null)switch(C){case"checked":break;case"value":break;case"defaultValue":g=L;default:l.hasOwnProperty(C)||Ce(e,t,C,null,l,L)}}for(var R in l){var C=l[R];if(L=a[R],l.hasOwnProperty(R)&&(C!=null||L!=null))switch(R){case"type":u=C;break;case"name":n=C;break;case"checked":D=C;break;case"defaultChecked":B=C;break;case"value":f=C;break;case"defaultValue":d=C;break;case"children":case"dangerouslySetInnerHTML":if(C!=null)throw Error(O(137,t));break;default:C!==L&&Ce(e,t,R,C,l,L)}}fu(e,f,d,g,D,B,u,n);return;case"select":C=f=d=R=null;for(u in a)if(g=a[u],a.hasOwnProperty(u)&&g!=null)switch(u){case"value":break;case"multiple":C=g;default:l.hasOwnProperty(u)||Ce(e,t,u,null,l,g)}for(n in l)if(u=l[n],g=a[n],l.hasOwnProperty(n)&&(u!=null||g!=null))switch(n){case"value":R=u;break;case"defaultValue":d=u;break;case"multiple":f=u;default:u!==g&&Ce(e,t,n,u,l,g)}t=d,a=f,l=C,R!=null?Ft(e,!!a,R,!1):!!l!=!!a&&(t!=null?Ft(e,!!a,t,!0):Ft(e,!!a,a?[]:"",!1));return;case"textarea":C=R=null;for(d in a)if(n=a[d],a.hasOwnProperty(d)&&n!=null&&!l.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:Ce(e,t,d,null,l,n)}for(f in l)if(n=l[f],u=a[f],l.hasOwnProperty(f)&&(n!=null||u!=null))switch(f){case"value":R=n;break;case"defaultValue":C=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(O(91));break;default:n!==u&&Ce(e,t,f,n,l,u)}Ra(e,R,C);return;case"option":for(var le in a)if(R=a[le],a.hasOwnProperty(le)&&R!=null&&!l.hasOwnProperty(le))switch(le){case"selected":e.selected=!1;break;default:Ce(e,t,le,null,l,R)}for(g in l)if(R=l[g],C=a[g],l.hasOwnProperty(g)&&R!==C&&(R!=null||C!=null))switch(g){case"selected":e.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:Ce(e,t,g,R,l,C)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var te in a)R=a[te],a.hasOwnProperty(te)&&R!=null&&!l.hasOwnProperty(te)&&Ce(e,t,te,null,l,R);for(D in l)if(R=l[D],C=a[D],l.hasOwnProperty(D)&&R!==C&&(R!=null||C!=null))switch(D){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(O(137,t));break;default:Ce(e,t,D,R,l,C)}return;default:if(jt(t)){for(var Oe in a)R=a[Oe],a.hasOwnProperty(Oe)&&R!==void 0&&!l.hasOwnProperty(Oe)&&Ef(e,t,Oe,void 0,l,R);for(B in l)R=l[B],C=a[B],!l.hasOwnProperty(B)||R===C||R===void 0&&C===void 0||Ef(e,t,B,R,l,C);return}}for(var T in a)R=a[T],a.hasOwnProperty(T)&&R!=null&&!l.hasOwnProperty(T)&&Ce(e,t,T,null,l,R);for(L in l)R=l[L],C=a[L],!l.hasOwnProperty(L)||R===C||R==null&&C==null||Ce(e,t,L,R,l,C)}var Tf=null,pf=null;function gc(e){return e.nodeType===9?e:e.ownerDocument}function _d(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function Md(e,t){if(e===0)switch(t){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&t==="foreignObject"?0:e}function Nf(e,t){return e==="textarea"||e==="noscript"||typeof t.children=="string"||typeof t.children=="number"||typeof t.children=="bigint"||typeof t.dangerouslySetInnerHTML=="object"&&t.dangerouslySetInnerHTML!==null&&t.dangerouslySetInnerHTML.__html!=null}var bf=null;function um(){var e=window.event;return e&&e.type==="popstate"?e===bf?!1:(bf=e,!0):(bf=null,!1)}var Bd=typeof setTimeout=="function"?setTimeout:void 0,im=typeof clearTimeout=="function"?clearTimeout:void 0,Gd=typeof Promise=="function"?Promise:void 0,cm=typeof queueMicrotask=="function"?queueMicrotask:typeof Gd<"u"?function(e){return Gd.resolve(null).then(e).catch(sm)}:Bd;function sm(e){setTimeout(function(){throw e})}function Ul(e){return e==="head"}function Ud(e,t){var a=t,l=0,n=0;do{var u=a.nextSibling;if(e.removeChild(a),u&&u.nodeType===8)if(a=u.data,a==="/$"){if(0<l&&8>l){a=l;var f=e.ownerDocument;if(a&1&&Ju(f.documentElement),a&2&&Ju(f.body),a&4)for(a=f.head,Ju(a),f=a.firstChild;f;){var d=f.nextSibling,g=f.nodeName;f[pa]||g==="SCRIPT"||g==="STYLE"||g==="LINK"&&f.rel.toLowerCase()==="stylesheet"||a.removeChild(f),f=d}}if(n===0){e.removeChild(u),li(t);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=u}while(a);li(t)}function Df(e){var t=e.firstChild;for(t&&t.nodeType===10&&(t=t.nextSibling);t;){var a=t;switch(t=t.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":Df(a),su(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}e.removeChild(a)}}function fm(e,t,a,l){for(;e.nodeType===1;){var n=a;if(e.nodeName.toLowerCase()!==t.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[pa])switch(t){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(t==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=Aa(e.nextSibling),e===null)break}return null}function rm(e,t,a){if(t==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!a||(e=Aa(e.nextSibling),e===null))return null;return e}function Rf(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function om(e,t){var a=e.ownerDocument;if(e.data!=="$?"||a.readyState==="complete")t();else{var l=function(){t(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Aa(e){for(;e!=null;e=e.nextSibling){var t=e.nodeType;if(t===1||t===3)break;if(t===8){if(t=e.data,t==="$"||t==="$!"||t==="$?"||t==="F!"||t==="F")break;if(t==="/$")return null}}return e}var Cf=null;function Hd(e){e=e.previousSibling;for(var t=0;e;){if(e.nodeType===8){var a=e.data;if(a==="$"||a==="$!"||a==="$?"){if(t===0)return e;t--}else a==="/$"&&t++}e=e.previousSibling}return null}function zd(e,t,a){switch(t=gc(a),e){case"html":if(e=t.documentElement,!e)throw Error(O(452));return e;case"head":if(e=t.head,!e)throw Error(O(453));return e;case"body":if(e=t.body,!e)throw Error(O(454));return e;default:throw Error(O(451))}}function Ju(e){for(var t=e.attributes;t.length;)e.removeAttributeNode(t[0]);su(e)}var ia=new Map,Ld=new Set;function vc(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var al=q.d;q.d={f:dm,r:hm,D:mm,C:ym,L:gm,m:vm,X:Am,S:Sm,M:Em};function dm(){var e=al.f(),t=sc();return e||t}function hm(e){var t=ha(e);t!==null&&t.tag===5&&t.type==="form"?to(t):al.r(e)}var lu=typeof document>"u"?null:document;function jd(e,t,a){var l=lu;if(l&&typeof t=="string"&&t){var n=$e(t);n='link[rel="'+e+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),Ld.has(n)||(Ld.add(n),e={rel:e,crossOrigin:a,href:t},l.querySelector(n)===null&&(t=l.createElement("link"),dt(t,"link",e),qe(t),l.head.appendChild(t)))}}function mm(e){al.D(e),jd("dns-prefetch",e,null)}function ym(e,t){al.C(e,t),jd("preconnect",e,t)}function gm(e,t,a){al.L(e,t,a);var l=lu;if(l&&e&&t){var n='link[rel="preload"][as="'+$e(t)+'"]';t==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+$e(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+$e(a.imageSizes)+'"]')):n+='[href="'+$e(e)+'"]';var u=n;switch(t){case"style":u=nu(e);break;case"script":u=uu(e)}ia.has(u)||(e=z({rel:"preload",href:t==="image"&&a&&a.imageSrcSet?void 0:e,as:t},a),ia.set(u,e),l.querySelector(n)!==null||t==="style"&&l.querySelector(Wu(u))||t==="script"&&l.querySelector(Fu(u))||(t=l.createElement("link"),dt(t,"link",e),qe(t),l.head.appendChild(t)))}}function vm(e,t){al.m(e,t);var a=lu;if(a&&e){var l=t&&typeof t.as=="string"?t.as:"script",n='link[rel="modulepreload"][as="'+$e(l)+'"][href="'+$e(e)+'"]',u=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=uu(e)}if(!ia.has(u)&&(e=z({rel:"modulepreload",href:e},t),ia.set(u,e),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Fu(u)))return}l=a.createElement("link"),dt(l,"link",e),qe(l),a.head.appendChild(l)}}}function Sm(e,t,a){al.S(e,t,a);var l=lu;if(l&&e){var n=rl(l).hoistableStyles,u=nu(e);t=t||"default";var f=n.get(u);if(!f){var d={loading:0,preload:null};if(f=l.querySelector(Wu(u)))d.loading=5;else{e=z({rel:"stylesheet",href:e,"data-precedence":t},a),(a=ia.get(u))&&Of(e,a);var g=f=l.createElement("link");qe(g),dt(g,"link",e),g._p=new Promise(function(D,B){g.onload=D,g.onerror=B}),g.addEventListener("load",function(){d.loading|=1}),g.addEventListener("error",function(){d.loading|=2}),d.loading|=4,Sc(f,t,l)}f={type:"stylesheet",instance:f,count:1,state:d},n.set(u,f)}}}function Am(e,t){al.X(e,t);var a=lu;if(a&&e){var l=rl(a).hoistableScripts,n=uu(e),u=l.get(n);u||(u=a.querySelector(Fu(n)),u||(e=z({src:e,async:!0},t),(t=ia.get(n))&&_f(e,t),u=a.createElement("script"),qe(u),dt(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function Em(e,t){al.M(e,t);var a=lu;if(a&&e){var l=rl(a).hoistableScripts,n=uu(e),u=l.get(n);u||(u=a.querySelector(Fu(n)),u||(e=z({src:e,async:!0,type:"module"},t),(t=ia.get(n))&&_f(e,t),u=a.createElement("script"),qe(u),dt(u,"link",e),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function qd(e,t,a,l){var n=(n=ue.current)?vc(n):null;if(!n)throw Error(O(446));switch(e){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(t=nu(a.href),a=rl(n).hoistableStyles,l=a.get(t),l||(l={type:"style",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){e=nu(a.href);var u=rl(n).hoistableStyles,f=u.get(e);if(f||(n=n.ownerDocument||n,f={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,f),(u=n.querySelector(Wu(e)))&&!u._p&&(f.instance=u,f.state.loading=5),ia.has(e)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},ia.set(e,a),u||Tm(n,e,a,f.state))),t&&l===null)throw Error(O(528,""));return f}if(t&&l!==null)throw Error(O(529,""));return null;case"script":return t=a.async,a=a.src,typeof a=="string"&&t&&typeof t!="function"&&typeof t!="symbol"?(t=uu(a),a=rl(n).hoistableScripts,l=a.get(t),l||(l={type:"script",instance:null,count:0,state:null},a.set(t,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(O(444,e))}}function nu(e){return'href="'+$e(e)+'"'}function Wu(e){return'link[rel="stylesheet"]['+e+"]"}function xd(e){return z({},e,{"data-precedence":e.precedence,precedence:null})}function Tm(e,t,a,l){e.querySelector('link[rel="preload"][as="style"]['+t+"]")?l.loading=1:(t=e.createElement("link"),l.preload=t,t.addEventListener("load",function(){return l.loading|=1}),t.addEventListener("error",function(){return l.loading|=2}),dt(t,"link",a),qe(t),e.head.appendChild(t))}function uu(e){return'[src="'+$e(e)+'"]'}function Fu(e){return"script[async]"+e}function wd(e,t,a){if(t.count++,t.instance===null)switch(t.type){case"style":var l=e.querySelector('style[data-href~="'+$e(a.href)+'"]');if(l)return t.instance=l,qe(l),l;var n=z({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),qe(l),dt(l,"style",n),Sc(l,a.precedence,e),t.instance=l;case"stylesheet":n=nu(a.href);var u=e.querySelector(Wu(n));if(u)return t.state.loading|=4,t.instance=u,qe(u),u;l=xd(a),(n=ia.get(n))&&Of(l,n),u=(e.ownerDocument||e).createElement("link"),qe(u);var f=u;return f._p=new Promise(function(d,g){f.onload=d,f.onerror=g}),dt(u,"link",l),t.state.loading|=4,Sc(u,a.precedence,e),t.instance=u;case"script":return u=uu(a.src),(n=e.querySelector(Fu(u)))?(t.instance=n,qe(n),n):(l=a,(n=ia.get(u))&&(l=z({},a),_f(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),qe(n),dt(n,"link",l),e.head.appendChild(n),t.instance=n);case"void":return null;default:throw Error(O(443,t.type))}else t.type==="stylesheet"&&(t.state.loading&4)===0&&(l=t.instance,t.state.loading|=4,Sc(l,a.precedence,e));return t.instance}function Sc(e,t,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,u=n,f=0;f<l.length;f++){var d=l[f];if(d.dataset.precedence===t)u=d;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(t=a.nodeType===9?a.head:a,t.insertBefore(e,t.firstChild))}function Of(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.title==null&&(e.title=t.title)}function _f(e,t){e.crossOrigin==null&&(e.crossOrigin=t.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=t.referrerPolicy),e.integrity==null&&(e.integrity=t.integrity)}var Ac=null;function Yd(e,t,a){if(Ac===null){var l=new Map,n=Ac=new Map;n.set(a,l)}else n=Ac,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(e))return l;for(l.set(e,null),a=a.getElementsByTagName(e),n=0;n<a.length;n++){var u=a[n];if(!(u[pa]||u[je]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var f=u.getAttribute(t)||"";f=e+f;var d=l.get(f);d?d.push(u):l.set(f,[u])}}return l}function Vd(e,t,a){e=e.ownerDocument||e,e.head.insertBefore(a,t==="title"?e.querySelector("head > title"):null)}function pm(e,t,a){if(a===1||t.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof t.precedence!="string"||typeof t.href!="string"||t.href==="")break;return!0;case"link":if(typeof t.rel!="string"||typeof t.href!="string"||t.href===""||t.onLoad||t.onError)break;switch(t.rel){case"stylesheet":return e=t.disabled,typeof t.precedence=="string"&&e==null;default:return!0}case"script":if(t.async&&typeof t.async!="function"&&typeof t.async!="symbol"&&!t.onLoad&&!t.onError&&t.src&&typeof t.src=="string")return!0}return!1}function Xd(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Iu=null;function Nm(){}function bm(e,t,a){if(Iu===null)throw Error(O(475));var l=Iu;if(t.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(t.state.loading&4)===0){if(t.instance===null){var n=nu(a.href),u=e.querySelector(Wu(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Ec.bind(l),e.then(l,l)),t.state.loading|=4,t.instance=u,qe(u);return}u=e.ownerDocument||e,a=xd(a),(n=ia.get(n))&&Of(a,n),u=u.createElement("link"),qe(u);var f=u;f._p=new Promise(function(d,g){f.onload=d,f.onerror=g}),dt(u,"link",a),t.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(t,e),(e=t.state.preload)&&(t.state.loading&3)===0&&(l.count++,t=Ec.bind(l),e.addEventListener("load",t),e.addEventListener("error",t))}}function Dm(){if(Iu===null)throw Error(O(475));var e=Iu;return e.stylesheets&&e.count===0&&Mf(e,e.stylesheets),0<e.count?function(t){var a=setTimeout(function(){if(e.stylesheets&&Mf(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=t,function(){e.unsuspend=null,clearTimeout(a)}}:null}function Ec(){if(this.count--,this.count===0){if(this.stylesheets)Mf(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var Tc=null;function Mf(e,t){e.stylesheets=null,e.unsuspend!==null&&(e.count++,Tc=new Map,t.forEach(Rm,e),Tc=null,Ec.call(e))}function Rm(e,t){if(!(t.state.loading&4)){var a=Tc.get(e);if(a)var l=a.get(null);else{a=new Map,Tc.set(e,a);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var f=n[u];(f.nodeName==="LINK"||f.getAttribute("media")!=="not all")&&(a.set(f.dataset.precedence,f),l=f)}l&&a.set(null,l)}n=t.instance,f=n.getAttribute("data-precedence"),u=a.get(f)||l,u===l&&a.set(null,n),a.set(f,n),this.count++,l=Ec.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),t.state.loading|=4}}var Pu={$$typeof:Ze,Provider:null,Consumer:null,_currentValue:ee,_currentValue2:ee,_threadCount:0};function Cm(e,t,a,l,n,u,f,d){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=sl(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=sl(0),this.hiddenUpdates=sl(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=f,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function Qd(e,t,a,l,n,u,f,d,g,D,B,L){return e=new Cm(e,t,a,f,d,g,D,L),t=1,u===!0&&(t|=24),u=Yt(3,null,null,t),e.current=u,u.stateNode=e,t=rs(),t.refCount++,e.pooledCache=t,t.refCount++,u.memoizedState={element:l,isDehydrated:a,cache:t},ms(u),e}function Zd(e){return e?(e=Ln,e):Ln}function $d(e,t,a,l,n,u){n=Zd(n),l.context===null?l.context=n:l.pendingContext=n,l=El(t),l.payload={element:a},u=u===void 0?null:u,u!==null&&(l.callback=u),a=Tl(e,l,t),a!==null&&($t(a,e,t),_u(a,e,t))}function Kd(e,t){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var a=e.retryLane;e.retryLane=a!==0&&a<t?a:t}}function Bf(e,t){Kd(e,t),(e=e.alternate)&&Kd(e,t)}function kd(e){if(e.tag===13){var t=zn(e,67108864);t!==null&&$t(t,e,67108864),Bf(e,67108864)}}var pc=!0;function Om(e,t,a,l){var n=M.T;M.T=null;var u=q.p;try{q.p=2,Gf(e,t,a,l)}finally{q.p=u,M.T=n}}function _m(e,t,a,l){var n=M.T;M.T=null;var u=q.p;try{q.p=8,Gf(e,t,a,l)}finally{q.p=u,M.T=n}}function Gf(e,t,a,l){if(pc){var n=Uf(l);if(n===null)Af(e,t,l,Nc,a),Wd(e,l);else if(Bm(n,e,t,a,l))l.stopPropagation();else if(Wd(e,l),t&4&&-1<Mm.indexOf(e)){for(;n!==null;){var u=ha(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var f=Ta(u.pendingLanes);if(f!==0){var d=u;for(d.pendingLanes|=2,d.entangledLanes|=2;f;){var g=1<<31-mt(f);d.entanglements[1]|=g,f&=~g}Ua(u),(Ne&6)===0&&(ic=Dt()+500,$u(0))}}break;case 13:d=zn(u,2),d!==null&&$t(d,u,2),sc(),Bf(u,2)}if(u=Uf(l),u===null&&Af(e,t,l,Nc,a),u===n)break;n=u}n!==null&&l.stopPropagation()}else Af(e,t,l,null,a)}}function Uf(e){return e=ou(e),Hf(e)}var Nc=null;function Hf(e){if(Nc=null,e=Na(e),e!==null){var t=Y(e);if(t===null)e=null;else{var a=t.tag;if(a===13){if(e=x(t),e!==null)return e;e=null}else if(a===3){if(t.stateNode.current.memoizedState.isDehydrated)return t.tag===3?t.stateNode.containerInfo:null;e=null}else t!==e&&(e=null)}}return Nc=e,null}function Jd(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Zf()){case cu:return 2;case Rt:return 8;case wl:case _c:return 32;case oa:return 268435456;default:return 32}default:return 32}}var zf=!1,Hl=null,zl=null,Ll=null,ei=new Map,ti=new Map,jl=[],Mm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function Wd(e,t){switch(e){case"focusin":case"focusout":Hl=null;break;case"dragenter":case"dragleave":zl=null;break;case"mouseover":case"mouseout":Ll=null;break;case"pointerover":case"pointerout":ei.delete(t.pointerId);break;case"gotpointercapture":case"lostpointercapture":ti.delete(t.pointerId)}}function ai(e,t,a,l,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:t,domEventName:a,eventSystemFlags:l,nativeEvent:u,targetContainers:[n]},t!==null&&(t=ha(t),t!==null&&kd(t)),e):(e.eventSystemFlags|=l,t=e.targetContainers,n!==null&&t.indexOf(n)===-1&&t.push(n),e)}function Bm(e,t,a,l,n){switch(t){case"focusin":return Hl=ai(Hl,e,t,a,l,n),!0;case"dragenter":return zl=ai(zl,e,t,a,l,n),!0;case"mouseover":return Ll=ai(Ll,e,t,a,l,n),!0;case"pointerover":var u=n.pointerId;return ei.set(u,ai(ei.get(u)||null,e,t,a,l,n)),!0;case"gotpointercapture":return u=n.pointerId,ti.set(u,ai(ti.get(u)||null,e,t,a,l,n)),!0}return!1}function Fd(e){var t=Na(e.target);if(t!==null){var a=Y(t);if(a!==null){if(t=a.tag,t===13){if(t=x(a),t!==null){e.blockedOn=t,yn(e.priority,function(){if(a.tag===13){var l=Zt();l=We(l);var n=zn(a,l);n!==null&&$t(n,a,l),Bf(a,l)}});return}}else if(t===3&&a.stateNode.current.memoizedState.isDehydrated){e.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}e.blockedOn=null}function bc(e){if(e.blockedOn!==null)return!1;for(var t=e.targetContainers;0<t.length;){var a=Uf(e.nativeEvent);if(a===null){a=e.nativeEvent;var l=new a.constructor(a.type,a);pn=l,a.target.dispatchEvent(l),pn=null}else return t=ha(a),t!==null&&kd(t),e.blockedOn=a,!1;t.shift()}return!0}function Id(e,t,a){bc(e)&&a.delete(t)}function Gm(){zf=!1,Hl!==null&&bc(Hl)&&(Hl=null),zl!==null&&bc(zl)&&(zl=null),Ll!==null&&bc(Ll)&&(Ll=null),ei.forEach(Id),ti.forEach(Id)}function Dc(e,t){e.blockedOn===t&&(e.blockedOn=null,zf||(zf=!0,K.unstable_scheduleCallback(K.unstable_NormalPriority,Gm)))}var Rc=null;function Pd(e){Rc!==e&&(Rc=e,K.unstable_scheduleCallback(K.unstable_NormalPriority,function(){Rc===e&&(Rc=null);for(var t=0;t<e.length;t+=3){var a=e[t],l=e[t+1],n=e[t+2];if(typeof l!="function"){if(Hf(l||a)===null)continue;break}var u=ha(a);u!==null&&(e.splice(t,3),t-=3,Us(u,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function li(e){function t(g){return Dc(g,e)}Hl!==null&&Dc(Hl,e),zl!==null&&Dc(zl,e),Ll!==null&&Dc(Ll,e),ei.forEach(t),ti.forEach(t);for(var a=0;a<jl.length;a++){var l=jl[a];l.blockedOn===e&&(l.blockedOn=null)}for(;0<jl.length&&(a=jl[0],a.blockedOn===null);)Fd(a),a.blockedOn===null&&jl.shift();if(a=(e.ownerDocument||e).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],u=a[l+1],f=n[pe]||null;if(typeof u=="function")f||Pd(a);else if(f){var d=null;if(u&&u.hasAttribute("formAction")){if(n=u,f=u[pe]||null)d=f.formAction;else if(Hf(n)!==null)continue}else d=f.action;typeof d=="function"?a[l+1]=d:(a.splice(l,3),l-=3),Pd(a)}}}function Lf(e){this._internalRoot=e}Cc.prototype.render=Lf.prototype.render=function(e){var t=this._internalRoot;if(t===null)throw Error(O(409));var a=t.current,l=Zt();$d(a,l,e,t,null,null)},Cc.prototype.unmount=Lf.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var t=e.containerInfo;$d(e.current,2,null,e,null,null),sc(),t[tt]=null}};function Cc(e){this._internalRoot=e}Cc.prototype.unstable_scheduleHydration=function(e){if(e){var t=_e();e={blockedOn:null,target:e,priority:t};for(var a=0;a<jl.length&&t!==0&&t<jl[a].priority;a++);jl.splice(a,0,e),a===0&&Fd(e)}};var eh=Te.version;if(eh!=="19.1.0")throw Error(O(527,eh,"19.1.0"));q.findDOMNode=function(e){var t=e._reactInternals;if(t===void 0)throw typeof e.render=="function"?Error(O(188)):(e=Object.keys(e).join(","),Error(O(268,e)));return e=V(t),e=e!==null?_(e):null,e=e===null?null:e.stateNode,e};var Um={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:M,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Oc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Oc.isDisabled&&Oc.supportsFiber)try{Yl=Oc.inject(Um),it=Oc}catch{}}return ui.createRoot=function(e,t){if(!v(e))throw Error(O(299));var a=!1,l="",n=go,u=vo,f=So,d=null;return t!=null&&(t.unstable_strictMode===!0&&(a=!0),t.identifierPrefix!==void 0&&(l=t.identifierPrefix),t.onUncaughtError!==void 0&&(n=t.onUncaughtError),t.onCaughtError!==void 0&&(u=t.onCaughtError),t.onRecoverableError!==void 0&&(f=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(d=t.unstable_transitionCallbacks)),t=Qd(e,1,!1,null,null,a,l,n,u,f,d,null),e[tt]=t.current,Sf(e),new Lf(t)},ui.hydrateRoot=function(e,t,a){if(!v(e))throw Error(O(299));var l=!1,n="",u=go,f=vo,d=So,g=null,D=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(u=a.onUncaughtError),a.onCaughtError!==void 0&&(f=a.onCaughtError),a.onRecoverableError!==void 0&&(d=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(g=a.unstable_transitionCallbacks),a.formState!==void 0&&(D=a.formState)),t=Qd(e,1,!0,t,a??null,l,n,u,f,d,g,D),t.context=Zd(null),a=t.current,l=Zt(),l=We(l),n=El(l),n.callback=null,Tl(a,n,l),a=l,t.current.lanes=a,Xl(t,a),Ua(t),e[tt]=t.current,Sf(e),new Cc(t)},ui.version="19.1.0",ui}var rh;function Xm(){if(rh)return xf.exports;rh=1;function K(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(K)}catch(Te){console.error(Te)}}return K(),xf.exports=Vm(),xf.exports}var Qm=Xm();const r={BANG:"BANG",MISSED:"MISSED",BEER:"BEER",PANIC:"PANIC",CAT_BALOU:"CAT_BALOU",STAGECOACH:"STAGECOACH",WELLS_FARGO:"WELLS_FARGO",GATLING:"GATLING",INDIANS:"INDIANS",DUEL:"DUEL",GENERAL_STORE:"GENERAL_STORE",SALOON:"SALOON",BARREL:"BARREL",SCOPE:"SCOPE",MUSTANG:"MUSTANG",JAIL:"JAIL",DYNAMITE:"DYNAMITE",VOLCANIC:"VOLCANIC",SCHOFIELD:"SCHOFIELD",REMINGTON:"REMINGTON",REV_CARABINE:"REV_CARABINE",WINCHESTER:"WINCHESTER",BINOCULAR:"BINOCULAR",HIDEOUT:"HIDEOUT",BRAWL:"BRAWL",DODGE:"DODGE",PUNCH:"PUNCH",RAG_TIME:"RAG_TIME",SPRINGFIELD:"SPRINGFIELD",TEQUILA:"TEQUILA",WHISKY:"WHISKY",BIBLE:"BIBLE",BUFFALO_RIFLE:"BUFFALO_RIFLE",CAN_CAN:"CAN_CAN",CANTEEN:"CANTEEN",CONESTOGA:"CONESTOGA",DERRINGER:"DERRINGER",HOWITZER:"HOWITZER",IRON_PLATE:"IRON_PLATE",KNIFE:"KNIFE",PEPPERBOX:"PEPPERBOX",PONY_EXPRESS:"PONY_EXPRESS",SOMBRERO:"SOMBRERO",TEN_GALLON_HAT:"TEN_GALLON_HAT"},U={HEARTS:"HEARTS",DIAMONDS:"DIAMONDS",CLUBS:"CLUBS",SPADES:"SPADES"},Xf={[r.VOLCANIC]:1,[r.SCHOFIELD]:2,[r.REMINGTON]:3,[r.REV_CARABINE]:4,[r.WINCHESTER]:5,[r.DERRINGER]:1,[r.PEPPERBOX]:1,[r.KNIFE]:1,[r.SPRINGFIELD]:2,[r.BUFFALO_RIFLE]:5,[r.HOWITZER]:4},Kt={WEAPON:"WEAPON",DEFENSIVE:"DEFENSIVE",SPECIAL:"SPECIAL",GREEN:"GREEN"},Ha={[r.BANG]:"/images/cards/bang.png",[r.MISSED]:"/images/cards/missed.png",[r.BEER]:"/images/cards/beer.png",[r.PANIC]:"/images/cards/panic.png",[r.CAT_BALOU]:"/images/cards/cat_balou.png",[r.STAGECOACH]:"/images/cards/stagecoach.png",[r.WELLS_FARGO]:"/images/cards/wells_fargo.png",[r.GATLING]:"/images/cards/gatling.png",[r.INDIANS]:"/images/cards/indians.png",[r.DUEL]:"/images/cards/duel.png",[r.GENERAL_STORE]:"/images/cards/general_store.png",[r.SALOON]:"/images/cards/saloon.png",[r.BARREL]:"/images/cards/barrel.png",[r.SCOPE]:"/images/cards/scope.png",[r.MUSTANG]:"/images/cards/mustang.png",[r.JAIL]:"/images/cards/jail.png",[r.DYNAMITE]:"/images/cards/dynamite.png",[r.VOLCANIC]:"/images/cards/volcanic.png",[r.SCHOFIELD]:"/images/cards/schofield.png",[r.REMINGTON]:"/images/cards/remington.png",[r.REV_CARABINE]:"/images/cards/rev_carabine.png",[r.WINCHESTER]:"/images/cards/winchester.png",[r.BINOCULAR]:"/images/cards/dodge_city/binocular.png",[r.HIDEOUT]:"/images/cards/dodge_city/hideout.png",[r.BRAWL]:"/images/cards/dodge_city/brawl.png",[r.DODGE]:"/images/cards/dodge_city/dodge.png",[r.PUNCH]:"/images/cards/dodge_city/punch.png",[r.RAG_TIME]:"/images/cards/dodge_city/rag_time.png",[r.SPRINGFIELD]:"/images/cards/dodge_city/springfield.png",[r.TEQUILA]:"/images/cards/dodge_city/tequila.png",[r.WHISKY]:"/images/cards/dodge_city/whisky.png",[r.BIBLE]:"/images/cards/dodge_city/bible.png",[r.BUFFALO_RIFLE]:"/images/cards/dodge_city/buffalo_rifle.png",[r.CAN_CAN]:"/images/cards/dodge_city/can_can.png",[r.CANTEEN]:"/images/cards/dodge_city/canteen.png",[r.CONESTOGA]:"/images/cards/dodge_city/conestoga.png",[r.DERRINGER]:"/images/cards/dodge_city/derringer.png",[r.HOWITZER]:"/images/cards/dodge_city/howitzer.png",[r.IRON_PLATE]:"/images/cards/dodge_city/iron_plate.png",[r.KNIFE]:"/images/cards/dodge_city/knife.png",[r.PEPPERBOX]:"/images/cards/dodge_city/pepperbox.png",[r.PONY_EXPRESS]:"/images/cards/dodge_city/pony_express.png",[r.SOMBRERO]:"/images/cards/dodge_city/sombrero.png",[r.TEN_GALLON_HAT]:"/images/cards/dodge_city/ten_gallon_hat.png"},ii={"Bart Cassidy":"/images/characters/bart_cassidy.png","Black Jack":"/images/characters/black_jack.png","Calamity Janet":"/images/characters/calamity_janet.png","El Gringo":"/images/characters/el_gringo.png","Jesse Jones":"/images/characters/jesse_jones.png",Jourdonnais:"/images/characters/jourdonnais.png","Kit Carlson":"/images/characters/kit_carlson.png","Lucky Duke":"/images/characters/lucky_duke.png","Paul Regret":"/images/characters/paul_regret.png","Pedro Ramirez":"/images/characters/pedro_ramirez.png","Rose Doolan":"/images/characters/rose_doolan.png","Sid Ketchum":"/images/characters/sid_ketchum.png","Slab the Killer":"/images/characters/slab_the_killer.png","Suzy Lafayette":"/images/characters/suzy_lafayette.png","Vulture Sam":"/images/characters/vulture_sam.png","Willy the Kid":"/images/characters/willy_the_kid.png","Apache Kid":"/images/characters/dodge_city/apcache_kid.png","Belle Star":"/images/characters/dodge_city/belle_star.png","Bill Noface":"/images/characters/dodge_city/bill_noface.png","Chuck Wengam":"/images/characters/dodge_city/chuck_wengam.png","Doc Holyday":"/images/characters/dodge_city/doc_holyday.png","Elena Fuente":"/images/characters/dodge_city/elena_fuente.png","Greg Digger":"/images/characters/dodge_city/greg_digger.png","Herb Hunter":"/images/characters/dodge_city/herb_hunter.png","José Delgado":"/images/characters/dodge_city/jose_delgado.png","Molly Stark":"/images/characters/dodge_city/molly_stark.png","Pat Brennan":"/images/characters/dodge_city/pat_brennan.png","Pixie Pete":"/images/characters/dodge_city/pixie_pete.png","Sean Mallory":"/images/characters/dodge_city/sean_mallory.png","Tequila Joe":"/images/characters/dodge_city/tequila_joe.png","Vera Custer":"/images/characters/dodge_city/vera_custer.png"},Zm="/images/box/card_back.png",ve={SHERIFF:"SHERIFF",DEPUTY:"DEPUTY",OUTLAW:"OUTLAW",RENEGADE:"RENEGADE"},$m={[ve.SHERIFF]:"/images/roles/sheriff.png",[ve.DEPUTY]:"/images/roles/deputy.png",[ve.OUTLAW]:"/images/roles/outlaw.png",[ve.RENEGADE]:"/images/roles/renegade.png"},Km=[{name:"Bart Cassidy",life:4,ability:"Draws a card when he loses a life point",abilityType:"ON_DAMAGE_TAKEN"},{name:"Black Jack",life:4,ability:"Shows second card when drawing; draws again if heart/diamond",abilityType:"ON_DRAW"},{name:"Calamity Janet",life:4,ability:"Can play BANG! as Missed! and vice versa",abilityType:"CARD_SUBSTITUTION"},{name:"El Gringo",life:3,ability:"Draws from attacker when hit",abilityType:"ON_DAMAGE_TAKEN"},{name:"Jesse Jones",life:4,ability:"Can draw first card from another player",abilityType:"DRAW_CHOICE"},{name:"Jourdonnais",life:4,ability:"Has built-in Barrel effect",abilityType:"BUILT_IN_BARREL"},{name:"Kit Carlson",life:4,ability:"Sees top 3 cards when drawing, chooses 2",abilityType:"DRAW_CHOICE"},{name:"Lucky Duke",life:4,ability:"Flips 2 cards for checks, chooses one",abilityType:"LUCKY_DRAW"},{name:"Paul Regret",life:3,ability:"Has built-in Mustang effect",abilityType:"BUILT_IN_MUSTANG"},{name:"Pedro Ramirez",life:4,ability:"Can draw first card from discard pile",abilityType:"DRAW_CHOICE"},{name:"Rose Doolan",life:4,ability:"Has built-in Scope effect",abilityType:"BUILT_IN_SCOPE"},{name:"Sid Ketchum",life:4,ability:"Can discard 2 cards to regain 1 life point",abilityType:"ACTIVE_ABILITY"},{name:"Slab the Killer",life:4,ability:"Players need 2 Missed! to avoid his BANG!",abilityType:"ATTACK_MODIFIER"},{name:"Suzy Lafayette",life:4,ability:"Draws a card when she has no cards in hand",abilityType:"AUTO_DRAW"},{name:"Vulture Sam",life:4,ability:"Takes cards of eliminated players",abilityType:"ON_ELIMINATION"},{name:"Willy the Kid",life:4,ability:"Can play any number of BANG! cards",abilityType:"UNLIMITED_BANG"},{name:"Apache Kid",life:3,ability:"Unaffected by Diamond cards played by other players (except during Duels)",abilityType:"DIAMOND_IMMUNITY"},{name:"Belle Star",life:4,ability:"During her turn, no card in front of any other player has any effect",abilityType:"DISABLE_EQUIPMENT"},{name:"Bill Noface",life:4,ability:"Draws 1 card plus 1 for each injury (lost life point) during phase 1",abilityType:"INJURY_DRAW"},{name:"Chuck Wengam",life:4,ability:"Can lose 1 life to draw 2 cards (multiple times per turn, not last life)",abilityType:"LIFE_FOR_CARDS"},{name:"Doc Holyday",life:4,ability:"Once per turn, discard 2 cards for BANG! effect (doesn't count toward limit)",abilityType:"DISCARD_FOR_BANG"},{name:"Elena Fuente",life:3,ability:"Can use any card in her hand as a Missed!",abilityType:"ANY_AS_MISSED"},{name:"Greg Digger",life:4,ability:"Regains 2 life when another character is eliminated",abilityType:"HEAL_ON_ELIMINATION"},{name:"Herb Hunter",life:4,ability:"Draws 2 extra cards when another character is eliminated",abilityType:"DRAW_ON_ELIMINATION"},{name:"José Delgado",life:4,ability:"Can discard a blue card to draw 2 cards (twice per turn)",abilityType:"BLUE_FOR_CARDS"},{name:"Molly Stark",life:4,ability:"Draws 1 card when voluntarily discarding Missed!/Beer/BANG! on others' turns",abilityType:"VOLUNTARY_DISCARD_DRAW"},{name:"Pat Brennan",life:4,ability:"Can draw 1 card from in play instead of 2 from deck during phase 1",abilityType:"STEAL_OR_DRAW"},{name:"Pixie Pete",life:3,ability:"Draws 3 cards instead of 2 during phase 1",abilityType:"ENHANCED_DRAW"},{name:"Sean Mallory",life:3,ability:"Can hold up to 10 cards in hand during phase 3",abilityType:"EXTENDED_HAND_LIMIT"},{name:"Tequila Joe",life:4,ability:"Regains 2 life from Beer instead of 1 (only 1 from other healing cards)",abilityType:"ENHANCED_BEER"},{name:"Vera Custer",life:3,ability:"At turn start, copies another character's ability until next turn",abilityType:"COPY_ABILITY"}];function km(){var vl,_i,vu,Su;const[K,Te]=Q.useState("setup"),[Ae,O]=Q.useState(null),[v,Y]=Q.useState([]),[x,Ue]=Q.useState(0),[V,_]=Q.useState([]),[z,X]=Q.useState([]),[he,Pe]=Q.useState(4),[ut,N]=Q.useState("Welcome to BANG!"),[Qe,ca]=Q.useState(null),[Ea,Ze]=Q.useState({}),[Bt,ye]=Q.useState({}),[At,bt]=Q.useState([]),Je=(i,c=!1,s=3e3)=>{if(Qe&&(clearTimeout(Qe),ca(null)),N(i),c){const o=setTimeout(()=>{N(""),ca(null)},s);ca(o)}},Gt=Q.useCallback(()=>{Da(120),dl(!0)},[]),kt=Q.useCallback(()=>{dl(!1),Da(120)},[]),sa=i=>{const c=Math.floor(i/60),s=i%60;return`${c}:${s.toString().padStart(2,"0")}`},[He,za]=Q.useState(!0),[fa,et]=Q.useState(null),M=Q.useCallback(()=>{if(!fa&&He){const i=new(window.AudioContext||window.webkitAudioContext);return et(i),i}return fa},[fa,He]),q=Q.useCallback(i=>{if(!He)return;if(i==="dominating")try{const s=new Audio("/src/Dominating.mp3");s.volume=.7,s.play().catch(o=>{console.log("Could not play dominating sound:",o),q("killingSpree")});return}catch(s){console.log("Error creating dominating audio:",s),q("killingSpree");return}if(i==="unstoppable")try{const s=new Audio("/src/Unstoppable.mp3");s.volume=.8,s.play().catch(o=>{console.log("Could not play unstoppable sound:",o),q("victory")});return}catch(s){console.log("Error creating unstoppable audio:",s),q("victory");return}if(i==="godlike")try{const s=new Audio("/src/Godlike.mp3");s.volume=.7,s.play().catch(o=>{console.log("Could not play godlike sound:",o),q("damage")});return}catch(s){console.log("Error creating godlike audio:",s),q("damage");return}const c=M();if(c)try{const s=c.createOscillator(),o=c.createGain();switch(s.connect(o),o.connect(c.destination),i){case"turnStart":s.frequency.setValueAtTime(800,c.currentTime),s.frequency.setValueAtTime(600,c.currentTime+.1),o.gain.setValueAtTime(.3,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.8),s.start(c.currentTime),s.stop(c.currentTime+.8);break;case"damage":s.frequency.setValueAtTime(200,c.currentTime),s.frequency.exponentialRampToValueAtTime(100,c.currentTime+.3),o.gain.setValueAtTime(.4,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.3),s.start(c.currentTime),s.stop(c.currentTime+.3);break;case"cardPlay":s.frequency.setValueAtTime(400,c.currentTime),s.frequency.setValueAtTime(300,c.currentTime+.05),o.gain.setValueAtTime(.2,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.15),s.start(c.currentTime),s.stop(c.currentTime+.15);break;case"cardDraw":s.frequency.setValueAtTime(500,c.currentTime),s.frequency.setValueAtTime(450,c.currentTime+.1),o.gain.setValueAtTime(.15,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2);break;case"elimination":s.frequency.setValueAtTime(150,c.currentTime),s.frequency.exponentialRampToValueAtTime(50,c.currentTime+1),o.gain.setValueAtTime(.5,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+1),s.start(c.currentTime),s.stop(c.currentTime+1);break;case"defense":s.frequency.setValueAtTime(600,c.currentTime),s.frequency.setValueAtTime(800,c.currentTime+.1),o.gain.setValueAtTime(.25,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.4),s.start(c.currentTime),s.stop(c.currentTime+.4);break;case"heal":s.frequency.setValueAtTime(400,c.currentTime),s.frequency.setValueAtTime(600,c.currentTime+.2),s.frequency.setValueAtTime(800,c.currentTime+.4),o.gain.setValueAtTime(.2,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.6),s.start(c.currentTime),s.stop(c.currentTime+.6);break;case"killingSpree":{s.type="sawtooth",s.frequency.setValueAtTime(80,c.currentTime),s.frequency.exponentialRampToValueAtTime(40,c.currentTime+.3),o.gain.setValueAtTime(.6,c.currentTime),o.gain.exponentialRampToValueAtTime(.1,c.currentTime+.3),s.start(c.currentTime);const h=c.createOscillator(),m=c.createGain();h.connect(m),m.connect(c.destination),h.type="sawtooth",h.frequency.setValueAtTime(160,c.currentTime+.1),h.frequency.exponentialRampToValueAtTime(80,c.currentTime+.4),m.gain.setValueAtTime(.4,c.currentTime+.1),m.gain.exponentialRampToValueAtTime(.01,c.currentTime+.4),h.start(c.currentTime+.1),h.stop(c.currentTime+.4);const y=c.createOscillator(),p=c.createGain();y.connect(p),p.connect(c.destination),y.type="triangle",y.frequency.setValueAtTime(200,c.currentTime+.5),y.frequency.exponentialRampToValueAtTime(400,c.currentTime+1.2),p.gain.setValueAtTime(.5,c.currentTime+.5),p.gain.exponentialRampToValueAtTime(.01,c.currentTime+1.2),y.start(c.currentTime+.5),y.stop(c.currentTime+1.2);const H=c.createOscillator(),j=c.createGain();H.connect(j),j.connect(c.destination),H.type="square",H.frequency.setValueAtTime(300,c.currentTime+1),H.frequency.setValueAtTime(350,c.currentTime+1.5),j.gain.setValueAtTime(.3,c.currentTime+1),j.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),H.start(c.currentTime+1),H.stop(c.currentTime+2),s.stop(c.currentTime+.3);break}case"victory":{s.type="triangle",s.frequency.setValueAtTime(440,c.currentTime),s.frequency.setValueAtTime(554,c.currentTime+.3),s.frequency.setValueAtTime(659,c.currentTime+.6),s.frequency.setValueAtTime(880,c.currentTime+.9),o.gain.setValueAtTime(.5,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),s.start(c.currentTime);const h=c.createOscillator(),m=c.createGain();h.connect(m),m.connect(c.destination),h.type="triangle",h.frequency.setValueAtTime(330,c.currentTime),h.frequency.setValueAtTime(415,c.currentTime+.3),h.frequency.setValueAtTime(494,c.currentTime+.6),h.frequency.setValueAtTime(659,c.currentTime+.9),m.gain.setValueAtTime(.3,c.currentTime),m.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),h.start(c.currentTime),h.stop(c.currentTime+2),s.stop(c.currentTime+2);break}case"warning":s.frequency.setValueAtTime(1e3,c.currentTime),s.frequency.setValueAtTime(800,c.currentTime+.1),o.gain.setValueAtTime(.3,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2);break;default:s.frequency.setValueAtTime(440,c.currentTime),o.gain.setValueAtTime(.2,c.currentTime),o.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2)}}catch(s){console.log("Audio playback failed:",s)}},[He,M]),ee=(i,c,s,o)=>{const h=`${Date.now()}-${Math.random()}-${Xl+1}`;Bc(p=>p+1);const m={id:h,card:i,fromPlayerIndex:c,toPlayerIndex:s,phase:"toTarget",progress:0,startTime:Date.now()};sl(p=>[...p,m]);const y=()=>{const p=Date.now()-m.startTime,j=Math.min(p/800,1);sl(k=>k.map(I=>I.id===h?{...I,progress:j}:I)),j<1?requestAnimationFrame(y):setTimeout(()=>{m.phase="toDiscard",m.startTime=Date.now(),m.progress=0;const k=()=>{const I=Date.now()-m.startTime,re=Math.min(I/600,1);sl(xe=>xe.map(ft=>ft.id===h?{...ft,progress:re}:ft)),re<1?requestAnimationFrame(k):sl(xe=>xe.filter(ft=>ft.id!==h))};requestAnimationFrame(k)},300)};requestAnimationFrame(y)},ne=Q.useCallback((i,c,s=2e3)=>{const o=`${i}-${c}-${Date.now()}`;Ze(h=>({...h,[o]:{playerIndex:i,effectType:c,timestamp:Date.now()}})),setTimeout(()=>{Ze(h=>{const m={...h};return delete m[o],m})},s)},[Ze]),A=(i,c,s="damage")=>{const o=document.querySelector(`[data-player-index="${i}"]`);if(!o)return;const h=document.createElement("div");h.className=s==="damage"?"floating-damage":"floating-heal",h.textContent=s==="damage"?`-${c}`:`+${c}`,o.style.position="relative",o.appendChild(h),setTimeout(()=>{h.parentNode&&h.parentNode.removeChild(h)},2e3)},w=()=>{const i=document.querySelector(".game-board");i&&(i.classList.add("screen-shake"),setTimeout(()=>{i.classList.remove("screen-shake")},800))},[Z,$]=Q.useState(null),[P,fe]=Q.useState(!1),[ue,ht]=Q.useState(null),[me,Jt]=Q.useState(null),[iu,ll]=Q.useState(!1),[nl,xl]=Q.useState(null),[ci,La]=Q.useState(!1),[ra,Dt]=Q.useState(null),[Zf,cu]=Q.useState(null),[Rt,wl]=Q.useState(null),[_c,oa]=Q.useState(!1),[si,fi]=Q.useState(!1),[Yl,it]=Q.useState(!1),[Ut,mt]=Q.useState(null),[ri,oi]=Q.useState(null),[Mc,ul]=Q.useState(!1),[Ht,Ta]=Q.useState(null),[Vl,il]=Q.useState(null),[di,cl]=Q.useState(!1),[hi,sl]=Q.useState([]),[Xl,Bc]=Q.useState(0),[ja,fl]=Q.useState(!1),[We,qa]=Q.useState([]),[_e,yn]=Q.useState([]),[De,je]=Q.useState(0),[pe,tt]=Q.useState("draw"),[ct,da]=Q.useState(!1),[Ql,Zl]=Q.useState(!1),[pa,su]=Q.useState(0),Na=Q.useRef(-1),[ha,xa]=Q.useState(!1),[rl,qe]=Q.useState(!1),[Wt,gn]=Q.useState(0),[yt,ma]=Q.useState([]),[mi,ya]=Q.useState(0),[yi,gi]=Q.useState(new Set),[vn,$l]=Q.useState(!1),[Et,Sn]=Q.useState(null),[ba,Da]=Q.useState(120),[ol,dl]=Q.useState(!1),[Tt,An]=Q.useState(1),[st,Kl]=Q.useState([""]),vi=Q.useCallback(()=>{var c;dl(!1),N(`Time's up! ${(c=v[x])==null?void 0:c.name}'s turn is over.`);const i=v[x];if(i&&i.hand.length>i.health){const s=i.hand.length-i.health,o=[...v],h=[];for(let m=0;m<s;m++){const y=Math.floor(Math.random()*o[x].hand.length),p=o[x].hand.splice(y,1)[0];h.push(p)}Y(o),X(m=>[...m,...h]),N(`${i.name} auto-discarded ${s} cards due to time limit.`)}tt("timeUp")},[v,x,dl,N,X,Y,tt]);Q.useEffect(()=>{pe==="timeUp"&&setTimeout(()=>{let c=(x+1)%v.length;for(let s=0;s<v.length&&!v[c].isAlive;s++)c=(c+1)%v.length;Ue(c),tt("draw"),da(!1),ya(0),N(`${v[c].name}'s turn!`)},1500)},[pe,x,v,Ue,tt,da,ya,N]),Q.useEffect(()=>{let i=null;return ol&&ba>0&&K==="playing"?i=setInterval(()=>{Da(c=>c<=1?(vi(),0):c-1)},1e3):(!ol||ba<=0)&&clearInterval(i),()=>clearInterval(i)},[ol,K,ba,v,x,vi]);const kl=()=>{var m;if(he<4||he>7){N("Please select 4-7 players");return}let i=Si();const c=En(he),s=[...Km].sort(()=>Math.random()-.5),o=[];for(let y=0;y<he;y++){const p=s[y],H=c[y],j=H===ve.SHERIFF?p.life+1:p.life,k=Ge(i,j),I=k.drawnCards||[];i=k.updatedDeck;let W,re;y<Tt?(W=((m=st[y])==null?void 0:m.trim())||`Player ${y+1}`,re=!1):(W=`Bot ${y-Tt+1}`,re=!0),o.push({id:y,character:p,role:H,health:j,maxHealth:j,hand:Array.isArray(I)?I:[],inPlay:[],isAlive:!0,isBot:re,name:W})}const h=o.findIndex(y=>y.role===ve.SHERIFF);Ue(h),Y(o),_(i),Te("playing"),tt("draw"),da(!1),ya(0),N(`Game started! ${o[h].name} is the Sheriff and goes first. Cards will be drawn automatically.`)},Si=()=>{const i=[{type:r.BANG,suit:U.SPADES,value:"A"},{type:r.BANG,suit:U.CLUBS,value:"2"},{type:r.BANG,suit:U.CLUBS,value:"3"},{type:r.BANG,suit:U.CLUBS,value:"4"},{type:r.BANG,suit:U.CLUBS,value:"5"},{type:r.BANG,suit:U.CLUBS,value:"6"},{type:r.BANG,suit:U.CLUBS,value:"7"},{type:r.BANG,suit:U.CLUBS,value:"8"},{type:r.BANG,suit:U.CLUBS,value:"9"},{type:r.BANG,suit:U.DIAMONDS,value:"2"},{type:r.BANG,suit:U.DIAMONDS,value:"3"},{type:r.BANG,suit:U.DIAMONDS,value:"4"},{type:r.BANG,suit:U.DIAMONDS,value:"5"},{type:r.BANG,suit:U.DIAMONDS,value:"6"},{type:r.BANG,suit:U.DIAMONDS,value:"7"},{type:r.BANG,suit:U.DIAMONDS,value:"8"},{type:r.BANG,suit:U.DIAMONDS,value:"9"},{type:r.BANG,suit:U.HEARTS,value:"12"},{type:r.BANG,suit:U.HEARTS,value:"13"},{type:r.BANG,suit:U.HEARTS,value:"A"},{type:r.BANG,suit:U.SPADES,value:"K"},{type:r.BANG,suit:U.SPADES,value:"Q"},{type:r.BANG,suit:U.SPADES,value:"J"},{type:r.BANG,suit:U.SPADES,value:"10"},{type:r.BANG,suit:U.SPADES,value:"9"},{type:r.MISSED,suit:U.SPADES,value:"8"},{type:r.MISSED,suit:U.SPADES,value:"7"},{type:r.MISSED,suit:U.SPADES,value:"6"},{type:r.MISSED,suit:U.SPADES,value:"5"},{type:r.MISSED,suit:U.SPADES,value:"4"},{type:r.MISSED,suit:U.SPADES,value:"3"},{type:r.MISSED,suit:U.SPADES,value:"2"},{type:r.MISSED,suit:U.CLUBS,value:"A"},{type:r.MISSED,suit:U.CLUBS,value:"K"},{type:r.MISSED,suit:U.CLUBS,value:"Q"},{type:r.MISSED,suit:U.CLUBS,value:"J"},{type:r.MISSED,suit:U.CLUBS,value:"10"},{type:r.BEER,suit:U.HEARTS,value:"6"},{type:r.BEER,suit:U.HEARTS,value:"7"},{type:r.BEER,suit:U.HEARTS,value:"8"},{type:r.BEER,suit:U.HEARTS,value:"9"},{type:r.BEER,suit:U.HEARTS,value:"10"},{type:r.BEER,suit:U.HEARTS,value:"J"},{type:r.PANIC,suit:U.HEARTS,value:"4"},{type:r.PANIC,suit:U.DIAMONDS,value:"J"},{type:r.PANIC,suit:U.DIAMONDS,value:"Q"},{type:r.PANIC,suit:U.DIAMONDS,value:"A"},{type:r.CAT_BALOU,suit:U.DIAMONDS,value:"K"},{type:r.CAT_BALOU,suit:U.DIAMONDS,value:"10"},{type:r.CAT_BALOU,suit:U.HEARTS,value:"K"},{type:r.CAT_BALOU,suit:U.HEARTS,value:"Q"},{type:r.STAGECOACH,suit:U.SPADES,value:"9"},{type:r.STAGECOACH,suit:U.SPADES,value:"9"},{type:r.WELLS_FARGO,suit:U.HEARTS,value:"3"},{type:r.GATLING,suit:U.HEARTS,value:"10"},{type:r.INDIANS,suit:U.DIAMONDS,value:"K"},{type:r.INDIANS,suit:U.DIAMONDS,value:"A"},{type:r.DUEL,suit:U.CLUBS,value:"8"},{type:r.DUEL,suit:U.DIAMONDS,value:"J"},{type:r.DUEL,suit:U.SPADES,value:"J"},{type:r.GENERAL_STORE,suit:U.CLUBS,value:"9"},{type:r.GENERAL_STORE,suit:U.SPADES,value:"Q"},{type:r.SALOON,suit:U.HEARTS,value:"5"},{type:r.BARREL,suit:U.SPADES,value:"K"},{type:r.BARREL,suit:U.SPADES,value:"Q"},{type:r.SCOPE,suit:U.SPADES,value:"A"},{type:r.MUSTANG,suit:U.HEARTS,value:"8"},{type:r.MUSTANG,suit:U.HEARTS,value:"9"},{type:r.JAIL,suit:U.SPADES,value:"10"},{type:r.JAIL,suit:U.SPADES,value:"J"},{type:r.JAIL,suit:U.HEARTS,value:"4"},{type:r.DYNAMITE,suit:U.HEARTS,value:"2"},{type:r.VOLCANIC,suit:U.SPADES,value:"10"},{type:r.VOLCANIC,suit:U.CLUBS,value:"10"},{type:r.SCHOFIELD,suit:U.CLUBS,value:"K"},{type:r.SCHOFIELD,suit:U.CLUBS,value:"Q"},{type:r.SCHOFIELD,suit:U.SPADES,value:"K"},{type:r.REMINGTON,suit:U.CLUBS,value:"K"},{type:r.REV_CARABINE,suit:U.CLUBS,value:"A"},{type:r.WINCHESTER,suit:U.SPADES,value:"8"},{type:r.DODGE,suit:U.CLUBS,value:"A"},{type:r.DODGE,suit:U.SPADES,value:"A"},{type:r.PUNCH,suit:U.HEARTS,value:"A"},{type:r.PUNCH,suit:U.DIAMONDS,value:"A"},{type:r.BRAWL,suit:U.CLUBS,value:"J"},{type:r.RAG_TIME,suit:U.HEARTS,value:"Q"},{type:r.TEQUILA,suit:U.HEARTS,value:"4"},{type:r.WHISKY,suit:U.HEARTS,value:"3"},{type:r.BINOCULAR,suit:U.CLUBS,value:"Q"},{type:r.HIDEOUT,suit:U.SPADES,value:"Q"},{type:r.BIBLE,suit:U.HEARTS,value:"K"},{type:r.CAN_CAN,suit:U.CLUBS,value:"K"},{type:r.CANTEEN,suit:U.HEARTS,value:"8"},{type:r.CONESTOGA,suit:U.HEARTS,value:"9"},{type:r.IRON_PLATE,suit:U.SPADES,value:"K"},{type:r.PONY_EXPRESS,suit:U.SPADES,value:"J"},{type:r.SOMBRERO,suit:U.CLUBS,value:"9"},{type:r.TEN_GALLON_HAT,suit:U.HEARTS,value:"10"},{type:r.DERRINGER,suit:U.CLUBS,value:"4"},{type:r.PEPPERBOX,suit:U.DIAMONDS,value:"8"},{type:r.KNIFE,suit:U.SPADES,value:"9"},{type:r.SPRINGFIELD,suit:U.CLUBS,value:"8"},{type:r.BUFFALO_RIFLE,suit:U.SPADES,value:"A"},{type:r.HOWITZER,suit:U.HEARTS,value:"2"}];return i.forEach(c=>{[r.VOLCANIC,r.SCHOFIELD,r.REMINGTON,r.REV_CARABINE,r.WINCHESTER,r.PEPPERBOX,r.KNIFE,r.SPRINGFIELD,r.BUFFALO_RIFLE].includes(c.type)?(c.equipmentType=Kt.WEAPON,c.range=Xf[c.type]):[r.BARREL,r.MUSTANG,r.SOMBRERO].includes(c.type)?c.equipmentType=Kt.DEFENSIVE:[r.SCOPE,r.JAIL,r.DYNAMITE].includes(c.type)?c.equipmentType=Kt.SPECIAL:[r.BINOCULAR,r.HIDEOUT,r.BIBLE,r.CAN_CAN,r.CANTEEN,r.CONESTOGA,r.PONY_EXPRESS,r.DERRINGER,r.TEN_GALLON_HAT,r.HOWITZER].includes(c.type)&&(c.equipmentType=Kt.GREEN)}),i.sort(()=>Math.random()-.5)},En=i=>{const c=[];c.push(ve.SHERIFF);const s=i<=4?2:i<=6?3:4;for(let m=0;m<s;m++)c.push(ve.OUTLAW);const o=i<=6?1:2;for(let m=0;m<o;m++)c.push(ve.RENEGADE);const h=i<=4?0:i===5?1:2;for(let m=0;m<h;m++)c.push(ve.DEPUTY);return c.sort(()=>Math.random()-.5)},Ge=Q.useCallback((i,c)=>{let s=[...i],o=[...z];const h=[];for(let m=0;m<c;m++){if(s.length===0){if(o.length===0){console.warn("Cannot reshuffle: both deck and discard pile are empty!");break}const y=o[o.length-1],H=[...o.slice(0,-1)];for(let j=H.length-1;j>0;j--){const k=Math.floor(Math.random()*(j+1));[H[j],H[k]]=[H[k],H[j]]}s=H,o=[y],_(s),X(o),N("Deck reshuffled from discard pile!")}s.length>0&&(h.push(s[0]),s=s.slice(1))}return{drawnCards:h,updatedDeck:s,updatedDiscardPile:o}},[z,_,X,N]),$e=(i,c,s)=>{const o=s.filter(W=>W.isAlive).length,h=s.map((W,re)=>({...W,originalIndex:re})).filter(W=>W.isAlive),m=h.findIndex(W=>W.originalIndex===i),y=h.findIndex(W=>W.originalIndex===c);if(m===-1||y===-1)return 1/0;const p=(y-m+o)%o,H=(m-y+o)%o;let j=Math.min(p,H);const k=s[c];k&&k.character.name==="Paul Regret"&&(j+=1);const I=s[i];return I&&I.character.name==="Rose Doolan"&&(j=Math.max(1,j-1)),j},fu=i=>{const c=i.inPlay.find(h=>h.equipmentType===Kt.WEAPON),s=c?c.range:1,o=i.character.abilityType==="BUILT_IN_SCOPE"||i.inPlay.some(h=>h.type===r.SCOPE)?1:0;return s+o},Ai=i=>1+(i.character.abilityType==="BUILT_IN_MUSTANG"||i.inPlay.some(s=>s.type===r.MUSTANG)?1:0),ru=(i,c,s)=>{const o=s[i],h=s[c];if(!o.isAlive||!h.isAlive)return!1;const m=$e(i,c,s),y=fu(o),p=Ai(h);return m<=y&&m>=p},Ft=i=>{if(V.length===0)return!1;const c=V[0];if(_(V.slice(1)),X([...z,c]),i.character.name==="Lucky Duke"&&V.length>0){const s=V[0];_(V.slice(1)),X([...z,s]);const o=c.suit===U.HEARTS?c:s.suit===U.HEARTS?s:c;return N(`${i.character.name} flipped ${c.suit} ${c.value} and ${s.suit} ${s.value}, chose ${o.suit} ${o.value}`),o.suit===U.HEARTS}return N(`${i.character.name} flipped ${c.suit} ${c.value} for barrel check`),c.suit===U.HEARTS},Ra=i=>i.character.name==="Jourdonnais"||i.inPlay.some(c=>c.type===r.BARREL),hl=i=>i.character.name==="Willy the Kid"||i.inPlay.some(c=>c.type===r.VOLCANIC)?!0:mi===0,ga=i=>i.role===ve.SHERIFF,zt=(i,c)=>{if(!i.inPlay.some(h=>h.type===c))return!1;const o=`${v.findIndex(h=>h.id===i.id)}-${c}`;return!yi.has(o)},Lt=(i,c)=>{const s=[...v],o=s[i].inPlay.findIndex(h=>h.type===c);if(o>=0){const h=s[i].inPlay.splice(o,1)[0];return X(m=>[...m,h]),Y(s),!0}return!1},It=(i,c)=>{const s=i[c],o=i.find(y=>y.character.name==="Vulture Sam"&&y.isAlive);o&&o!==s&&(jt(o,"ON_ELIMINATION",{eliminatedPlayer:s}),i[c].hand=[],i[c].inPlay=[]);const h=i.find(y=>y.character.name==="Greg Digger"&&y.isAlive);h&&h!==s&&jt(h,"HEAL_ON_ELIMINATION",{eliminatedPlayer:s});const m=i.find(y=>y.character.name==="Herb Hunter"&&y.isAlive);m&&m!==s&&jt(m,"DRAW_ON_ELIMINATION",{eliminatedPlayer:s})},jt=(i,c,s={})=>{switch(c){case"ON_DAMAGE_TAKEN":if(i.character.name==="Bart Cassidy"){if(V.length>0){const o=V[0];_(V.slice(1)),Y(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,o]}:m)),N(`${i.name} (Bart Cassidy) draws a card from taking damage!`)}}else if(i.character.name==="El Gringo"){const{attackerIndex:o}=s;if(o!==void 0&&v[o]&&v[o].hand.length>0){const h=v[o],m=Math.floor(Math.random()*h.hand.length),y=h.hand[m];Y(p=>p.map((H,j)=>{if(j===o){const k=[...H.hand];return k.splice(m,1),{...H,hand:k}}else if(H.id===i.id)return{...H,hand:[...H.hand,y]};return H})),N(`${i.name} (El Gringo) draws a card from ${h.name}!`)}}break;case"AUTO_DRAW":if(i.character.name==="Suzy Lafayette"&&i.hand.length===0&&V.length>0){const o=V[0];_(V.slice(1)),Y(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,o]}:m)),N(`${i.name} (Suzy Lafayette) draws a card for having no cards!`)}break;case"ON_ELIMINATION":if(i.character.name==="Vulture Sam"){const{eliminatedPlayer:o}=s;o&&o.hand.length>0&&(Y(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,...o.hand]}:m)),N(`${i.name} (Vulture Sam) takes all cards from eliminated ${o.name}!`))}break;case"HEAL_ON_ELIMINATION":if(i.character.name==="Greg Digger"){const{eliminatedPlayer:o}=s;o&&o.id!==i.id&&(Y(h=>h.map(m=>m.id===i.id?{...m,health:Math.min(m.health+2,m.character.life)}:m)),N(`${i.name} (Greg Digger) regained 2 life from elimination!`))}break;case"DRAW_ON_ELIMINATION":if(i.character.name==="Herb Hunter"){const{eliminatedPlayer:o}=s;if(o&&o.id!==i.id&&V.length>=2){const h=Ge(V,2);Y(m=>m.map(y=>y.id===i.id?{...y,hand:[...y.hand,...h.drawnCards]}:y)),_(h.updatedDeck),N(`${i.name} (Herb Hunter) drew 2 cards from elimination!`)}}break}},pt=i=>{Jt(i),ll(!0)},ml=i=>{const c={type:i.name,suit:"",value:"",isCharacter:!0,character:i};Jt(c),ll(!0)},Tn=()=>{ll(!1),Jt(null)},pn=()=>{oa(!1),Te("setup"),Y([]),Ue(0),_([]),X([]),tt("draw"),da(!1),ya(0),An(1),Kl([""]),N("Welcome to BANG!"),fi(!1)},ou=()=>{oa(!1),fi(!0),N("You are now spectating the game. You can watch until the game ends.")},qt=i=>{const c=setTimeout(()=>{La(!0),pt(i)},500);xl(c)},xt=()=>{nl&&(clearTimeout(nl),xl(null)),La(!1)},wa=()=>{nl&&(clearTimeout(nl),xl(null)),La(!1)};Q.useCallback(i=>[r.BANG,r.PANIC,r.CAT_BALOU,r.DUEL,r.JAIL,r.PUNCH,r.TEQUILA].includes(i.type),[]);const du=Q.useCallback((i,c,s)=>{i.preventDefault(),ra&&clearTimeout(ra),cu({card:c,cardIndex:s}),La(!1);const o=i.currentTarget,h=setTimeout(()=>{La(!0),wl(c),o.classList.add("long-pressing"),navigator.vibrate&&navigator.vibrate(50)},800);Dt(h)},[ra]),Ei=Q.useCallback(i=>{i.preventDefault(),i.currentTarget.classList.remove("long-pressing"),ra&&(clearTimeout(ra),Dt(null)),La(!1),cu(null)},[ra]),Jl=Q.useCallback(i=>{ra&&(clearTimeout(ra),Dt(null)),i.currentTarget.classList.remove("long-pressing"),La(!1)},[ra]),Pt=(i,c,s=500)=>{ye(o=>({...o,[i]:{type:c,timestamp:Date.now()}})),setTimeout(()=>{ye(o=>{const h={...o};return delete h[i],h})},s)},Nn=()=>{Pt(`draw-${Date.now()}`,"cardDraw",400)},yl=i=>({[r.BANG]:"Deal 1 damage to a player within range. Target must play a Missed! card or lose 1 life point.",[r.MISSED]:"Play in response to a BANG! card to avoid taking damage.",[r.BEER]:"Regain 1 life point (up to your maximum). Cannot be played if you're already at maximum health. Requires 3+ players alive.",[r.SALOON]:"All players regain 1 life point (up to their maximum).",[r.PANIC]:"Draw a random card from a player at distance 1.",[r.CAT_BALOU]:"Force a player to discard a random card from their hand or remove a card from play.",[r.DUEL]:"Challenge another player to a duel. You and target alternate playing BANG! cards. First player who cannot play a BANG! loses 1 life point.",[r.JAIL]:"Place on another player. They must draw a Heart to escape or skip their turn. Cannot target the Sheriff or players already in jail.",[r.DYNAMITE]:"Place in front of you. Each turn, draw a card - if it's Spades 2-9, take 3 damage!",[r.BARREL]:"When targeted by BANG!, draw a card. If it's a Heart, the BANG! misses.",[r.MUSTANG]:"Increases distance from you to other players by 1.",[r.SCOPE]:"Decreases distance from other players to you by 1.",[r.VOLCANIC]:"Allows you to play unlimited BANG! cards per turn.",[r.SCHOFIELD]:"Range 2 weapon. Can target players at distance 1-2.",[r.REMINGTON]:"Range 3 weapon. Can target players at distance 1-3.",[r.REV_CARABINE]:"Range 4 weapon. Can target players at distance 1-4.",[r.WINCHESTER]:"Range 5 weapon. Can target players at distance 1-5.",[r.STAGECOACH]:"Draw 2 cards from the deck.",[r.WELLS_FARGO]:"Draw 3 cards from the deck.",[r.GENERAL_STORE]:"All players draw 1 card from a shared selection.",[r.INDIANS]:"All other players must play a BANG! card or lose 1 life point.",[r.GATLING]:"Deal 1 damage to all other players (they can play Missed! to avoid).",[r.DODGE]:"Play in response to a BANG! card to avoid taking damage (like Missed!) and draw 1 card.",[r.PUNCH]:"Deal 1 damage to a player at distance 1 (like BANG! but only range 1).",[r.BRAWL]:"Discard another card + Brawl: Force all other players to discard a card from hand or play.",[r.RAG_TIME]:"All players draw 1 card, then discard 1 card.",[r.TEQUILA]:"Discard another card + Tequila: Choose any player to regain 1 life point.",[r.WHISKY]:"Regain 2 life points (enhanced Beer effect).",[r.BINOCULAR]:"GREEN: Discard to look at top 3 cards of deck, put them back in any order.",[r.HIDEOUT]:"GREEN: Discard when targeted by BANG! - draw a card, if Spade the BANG! misses.",[r.BIBLE]:"GREEN: Discard to avoid BANG! (like Missed!) and draw 1 card.",[r.CAN_CAN]:"GREEN: Discard when playing Missed! to draw 1 card.",[r.CANTEEN]:"GREEN: Discard when playing Beer to regain 2 life instead of 1.",[r.CONESTOGA]:"GREEN: Discard during hand limit phase to discard 1 less card.",[r.IRON_PLATE]:"GREEN: Discard to avoid BANG! (like Missed!).",[r.PONY_EXPRESS]:"GREEN: Discard at start of turn to draw 1 extra card.",[r.SOMBRERO]:"GREEN: Discard to avoid BANG! (like Missed!).",[r.TEN_GALLON_HAT]:"GREEN: Discard to avoid BANG! (like Missed!).",[r.DERRINGER]:"GREEN: Discard to play as BANG! at range 1 and draw 1 card.",[r.PEPPERBOX]:"Range 1 weapon. Can play unlimited BANG! cards per turn.",[r.KNIFE]:"Range 1 weapon. BANG! cards played cannot be avoided with Missed!",[r.SPRINGFIELD]:"Range 2 weapon. Can target players at distance 1-2.",[r.BUFFALO_RIFLE]:"Range 5 weapon. Can target players at distance 1-5.",[r.HOWITZER]:"GREEN: Discard to play as BANG! against all other players. Doesn't count toward BANG! limit."})[i]||"No description available.",Ct=Q.useCallback(()=>{if(Na.current===pa)return;if(pe!=="draw"||ct||Ql){Ql||N("You have already drawn cards this turn!");return}Na.current=pa,Zl(!0);const i=v[x],c=[...v];let s=[...V],o=0;const h=i.inPlay.findIndex(y=>y.type===r.JAIL);if(h>=0&&s.length>0){let y=!1;if(i.character.name==="Lucky Duke"){if(s.length>=2){const p=s[0],H=s[1];s=s.slice(2),X([...z,p,H]);const j=p.suit===U.HEARTS?p:H.suit===U.HEARTS?H:p;y=j.suit===U.HEARTS,N(`${i.character.name} (Lucky Duke) drew 2 cards for jail escape and chose ${j.suit} ${j.value}`)}}else{const p=s[0];s=s.slice(1),X([...z,p]),y=p.suit===U.HEARTS,N(`${i.character.name} flipped ${p.suit} ${p.value} for jail escape`)}if(y){const p=c[x].inPlay.splice(h,1)[0];X([...z,p]),N(`${i.character.name} escaped from jail!`)}else{N(`${i.character.name} remains in jail and skips their turn.`),Y(c),_(s),Mn();return}}const m=i.inPlay.findIndex(y=>y.type===r.DYNAMITE);if(m>=0&&s.length>0){const y=s[0];if(s=s.slice(1),X([...z,y]),y.suit===U.SPADES&&["2","3","4","5","6","7","8","9"].includes(y.value)){const p=c[x].inPlay.splice(m,1)[0];if(X([...z,p]),c[x].health-=3,c[x].health<=0){c[x].isAlive=!1,It(c,x),N(`${i.character.name} (${i.role}) was killed by dynamite!`),q("dominating"),x===0&&!c[0].isBot&&oa(!0),Y(c),_(s),Ca(c);return}else q("godlike"),N(`${i.character.name} was hurt by dynamite! Health: ${c[x].health}`)}else{const p=c[x].inPlay.splice(m,1)[0];let H=(x+1)%v.length;for(;!c[H].isAlive;)H=(H+1)%v.length;c[H].inPlay.push(p),N(`${i.character.name} passed dynamite to ${c[H].character.name}`)}}if(i.character.abilityType==="DRAW_CHOICE"){if(i.character.name==="Jesse Jones"){const y=Ge(s,2);c[x].hand.push(...y.drawnCards),s=y.updatedDeck,o=y.drawnCards.length}else if(i.character.name==="Kit Carlson"){const y=Ge(s,3);y.drawnCards.length>=3?(c[x].hand.push(...y.drawnCards.slice(0,2)),X([...z,y.drawnCards[2]]),o=2):(c[x].hand.push(...y.drawnCards),o=y.drawnCards.length),s=y.updatedDeck}else if(i.character.name==="Pedro Ramirez")if(z.length>0){const y=z[z.length-1];c[x].hand.push(y),X(z.slice(0,-1)),o=1;const p=Ge(s,1);p.drawnCards.length>0&&(c[x].hand.push(...p.drawnCards),s=p.updatedDeck,o=2)}else{const y=Ge(s,2);c[x].hand.push(...y.drawnCards),s=y.updatedDeck,o=y.drawnCards.length}}else if(i.character.abilityType==="ON_DRAW"){const y=Ge(s,2);if(c[x].hand.push(...y.drawnCards),s=y.updatedDeck,o=y.drawnCards.length,y.drawnCards.length>=2&&(y.drawnCards[1].suit===U.HEARTS||y.drawnCards[1].suit===U.DIAMONDS)){const p=Ge(s,1);p.drawnCards.length>0&&(c[x].hand.push(...p.drawnCards),s=p.updatedDeck,o=y.drawnCards.length+p.drawnCards.length,N(`${i.character.name} drew an extra card (Black Jack ability)!`))}}else if(i.character.abilityType==="INJURY_DRAW"){const y=i.character.life-i.health,p=1+y,H=Ge(s,p);c[x].hand.push(...H.drawnCards),s=H.updatedDeck,o=H.drawnCards.length,N(`${i.character.name} drew ${p} cards (1 + ${y} injuries)!`)}else if(i.character.abilityType==="ENHANCED_DRAW"){const y=Ge(s,3);c[x].hand.push(...y.drawnCards),s=y.updatedDeck,o=y.drawnCards.length,N(`${i.character.name} drew 3 cards!`)}else{const y=Ge(s,2);c[x].hand.push(...y.drawnCards),s=y.updatedDeck,o=y.drawnCards.length}if(i.character.name==="Suzy Lafayette"&&c[x].hand.length===0){const y=Ge(s,1);y.drawnCards.length>0&&(c[x].hand.push(...y.drawnCards),s=y.updatedDeck,N(`${i.character.name} drew a card (Suzy Lafayette ability)!`))}q("cardDraw"),ne(x,"card-draw-effect",1e3),Nn(),Y(c),_(s),da(!0),tt("play"),N(`${i.name} drew ${o} cards. Now play cards or end turn.`),Zl(!1)},[pe,ct,Ql,v,x,V,z,Y,_,da,tt,N,X,q,ne,A,Nn,Ge,Zl]),Ya=(i,c,s=null)=>{if(i!==x){N("It's not your turn!");return}if(pe!=="play"){N(pe==="draw"?"You must draw cards first!":"You can only play cards during the play phase!");return}const o=v[i],h=o.hand[c];if(q("cardPlay"),Pt(`play-${i}-${c}`,"cardPlay",600),o.character.name==="Calamity Janet")if(h.type===r.MISSED&&s!==null){Va(i,c,s,!0);return}else h.type,r.BANG;switch(h.type){case r.BANG:if(s===null){N("Select a target for BANG!");return}Va(i,c,s);break;case r.MISSED:o.character.name==="Calamity Janet"&&s!==null?Va(i,c,s,!0):N("Missed! cards can only be played defensively!");break;case r.BEER:Ti(i,c);break;case r.PANIC:if(s===null){N("Select a target for Panic!");return}bn(i,c,s);break;case r.CAT_BALOU:if(s===null){N("Select a target for Cat Balou!");return}Dn(i,c,s);break;case r.STAGECOACH:pi(i,c);break;case r.WELLS_FARGO:gt(i,c);break;case r.GATLING:Xa(i,c);break;case r.INDIANS:Rn(i,c);break;case r.DUEL:if(s===null){N("Select a target for Duel!");return}Wl(i,c,s);break;case r.GENERAL_STORE:hu(i,c);break;case r.SALOON:On(i,c);break;case r.BARREL:case r.SCOPE:case r.MUSTANG:case r.VOLCANIC:case r.SCHOFIELD:case r.REMINGTON:case r.REV_CARABINE:case r.WINCHESTER:_n(i,c);break;case r.JAIL:if(s===null){N("Select a target for Jail!");return}Gc(i,c,s);break;case r.DYNAMITE:Uc(i,c);break;case r.DODGE:_n(i,c);break;case r.PUNCH:if(s===null){N("Select a target for Punch!");return}Hc(i,c,s);break;case r.BRAWL:mu(i,c);break;case r.RAG_TIME:qc(i,c);break;case r.TEQUILA:if(s===null){N("Select a target for Tequila!");return}Di(i,c,s);break;case r.WHISKY:xc(i,c);break;case r.BINOCULAR:case r.HIDEOUT:case r.BIBLE:case r.CAN_CAN:case r.CANTEEN:case r.CONESTOGA:case r.IRON_PLATE:case r.PONY_EXPRESS:case r.SOMBRERO:case r.TEN_GALLON_HAT:case r.DERRINGER:case r.PEPPERBOX:case r.KNIFE:case r.SPRINGFIELD:case r.BUFFALO_RIFLE:_n(i,c);break;default:N(`Card ${h.type} not implemented yet`)}},Va=(i,c,s,o=!1)=>{const h=v[i],m=v[s],y=h.hand[c];if(m.character.name==="Apache Kid"&&y.suit===U.DIAMONDS){N(`${y.type} (Diamond) cannot affect Apache Kid!`);return}if(!o&&!hl(h)){N(`${h.name} has already played a BANG! card this turn!`);return}if(!ru(i,s,v)){N("Target is out of range!");return}const p=[...v],H=p[i].hand[c];ee(H,i,s),p[i].hand.splice(c,1),X(I=>[...I,H]),o||ya(mi+1);const j=o?"Missed! (as BANG!)":"BANG!";N(`${h.name} played ${j} on ${m.name}!`);let k=!1;if(Ra(m)&&!m.isBot){oi({type:"BANG",attackerIndex:i,targetIndex:s,cardIndex:c,isSubstitution:o}),mt({type:"BARREL_DEFENSE",playerIndex:s,message:`${m.name}, do you want to use your Barrel defense?`}),it(!0);return}else Ra(m)&&m.isBot&&Ft(m)&&(k=!0,N(`${m.character.name} defended with Barrel!`));if(!k){const I=h.character.name==="Slab the Killer"?2:1;let W=0;if(zt(p[s],r.BIBLE)&&W<I&&Lt(s,r.BIBLE)){W++;const re=Ge(V,1);re.drawnCards.length>0&&(p[s].hand.push(...re.drawnCards),_(re.updatedDeck)),N(`${m.name} used Bible to defend and drew 1 card!`)}if(zt(p[s],r.HIDEOUT)&&W<I&&Lt(s,r.HIDEOUT)){const re=Ge(V,1);if(re.drawnCards.length>0){const xe=re.drawnCards[0];p[s].hand.push(xe),_(re.updatedDeck),xe.suit===U.SPADES?(W++,N(`${m.name} used Hideout, drew ${xe.type} of Spades - BANG! missed!`)):N(`${m.name} used Hideout, drew ${xe.type} of ${xe.suit} - BANG! still hits!`)}}zt(p[s],r.SOMBRERO)&&W<I&&Lt(s,r.SOMBRERO)&&(W++,N(`${m.name} used Sombrero to defend against BANG!!`)),zt(p[s],r.IRON_PLATE)&&W<I&&Lt(s,r.IRON_PLATE)&&(W++,N(`${m.name} used Iron Plate to defend against BANG!!`)),zt(p[s],r.TEN_GALLON_HAT)&&W<I&&Lt(s,r.TEN_GALLON_HAT)&&(W++,N(`${m.name} used Ten Gallon Hat to defend against BANG!!`));for(let re=W;re<I;re++){const xe=p[s].hand.findIndex(ft=>ft.type===r.MISSED||ft.type===r.DODGE||p[s].character.name==="Calamity Janet"&&ft.type===r.BANG);if(xe>=0){const ft=p[s].hand.splice(xe,1)[0];if(X([...z,ft]),W++,ft.type===r.DODGE){const Il=Ge(V,1);Il.drawnCards.length>0&&(p[s].hand.push(...Il.drawnCards),_(Il.updatedDeck)),N(`${m.name} used Dodge to defend and drew 1 card!`)}else p[s].character.name==="Calamity Janet"&&ft.type===r.BANG&&N(`${m.name} (Calamity Janet) used BANG! as Missed!`)}else break}W>=I&&(k=!0,N(`${m.character.name} defended with ${W} defensive card(s)`))}k?Ra(m)?ne(s,"barrel-defense-effect",1e3):ne(s,"dodge-effect",800):(p[s].health-=1,ne(s,"damage-effect",1500),A(s,1,"damage"),jt(p[s],"ON_DAMAGE_TAKEN",{attackerIndex:i}),p[s].health<=0?(p[s].isAlive=!1,ne(s,"elimination-effect",2e3),w(),q("dominating"),It(p,s),N(`${m.character.name} (${m.role}) was eliminated!`),s===0&&!p[0].isBot&&oa(!0),Ca(p)):(q("godlike"),N(`${m.character.name} lost a life point! Health: ${p[s].health}`))),Y(p)},Ti=(i,c)=>{const s=v[i];if(v.filter(j=>j.isAlive).length<3){Je("Beer can only be used when there are 3 or more players alive!",!0,4e3);return}if(s.health>=s.maxHealth){Je(`${s.name} is already at maximum health! Cannot play Beer.`,!0,4e3);return}const h=[...v],m=h[i].hand.splice(c,1)[0];X([...z,m]);const y=s.character.name==="Tequila Joe"?2:1,p=Math.min(y,s.character.life-s.health);h[i].health+=p,q("heal"),ne(i,"healing-effect",2e3),A(i,p,"heal");const H=s.character.name==="Tequila Joe"?`${s.name} (Tequila Joe) gained ${p} life points from Beer! Health: ${h[i].health}`:`${s.name} gained ${p} life point! Health: ${h[i].health}`;N(H),Y(h)},bn=(i,c,s)=>{const o=v[i],h=v[s];if($e(i,s,v)!==1){N("Panic can only target players at distance 1!");return}if(h.hand.length===0&&h.inPlay.length===0){N("Target has no cards to steal!");return}const m=[...v],y=m[i].hand.splice(c,1)[0];X([...z,y]);const p=[...h.hand,...h.inPlay],H=Math.floor(Math.random()*p.length),j=p[H];H<h.hand.length?m[s].hand.splice(H,1):m[s].inPlay.splice(H-h.hand.length,1),m[i].hand.push(j),N(`${o.character.name} stole ${j.type} from ${h.character.name}!`),Y(m)},Dn=(i,c,s)=>{const o=v[i],h=v[s],m=h.inPlay.filter(I=>I.type!==r.JAIL),y=[...h.hand,...m];if(y.length===0){N("Target has no cards that can be discarded!");return}const p=[...v],H=p[i].hand.splice(c,1)[0];X([...z,H]);const j=Math.floor(Math.random()*y.length),k=y[j];if(j<h.hand.length){const I=h.hand.findIndex(W=>W===k);p[s].hand.splice(I,1)}else{const I=h.inPlay.findIndex(W=>W===k);p[s].inPlay.splice(I,1)}X([...z,k]),N(`${o.character.name} forced ${h.character.name} to discard ${k.type}!`),Y(p)},pi=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]);const m=Ge(V,2);o[i].hand.push(...m.drawnCards),_(m.updatedDeck),N(`${s.character.name} drew ${m.drawnCards.length} cards with Stagecoach!`),Y(o)},gt=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]);const m=Ge(V,3);o[i].hand.push(...m.drawnCards),_(m.updatedDeck),N(`${s.character.name} drew ${m.drawnCards.length} cards with Wells Fargo!`),Y(o)},Xa=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]);for(let m=0;m<o.length;m++)if(m!==i&&o[m].isAlive){const y=o[m];let p=!1;if(Ra(y)&&Ft(y)&&(p=!0,N(`${y.character.name} defended against Gatling with Barrel!`)),!p){const H=y.hand.findIndex(j=>j.type===r.MISSED||y.character.abilityType==="CARD_SUBSTITUTION"&&j.type===r.BANG);if(H>=0){const j=o[m].hand.splice(H,1)[0];X([...z,j]),p=!0}}p||(o[m].health-=1,o[m].health<=0?(o[m].isAlive=!1,It(o,m),q("dominating"),N(`${y.character.name} (${y.role}) was eliminated by Gatling!`)):q("godlike"))}N(`${s.character.name} played Gatling!`),Y(o),Ca(o)},Rn=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]);for(let m=0;m<o.length;m++)if(m!==i&&o[m].isAlive){const y=o[m],p=y.hand.findIndex(H=>H.type===r.BANG||y.character.name==="Calamity Janet"&&H.type===r.MISSED);if(p>=0){const H=o[m].hand.splice(p,1)[0];X([...z,H])}else o[m].health-=1,o[m].health<=0?(o[m].isAlive=!1,It(o,m),q("dominating"),N(`${y.character.name} (${y.role}) was eliminated by Indians!`)):q("godlike")}N(`${s.character.name} played Indians!`),Y(o),Ca(o)},Wl=(i,c,s)=>{const o=v[i],h=v[s],m=[...v],y=m[i].hand.splice(c,1)[0];X([...z,y]),N(`${o.character.name} challenged ${h.character.name} to a duel!`),Ni(i,s,!0,m)},Ni=(i,c,s,o)=>{const h=s?i:c,m=o[h],y=s?c:i,p=m.hand.findIndex(H=>H.type===r.BANG||m.character.name==="Calamity Janet"&&H.type===r.MISSED);if(p>=0){const H=o[h].hand.splice(p,1)[0];X(I=>[...I,H]);const j=H.type===r.MISSED?"Missed! (as BANG!)":"BANG!";N(`${m.character.name} played ${j} in the duel!`),Y([...o]);const k=m.isBot?1e3:1500;setTimeout(()=>{Ni(i,c,!s,o)},k)}else o[h].health-=1,jt(o[h],"ON_DAMAGE_TAKEN",{attackerIndex:y}),o[h].health<=0?(o[h].isAlive=!1,q("dominating"),It(o,h),N(`${m.character.name} (${m.role}) was eliminated in the duel!`),Ca(o)):(q("godlike"),N(`${m.character.name} lost the duel and 1 life point! Health: ${o[h].health}`)),Y(o)},hu=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]);const m=o.filter(j=>j.isAlive),y=Ge(V,m.length),p=y.drawnCards;_(y.updatedDeck);const H=[];for(let j=0;j<v.length;j++){const k=(i+j)%v.length;v[k].isAlive&&H.push(k)}qa(p),yn(H),je(0),fl(!0),Y(o),N(`${s.character.name} played General Store! ${m.length} cards revealed. Starting with ${s.character.name}, each player chooses one card clockwise.`)},Cn=i=>{if(!We[i])return;const c=_e[De],s=We[i],o=v[c];if(c!==_e[De])return;const h=We.filter((p,H)=>H!==i);qa(h);const m=[...v];m[c].hand.push(s),Y(m),N(`${o.character.name} chose ${s.type}!`);const y=De+1;je(y),y>=_e.length||h.length===0?setTimeout(()=>{fl(!1),qa([]),yn([]),je(0),h.length>0?(X(p=>[...p,...h]),N(`General Store complete! All players chose their cards. ${h.length} card(s) discarded.`)):N("General Store complete! All cards were chosen by players.")},1e3):setTimeout(()=>{const p=_e[y],H=v[p].character.name;N(`${H}'s turn to choose from General Store (${h.length} cards left).`)},800)},gl=()=>{if(!ja||We.length===0||_e.length===0||De>=_e.length)return;const i=_e[De],c=v[i];if(!c||!c.isBot)return;let s=0,o=Oa(We[0],c,[]);for(let h=1;h<We.length;h++){const m=Oa(We[h],c,[]);m>o&&(o=m,s=h)}setTimeout(()=>{Cn(s)},300)},On=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]),o.forEach(m=>{m.isAlive&&m.health<m.maxHealth&&(m.health+=1)}),N(`${s.name} played Saloon! All players regained 1 life point.`),Y(o)},_n=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];if(o[i].inPlay.findIndex(y=>y.type===h.type)>=0){o[i].hand.push(h),Y(o),N(`${s.name} already has ${h.type} equipped! Cannot have duplicate equipment.`);return}if(h.equipmentType===Kt.WEAPON){const y=o[i].inPlay.findIndex(p=>p.equipmentType===Kt.WEAPON);if(y>=0){const p=o[i].inPlay.splice(y,1)[0];X([...z,p]),N(`${s.name} replaced ${p.type} with ${h.type}!`)}else N(`${s.name} equipped ${h.type}!`)}else h.equipmentType===Kt.GREEN?N(`${s.name} equipped ${h.type}! (Can be used starting next turn)`):N(`${s.name} equipped ${h.type}!`);h.equipmentType===Kt.GREEN&&gi(y=>new Set([...y,`${i}-${h.type}`])),o[i].inPlay.push(h),Y(o)},Gc=(i,c,s)=>{const o=v[i],h=v[s];if(h.role===ve.SHERIFF){Je("Cannot jail the Sheriff!",!0,4e3);return}if(h.inPlay.some(H=>H.type===r.JAIL)){Je(`${h.character.name} is already in jail! Cannot jail the same player twice.`,!0,4e3);return}const y=[...v],p=y[i].hand.splice(c,1)[0];y[s].inPlay.push(p),ne(s,"jail-effect",500),N(`${o.character.name} put ${h.character.name} in jail!`),Y(y)},Uc=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];o[i].inPlay.push(h),N(`${s.character.name} placed Dynamite!`),Y(o)},Hc=(i,c,s)=>{if(v[i],v[s],$e(i,s,v)!==1){N("Punch can only target players at distance 1!");return}Va(i,c,s)},mu=(i,c)=>{const s=v[i];if(s.hand.length<2){N("You need at least 2 cards to play Brawl (Brawl + another card)!");return}if(s.isBot){const o=[...v],h=o[i].hand.splice(c,1)[0],m=o[i].hand.splice(0,1)[0];X([...z,h,m]);let y=0;o.forEach((p,H)=>{if(H!==i&&p.isAlive){if(p.hand.length>0){const j=p.hand.splice(0,1)[0];X(k=>[...k,j]),y++}else if(p.inPlay.length>0){const j=p.inPlay.splice(0,1)[0];X(k=>[...k,j]),y++}}}),N(`${s.character.name} played Brawl! All other players discarded ${y} cards.`),Y(o)}else{const o=[...v],h=o[i].hand.splice(c,1)[0];Y(o),X([...z,h]),Sn({type:"BRAWL_ADDITIONAL_DISCARD",playerIndex:i,brawlCard:h,message:"Choose an additional card to discard for Brawl:"}),$l(!0)}},bi=(i,c,s)=>{const o=v[i],h=v[c];if(s===r.DERRINGER&&$e(i,c,v)!==1){N("Derringer can only target players at range 1!");return}const m=[...v];N(`${o.name} used ${s} on ${h.name}!`);let y=!1;if(Ra(h)&&h.isBot&&Ft(h)&&(y=!0,N(`${h.character.name} defended with Barrel!`)),!y){const p=o.character.name==="Slab the Killer"?2:1;let H=0;if(zt(m[c],r.BIBLE)&&H<p&&Lt(c,r.BIBLE)){H++;const j=Ge(V,1);j.drawnCards.length>0&&(m[c].hand.push(...j.drawnCards),_(j.updatedDeck)),N(`${h.name} used Bible to defend and drew 1 card!`)}zt(m[c],r.SOMBRERO)&&H<p&&Lt(c,r.SOMBRERO)&&(H++,N(`${h.name} used Sombrero to defend!`));for(let j=H;j<p;j++){const k=m[c].hand.findIndex(I=>I.type===r.MISSED||I.type===r.DODGE||m[c].character.name==="Calamity Janet"&&I.type===r.BANG);if(k>=0){const I=m[c].hand.splice(k,1)[0];if(X(W=>[...W,I]),H++,I.type===r.DODGE){const W=Ge(V,1);W.drawnCards.length>0&&(m[c].hand.push(...W.drawnCards),_(W.updatedDeck)),N(`${h.name} used Dodge to defend and drew 1 card!`)}}else break}H>=p&&(y=!0,N(`${h.character.name} defended with ${H} defensive card(s)`))}y||(m[c].health-=1,jt(m[c],"ON_DAMAGE_TAKEN",{attackerIndex:i}),m[c].health<=0?(m[c].isAlive=!1,It(m,c),q("dominating"),N(`${h.character.name} was eliminated by ${s}!`),c===0&&!m[0].isBot&&oa(!0),Ca(m)):(q("godlike"),N(`${h.character.name} lost a life point! Health: ${m[c].health}`))),Y(m)},zc=(i,c,s)=>{const o=v[i];if(x!==i){N("You can only activate green cards on your turn!");return}if(!zt(o,c)){N("This green card cannot be used right now!");return}switch(c){case r.DERRINGER:{if($e(i,s,v)!==1){N("Derringer can only target players at range 1!");return}Lt(i,c),bi(i,s,c);const h=Ge(V,1);if(h.drawnCards.length>0){const m=[...v];m[i].hand.push(...h.drawnCards),Y(m),_(h.updatedDeck),N(`${o.name} used Derringer and drew 1 card!`)}break}default:N(`${c} targeting not implemented yet!`);break}},Lc=(i,c)=>{const s=v[i];if(x!==i){N("You can only activate green cards on your turn!");return}if(!zt(s,c)){N("This green card cannot be used right now!");return}switch(c){case r.DERRINGER:{const o=v.map((h,m)=>({player:h,index:m})).filter(({player:h,index:m})=>h.isAlive&&m!==i&&$e(i,m,v)===1);if(o.length===0){N("No targets at range 1 for Derringer!");return}if(o.length===1){Lt(i,c),bi(i,o[0].index,c);const h=Ge(V,1);if(h.drawnCards.length>0){const m=[...v];m[i].hand.push(...h.drawnCards),Y(m),_(h.updatedDeck),N(`${s.name} used Derringer and drew 1 card!`)}}else $({playerIndex:i,cardIndex:-1,card:{type:c}}),fe(!0);break}case r.HOWITZER:{Lt(i,c);const o=[...v];let h=0;for(let m=0;m<o.length;m++)if(m!==i&&o[m].isAlive){const y=o[m];let p=!1;if(Ra(y)&&(p=Ft(y),p)){N(`${y.character.name} defended with barrel against Howitzer!`);continue}if(!p&&y.isBot){const H=o[m].hand.findIndex(j=>j.type===r.MISSED);if(H>=0){const j=o[m].hand.splice(H,1)[0];X(k=>[...k,j]),p=!0,N(`${y.character.name} defended with Missed! against Howitzer!`);continue}}p||(o[m].health-=1,h++,jt(y,"ON_DAMAGE_TAKEN",{attackerIndex:i}),o[m].health<=0&&(o[m].isAlive=!1,It(o,m),q("dominating"),N(`${y.character.name} was eliminated by Howitzer!`)))}Y(o),N(`${s.name} used Howitzer! ${h} players took damage.`);break}default:N(`${c} activation not implemented yet!`);break}},jc=i=>{if(!Et)return;const{type:c,playerIndex:s}=Et,o=[...v];if(c==="BRAWL_ADDITIONAL_DISCARD"){const h=o[s].hand.splice(i,1)[0];X(y=>[...y,h]);let m=0;o.forEach((y,p)=>{if(p!==s&&y.isAlive){if(y.hand.length>0){const H=y.hand.splice(0,1)[0];X(j=>[...j,H]),m++}else if(y.inPlay.length>0){const H=y.inPlay.splice(0,1)[0];X(j=>[...j,H]),m++}}}),N(`${v[s].character.name} played Brawl! All other players discarded ${m} cards.`)}Y(o),$l(!1),Sn(null)},qc=(i,c)=>{const s=v[i],o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]);let m=[...V];o.forEach(y=>{if(y.isAlive&&m.length>0){const p=m.shift();if(y.hand.push(p),y.isBot&&y.hand.length>0){const H=y.hand.reduce((k,I,W)=>Oa(I,y,[])<Oa(k.card,y,[])?{card:I,index:W}:k,{card:y.hand[0],index:0}),j=y.hand.splice(H.index,1)[0];X(k=>[...k,j])}}}),_(m),N(`${s.character.name} played Rag Time! All players drew and discarded 1 card.`),Y(o)},Di=(i,c,s=null)=>{const o=v[i];if(o.hand.length<2){N("You need at least 2 cards to play Tequila (Tequila + another card)!");return}const h=s!==null?s:i,m=v[h];if(m.health>=m.character.life){N(`${m.character.name} is already at maximum health!`);return}const y=[...v],p=y[i].hand.splice(c,1)[0],H=y[i].hand.splice(0,1)[0];X([...z,p,H]),y[h].health=Math.min(y[h].health+1,y[h].character.life),ne(h,"heal-effect",1e3),A(h,1,"heal");const j=h===i?"themselves":m.character.name;N(`${o.character.name} played Tequila on ${j} who regained 1 life! Health: ${y[h].health}`),Y(y)},xc=(i,c)=>{const s=v[i];if(s.health>=s.character.life){N(`${s.character.name} is already at maximum health!`);return}const o=[...v],h=o[i].hand.splice(c,1)[0];X([...z,h]);const m=Math.min(2,o[i].character.life-o[i].health);o[i].health+=m,ne(i,"heal-effect",1e3),A(i,m,"heal"),N(`${s.character.name} drank Whisky and regained ${m} life! Health: ${o[i].health}`),Y(o)},Fl=Q.useCallback(()=>{kt();const i=v[x],c=[...v];if(i.character.name==="Suzy Lafayette"&&c[x].hand.length===0&&V.length>0){const o=V[0];c[x].hand.push(o),_(V.slice(1)),N(`${i.character.name} drew a card at end of turn (Suzy Lafayette ability)!`)}Y(c);let s=(x+1)%c.length;for(;!c[s].isAlive;)s=(s+1)%c.length;Ue(s),tt("draw"),da(!1),Zl(!1),su(o=>o+1),ya(0),gi(new Set),setTimeout(()=>{ne(s,"turn-start-effect",1500)},100),N(`${c[s].name}'s turn - Cards will be drawn automatically`),q("turnStart")},[v,x,V,_,Y,Ue,tt,da,ya,N,ne,kt,q]),Ri=Q.useCallback((i,c)=>{const s=[...v],o=s[i],h=o.hand.map((y,p)=>({card:y,index:p,priority:Oa(y,o,[])}));h.sort((y,p)=>y.priority-p.priority);const m=[];for(let y=0;y<c;y++){const p=h[y],H=s[i].hand.splice(p.index-y,1)[0];m.push(H),X(j=>[...j,H])}Y(s),N(`${o.name} discarded ${c} card${c>1?"s":""} to hand limit: ${m.map(y=>y.type).join(", ")}`),setTimeout(()=>Fl(),1e3)},[v,X,Y,N,Fl]),Mn=Q.useCallback(()=>{if(!ct){N("You must draw cards before ending your turn!");return}const i=v[x],c=[...v];let s=i.health;i.character.name==="Sean Mallory"&&(s=Math.max(s,10));const o=c[x].hand.length-s;if(o>0)if(i.isBot){Ri(x,o);return}else{gn(o),ma([]),qe(!0),N(`You must discard ${o} card${o>1?"s":""} (hand limit: ${s})`);return}Fl()},[ct,v,x,Ri,Fl,N,gn,ma,qe]),yu=i=>{const c=[...yt],s=c.indexOf(i);s>=0?c.splice(s,1):c.length<Wt&&c.push(i),ma(c)},wc=()=>{if(yt.length!==Wt){N(`Please select exactly ${Wt} card${Wt>1?"s":""} to discard.`);return}const i=[...v],c=[];[...yt].sort((o,h)=>h-o).forEach(o=>{const h=i[x].hand.splice(o,1)[0];c.push(h),X(m=>[...m,h])}),Y(i),qe(!1),ma([]),gn(0),N(`You discarded ${c.length} card${c.length>1?"s":""}: ${c.map(o=>o.type).join(", ")}`),setTimeout(()=>Fl(),1e3)},Ca=i=>{const c=i.find(p=>p.role===ve.SHERIFF),s=i.filter(p=>p.role===ve.OUTLAW&&p.isAlive),o=i.filter(p=>p.role===ve.DEPUTY&&p.isAlive),h=i.filter(p=>p.role===ve.RENEGADE&&p.isAlive),m=i[0],y=m&&!m.isBot&&m.isAlive;c.isAlive?s.length===0&&h.length===0&&(Te("ended"),N("Game over! The Sheriff and Deputies win!"),y&&(m.role===ve.SHERIFF||m.role===ve.DEPUTY)?(O("won"),q("unstoppable")):O("lost")):h.length===1&&s.length===0&&o.length===0?(Te("ended"),N("Game over! The Renegade wins!"),y&&m.role===ve.RENEGADE?(O("won"),q("unstoppable")):O("lost")):(Te("ended"),N("Game over! The Outlaws win!"),y&&m.role===ve.OUTLAW?(O("won"),q("unstoppable")):O("lost"))},Ci=i=>{if(it(!1),Ut.type==="BARREL_DEFENSE"){const c=v[Ut.playerIndex];let s=!1;i&&Ft(c)?(s=!0,N(`${c.character.name} defended with Barrel!`),q("defense")):i&&N(`${c.character.name} tried to use Barrel but failed!`),$c(s)}mt(null),oi(null)},gu=i=>{if(ul(!1),Ht.type==="MISSED_DEFENSE"&&Vl){const{attackerIndex:c,targetIndex:s,missedRequired:o,updatedPlayers:h}=Vl,m=v[s];let y=!1;if(i){let p=0;for(let H=0;H<o;H++){const j=h[s].hand.findIndex(k=>k.type===r.MISSED||m.character.name==="Calamity Janet"&&k.type===r.BANG);if(j>=0){const k=h[s].hand.splice(j,1)[0];X(I=>[...I,k]),p++}else break}p>=o&&(y=!0,N(`${m.character.name} defended with ${p} Missed! card(s)`),q("defense"))}else N(`${m.character.name} chose not to use Missed! cards`);Y(h),Zc(y,c,s)}Ta(null),il(null)},Yc=()=>{cl(!di)},Vc=()=>{Te("setup"),O(null),cl(!1),Y([]),_([]),X([]),Ue(0),N("Welcome to BANG!"),oa(!1),fl(!1),it(!1),ul(!1)},Xc=()=>{cl(!1),O(null),v.length,v.filter(i=>!i.isBot).length,v.filter(i=>!i.isBot).map(i=>i.name),Te("setup"),setTimeout(()=>{kl()},100)},Qc=()=>{za(!He),He||q("click")},Zc=(i,c,s)=>{v[c];const o=v[s],h=[...v];i?ne(s,"dodge-effect",800):(h[s].health-=1,ne(s,"damage-effect",1500),A(s,1,"damage"),jt(h[s],"ON_DAMAGE_TAKEN",{attackerIndex:c}),h[s].health<=0?(h[s].isAlive=!1,It(h,s),N(`${o.character.name} (${o.role}) was eliminated!`),q("dominating"),s===0&&!h[0].isBot&&oa(!0),Ca(h)):(q("godlike"),N(`${o.character.name} lost a life point! Health: ${h[s].health}`))),Y(h)},$c=i=>{if(!ri)return;const{attackerIndex:c,targetIndex:s,isSubstitution:o}=ri,h=v[c],m=v[s],y=[...v];if(!i){const p=h.character.name==="Slab the Killer"?2:1,H=y[s].hand.filter(j=>j.type===r.MISSED||m.character.name==="Calamity Janet"&&j.type===r.BANG);if(H.length>=p)if(m.isBot){let j=0;for(let k=0;k<p;k++){const I=y[s].hand.findIndex(W=>W.type===r.MISSED||m.character.name==="Calamity Janet"&&W.type===r.BANG);if(I>=0){const W=y[s].hand.splice(I,1)[0];X(re=>[...re,W]),j++}else break}j>=p&&(i=!0,N(`${m.character.name} defended with ${j} Missed! card(s)`))}else{il({attackerIndex:c,targetIndex:s,missedRequired:p,availableMissedCards:H.length,updatedPlayers:y}),Ta({type:"MISSED_DEFENSE",playerIndex:s,missedRequired:p,availableMissedCards:H.length,message:`${m.character.name}, do you want to use ${p} Missed! card${p>1?"s":""} to defend?`}),ul(!0);return}}i?Ra(m)?ne(s,"barrel-defense-effect",1e3):ne(s,"dodge-effect",800):(y[s].health-=1,ne(s,"damage-effect",1500),A(s,1,"damage"),jt(y[s],"ON_DAMAGE_TAKEN",{attackerIndex:c}),y[s].health<=0?(y[s].isAlive=!1,It(y,s),N(`${m.character.name} (${m.role}) was eliminated!`),q("dominating"),s===0&&!y[0].isBot&&oa(!0),Ca(y)):(q("godlike"),N(`${m.character.name} lost a life point! Health: ${y[s].health}`))),Y(y)},Kc=()=>{const i=v[x];if(i.hand.length<2){N("Need at least 2 cards to use Sid Ketchum's ability!");return}if(i.health>=i.maxHealth){Je("Already at maximum health!",!0,4e3);return}const c=[...v];for(let s=0;s<2;s++){const o=Math.floor(Math.random()*c[x].hand.length),h=c[x].hand.splice(o,1)[0];X([...z,h])}c[x].health+=1,ne(x,"healing-effect",2e3),A(x,1,"heal"),Y(c),N(`${i.character.name} used Sid Ketchum's ability to gain 1 life point!`)},Oi=(i,c)=>{const s=v.filter((o,h)=>o.isAlive&&h!==i);switch(c){case ve.SHERIFF:case ve.DEPUTY:return s.filter(o=>o.role===ve.OUTLAW||o.role===ve.RENEGADE);case ve.OUTLAW:return s.filter(o=>o.role===ve.SHERIFF||o.role===ve.DEPUTY);case ve.RENEGADE:return s.length>2?s.filter(o=>o.role!==ve.RENEGADE):s;default:return s}},Oa=(i,c,s)=>{let o=0;if([r.BARREL,r.MUSTANG,r.SCOPE,r.VOLCANIC,r.SCHOFIELD,r.REMINGTON,r.REV_CARABINE,r.WINCHESTER,r.JAIL,r.DYNAMITE].includes(i.type)&&c.inPlay.some(h=>h.type===i.type))return 0;switch(i.type){case r.BANG:s.length>0&&hl(c)?o=8:hl(c)||(o=0);break;case r.BEER:c.health<c.maxHealth&&(o=9);break;case r.MISSED:o=2;break;case r.BARREL:case r.MUSTANG:case r.SCOPE:o=6;break;case r.VOLCANIC:case r.SCHOFIELD:case r.REMINGTON:case r.REV_CARABINE:case r.WINCHESTER:if(!c.inPlay.some(h=>[r.VOLCANIC,r.SCHOFIELD,r.REMINGTON,r.REV_CARABINE,r.WINCHESTER].includes(h.type)))o=7;else{const h=c.inPlay.find(p=>[r.VOLCANIC,r.SCHOFIELD,r.REMINGTON,r.REV_CARABINE,r.WINCHESTER].includes(p.type)),m=Xf[h.type]||1;(Xf[i.type]||1)>m?o=6:o=1}break;case r.GATLING:s.length>=2&&(o=10);break;case r.INDIANS:s.length>=2&&(o=9);break;case r.DUEL:s.length>0&&(o=7);break;case r.PANIC:case r.CAT_BALOU:s.length>0&&(o=6);break;case r.STAGECOACH:case r.WELLS_FARGO:o=6;break;case r.SALOON:c.health<c.maxHealth?o=8:o=3;break;case r.JAIL:{s.filter(m=>m.role!==ve.SHERIFF&&!m.inPlay.some(y=>y.type===r.JAIL)).length>0?o=5:o=0;break}case r.DYNAMITE:o=4;break;case r.GENERAL_STORE:o=5;break;default:o=3}return o},Bn=Q.useCallback(async()=>{if(ha)return;const i=v[x];if(i.isBot){if(xa(!0),pe==="draw"&&!ct&&(await new Promise(c=>setTimeout(c,1e3)),Ct(),await new Promise(c=>setTimeout(c,500))),pe==="play"){await new Promise(m=>setTimeout(m,1500));const c=Oi(x,i.role),s=[...i.hand];s.map((m,y)=>({card:m,index:y,priority:Oa(m,i,c)})).sort((m,y)=>y.priority-m.priority);const h=Math.min(3,s.length);for(let m=0;m<h;m++){let y=!1;const H=[...v[x].hand].map((j,k)=>({card:j,index:k,priority:Oa(j,v[x],c)}));H.sort((j,k)=>k.priority-j.priority);for(const j of H)if(j.priority>2){const k=[r.BANG,r.PANIC,r.CAT_BALOU,r.DUEL,r.JAIL].includes(j.card.type);if(k&&c.length>0){let I=-1;if(j.card.type===r.BANG||j.card.type===r.DUEL){const W=c.sort((re,xe)=>re.health-xe.health);I=v.findIndex(re=>re===W[0])}else if(j.card.type===r.JAIL){const W=c.filter(re=>re.role!==ve.SHERIFF&&!re.inPlay.some(xe=>xe.type===r.JAIL));if(W.length>0){const re=W[Math.floor(Math.random()*W.length)];I=v.findIndex(xe=>xe===re)}}else{const W=c[Math.floor(Math.random()*c.length)];I=v.findIndex(re=>re===W)}if(I!==-1){Ya(x,j.index,I),y=!0;break}}else if(!k){Ya(x,j.index),y=!0;break}}if(!y)break;await new Promise(j=>setTimeout(j,800))}await new Promise(m=>setTimeout(m,1e3)),Mn()}xa(!1)}},[ha,v,x,pe,ct,Ct,Mn,Ya,Oi,Oa,xa]);return Q.useEffect(()=>{if(K==="playing"&&x!==-1&&v.length>0){const i=v[x];if(i&&i.isBot&&!ha){const c=setTimeout(()=>{Bn()},1e3);return()=>clearTimeout(c)}}},[x,pe,K,v,ha,Bn]),Q.useEffect(()=>{ja&&We.length===0&&(fl(!1),qa([]),yn([]),je(0),N("General Store complete! All cards have been taken."))},[ja,We.length]),Q.useEffect(()=>{if(ja&&_e.length>0&&De<_e.length&&We.length>0){const i=_e[De],c=v[i];if(c&&c.isBot){const s=setTimeout(()=>{gl()},1200);return()=>clearTimeout(s)}}},[ja,De,_e,We.length,v]),Q.useEffect(()=>()=>{Qe&&clearTimeout(Qe)},[Qe]),Q.useEffect(()=>{if(K==="playing"&&x!==-1&&v.length>0){const i=v[x];if(i&&!i.isBot&&pe==="draw"&&!ct&&!Ql&&Na.current!==pa){const c=setTimeout(()=>{Ct()},500);return()=>{clearTimeout(c)}}}},[K,x,pe,ct,Ql,pa,Ct,v]),Q.useEffect(()=>{if(K==="playing"&&x!==-1&&v.length>0){const i=v[x];i&&!i.isBot?Gt():kt()}else kt()},[K,x,Gt,kt]),S.jsxs("div",{className:`bang-game ${K==="setup"?"setup-mode":""}`,children:[S.jsxs("div",{className:`game-header ${K!=="setup"?"hidden":""}`,children:[S.jsx("h1",{children:"🤠 BANG! The Card Game "}),S.jsx("p",{className:"game-subtitle",children:"by Kirtap Studio"})]}),K==="setup"&&S.jsxs("div",{className:"setup-screen",children:[S.jsxs("div",{className:"setup-form",children:[S.jsxs("div",{className:"game-mode",children:[S.jsx("label",{children:"Game Mode: "}),S.jsxs("select",{value:Tt,onChange:i=>{const c=parseInt(i.target.value);An(c),he<c&&Pe(c);const s=Array(c).fill("").map((o,h)=>st[h]||"");Kl(s)},children:[S.jsx("option",{value:"1",children:"Single Player (vs Bots)"}),S.jsx("option",{value:"2",children:"2 Human Players"}),S.jsx("option",{value:"3",children:"3 Human Players"}),S.jsx("option",{value:"4",children:"4 Human Players"}),S.jsx("option",{value:"5",children:"5 Human Players"}),S.jsx("option",{value:"6",children:"6 Human Players"}),S.jsx("option",{value:"7",children:"7 Human Players"})]})]}),S.jsx("div",{className:"human-players",children:Array(Tt).fill(0).map((i,c)=>S.jsxs("div",{className:"player-name",children:[S.jsxs("label",{children:["Player ",c+1," Name: "]}),S.jsx("input",{type:"text",value:st[c]||"",onChange:s=>{const o=[...st];o[c]=s.target.value,Kl(o)},placeholder:`Enter Player ${c+1} name`,maxLength:"20"})]},`human-player-${c}`))}),S.jsxs("div",{className:"player-count",children:[S.jsx("label",{children:"Total Players: "}),S.jsx("select",{value:he,onChange:i=>Pe(parseInt(i.target.value)),disabled:Tt===7,children:Array.from({length:8-Tt},(i,c)=>{const s=Tt+c;if(s<4)return null;const o=s-Tt;return S.jsxs("option",{value:s,children:[s," (",Tt," Human",Tt>1?"s":"",o>0?` + ${o} Bot${o>1?"s":""}`:"",")"]},s)}).filter(Boolean)})]})]}),he-Tt>0&&S.jsxs("p",{className:"bot-info",children:["🤖 ",he-Tt," AI bot",he-Tt>1?"s":""," will join the game and make intelligent decisions!"]}),S.jsx("button",{className:"start-button",onClick:kl,children:"Start Game"})]}),K==="playing"&&S.jsxs("div",{className:"playing-container",children:[ol&&S.jsxs("div",{className:`turn-timer ${ba<=30&&ba>10?"warning":""} ${ba<=10?"critical":""}`,children:["⏰ Time: ",sa(ba)]}),S.jsx("button",{onClick:Yc,className:"game-menu-button-fixed",title:"Game Menu",children:"☰"}),S.jsxs("div",{className:"top-ui-container",children:[S.jsx("div",{className:"message-box-playing",children:ut}),x!==-1&&!v[x].isBot&&S.jsxs("div",{className:"actions-right",children:[S.jsx("button",{onClick:Ct,disabled:pe!=="draw"||ct,className:pe!=="draw"||ct?"disabled":"",children:ct?"Cards Drawn ✓":"Auto-Drawing..."}),S.jsx("button",{onClick:Mn,disabled:!ct,className:ct?"":"disabled",children:"End Turn"}),v[x].character.name==="Sid Ketchum"&&v[x].hand.length>=2&&v[x].health<v[x].maxHealth&&S.jsx("button",{onClick:()=>Kc(),children:"Sid Ketchum Ability"})]})]}),S.jsxs("div",{className:"game-board",children:[S.jsxs("div",{className:`current-player-area ${x!==0||v[0]&&v[0].isBot?"disabled":""}`,"data-player-index":"0",children:[v.length>0&&v[0]&&!v[0].isBot&&!si&&S.jsxs("div",{className:"current-player effect-container","data-player-index":0,children:[S.jsx("div",{className:"player-status",children:S.jsxs("div",{className:"character-info",children:[S.jsxs("div",{className:"character-image-container",children:[S.jsx("img",{src:ii[v[0].character.name],alt:v[0].character.name,className:"character-image",onContextMenu:i=>{i.preventDefault(),ml(v[0].character)},onDoubleClick:()=>{ml(v[0].character)},onTouchStart:()=>qt({isCharacter:!0,character:v[0].character,type:v[0].character.name}),onTouchEnd:xt,onTouchMove:wa}),S.jsxs("div",{className:"player-name-overlay",children:[v[0].name,v[0].isBot&&S.jsx("span",{className:"bot-indicator-overlay",children:" 🤖"})]}),v[0].isAlive&&S.jsxs("div",{className:"health-display-overlay",children:["❤️ ",v[0].health,"/",v[0].maxHealth]})]}),!v[0].isAlive&&S.jsxs("div",{className:"role-display-below",children:["💀 ",v[0].role]}),S.jsxs("div",{className:"character-details",children:[ga(v[0])&&S.jsx("div",{className:"sheriff-badge-current",children:"⭐"}),S.jsx("div",{className:"role-display",children:S.jsx("img",{src:$m[v[0].role],alt:v[0].role,className:"role-image"})})]})]})}),S.jsxs("div",{className:"hand-area",children:[S.jsxs("h4",{children:["Your Hand (",v[0].hand.length," cards)",S.jsxs("span",{className:"hand-limit-info",children:["- Hand Limit: ",v[0].health]})]}),S.jsx("div",{className:"hand-cards",children:v[0]&&v[0].hand&&Array.isArray(v[0].hand)&&v[0].hand.map((i,c)=>{if(!i||!i.type)return null;const s=`play-0-${c}`,o=Bt[s];return S.jsxs("div",{"data-card-index":c,className:`hand-card ${[r.BARREL,r.MUSTANG,r.SCOPE,r.VOLCANIC,r.SCHOFIELD,r.REMINGTON,r.REV_CARABINE,r.WINCHESTER,r.JAIL,r.DYNAMITE].includes(i.type)&&v[0].inPlay.some(h=>h.type===i.type)?"duplicate-equipment":""} ${i.type===r.BANG&&!hl(v[0])?"bang-limit-reached":""} ${i.type===r.BEER&&v[0].health>=v[0].maxHealth?"beer-unplayable":""} ${o?`card-${o.type}-animation`:""}`,onClick:()=>{if(x!==0){N("It's not your turn!");return}[r.BANG,r.PANIC,r.CAT_BALOU,r.DUEL,r.JAIL,r.PUNCH,r.TEQUILA].includes(i.type)?($({playerIndex:0,cardIndex:c,card:i}),fe(!0)):Ya(0,c)},onContextMenu:h=>{h.preventDefault(),pt(i)},onDoubleClick:()=>{pt(i)},onMouseEnter:()=>ht(i),onMouseLeave:()=>ht(null),onTouchStart:h=>du(h,i,c),onTouchEnd:Ei,onTouchMove:Jl,title:[r.BARREL,r.MUSTANG,r.SCOPE,r.VOLCANIC,r.SCHOFIELD,r.REMINGTON,r.REV_CARABINE,r.WINCHESTER,r.JAIL,r.DYNAMITE].includes(i.type)&&v[0].inPlay.some(h=>h.type===i.type)?`${i.type} - ${i.suit} ${i.value} (DUPLICATE - Cannot play!) | Right-click or double-click to preview`:i.type===r.BANG&&!hl(v[0])?`${i.type} - ${i.suit} ${i.value} (BANG! limit reached - Need Volcanic or Willy the Kid!) | Right-click or double-click to preview`:i.type===r.BEER&&v[0].health>=v[0].maxHealth?`${i.type} - ${i.suit} ${i.value} (Already at maximum health - Cannot play!) | Right-click or double-click to preview`:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[S.jsx("img",{src:Ha[i.type],alt:i.type,className:"hand-card-image",onError:h=>{h.target.style.display="none",h.target.nextSibling.style.display="block"}}),S.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[S.jsx("div",{className:"card-name",children:i.type}),S.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},`hand-${c}-${i.type}-${i.suit}-${i.value}-${Date.now()}`)})})]}),v[0].inPlay.length>0&&S.jsxs("div",{className:"equipment-area",children:[S.jsx("h4",{children:"Equipment:"}),S.jsx("div",{className:"equipment-cards",children:v[0]&&v[0].inPlay&&Array.isArray(v[0].inPlay)&&v[0].inPlay.map((i,c)=>!i||!i.type?null:S.jsx("div",{className:`equipment-card ${i.equipmentType===Kt.GREEN?"green-card":""} ${i.equipmentType===Kt.GREEN&&zt(v[0],i.type)?"usable-green":""}`,title:`${i.type} ${i.equipmentType===Kt.GREEN?"(Green - Click to activate)":""}`,onClick:()=>{i.equipmentType===Kt.GREEN&&zt(v[0],i.type)&&Lc(0,i.type)},onContextMenu:s=>{s.preventDefault(),pt(i)},onDoubleClick:()=>{pt(i)},onTouchStart:()=>qt(i),onTouchEnd:xt,onTouchMove:wa,children:S.jsx("img",{src:Ha[i.type],alt:i.type,className:"equipment-card-image"})},`equipment-${c}-${i.type}-${Date.now()}`))})]})]}),si&&S.jsx("div",{className:"spectator-mode",children:S.jsxs("div",{className:"spectator-info",children:[S.jsx("h3",{children:"👻 Spectator Mode"}),S.jsx("p",{children:"You have been eliminated but are watching the game continue."}),S.jsx("p",{children:"The game will end when a winning condition is met."}),S.jsx("button",{className:"exit-spectator-button",onClick:pn,children:"Exit to Main Menu"})]})})]}),S.jsx("div",{className:"central-area",children:S.jsxs("div",{className:"game-info",children:[S.jsxs("div",{className:"deck-info",children:[S.jsxs("h4",{children:["Deck (",V.length,")"]}),S.jsx("div",{className:"deck-card",children:S.jsx("img",{src:Zm,alt:"Card Back",className:"deck-image"})})]}),S.jsxs("div",{className:"discard-info",children:[S.jsxs("h4",{children:["Discard (",z.length,")"]}),S.jsx("div",{className:`discard-card discard-pile ${z.length===0?"empty":""}`,children:z.length>0&&z[z.length-1]&&z[z.length-1].type&&S.jsx("img",{src:Ha[z[z.length-1].type],alt:z[z.length-1].type,className:"discard-image",onContextMenu:i=>{i.preventDefault(),pt(z[z.length-1])},onDoubleClick:()=>{pt(z[z.length-1])},onTouchStart:()=>qt(z[z.length-1]),onTouchEnd:xt,onTouchMove:wa,title:`${z[z.length-1].type} - ${z[z.length-1].suit} ${z[z.length-1].value} | Right-click, double-click, or touch-hold to preview`})})]})]})}),S.jsx("div",{className:"other-players-area",children:v.map((i,c)=>c!==0&&S.jsxs("div",{className:`other-player ${i.isAlive?"":"dead-player"} effect-container`,"data-player-index":c,children:[S.jsxs("div",{className:"player-info",children:[S.jsxs("div",{className:"character-image-container",children:[S.jsx("img",{src:ii[i.character.name],alt:i.character.name,className:"character-image",onError:s=>{s.target.style.display="none"},onContextMenu:s=>{s.preventDefault(),ml(i.character)},onDoubleClick:()=>{ml(i.character)},onTouchStart:()=>qt({isCharacter:!0,character:i.character,type:i.character.name}),onTouchEnd:xt,onTouchMove:wa}),S.jsxs("div",{className:"player-name-overlay",children:[i.name,i.isBot&&S.jsx("span",{className:"bot-indicator-overlay",children:" 🤖"})]}),i.isAlive&&S.jsxs("div",{className:"health-display-overlay",children:["❤️ ",i.health,"/",i.maxHealth]})]}),!i.isAlive&&S.jsxs("div",{className:"role-display-below",children:["💀 ",i.role]}),S.jsxs("div",{className:"player-details",children:[ga(i)&&S.jsx("div",{className:"sheriff-badge-below",children:"⭐"}),S.jsxs("div",{className:"cards-count",children:["🃏 ",i.hand.length]})]})]}),i.inPlay.length>0&&S.jsx("div",{className:"other-player-equipment",children:i&&i.inPlay&&Array.isArray(i.inPlay)&&i.inPlay.map((s,o)=>!s||!s.type?null:S.jsx("div",{className:"small-card",title:s.type,onContextMenu:h=>{h.preventDefault(),pt(s)},onDoubleClick:()=>{pt(s)},onTouchStart:()=>qt(s),onTouchEnd:xt,onTouchMove:wa,children:S.jsx("img",{src:Ha[s.type],alt:s.type,className:"small-card-image"})},`player-${c}-equipment-${o}-${s.type}-${Date.now()}`))})]},`other-player-${c}-${i.character.name}`))})]})]}),K==="ended"&&S.jsxs("div",{className:"game-over",children:[S.jsx("div",{className:"game-result-header",children:Ae==="won"?S.jsxs(S.Fragment,{children:[S.jsx("h2",{className:"victory-title",children:"🎉 Victory! 🎉"}),S.jsx("p",{className:"victory-subtitle",children:"Congratulations! You have won the game!"})]}):Ae==="lost"?S.jsxs(S.Fragment,{children:[S.jsx("h2",{className:"defeat-title",children:"💀 Defeat 💀"}),S.jsx("p",{className:"defeat-subtitle",children:"Better luck next time!"})]}):S.jsxs(S.Fragment,{children:[S.jsx("h2",{children:"Game Over!"}),S.jsx("p",{children:"The game has ended."})]})}),S.jsx("div",{className:"final-roles",children:v.map((i,c)=>S.jsxs("div",{className:`player-result ${i.isAlive?"":"dead-player"}`,children:[S.jsx("img",{src:ii[i.character.name],alt:i.character.name,className:"character-image-result",onError:s=>{s.target.style.display="none"},onContextMenu:s=>{s.preventDefault(),ml(i.character)},onDoubleClick:()=>{ml(i.character)},onTouchStart:()=>qt({isCharacter:!0,character:i.character,type:i.character.name}),onTouchEnd:xt,onTouchMove:wa}),S.jsxs("h3",{children:[i.name,i.isBot&&S.jsx("span",{className:"bot-indicator-small",children:" 🤖"})]}),ga(i)&&S.jsx("div",{className:"sheriff-badge-result",children:"⭐"}),S.jsx("p",{className:"character-name",children:i.character.name}),S.jsx("p",{className:"role",children:i.role}),S.jsx("p",{className:"status",children:i.isAlive?"Survived":"Eliminated"})]},`final-result-${c}-${i.character.name}`))}),S.jsx("button",{className:"restart-button",onClick:()=>{Te("setup"),Y([]),Ue(0),_([]),X([]),tt("draw"),da(!1),ya(0),An(1),Kl([""]),N("Welcome to BANG!")},children:"Play Again"})]}),P&&Z&&S.jsxs("div",{className:"target-selection",children:[S.jsxs("h3",{children:["Select a target for ",Z.card.type]}),S.jsx("div",{className:"target-options",children:v.map((i,c)=>x!==c&&i.isAlive&&(Z.card.type===r.JAIL?i.role!==ve.SHERIFF&&!i.inPlay.some(o=>o.type===r.JAIL):!0)&&S.jsxs("div",{className:`target-option ${Z.card.type===r.JAIL&&(i.role===ve.SHERIFF||i.inPlay.some(o=>o.type===r.JAIL))?"invalid-target":""}`,onClick:()=>{Z.cardIndex===-1?zc(Z.playerIndex,Z.card.type,c):Ya(Z.playerIndex,Z.cardIndex,c),fe(!1),$(null)},children:[S.jsxs("div",{className:"target-player-name",children:[i.name,i.isBot&&S.jsx("span",{className:"bot-indicator-small",children:" 🤖"}),ga(i)&&S.jsx("span",{className:"sheriff-badge-target",children:" ⭐"}),Z.card.type===r.JAIL&&i.inPlay.some(o=>o.type===r.JAIL)&&S.jsx("span",{className:"jail-indicator",children:" 🔒 IN JAIL"})]}),S.jsx("div",{className:"target-character-name",children:i.character.name}),S.jsxs("div",{className:"target-info",children:["Health: ",i.health," | Distance: ",$e(x,c,v)]})]},`target-${c}-${i.character.name}`))}),S.jsx("button",{onClick:()=>{fe(!1),$(null)},children:"Cancel"})]}),rl&&S.jsxs("div",{className:"discard-selection",children:[S.jsx("h3",{children:"Discard Cards - Hand Limit Exceeded"}),S.jsxs("p",{children:["You must discard ",Wt," card",Wt>1?"s":""," (Hand limit: ",v[x].health,")"]}),S.jsx("div",{className:"discard-cards",children:v[x]&&v[x].hand&&Array.isArray(v[x].hand)&&v[x].hand.map((i,c)=>!i||!i.type?null:S.jsxs("div",{className:`discard-card ${yt.includes(c)?"selected":""}`,onClick:()=>yu(c),onContextMenu:s=>{s.preventDefault(),pt(i)},onDoubleClick:()=>{pt(i)},title:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[S.jsx("img",{src:Ha[i.type],alt:i.type,className:"discard-card-image"}),S.jsx("div",{className:"card-name",children:i.type}),yt.includes(c)&&S.jsx("div",{className:"selected-indicator",children:"✓"})]},`discard-${c}-${i.type}-${i.suit}-${i.value}-${Date.now()}`))}),S.jsx("div",{className:"discard-actions",children:S.jsxs("button",{onClick:wc,disabled:yt.length!==Wt,className:yt.length!==Wt?"disabled":"",children:["Discard Selected Cards (",yt.length,"/",Wt,")"]})})]}),_c&&S.jsx("div",{className:"death-modal-overlay",children:S.jsx("div",{className:"death-modal",children:S.jsxs("div",{className:"death-modal-content",children:[S.jsx("h2",{children:"💀 You Have Been Eliminated!"}),S.jsx("p",{children:"Your character has been eliminated from the game."}),S.jsx("p",{children:"What would you like to do?"}),S.jsxs("div",{className:"death-modal-buttons",children:[S.jsx("button",{className:"spectate-button",onClick:ou,children:"👻 Spectate Game"}),S.jsx("button",{className:"exit-button",onClick:pn,children:"🚪 Exit to Main Menu"})]})]})})}),ja&&We.length>0&&S.jsx("div",{className:"modal-overlay",children:S.jsxs("div",{className:"general-store-modal",children:[S.jsx("h3",{children:"General Store"}),S.jsx("p",{children:_e.length>0&&De<_e.length?`${(vl=v[_e[De]])==null?void 0:vl.character.name}'s turn to choose`:"Choose a card"}),S.jsxs("p",{className:"general-store-instruction",children:["Cards available: ",We.length," | Players remaining: ",_e.length-De]}),S.jsx("p",{className:"general-store-rule",children:"Rule: Starting with the player who played General Store, each player chooses one card clockwise."}),S.jsx("div",{className:"general-store-cards",children:We.map((i,c)=>{var s;return!i||!i.type?null:S.jsxs("div",{className:`general-store-card ${(s=v[_e[De]])!=null&&s.isBot?"disabled":""}`,onClick:()=>{var h;const o=_e[De];!((h=v[o])!=null&&h.isBot)&&o===_e[De]&&We[c]&&Cn(c)},onContextMenu:o=>{o.preventDefault(),pt(i)},onDoubleClick:()=>{pt(i)},title:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[S.jsx("img",{src:Ha[i.type],alt:i.type,className:"general-store-card-image"}),S.jsx("div",{className:"card-name",children:i.type}),S.jsxs("div",{className:"card-suit",children:[i.value,i.suit]})]},`general-store-${c}-${i.type}-${i.suit}-${i.value}`)})}),_e.length>0&&De<_e.length&&S.jsx("div",{className:"general-store-info",children:S.jsxs("p",{children:["Turn order: ",_e.map((i,c)=>{var s;return`${c===De?"→ ":""}${(s=v[i])==null?void 0:s.character.name}${c===De?" ←":""}`}).join(" → ")]})}),S.jsxs("div",{className:"general-store-actions",children:[S.jsx("button",{className:"close-general-store-button",onClick:()=>{fl(!1),qa([]),yn([]),je(0),N("General Store cancelled.")},children:"Cancel General Store"}),_e.length>0&&De<_e.length&&((_i=v[_e[De]])==null?void 0:_i.isBot)&&S.jsx("button",{className:"force-bot-selection-button",onClick:()=>gl(),children:"Force Bot Selection"})]})]})}),Yl&&Ut&&S.jsx("div",{className:"modal-overlay",children:S.jsxs("div",{className:"ability-choice-modal",children:[S.jsx("h3",{children:"Character Ability"}),S.jsx("p",{children:Ut.message}),S.jsxs("div",{className:"ability-choice-buttons",children:[S.jsx("button",{className:"ability-yes-button",onClick:()=>Ci(!0),children:"Use Ability"}),S.jsx("button",{className:"ability-no-button",onClick:()=>Ci(!1),children:"Don't Use"})]})]})}),Mc&&Ht&&S.jsx("div",{className:"modal-overlay",children:S.jsxs("div",{className:"missed-choice-modal",children:[S.jsx("h3",{children:"Defend with Missed!"}),S.jsx("p",{children:Ht.message}),S.jsxs("div",{className:"missed-choice-info",children:[S.jsxs("p",{children:["You have ",Ht.availableMissedCards," Missed! card",Ht.availableMissedCards>1?"s":""," available."]}),S.jsxs("p",{children:["You need ",Ht.missedRequired," Missed! card",Ht.missedRequired>1?"s":""," to defend."]})]}),S.jsxs("div",{className:"missed-choice-buttons",children:[S.jsxs("button",{className:"missed-yes-button",onClick:()=>gu(!0),children:["Use Missed! Card",Ht.missedRequired>1?"s":""]}),S.jsx("button",{className:"missed-no-button",onClick:()=>gu(!1),children:"Take Damage"})]})]})}),vn&&Et&&S.jsx("div",{className:"modal-overlay",children:S.jsxs("div",{className:"card-selection-modal",children:[S.jsx("h3",{children:"Card Selection"}),S.jsx("p",{children:Et.message}),S.jsx("div",{className:"card-selection-grid",children:(vu=v[Et.playerIndex])==null?void 0:vu.hand.map((i,c)=>S.jsxs("div",{className:"selectable-card",onClick:()=>jc(c),children:[S.jsx("img",{src:Ha[i.type],alt:i.type,className:"card-selection-image",onError:s=>{s.target.style.display="none",s.target.nextSibling.style.display="block"}}),S.jsxs("div",{className:"card-selection-fallback",style:{display:"none"},children:[S.jsx("div",{className:"card-name",children:i.type}),S.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},c))})]})}),di&&S.jsx("div",{className:"modal-overlay",onClick:()=>cl(!1),children:S.jsxs("div",{className:"game-menu-modal",onClick:i=>i.stopPropagation(),children:[S.jsx("h3",{children:"Game Menu"}),S.jsxs("div",{className:"game-menu-buttons",children:[S.jsx("button",{className:"menu-button",onClick:Qc,children:He?"🔊 Sound: ON":"🔇 Sound: OFF"}),S.jsx("button",{className:"menu-button",onClick:Xc,children:"🔄 Restart Game"}),S.jsx("button",{className:"menu-button",onClick:Vc,children:"🏠 Main Menu"}),S.jsx("button",{className:"menu-button menu-close",onClick:()=>cl(!1),children:"✕ Close"})]})]})}),hi.map(i=>{if(!i||!i.card||!i.card.type)return null;const c=document.querySelector(`[data-player-index="${i.fromPlayerIndex}"]`),s=i.toPlayerIndex>=0?document.querySelector(`[data-player-index="${i.toPlayerIndex}"]`):document.querySelector(".discard-pile"),o=document.querySelector(".discard-pile");if(!c||!s&&i.phase==="toTarget"||!o&&i.phase==="toDiscard")return null;const h=c.getBoundingClientRect(),m=i.phase==="toTarget"?s.getBoundingClientRect():o.getBoundingClientRect(),y=h.left+h.width/2,p=h.top+h.height/2,H=m.left+m.width/2,j=m.top+m.height/2,k=y+(H-y)*i.progress,I=p+(j-p)*i.progress,W=i.phase==="toTarget"?1+i.progress*.2:1.2-i.progress*.2,re=i.progress*360;return S.jsx("div",{className:`animating-card ${i.card.type==="BANG!"?"bang-card":""}`,style:{left:k-30,top:I-42,transform:`scale(${W}) rotate(${re}deg)`,opacity:1-i.progress*.1},children:S.jsxs("div",{className:`card ${i.card.suit==="♥"||i.card.suit==="♦"?"red":"black"}`,children:[S.jsx("div",{children:i.card.type||"Unknown"}),S.jsxs("div",{children:[i.card.value||"",i.card.suit||""]})]})},i.id)}),iu&&me&&S.jsx("div",{className:"card-zoom-overlay",onClick:Tn,children:S.jsx("div",{className:"card-zoom-modal",onClick:i=>i.stopPropagation(),children:S.jsxs("div",{className:"card-zoom-content",children:[S.jsxs("div",{className:"card-zoom-image",children:[S.jsx("img",{src:me.isCharacter?ii[me.type]:Ha[me.type],alt:me.type,className:"zoomed-card-image",onError:i=>{i.target.style.display="none",i.target.nextSibling.style.display="block"}}),S.jsxs("div",{className:"card-zoom-fallback",style:{display:"none"},children:[S.jsx("div",{className:"zoomed-card-name",children:me.type}),S.jsxs("div",{className:"zoomed-card-suit",children:[me.suit," ",me.value]})]})]}),S.jsx("div",{className:"card-description",children:me.isCharacter?me.character.ability:yl(me.type)})]})})}),Rt&&S.jsx("div",{className:"card-zoom-overlay",onClick:()=>wl(null),children:S.jsx("div",{className:"card-zoom-modal",onClick:i=>i.stopPropagation(),children:S.jsxs("div",{className:"card-zoom-content",children:[S.jsxs("div",{className:"card-zoom-image",children:[S.jsx("img",{src:Rt.isCharacter?ii[Rt.type]:Ha[Rt.type],alt:Rt.type,className:"zoomed-card-image",onError:i=>{i.target.style.display="none",i.target.nextSibling.style.display="block"}}),S.jsxs("div",{className:"card-zoom-fallback",style:{display:"none"},children:[S.jsx("div",{className:"zoomed-card-name",children:Rt.type}),S.jsxs("div",{className:"zoomed-card-suit",children:[Rt.suit," ",Rt.value]})]})]}),S.jsxs("div",{className:"card-description",children:[Rt.isCharacter?(Su=Rt.character)==null?void 0:Su.ability:yl(Rt.type),S.jsxs("div",{className:"long-press-hint",children:["💡 ",S.jsx("strong",{children:"Mobile Tip:"})," Hold cards for 0.8s to zoom, tap quickly to play"]})]})]})})}),At.map(i=>S.jsx("div",{className:"moving-card",style:{left:i.startX,top:i.startY,transform:"translate(-50%, -50%)","--end-x":`${i.endX-i.startX}px`,"--end-y":`${i.endY-i.startY}px`},children:S.jsx("img",{src:Ha[i.card.type]||"/images/cards/card-back.png",alt:i.card.type,onError:c=>{c.target.src="/images/cards/card-back.png"}})},i.id))]})}Qm.createRoot(document.getElementById("root")).render(S.jsx(Q.StrictMode,{children:S.jsx(km,{})}));
