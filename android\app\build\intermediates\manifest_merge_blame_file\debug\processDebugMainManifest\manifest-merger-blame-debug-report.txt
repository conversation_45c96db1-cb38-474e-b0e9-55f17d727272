1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.bangcardgame.app"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="23"
9        android:targetSdkVersion="35" />
10
11    <!-- Permissions -->
12
13    <uses-permission android:name="android.permission.INTERNET" />
13-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:40:5-67
13-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:40:22-64
14
15    <permission
15-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5144e1810ac2af7a556a9c2ecb9ded\transformed\core-1.15.0\AndroidManifest.xml:22:5-24:47
16        android:name="com.bangcardgame.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
16-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5144e1810ac2af7a556a9c2ecb9ded\transformed\core-1.15.0\AndroidManifest.xml:23:9-81
17        android:protectionLevel="signature" />
17-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5144e1810ac2af7a556a9c2ecb9ded\transformed\core-1.15.0\AndroidManifest.xml:24:9-44
18
19    <uses-permission android:name="com.bangcardgame.app.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5144e1810ac2af7a556a9c2ecb9ded\transformed\core-1.15.0\AndroidManifest.xml:26:5-97
19-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5144e1810ac2af7a556a9c2ecb9ded\transformed\core-1.15.0\AndroidManifest.xml:26:22-94
20
21    <application
21-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:4:5-36:19
22        android:allowBackup="true"
22-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:5:9-35
23        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
23-->[androidx.core:core:1.15.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\9a5144e1810ac2af7a556a9c2ecb9ded\transformed\core-1.15.0\AndroidManifest.xml:28:18-86
24        android:debuggable="true"
25        android:extractNativeLibs="false"
26        android:icon="@mipmap/ic_launcher"
26-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:6:9-43
27        android:label="@string/app_name"
27-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:7:9-41
28        android:roundIcon="@mipmap/ic_launcher_round"
28-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:8:9-54
29        android:supportsRtl="true"
29-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:9:9-35
30        android:theme="@style/AppTheme" >
30-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:10:9-40
31        <activity
31-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:12:9-25:20
32            android:name="com.bangcardgame.app.MainActivity"
32-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:14:13-41
33            android:configChanges="orientation|keyboardHidden|keyboard|screenSize|locale|smallestScreenSize|screenLayout|uiMode|navigation"
33-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:13:13-140
34            android:exported="true"
34-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:18:13-36
35            android:label="@string/title_activity_main"
35-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:15:13-56
36            android:launchMode="singleTask"
36-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:17:13-44
37            android:theme="@style/AppTheme.NoActionBarLaunch" >
37-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:16:13-62
38            <intent-filter>
38-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:20:13-23:29
39                <action android:name="android.intent.action.MAIN" />
39-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:21:17-69
39-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:21:25-66
40
41                <category android:name="android.intent.category.LAUNCHER" />
41-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:22:17-77
41-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:22:27-74
42            </intent-filter>
43        </activity>
44
45        <provider
46            android:name="androidx.core.content.FileProvider"
46-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:28:13-62
47            android:authorities="com.bangcardgame.app.fileprovider"
47-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:29:13-64
48            android:exported="false"
48-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:30:13-37
49            android:grantUriPermissions="true" >
49-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:31:13-47
50            <meta-data
50-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:32:13-34:64
51                android:name="android.support.FILE_PROVIDER_PATHS"
51-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:33:17-67
52                android:resource="@xml/file_paths" />
52-->C:\Programy a hry\Podnikání\Programování\Bang! online\bang\android\app\src\main\AndroidManifest.xml:34:17-51
53        </provider>
54        <provider
54-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87bf043274614f86814900ef4ddf1d0\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
55            android:name="androidx.startup.InitializationProvider"
55-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87bf043274614f86814900ef4ddf1d0\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
56            android:authorities="com.bangcardgame.app.androidx-startup"
56-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87bf043274614f86814900ef4ddf1d0\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
57            android:exported="false" >
57-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87bf043274614f86814900ef4ddf1d0\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
58            <meta-data
58-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87bf043274614f86814900ef4ddf1d0\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
59                android:name="androidx.emoji2.text.EmojiCompatInitializer"
59-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87bf043274614f86814900ef4ddf1d0\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
60                android:value="androidx.startup" />
60-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\a87bf043274614f86814900ef4ddf1d0\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
61            <meta-data
61-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efc9dc58aaa75d5b1efcff0d9df07245\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
62                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
62-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efc9dc58aaa75d5b1efcff0d9df07245\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
63                android:value="androidx.startup" />
63-->[androidx.lifecycle:lifecycle-process:2.6.2] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\efc9dc58aaa75d5b1efcff0d9df07245\transformed\lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
64            <meta-data
64-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
65                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
65-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
66                android:value="androidx.startup" />
66-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
67        </provider>
68
69        <receiver
69-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
70            android:name="androidx.profileinstaller.ProfileInstallReceiver"
70-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
71            android:directBootAware="false"
71-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
72            android:enabled="true"
72-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
73            android:exported="true"
73-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
74            android:permission="android.permission.DUMP" >
74-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
75            <intent-filter>
75-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
76                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
76-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
77            </intent-filter>
78            <intent-filter>
78-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
79                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
79-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
80            </intent-filter>
81            <intent-filter>
81-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
82                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
82-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
83            </intent-filter>
84            <intent-filter>
84-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
85                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
85-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\aef6dd0653ee5852ea657539d9688a92\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
86            </intent-filter>
87        </receiver>
88    </application>
89
90</manifest>
