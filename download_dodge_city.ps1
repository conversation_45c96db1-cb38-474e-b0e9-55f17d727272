# Download Dodge City card images
$baseUrl = "https://bang.dvgiochi.com/content/3/cards/"
$cardDir = "src/assets/cards/dodge_city/"
$charDir = "src/assets/characters/dodge_city/"

# Create directories if they don't exist
New-Item -ItemType Directory -Path $cardDir -Force | Out-Null
New-Item -ItemType Directory -Path $charDir -Force | Out-Null

# Card images to download
$cards = @{
    "binocular.png" = "03_binocolo.png"
    "hideout.png" = "03_riparo.png"
    "brawl.png" = "03_rissa.png"
    "dodge.png" = "03_schivata.png"
    "punch.png" = "03_pugno.png"
    "rag_time.png" = "03_rag_time.png"
    "springfield.png" = "03_springfield.png"
    "tequila.png" = "03_tequila.png"
    "whisky.png" = "03_whisky.png"
    "bible.png" = "03_bibbia.png"
    "buffalo_rifle.png" = "03_fucile_da_caccia.png"
    "can_can.png" = "03_can_can.png"
    "canteen.png" = "03_borraccia.png"
    "conestoga.png" = "03_conestoga.png"
    "derringer.png" = "03_derringer.png"
    "howitzer.png" = "03_howitzer.png"
    "iron_plate.png" = "03_placca_di_ferro.png"
    "knife.png" = "03_pugnale.png"
    "pepperbox.png" = "03_pepperbox.png"
    "pony_express.png" = "03_pony_express.png"
    "sombrero.png" = "03_sombrero.png"
    "ten_gallon_hat.png" = "03_cappello.png"
}

# Character images to download
$characters = @{
    "apache_kid.png" = "03_apache_kid.png"
    "belle_star.png" = "03_belle_star.png"
    "bill_noface.png" = "03_bill_noface.png"
    "chuck_wengam.png" = "03_chuck_wengam.png"
    "doc_holyday.png" = "03_doc_holyday.png"
    "elena_fuente.png" = "03_elena_fuente.png"
    "greg_digger.png" = "03_greg_digger.png"
    "herb_hunter.png" = "03_herb_hunter.png"
    "jose_delgado.png" = "03_jose_delgado.png"
    "molly_stark.png" = "03_molly_stark.png"
    "pat_brennan.png" = "03_pat_brennan.png"
    "pixie_pete.png" = "03_pixie_pete.png"
    "sean_mallory.png" = "03_sean_mallory.png"
    "tequila_joe.png" = "03_tequila_joe.png"
    "vera_custer.png" = "03_vera_custer.png"
}

Write-Host "Downloading Dodge City card images..."
foreach ($card in $cards.GetEnumerator()) {
    $url = $baseUrl + $card.Value
    $output = $cardDir + $card.Key
    Write-Host "Downloading $($card.Key)..."
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -ErrorAction Stop
        Write-Host "✓ Downloaded $($card.Key)" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to download $($card.Key): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Downloading Dodge City character images..."
foreach ($char in $characters.GetEnumerator()) {
    $url = $baseUrl + $char.Value
    $output = $charDir + $char.Key
    Write-Host "Downloading $($char.Key)..."
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -ErrorAction Stop
        Write-Host "✓ Downloaded $($char.Key)" -ForegroundColor Green
    } catch {
        Write-Host "✗ Failed to download $($char.Key): $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "Download complete!"
