# Download official Dodge City card images from BANG! website
$baseUrl = "https://bang.dvgiochi.com/content/3/cards/"
$cardDir = "src/assets/cards/dodge_city/"
$charDir = "src/assets/characters/dodge_city/"

# Create directories if they don't exist
New-Item -ItemType Directory -Path $cardDir -Force | Out-Null
New-Item -ItemType Directory -Path $charDir -Force | Out-Null

Write-Host "Downloading official Dodge City card images..." -ForegroundColor Green

# Equipment cards (blue-bordered)
$equipmentCards = @{
    "barrel.png" = "03_barile.png"
    "binocular.png" = "03_binocolo.png"
    "dynamite.png" = "03_dinamite.png"
    "mustang.png" = "03_mustang.png"
    "remington.png" = "03_remington.png"
    "rev_carabine.png" = "03_rev_carabine.png"
    "hideout.png" = "03_riparo.png"
    "bible.png" = "03_bibbia.png"
    "canteen.png" = "03_borraccia.png"
    "can_can.png" = "03_can_can.png"
    "ten_gallon_hat.png" = "03_cappello.png"
    "conestoga.png" = "03_conestoga.png"
    "derringer.png" = "03_derringer.png"
    "buffalo_rifle.png" = "03_fucile_da_caccia.png"
    "howitzer.png" = "03_howitzer.png"
    "pepperbox.png" = "03_pepperbox.png"
    "iron_plate.png" = "03_placca_di_ferro.png"
    "pony_express.png" = "03_pony_express.png"
    "knife.png" = "03_pugnale.png"
    "sombrero.png" = "03_sombrero.png"
    "springfield.png" = "03_springfield.png"
}

# Action cards
$actionCards = @{
    "bang.png" = "03_bang.png"
    "beer.png" = "03_birra.png"
    "cat_balou.png" = "03_cat_balou.png"
    "general_store.png" = "03_emporio.png"
    "indians.png" = "03_indiani.png"
    "missed.png" = "03_mancato.png"
    "panic.png" = "03_panico.png"
    "punch.png" = "03_pugno.png"
    "rag_time.png" = "03_rag_time.png"
    "brawl.png" = "03_rissa.png"
    "dodge.png" = "03_schivata.png"
    "tequila.png" = "03_tequila.png"
    "whisky.png" = "03_whisky.png"
}

# Character cards
$characterCards = @{
    "apache_kid.png" = "03_apache_kid.png"
    "belle_star.png" = "03_belle_star.png"
    "bill_noface.png" = "03_bill_noface.png"
    "chuck_wengam.png" = "03_chuck_wengam.png"
    "doc_holyday.png" = "03_doc_holyday.png"
    "elena_fuente.png" = "03_elena_fuente.png"
    "greg_digger.png" = "03_greg_digger.png"
    "herb_hunter.png" = "03_herb_hunter.png"
    "jose_delgado.png" = "03_jose_delgado.png"
    "molly_stark.png" = "03_molly_stark.png"
    "pat_brennan.png" = "03_pat_brennan.png"
    "pixie_pete.png" = "03_pixie_pete.png"
    "sean_mallory.png" = "03_sean_mallory.png"
    "tequila_joe.png" = "03_tequila_joe.png"
    "vera_custer.png" = "03_vera_custer.png"
}

# Role cards
$roleCards = @{
    "outlaw.png" = "03_fuorilegge.png"
    "renegade.png" = "03_rinnegato.png"
    "sheriff.png" = "03_sceriffo.png"
    "deputy.png" = "03_vice.png"
}

# Download equipment cards
Write-Host "Downloading equipment cards..." -ForegroundColor Yellow
foreach ($card in $equipmentCards.GetEnumerator()) {
    $url = $baseUrl + $card.Value
    $output = $cardDir + $card.Key
    Write-Host "  Downloading $($card.Key)..." -NoNewline
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -ErrorAction Stop
        Write-Host " ✓" -ForegroundColor Green
    } catch {
        Write-Host " ✗ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Download action cards
Write-Host "Downloading action cards..." -ForegroundColor Yellow
foreach ($card in $actionCards.GetEnumerator()) {
    $url = $baseUrl + $card.Value
    $output = $cardDir + $card.Key
    Write-Host "  Downloading $($card.Key)..." -NoNewline
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -ErrorAction Stop
        Write-Host " ✓" -ForegroundColor Green
    } catch {
        Write-Host " ✗ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Download character cards
Write-Host "Downloading character cards..." -ForegroundColor Yellow
foreach ($card in $characterCards.GetEnumerator()) {
    $url = $baseUrl + $card.Value
    $output = $charDir + $card.Key
    Write-Host "  Downloading $($card.Key)..." -NoNewline
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -ErrorAction Stop
        Write-Host " ✓" -ForegroundColor Green
    } catch {
        Write-Host " ✗ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# Download role cards
Write-Host "Downloading role cards..." -ForegroundColor Yellow
foreach ($card in $roleCards.GetEnumerator()) {
    $url = $baseUrl + $card.Value
    $output = $cardDir + $card.Key
    Write-Host "  Downloading $($card.Key)..." -NoNewline
    try {
        Invoke-WebRequest -Uri $url -OutFile $output -ErrorAction Stop
        Write-Host " ✓" -ForegroundColor Green
    } catch {
        Write-Host " ✗ Failed: $($_.Exception.Message)" -ForegroundColor Red
    }
}

Write-Host "`nDownload complete! All official Dodge City images downloaded." -ForegroundColor Green
Write-Host "Equipment cards: $($equipmentCards.Count)" -ForegroundColor Cyan
Write-Host "Action cards: $($actionCards.Count)" -ForegroundColor Cyan
Write-Host "Character cards: $($characterCards.Count)" -ForegroundColor Cyan
Write-Host "Role cards: $($roleCards.Count)" -ForegroundColor Cyan
Write-Host "Total: $($equipmentCards.Count + $actionCards.Count + $characterCards.Count + $roleCards.Count) images" -ForegroundColor Cyan
