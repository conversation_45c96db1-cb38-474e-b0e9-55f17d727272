.bang-game {
    max-width: 100%;
    padding: 10px;
    font-family: 'Western', sans-serif;
    background-color: #f5e8c0;
    color:#000;
    min-height: 96vh;
  }
  
  .game-header {
    text-align: center;
    margin-bottom: 20px;
    padding: 20px;
    background: linear-gradient(135deg, #f5e8c0 0%, #e8d5a0 100%);
    border-radius: 15px;
    border: 3px solid #8b4513;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: all 0.3s ease-out;
  }

  /* Hide game header when game is in progress */
  .game-header.hidden {
    display: none;
  }

  .game-header h1 {
    margin: 0 0 10px 0;
    color: #8b4513;
    font-size: 2.5em;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
    font-family: 'Georgia', serif;
  }

  .game-subtitle {
    margin: 0;
    color: #666;
    font-style: italic;
    font-size: 1.1em;
  }
  
  .message-box {
    background: linear-gradient(135deg, #fff 0%, #f8f8f8 100%);
    border: 3px solid #8b4513;
    padding: 15px 20px;
    margin: 10px 20px;
    border-radius: 10px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    color: #333;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    z-index: 100;
    position: relative;
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    
  }

  .message-box:empty {
    display: none;
  }

  /* Top UI container for message box and actions */
  .top-ui-container {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 20px;
    margin: 10px auto;
    max-width: 90%;
    z-index: 30;
  }

  /* Ensure message box is visible during gameplay */
  .bang-game:not(.setup-mode) .message-box {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    color: #fff;
    border-color: #FFD700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    margin: 0;
    flex-shrink: 0;
  }

  /* Message box specifically in the playing state */
  .message-box-playing {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: 3px solid #FFD700;
    padding: 15px 20px;
    border-radius: 10px;
    text-align: center;
    font-weight: bold;
    font-size: 16px;
    color: #fff;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    min-height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    margin: 0;
    flex-shrink: 0;
  }

  /* Actions buttons positioned to the right of message box */
  .actions-right {
    display: flex;
    gap: 10px;
    flex-shrink: 0;
  }

  .actions-right button {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    color: white;
    border: 2px solid #654321;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    white-space: nowrap;
  }

  .actions-right button:hover:not(.disabled) {
    background: linear-gradient(135deg, #A0522D 0%, #8B4513 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
  }

  .actions-right button.disabled {
    background: linear-gradient(135deg, #666 0%, #555 100%);
    color: #999;
    cursor: not-allowed;
    border-color: #444;
  }
  
  .setup-screen {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin-top: 20px;
    overflow-y: hidden;
  }
  
  .player-count {
    margin: 20px 0;
  }
  
  .start-button {
    background-color: #8b4513;
    color: #000;
    padding: 10px 20px;
    border: none;
    border-radius: 5px;
    font-size: 18px;
    cursor: pointer;
  }
  
  .game-board {
    display: flex;
    flex-direction: column;
    min-height: calc(100vh - 60px);
    max-height: calc(100vh - 60px);
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 50%, #654321 100%);
    padding: 20px;
    position: relative;
    overflow-y: auto;
  }

  /* Adjust game board height when header is visible (setup mode) */
  .bang-game.setup-mode .game-board {
    min-height: calc(100vh - 180px);
    max-height: calc(100vh - 180px);
  }

  /* Other players area at bottom */
  .other-players-area {
    display: flex;
    flex-direction: row;
    gap: 12px;
    margin-top: 20px;
    padding: 15px;
    background: rgba(0,0,0,0.1);
    border-radius: 10px;
    max-height: 35vh;
    overflow-y: auto;
    flex-wrap: wrap;
    justify-content: center;
  }

  .other-player {
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border: 2px solid #8B4513;
    border-radius: 10px;
    padding: 10px;
    margin: 5px;
    max-width: 100%;
    box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    color: #fff;
  }

  .other-player.dead-player {
    opacity: 0.4;
    filter: grayscale(100%);
  }

  .player-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
    margin-bottom: 10px;
    width: 100%;
  }

  /* Character image in other players area - smaller for vertical layout */
  .other-player .character-image-container {
    position: relative;
    display: inline-block;
  }

  .other-player .character-image {
    width: 60px;
    height: 95px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #8b4513;
    flex-shrink: 0;
    box-shadow: 0 2px 4px rgba(0,0,0,0.6);
    display: block;
  }

  .other-player .player-name-overlay {
    position: absolute;
    bottom: 25px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: #FFD700;
    padding: 2px 4px;
    border-radius: 6px;
    font-size: 8px;
    font-weight: bold;
    border: 1px solid #FFD700;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    z-index: 10;
    white-space: nowrap;
    backdrop-filter: blur(2px);
    max-width: 50px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .other-player .health-display-overlay {
    position: absolute;
    bottom: 3px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: #ff6b6b;
    padding: 2px 6px;
    border-radius: 8px;
    font-size: 10px;
    font-weight: bold;
    border: 1px solid #ff6b6b;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    z-index: 10;
    white-space: nowrap;
    backdrop-filter: blur(2px);
  }

  .player-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    min-width: 0;
  }

  .player-details h4 {
    margin: 0 0 5px 0;
    font-size: 16px;
    color: #fff;
    font-weight: bold;
  }

  .health-display, .cards-count {
    font-size: 12px;
    color: #ccc;
    margin: 2px 0;
  }

  .other-player-equipment {
    display: flex;
    gap: 5px;
    flex-wrap: wrap;
    margin-top: 8px;
    justify-content: flex-start;
  }

  .small-card {
    width: 30px;
    height: 42px;
    border-radius: 3px;
    overflow: hidden;
  }

  .small-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Central game area */
  .central-area {
    flex: 1;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 20px 0;
    min-height: 140px;
  }

  .game-info {
    display: flex;
    gap: 60px;
    align-items: center;
  }

  .deck-info, .discard-info {
    text-align: center;
  }

  .deck-info h4, .discard-info h4 {
    margin: 0 0 10px 0;
    color: #fff;
    font-size: 16px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  }

  .deck-card, .discard-card {
    width: 100px;
    height: 160px;
    border: 3px solid #8B4513;
    border-radius: 10px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 4px 8px rgba(0,0,0,0.6);
  }

  /* Empty discard pile styling */
  .discard-card.empty {
    opacity: 0.4;
    background-color: #f5f5f5;
    border-color: #ccc;
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
  }

  .deck-image, .discard-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Current player area at top */
  .current-player-area {
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 100%);
    border: 3px solid #8B4513;
    border-radius: 15px;
    padding: 15px;
    margin-bottom: 15px;
    color: #fff;
    box-shadow: 0 4px 12px rgba(0,0,0,0.8);
    flex-shrink: 0;
    overflow-y: auto;
    transition: all 0.3s ease;
  }

  .current-player-area.disabled {
    background: linear-gradient(135deg, #3a3a3a 0%, #4a4a4a 100%);
    border-color: #666;
    opacity: 0.6;
    box-shadow: 0 2px 8px rgba(0,0,0,0.5);
  }

  .current-player-area.disabled .hand-card {
    opacity: 0.5;
    filter: grayscale(30%);
    pointer-events: none;
  }

  .current-player-area.disabled .equipment-card {
    opacity: 0.5;
    filter: grayscale(30%);
  }

  .current-player-area.disabled button {
    opacity: 0.5;
    pointer-events: none;
    filter: grayscale(50%);
  }

  .current-player-area.disabled .player-name {
    color: #999;
  }

  .current-player {
    display: flex;
    flex-direction: row;
    gap: 20px;
  }

  .player-status {
    display: flex;
    gap: 20px;
    align-items: flex-start;
  }

  .character-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
  }

  .character-image-container {
    position: relative;
    display: inline-block;
  }

  .character-image {
    width: 100px;
    height: 160px;
    object-fit: cover;
    border-radius: 10px;
    border: 3px solid #8b4513;
    flex-shrink: 0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    display: block;
  }

  .player-name-overlay {
    position: absolute;
    bottom: 35px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #FFD700;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    border: 2px solid #FFD700;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
    z-index: 10;
    white-space: nowrap;
    backdrop-filter: blur(3px);
    max-width: 90px;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  .bot-indicator-overlay {
    font-size: 10px;
    margin-left: 2px;
  }

  .health-display-overlay {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: #ff6b6b;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: bold;
    border: 2px solid #ff6b6b;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
    z-index: 10;
    white-space: nowrap;
    backdrop-filter: blur(3px);
  }

  .role-display-overlay {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.9);
    color: #e74c3c;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: bold;
    border: 2px solid #e74c3c;
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.6);
    z-index: 10;
    white-space: nowrap;
    backdrop-filter: blur(3px);
    animation: roleReveal 0.5s ease-out;
  }

  @keyframes roleReveal {
    0% {
      opacity: 0;
      transform: translateX(-50%) scale(0.8);
    }
    100% {
      opacity: 1;
      transform: translateX(-50%) scale(1);
    }
  }

  .role-display-below {
    background: rgba(0, 0, 0, 0.9);
    color: #e74c3c;
    padding: 3px 8px;
    border-radius: 8px;
    font-size: 11px;
    font-weight: bold;
    border: 1px solid #e74c3c;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.6);
    text-align: center;
    margin-top: 4px;
    animation: roleReveal 0.5s ease-out;
    backdrop-filter: blur(3px);
  }

  .character-details {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-align: center;
    width: 100%;
    min-width: 0;
  }

  .character-details h3 {
    margin: 0 0 10px 0;
    color: #fff;
    font-size: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  }

  .role-display {
    display: flex;
    align-items: center;
    gap: 8px;
    margin: 8px 0;
    font-weight: bold;
    color: #fff;
  }

  .role-image {
    width: 40px;
    height: 56px;
    object-fit: cover;
    border-radius: 6px;
    border: 2px solid #8B4513;
  }

  .health-display {
    font-weight: bold;
    color: #ff6b6b;
    margin: 8px 0;
    font-size: 16px;
    display: flex;
    align-items: center;
    gap: 4px;
  }

  /* Current player health display */
  .current-player .health-display {
    font-size: 18px;
    margin: 10px 0;
  }

  /* Other players health display */
  .other-players .health-display {
    font-size: 14px;
  }

  .ability-text {
    font-size: 14px;
    color: #ccc;
    font-style: italic;
    line-height: 1.4;
    margin-top: 10px;
  }

  .equipment-area {
    
    padding-top: 15px;
    border-top: 2px solid #8B4513;
  }

  .equipment-area h4 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 18px;
    text-align: center;
  }

  .equipment-cards {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
    padding: 10px;
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
  }

  .equipment-card {
    width: 60px;
    height: 96px;
    border-radius: 6px;
    overflow: hidden;
    border: 2px solid #8B4513;
    box-shadow: 0 2px 4px rgba(0,0,0,0.4);
  }

  .equipment-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  /* Hand area */
  .hand-area {
    border-top: 2px solid #8B4513;
    padding-top: 15px;
  }

  .hand-area h4 {
    margin: 0 0 15px 0;
    color: #fff;
    font-size: 18px;
    text-align: center;
  }

  .hand-cards {
    display: flex;
    justify-content: center;
    gap: 8px;
    flex-wrap: wrap;
    max-height: 200px;
    overflow-y: auto;
    padding: 10px;
    background: rgba(0,0,0,0.3);
    border-radius: 10px;
  }

  .hand-cards:empty {
    display: none;
  }
  .hand-card {
    width: 80px;
    height: 128px;
    border-radius: 8px;
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    border: 2px solid #8B4513;
    box-shadow: 0 2px 4px rgba(0,0,0,0.4);
    touch-action: manipulation; /* Prevent default touch behaviors */
  }

  .hand-card:hover {
    transform: translateY(-10px) scale(1.05);
    box-shadow: 0 8px 16px rgba(0,0,0,0.8);
    z-index: 10;
  }

  .hand-card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }

  .card-fallback {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    width: 100%;
    background: #f8d775;
    padding: 5px;
    text-align: center;
  }

  .card-name {
    font-weight: bold;
    font-size: 11px;
    color: #8b4513;
  }

  .card-suit {
    font-size: 9px;
    color: #666;
    margin-top: 2px;
  }

  /* Actions in current player area */
  .hand-area .actions {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #8B4513;
  }

  .hand-area .actions button {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    color: #fff;
    padding: 12px 20px;
    border: 2px solid #654321;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    transition: all 0.2s ease;
  }

  .hand-area .actions button:hover {
    background: linear-gradient(135deg, #A0522D 0%, #8B4513 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.6);
  }

  .hand-area .actions button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0,0,0,0.4);
  }

  .hand-area .actions button.disabled {
    background: linear-gradient(135deg, #666 0%, #555 100%);
    color: #999;
    cursor: not-allowed;
    opacity: 0.6;
  }

  .hand-area .actions button.disabled:hover {
    background: linear-gradient(135deg, #666 0%, #555 100%);
    transform: none;
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
  }



  /* Bot indicator styling */
  .bot-indicator {
    color: #4CAF50;
    font-size: 16px;
  }

  .bot-turn-indicator {
    text-align: center;
    padding-top: 5px;
    background: rgba(76, 175, 80, 0.2);
    border: 2px solid #4CAF50;
    border-radius: 10px;
    margin: 0px;
  }

  .bot-turn-indicator h4 {
    margin: 0 0 10px 0;
    color: #4CAF50;
    font-size: 18px;
  }

  .bot-turn-indicator p {
    margin: 0;
    color: #fff;
    font-size: 14px;
  }

  .bot-info {
    text-align: center;
    color: #8b4513;
    font-style: italic;
    margin: 15px 0;
    font-size: 16px;
  }

  /* Setup form styling */
  .setup-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
    margin: 20px 0;
  }

  .game-mode,
  .player-count {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .game-mode label,
  .player-count label {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }

  .game-mode select,
  .player-count select {
    padding: 10px 15px;
    border: 2px solid #8B4513;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 16px;
    text-align: center;
    min-width: 250px;
    transition: all 0.2s ease;
  }

  .game-mode select:focus,
  .player-count select:focus {
    outline: none;
    border-color: #A0522D;
    box-shadow: 0 0 10px rgba(160, 82, 45, 0.5);
    background: #fff;
  }

  .game-mode select:disabled,
  .player-count select:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  .human-players {
    display: flex;
    flex-direction: column;
    gap: 15px;
    margin: 10px 0;
  }

  .player-name {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 10px;
  }

  .player-name label {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
  }

  .player-name input {
    padding: 10px 15px;
    border: 2px solid #8B4513;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.9);
    color: #333;
    font-size: 16px;
    text-align: center;
    width: 200px;
    transition: all 0.2s ease;
  }

  .player-name input:focus {
    outline: none;
    border-color: #A0522D;
    box-shadow: 0 0 10px rgba(160, 82, 45, 0.5);
    background: #fff;
  }

  .player-name input::placeholder {
    color: #999;
    font-style: italic;
  }

  /* Player name display styling */
  .character-name {
    font-size: 12px;
    color: #ccc;
    font-style: italic;
    margin-top: 2px;
  }

  .character-name-current {
    font-size: 14px;
    color: #ccc;
    font-style: italic;
    margin-top: 5px;
  }

  .bot-indicator-small {
    color: #4CAF50;
    font-size: 12px;
  }

  /* Sheriff badge styling - below name variants */
  .sheriff-badge-below,
  .sheriff-badge-current,
  .sheriff-badge-result {
    color: #FFD700;
    font-size: 11px;
    font-weight: bold;
    background: rgba(255, 215, 0, 0.2);
    padding: 3px 8px;
    border-radius: 6px;
    border: 1px solid #FFD700;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    animation: sheriffGlow 2s ease-in-out infinite alternate;
    display: inline-block;
    margin: 2px 0;
  }

  /* Specific sizing for different contexts */
  .sheriff-badge-below {
    font-size: 10px;
    padding: 2px 6px;
  }

  .sheriff-badge-current {
    font-size: 12px;
    padding: 3px 8px;
    margin: 3px 0;
  }

  .sheriff-badge-result {
    font-size: 11px;
    padding: 2px 7px;
    margin: 2px 0;
  }

  .sheriff-badge-target {
    font-size: 9px;
    padding: 1px 4px;
    margin-left: 5px;
  }

  @keyframes sheriffGlow {
    from {
      box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }
    to {
      box-shadow: 0 0 15px rgba(255, 215, 0, 0.8);
    }
  }

  /* Discard selection modal */
  .discard-selection {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    border: 3px solid #8B4513;
    border-radius: 15px;
    padding: 30px;
    z-index: 1000;
    max-width: 90vw;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 8px 32px rgba(0,0,0,0.8);
  }

  .discard-selection h3 {
    margin: 0 0 15px 0;
    color: #fff;
    text-align: center;
    font-size: 20px;
  }

  .discard-selection p {
    margin: 0 0 20px 0;
    color: #ccc;
    text-align: center;
    font-size: 16px;
  }

  .discard-cards {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
    margin-bottom: 20px;
  }

  .discard-card {
    position: relative;
    width: 100px;
    height: 160px;
    border-radius: 10px;
    overflow: hidden;
    cursor: pointer;
    transition: all 0.2s ease;
    border: 3px solid #8B4513;
    box-shadow: 0 4px 8px rgba(0,0,0,0.6);
  }

  .discard-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 6px 12px rgba(0,0,0,0.6);
  }

  .discard-card.selected {
    border-color: #ff6b6b;
    box-shadow: 0 0 15px rgba(255, 107, 107, 0.6);
    transform: translateY(-5px);
  }

  .discard-card-image {
    width: 100%;
    height: 85%;
    object-fit: cover;
  }

  .discard-card .card-name {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0,0,0,0.8);
    color: #fff;
    font-size: 12px;
    padding: 3px;
    text-align: center;
  }

  .selected-indicator {
    position: absolute;
    top: 5px;
    right: 5px;
    background: #ff6b6b;
    color: #fff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 12px;
    font-weight: bold;
  }

  .discard-actions {
    text-align: center;
  }

  .discard-actions button {
    background: linear-gradient(135deg, #8B4513 0%, #A0522D 100%);
    color: #fff;
    padding: 12px 25px;
    border: 2px solid #654321;
    border-radius: 8px;
    cursor: pointer;
    font-size: 16px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
    transition: all 0.2s ease;
  }

  .discard-actions button:hover:not(.disabled) {
    background: linear-gradient(135deg, #A0522D 0%, #8B4513 100%);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0,0,0,0.6);
  }

  .discard-actions button.disabled {
    background: linear-gradient(135deg, #666 0%, #555 100%);
    color: #999;
    cursor: not-allowed;
    opacity: 0.6;
  }

  /* Hand limit indicator */
  .hand-limit-info {
    font-size: 14px;
    color: #ccc;
    font-weight: normal;
  }

  .over-limit {
    color: #ff6b6b;
    font-weight: bold;
    animation: pulse 1.5s infinite;
  }

  @keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.6; }
    100% { opacity: 1; }
  }

  /* Duplicate equipment indicator */
  .hand-card.duplicate-equipment {
    border: 2px solid #ff9800;
    box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
    opacity: 0.7;
  }

  .hand-card.duplicate-equipment::after {
    content: "DUPLICATE";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    background: rgba(255, 152, 0, 0.9);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    pointer-events: none;
    z-index: 10;
  }

  .hand-card.duplicate-equipment:hover {
    transform: translateY(-5px) scale(1.05);
    border-color: #f57c00;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.7);
  }

  /* Equipment rule text */
  .equipment-rule {
    color: #ff9800;
    font-size: 12px;
    font-style: italic;
    margin: 5px 0 0 0;
  }

  /* BANG! limit reached indicator */
  .hand-card.bang-limit-reached {
    border: 2px solid #f44336;
    box-shadow: 0 0 10px rgba(244, 67, 54, 0.5);
    opacity: 0.6;
  }

  .hand-card.bang-limit-reached::after {
    content: "LIMIT REACHED";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    background: rgba(244, 67, 54, 0.9);
    color: #fff;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 9px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    pointer-events: none;
    z-index: 10;
  }

  .hand-card.bang-limit-reached:hover {
    transform: translateY(-5px) scale(1.05);
    border-color: #d32f2f;
    box-shadow: 0 6px 20px rgba(244, 67, 54, 0.7);
  }

  /* Beer unplayable indicator (at max health) */
  .hand-card.beer-unplayable {
    border: 2px solid #ff9800;
    box-shadow: 0 0 10px rgba(255, 152, 0, 0.5);
    opacity: 0.6;
  }

  .hand-card.beer-unplayable::after {
    content: "MAX HEALTH";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%) rotate(-15deg);
    background: rgba(255, 152, 0, 0.9);
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    pointer-events: none;
    z-index: 10;
  }

  .hand-card.beer-unplayable:hover {
    transform: translateY(-5px) scale(1.05);
    border-color: #f57c00;
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.7);
  }

  /* Jail indicator for players already in jail */
  .jail-indicator {
    color: #ff5722;
    font-weight: bold;
    font-size: 12px;
  }

  /* Invalid target styling */
  .target-option.invalid-target {
    opacity: 0.5;
    border: 2px solid #f44336;
    background: linear-gradient(135deg, #ffebee, #ffcdd2);
    cursor: not-allowed;
  }

  .target-option.invalid-target:hover {
    transform: none;
    box-shadow: 0 2px 4px rgba(244, 67, 54, 0.3);
  }

  /* BANG! counter display */
  .bang-counter {
    color: #fff;
    font-size: 12px;
    margin: 5px 0 0 0;
    font-weight: bold;
  }

  .bang-limit-warning {
    color: #f44336;
    animation: pulse 1.5s infinite;
  }

  .bang-available {
    color: #4CAF50;
  }

  .bang-unlimited {
    color: #2196F3;
    font-weight: bold;
  }

  .card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    z-index: 10;
  }

  .card-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    border-radius: 5px;
  }

  .card-fallback {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    height: 100%;
    width: 100%;
  }

  .card-name {
    font-weight: bold;
    font-size: 11px;
    color: #8b4513;
  }

  .card-suit {
    font-size: 9px;
    color: #666;
    margin-top: 2px;
  }

  /* Responsive design for vertical layout */
  @media (max-width: 1200px) {
    .other-players-area {
      max-height: 40vh;
      gap: 10px;
    }

    .other-player {
      padding: 10px;
    }

    .other-player .character-image {
      width: 50px;
      height: 80px;
    }

    .player-details h4 {
      font-size: 14px;
    }
  }

  @media (max-width: 768px) {
    .game-board {
      padding: 15px;
      margin-top:20px;
    }

    .other-players-area {
      max-height: 35vh;
      gap: 8px;
      padding: 10px;
    }

    .other-player {
      padding: 8px;
    }

    .other-player .character-image {
      width: 45px;
      height: 72px;
    }

    .player-details h4 {
      font-size: 13px;
    }

    .health-display, .cards-count {
      font-size: 11px;
    }

    .current-player-area {
      padding: 15px;
    }

    .player-status {
      flex-direction: column;
      align-items: center;
      text-align: center;
    }
  }

  @media (max-width: 600px) {
    .hand-cards {
      gap: 5px;
    }

    .hand-card {
      width: 60px;
      height: 96px;
    }

    .deck-card, .discard-card {
      width: 80px;
      height: 128px;
    }

    .game-info {
      gap: 40px;
    }

    .hand-area .actions {
      flex-direction: column;
      gap: 10px;
    }

    .hand-area .actions button {
      padding: 10px 15px;
      font-size: 12px;
    }
  }

  /* Card preview panel */
  .card-preview {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: white;
    border: 2px solid #8b4513;
    border-radius: 10px;
    padding: 15px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    z-index: 1000;
    max-width: 200px;
  }

  .preview-image {
    width: 100%;
    height: auto;
    border-radius: 8px;
    margin-bottom: 10px;
  }

  .preview-info h4 {
    margin: 0 0 5px 0;
    color: #8b4513;
    font-size: 14px;
  }

  .preview-info p {
    margin: 0;
    color: #666;
    font-size: 12px;
  }

  @media (max-width: 768px) {
    .card-preview {
      top: 10px;
      right: 10px;
      max-width: 150px;
      padding: 10px;
    }
  }


  
  .game-over {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a 0%, #2a2a2a 50%, #1a1a1a 100%);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    color: #f8d775;
    z-index: 1000;
    padding: 20px;
    box-sizing: border-box;
  }

  .game-result-header {
    text-align: center;
    margin-bottom: 40px;
    background: linear-gradient(135deg, #8B4513, #A0522D);
    border: 3px solid #D2691E;
    border-radius: 20px;
    padding: 30px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
    backdrop-filter: blur(10px);
  }

  .victory-title {
    color: #FFD700;
    font-size: 48px;
    margin: 0 0 15px 0;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);
    animation: victoryGlow 2s ease-in-out infinite alternate;
    font-family: 'Georgia', serif;
    font-weight: bold;
  }

  .victory-subtitle {
    color: #f8d775;
    font-size: 24px;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
    font-style: italic;
  }

  .defeat-title {
    color: #DC143C;
    font-size: 48px;
    margin: 0 0 15px 0;
    text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), 0 0 20px rgba(220, 20, 60, 0.5);
    animation: defeatPulse 1.5s ease-in-out infinite;
    font-family: 'Georgia', serif;
    font-weight: bold;
  }

  .defeat-subtitle {
    color: #f8d775;
    font-size: 24px;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
    font-style: italic;
  }

  @keyframes victoryGlow {
    0% {
      text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), 0 0 20px rgba(255, 215, 0, 0.5);
    }
    100% {
      text-shadow: 3px 3px 6px rgba(0, 0, 0, 0.8), 0 0 30px rgba(255, 215, 0, 0.8);
    }
  }

  @keyframes defeatPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.02);
      opacity: 0.95;
    }
  }
  
  .final-roles {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 30px 0;
    max-width: 1000px;
    width: 100%;
  }

  .player-result {
    border: 3px solid #8B4513;
    border-radius: 15px;
    padding: 20px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 15px;
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.4);
    transition: transform 0.2s ease;
  }

  .player-result:hover {
    transform: translateY(-5px);
  }

  .character-image-result {
    width: 80px;
    height: 125px;
    object-fit: cover;
    border-radius: 8px;
    border: 2px solid #8b4513;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
  }

  .player-result.dead-player {
    background: linear-gradient(135deg, #666, #888);
    border-color: #555;
  }

  .player-result.dead-player .character-image-result {
    opacity: 0.6;
    filter: grayscale(100%);
  }

  .player-result.dead-player h4,
  .player-result.dead-player p {
    color: #ccc;
  }
  
  .restart-button {
    background: linear-gradient(135deg, #8B4513, #A0522D);
    color: #f8d775;
    padding: 15px 30px;
    border: 3px solid #D2691E;
    border-radius: 10px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    margin-top: 20px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    transition: all 0.3s ease;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.6);
  }

  .restart-button:hover {
    background: linear-gradient(135deg, #A0522D, #CD853F);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
  }
  
  .target-selection {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff;
    border: 2px solid #8b4513;
    border-radius: 5px;
    padding: 20px;
    z-index: 100;
    width: 80%;
    max-width: 300px;
  }
  
  .target-options {
    display: flex;
    flex-direction: column;
    gap: 10px;
    margin: 15px 0;
  }
  
  .target-option {
    background-color: #f8d775;
    padding: 10px;
    border-radius: 5px;
    cursor: pointer;
    text-align: center;
    border: 1px solid #8b4513;
  }

  .target-option:hover {
    background-color: #f0c040;
    transform: translateY(-1px);
  }

  .target-player-name {
    font-size: 16px;
    font-weight: bold;
    color: #333;
    margin-bottom: 3px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-wrap: wrap;
  }

  .target-character-name {
    font-size: 13px;
    color: #666;
    font-style: italic;
    margin-bottom: 5px;
  }

  .target-info {
    font-size: 12px;
    color: #666;
    margin-top: 5px;
  }

  /* Modal overlay styles */
  .modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(3px);
  }

  /* Card zoom/preview modal styling */
  .card-zoom-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
    backdrop-filter: blur(5px);
  }

  .card-zoom-modal {
    background: linear-gradient(135deg, #8B4513, #A0522D);
    border-radius: 15px;
    border: 3px solid #D2691E;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    max-width: 90%;
    max-height: 90%;
    overflow: hidden;
    animation: zoomIn 0.3s ease-out;
    padding: 20px;
  }

  @keyframes zoomIn {
    from {
      transform: scale(0.7);
      opacity: 0;
    }
    to {
      transform: scale(1);
      opacity: 1;
    }
  }

  .card-zoom-header {
    background: rgba(0, 0, 0, 0.3);
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 2px solid #D2691E;
  }

  .card-zoom-header h3 {
    color: #FFD700;
    margin: 0;
    font-size: 20px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  }

  .close-preview {
    background: #DC143C;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
  }

  .close-preview:hover {
    background: #B22222;
    transform: scale(1.1);
  }

  .card-zoom-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 20px;
    text-align: center;
  }

  .card-zoom-image {
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .zoomed-card-image {
    width: 250px;
    height: auto;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    transition: transform 0.2s ease;
  }

  .zoomed-card-image:hover {
    transform: scale(1.05);
  }

  .card-zoom-fallback {
    width: 200px;
    height: 280px;
    background: linear-gradient(135deg, #f8d775, #f0c040);
    border: 2px solid #8b4513;
    border-radius: 10px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  }

  .zoomed-card-name {
    font-size: 18px;
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    padding: 0 10px;
  }

  .zoomed-card-suit {
    font-size: 16px;
    color: #666;
  }

  .card-zoom-details {
    flex: 1;
    color: white;
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .card-zoom-details h4 {
    color: #FFD700;
    font-size: 24px;
    margin: 0;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    border-bottom: 2px solid #FFD700;
    padding-bottom: 10px;
  }

  .card-suit-value {
    font-size: 18px;
    color: #DDD;
    margin: 0;
    font-style: italic;
  }

  .card-description {
    background: rgba(0, 0, 0, 0.4);
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #FFD700;
    font-size: 18px;
    line-height: 1.6;
    color: #FFF;
    max-width: 400px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
  }

  /* Death modal styling */
  .death-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 2000;
    backdrop-filter: blur(5px);
  }

  .death-modal {
    background: linear-gradient(135deg, #2a1810, #3a2520);
    border: 3px solid #8B4513;
    border-radius: 20px;
    box-shadow: 0 15px 40px rgba(0, 0, 0, 0.8);
    max-width: 500px;
    width: 90%;
    animation: deathModalAppear 0.5s ease-out;
  }

  @keyframes deathModalAppear {
    0% {
      transform: scale(0.8) translateY(-50px);
      opacity: 0;
    }
    100% {
      transform: scale(1) translateY(0);
      opacity: 1;
    }
  }

  .death-modal-content {
    padding: 30px;
    text-align: center;
    color: #FFF;
  }

  .death-modal-content h2 {
    color: #FF6B6B;
    font-size: 28px;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  }

  .death-modal-content p {
    font-size: 16px;
    margin-bottom: 10px;
    color: #DDD;
    line-height: 1.5;
  }

  .death-modal-buttons {
    display: flex;
    gap: 20px;
    justify-content: center;
    margin-top: 25px;
  }

  .spectate-button,
  .exit-button {
    padding: 12px 24px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 150px;
  }

  .spectate-button {
    background: linear-gradient(135deg, #4A90E2, #357ABD);
    color: white;
    border: 2px solid #357ABD;
  }

  .spectate-button:hover {
    background: linear-gradient(135deg, #357ABD, #2E6BA8);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(74, 144, 226, 0.4);
  }

  .exit-button {
    background: linear-gradient(135deg, #E74C3C, #C0392B);
    color: white;
    border: 2px solid #C0392B;
  }

  .exit-button:hover {
    background: linear-gradient(135deg, #C0392B, #A93226);
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(231, 76, 60, 0.4);
  }

  /* Spectator mode styling */
  .spectator-mode {
    background: linear-gradient(135deg, #1a1a2e, #16213e);
    border: 3px solid #4A90E2;
    border-radius: 15px;
    padding: 20px;
    margin-bottom: 15px;
    color: #fff;
    box-shadow: 0 4px 12px rgba(74, 144, 226, 0.3);
    text-align: center;
  }

  .spectator-info h3 {
    color: #4A90E2;
    font-size: 20px;
    margin-bottom: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
  }

  .spectator-info p {
    font-size: 14px;
    margin-bottom: 8px;
    color: #DDD;
    line-height: 1.4;
  }

  .exit-spectator-button {
    background: linear-gradient(135deg, #E74C3C, #C0392B);
    color: white;
    border: 2px solid #C0392B;
    border-radius: 8px;
    padding: 10px 20px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-top: 15px;
  }

  .exit-spectator-button:hover {
    background: linear-gradient(135deg, #C0392B, #A93226);
    transform: translateY(-1px);
    box-shadow: 0 3px 10px rgba(231, 76, 60, 0.4);
  }

  /* Turn timer styling */
  .turn-timer {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 2px solid #e74c3c;
    border-radius: 10px;
    padding: 8px 16px;
    color: #fff;
    font-weight: bold;
    font-size: 16px;
    text-align: center;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    animation: timerPulse 2s ease-in-out infinite;
    margin: 0 auto;
    display: block;
    width: fit-content;
  }

  .turn-timer.warning {
    border-color: #f39c12;
    background: linear-gradient(135deg, #e67e22, #d35400);
    animation: timerWarning 1s ease-in-out infinite;
  }

  .turn-timer.critical {
    border-color: #e74c3c;
    background: linear-gradient(135deg, #c0392b, #a93226);
    animation: timerCritical 0.5s ease-in-out infinite;
  }

  @keyframes timerPulse {
    0%, 100% {
      box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
    }
    50% {
      box-shadow: 0 6px 16px rgba(231, 76, 60, 0.5);
    }
  }

  @keyframes timerWarning {
    0%, 100% {
      box-shadow: 0 4px 12px rgba(243, 156, 18, 0.5);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 6px 16px rgba(243, 156, 18, 0.7);
      transform: scale(1.02);
    }
  }

  @keyframes timerCritical {
    0%, 100% {
      box-shadow: 0 4px 12px rgba(231, 76, 60, 0.7);
      transform: scale(1);
    }
    50% {
      box-shadow: 0 8px 20px rgba(231, 76, 60, 0.9);
      transform: scale(1.05);
    }
  }

  /* Ability Choice Modal */
  .ability-choice-modal {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid #e74c3c;
    border-radius: 15px;
    padding: 25px;
    color: #fff;
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
    animation: modalSlideIn 0.3s ease-out;
  }

  .ability-choice-modal h3 {
    margin: 0 0 15px 0;
    color: #e74c3c;
    font-size: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .ability-choice-modal p {
    margin: 0 0 20px 0;
    font-size: 16px;
    line-height: 1.4;
  }

  .ability-choice-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
  }

  .ability-yes-button, .ability-no-button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
  }

  .ability-yes-button {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
  }

  .ability-yes-button:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(46, 204, 113, 0.4);
  }

  .ability-no-button {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
  }

  .ability-no-button:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
  }

  @keyframes modalSlideIn {
    from {
      opacity: 0;
      transform: translateY(-50px) scale(0.9);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  /* Missed Choice Modal */
  .missed-choice-modal {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid #e74c3c;
    border-radius: 15px;
    padding: 25px;
    color: #fff;
    text-align: center;
    max-width: 450px;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
    animation: modalSlideIn 0.3s ease-out;
  }

  .missed-choice-modal h3 {
    margin: 0 0 15px 0;
    color: #e74c3c;
    font-size: 20px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .missed-choice-modal p {
    margin: 0 0 15px 0;
    font-size: 16px;
    line-height: 1.4;
  }

  .missed-choice-info {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    border-left: 4px solid #e74c3c;
  }

  .missed-choice-info p {
    margin: 5px 0;
    font-size: 14px;
    color: #ecf0f1;
  }

  .missed-choice-buttons {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 20px;
  }

  .missed-yes-button, .missed-no-button {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 120px;
  }

  .missed-yes-button {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    color: white;
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
  }

  .missed-no-button {
    background: linear-gradient(135deg, #e74c3c, #c0392b);
    color: white;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3);
  }

  .missed-yes-button:hover {
    background: linear-gradient(135deg, #229954, #27ae60);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(46, 204, 113, 0.4);
  }

  .missed-no-button:hover {
    background: linear-gradient(135deg, #c0392b, #a93226);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4);
  }

  /* Game Menu Button */
  .game-menu-button {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    border: 2px solid #7f8c8d;
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    min-width: 40px;
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .game-menu-button:hover {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    border-color: #95a5a6;
  }

  .game-menu-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3);
  }

  /* Fixed position game menu button - always visible */
  .game-menu-button-fixed {
    position: fixed;
    bottom: 20px;
    right: 20px;
    background: linear-gradient(135deg, #34495e, #2c3e50);
    color: white;
    border: 2px solid #7f8c8d;
    border-radius: 50%;
    padding: 12px;
    font-size: 18px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 999;
    backdrop-filter: blur(5px);
  }

  .game-menu-button-fixed:hover {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    transform: translateY(-2px) scale(1.05);
    box-shadow: 0 6px 16px rgba(0, 0, 0, 0.5);
    border-color: #95a5a6;
  }

  .game-menu-button-fixed:active {
    transform: translateY(0) scale(1);
    box-shadow: 0 3px 8px rgba(0, 0, 0, 0.4);
  }

  /* Game Menu Modal */
  .game-menu-modal {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid #7f8c8d;
    border-radius: 15px;
    padding: 30px;
    color: #fff;
    text-align: center;
    max-width: 400px;
    margin: 0 auto;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
    animation: modalSlideIn 0.3s ease-out;
  }

  .game-menu-modal h3 {
    margin: 0 0 25px 0;
    color: #ecf0f1;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .game-menu-buttons {
    display: flex;
    flex-direction: column;
    gap: 15px;
  }

  .menu-button {
    padding: 15px 20px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.2s ease;
    background: linear-gradient(135deg, #3498db, #2980b9);
    color: white;
    box-shadow: 0 4px 12px rgba(52, 152, 219, 0.3);
  }

  .menu-button:hover {
    background: linear-gradient(135deg, #2980b9, #3498db);
    transform: translateY(-2px);
    box-shadow: 0 6px 16px rgba(52, 152, 219, 0.4);
  }

  .menu-button:active {
    transform: translateY(0);
    box-shadow: 0 3px 8px rgba(52, 152, 219, 0.3);
  }

  /* Specific menu button styles */
  .menu-button:nth-child(1) {
    background: linear-gradient(135deg, #f39c12, #e67e22);
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.3);
  }

  .menu-button:nth-child(1):hover {
    background: linear-gradient(135deg, #e67e22, #f39c12);
    box-shadow: 0 6px 16px rgba(243, 156, 18, 0.4);
  }

  .menu-button:nth-child(2) {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    box-shadow: 0 4px 12px rgba(46, 204, 113, 0.3);
  }

  .menu-button:nth-child(2):hover {
    background: linear-gradient(135deg, #2ecc71, #27ae60);
    box-shadow: 0 6px 16px rgba(46, 204, 113, 0.4);
  }

  .menu-button:nth-child(3) {
    background: linear-gradient(135deg, #9b59b6, #8e44ad);
    box-shadow: 0 4px 12px rgba(155, 89, 182, 0.3);
  }

  .menu-button:nth-child(3):hover {
    background: linear-gradient(135deg, #8e44ad, #9b59b6);
    box-shadow: 0 6px 16px rgba(155, 89, 182, 0.4);
  }

  .menu-close {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    box-shadow: 0 4px 12px rgba(231, 76, 60, 0.3) !important;
  }

  .menu-close:hover {
    background: linear-gradient(135deg, #c0392b, #e74c3c) !important;
    box-shadow: 0 6px 16px rgba(231, 76, 60, 0.4) !important;
  }

  /* Sound toggle button */
  .sound-toggle-button {
    background: linear-gradient(135deg, #34495e, #2c3e50);
    border: 2px solid #95a5a6;
    border-radius: 8px;
    color: #fff;
    font-size: 18px;
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .sound-toggle-button:hover {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border-color: #bdc3c7;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(52, 73, 94, 0.3);
  }

  .sound-toggle-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 6px rgba(52, 73, 94, 0.3);
  }

  /* Card animation styles */
  .animating-card {
    position: fixed;
    z-index: 1000;
    pointer-events: none;
    transition: none;
    transform-origin: center;
  }

  .animating-card .card {
    width: 60px;
    height: 84px;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid #8b4513;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    font-size: 10px;
    font-weight: bold;
    color: #8b4513;
    text-align: center;
    padding: 4px;
  }

  .animating-card .card.red {
    color: #dc3545;
    border-color: #dc3545;
  }

  .animating-card .card.black {
    color: #212529;
    border-color: #212529;
  }

  /* Card animation effects */
  @keyframes cardGlow {
    0%, 100% {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    }
    50% {
      box-shadow: 0 6px 20px rgba(231, 76, 60, 0.6);
    }
  }

  .animating-card.bang-card .card {
    animation: cardGlow 0.5s ease-in-out infinite;
  }

  /* General Store Modal */
  .general-store-modal {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid #e74c3c;
    border-radius: 15px;
    padding: 25px;
    color: #fff;
    text-align: center;
    max-width: 800px;
    max-height: 80vh;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
    animation: modalSlideIn 0.3s ease-out;
    overflow-y: auto;
    position: relative;
    margin: auto;
  }

  .general-store-modal h3 {
    margin: 0 0 15px 0;
    color: #e74c3c;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .general-store-modal p {
    margin: 0 0 20px 0;
    font-size: 16px;
    line-height: 1.4;
  }

  .general-store-cards {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 15px;
    margin: 20px 0;
    max-height: 400px;
    overflow-y: auto;
  }

  .general-store-card {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    border: 2px solid #8b4513;
    border-radius: 10px;
    padding: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    max-width: 150px;
    text-align: center;
    position: relative;
  }

  .general-store-card:hover {
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
    border-color: #e74c3c;
  }

  .general-store-card-image {
    width: 80px;
    height: 128px;
    object-fit: cover;
    border-radius: 5px;
    margin-bottom: 8px;
  }

  .general-store-card .card-name {
    font-size: 12px;
    font-weight: bold;
    color: #8b4513;
    margin-bottom: 4px;
  }

  .general-store-card .card-suit {
    font-size: 11px;
    color: #666;
  }

  .general-store-info {
    margin-top: 20px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    font-size: 14px;
  }

  .general-store-info p {
    margin: 0;
    color: #bdc3c7;
  }

  /* General Store Selection Overlay */
  .general-store-selection {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 100;
    width: 90%;
    max-width: 800px;
    max-height: 80vh;
  }

  /* Touch and long press feedback */
  .hand-card:active,
  .equipment-card:active,
  .small-card:active,
  .character-image:active,
  .character-image-result:active {
    transform: scale(0.95);
    transition: transform 0.1s ease;
  }

  /* Prevent text selection and context menu on touch devices */
  .hand-card,
  .equipment-card,
  .small-card,
  .character-image,
  .character-image-result {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Responsive design for card zoom */
  @media (max-width: 768px) {
    .zoomed-card-image {
      width: 200px;
    }

    .card-zoom-fallback {
      width: 200px;
      height: 280px;
    }

    .card-description {
      max-width: 300px;
      font-size: 16px;
      padding: 15px;
    }

    /* Better touch targets on mobile */
    .card-zoom-overlay {
      padding: 10px;
    }

    .card-zoom-modal {
      max-width: 95%;
      max-height: 95%;
      padding: 15px;
      margin-right:20px;
    }
  }

  /* Mobile app optimizations */
  @media (max-width: 768px) {
    .top-ui-container {
      margin: 1px auto;
      gap: 3px;
      max-width: 98%;
      flex-direction: column;
      align-items: stretch;
    }

    .message-box-playing {
      padding: 6px 8px;
      font-size: 12px;
      min-height: auto;
      line-height: 1.3;
      text-align: center;
      margin-bottom: 2px;
    }

    .actions-right {
      display: flex;
      justify-content: center;
      gap: 4px;
      flex-wrap: wrap;
    }

    .actions-right button {
      padding: 6px 10px;
      font-size: 11px;
      min-width: auto;
      flex: 1;
      max-width: 120px;
    }

    .turn-timer {
      padding: 4px 8px;
      font-size: 14px;
      margin: 1px auto;
      max-width: 150px;
      display: block;
      text-align: center;
      width: fit-content;
      left: 50%;
      transform: translateX(-50%);
      position: relative;
    }

    .bang-game {
      padding: 3px;
      overflow-x: hidden;
      overflow-y: hidden!important;
    }

    .playing-container {
      max-width: 100vw;
      overflow-x: hidden;
      overflow-y:hidden!important;
    }

    .game-board {
      max-width: 100%;
      overflow-x: hidden;
    }

    .ability-choice-modal {
      max-width: 90%;
      padding: 20px;
      margin: 20px auto;
    }

    .ability-choice-modal h3 {
      font-size: 18px;
    }

    .ability-choice-modal p {
      font-size: 14px;
    }

    .ability-choice-buttons {
      flex-direction: column;
      gap: 10px;
    }

    .ability-yes-button, .ability-no-button {
      padding: 10px 16px;
      font-size: 13px;
      min-width: auto;
      width: 100%;
    }

    .missed-choice-modal {
      max-width: 90%;
      padding: 20px;
      margin: 20px auto;
    }

    .missed-choice-modal h3 {
      font-size: 18px;
    }

    .missed-choice-modal p {
      font-size: 14px;
    }

    .missed-choice-info p {
      font-size: 13px;
    }

    .missed-choice-buttons {
      flex-direction: column;
      gap: 10px;
    }

    .missed-yes-button, .missed-no-button {
      padding: 10px 16px;
      font-size: 13px;
      min-width: auto;
      width: 100%;
    }

    .game-menu-button {
      padding: 6px 10px;
      font-size: 14px;
      min-width: 35px;
      height: 35px;
    }

    .game-menu-modal {
      max-width: 90%;
      padding: 25px 20px;
      margin: 20px auto;
    }

    .game-menu-modal h3 {
      font-size: 20px;
      margin-bottom: 20px;
    }

    .menu-button {
      padding: 12px 16px;
      font-size: 14px;
    }

    .game-menu-button-fixed {
      bottom: 15px;
      right: 15px;
      width: 45px;
      height: 45px;
      font-size: 16px;
      padding: 10px;
    }

    .victory-title, .defeat-title {
      font-size: 32px;
    }

    .victory-subtitle, .defeat-subtitle {
      font-size: 18px;
    }

    .game-result-header {
      padding: 20px;
      margin-bottom: 30px;
    }

    .final-roles {
      grid-template-columns: 1fr;
      gap: 15px;
      margin: 20px 0;
    }

    .player-result {
      padding: 15px;
      gap: 10px;
    }

    .character-image-result {
      width: 60px;
      height: 95px;
    }

    .sound-toggle-button {
      padding: 6px 8px;
      font-size: 16px;
      min-width: 40px;
    }

    .final-roles {
      grid-template-columns: 1fr 1fr;
      gap: 10px;
      margin: 15px 5px;
      max-width: 100%;
    }

    .player-result {
      padding: 12px;
      font-size: 14px;
    }

    .general-store-modal {
      max-width: 95%;
      padding: 20px;
      margin: 10px auto;
    }

    .general-store-modal h3 {
      font-size: 20px;
    }

    .general-store-modal p {
      font-size: 14px;
    }

    .general-store-cards {
      gap: 10px;
      max-height: 300px;
    }

    .general-store-card {
      min-width: 100px;
      max-width: 120px;
      padding: 8px;
    }

    .general-store-card-image {
      width: 60px;
      height: 96px;
    }

    .general-store-card .card-name {
      font-size: 11px;
    }

    .general-store-card .card-suit {
      font-size: 10px;
    }

    .general-store-info {
      padding: 10px;
      font-size: 12px;
    }

    .general-store-selection {
      width: 95%;
      max-width: 95%;
      max-height: 85vh;
    }

    .game-board {
      min-height: calc(100vh - 40px);
      max-height: calc(100vh - 40px);
      padding: 10px;
      
    }

    /* Adjust for setup mode on mobile */
    .bang-game.setup-mode .game-board {
      min-height: calc(100vh - 120px);
      max-height: calc(100vh - 120px);
    }

    .other-players-area {
      max-height: 40vh;
      margin-bottom: 10px;
      gap: 20px;
      padding: 8px;
    }

    .central-area {
      margin: 20px 0;
      max-height: 80px!important;

    }

    .current-player-area {
      padding: 8px;
      margin-top: 10px;
      margin-bottom: 10px;
      
    }

    .hand-card {
      min-height: 48px; /* Better touch targets */
      margin: 2px;
      width: 60px;
      height: 96px;
    }

    .hand-card-image {
      max-height: 92px; /* Taller cards on mobile */
    }

    .target-option {
      min-height: 48px; /* Better touch targets */
      padding: 12px;
    }

    

    .start-button,
    .restart-button {
      min-height: 44px;
      padding: 12px 20px;
      font-size: 16px;
    }

    .hand-cards {
      max-height: 120px;
      padding: 3px;
    }

    .hand-area h4 {
      font-size: 14px;
      margin: 5px 0;
    }

    .equipment-area h4 {
      font-size: 14px;
      margin: 5px 0;
    }

    .character-details h3 {
      font-size: 16px;
      margin: 5px 0;
    }

    .character-image {
      width: 60px;
      height: 84px;
    }

    .player-name-overlay {
      font-size: 10px;
      padding: 2px 4px;
      border-radius: 6px;
      bottom: 25px;
      max-width: 50px;
    }

    .other-player .player-name-overlay {
      font-size: 7px;
      padding: 1px 2px;
      border-radius: 3px;
      bottom: 18px;
      max-width: 40px;
    }

    .health-display-overlay {
      font-size: 10px;
      padding: 2px 4px;
      border-radius: 6px;
      bottom: 2px;
    }

    .other-player .health-display-overlay {
      font-size: 8px;
      padding: 1px 3px;
      border-radius: 4px;
      bottom: 1px;
    }

    .role-display {
      font-size: 12px;
    }

    .health-display {
      font-size: 12px;
    }

    .equipment-cards {
      gap: 3px;
    }

    .equipment-card {
      width: 40px;
      height: 64px;
    }

    .equipment-card-image {
      width: 100%;
      height: 100%;
    }
  }

  /* Prevent text selection on mobile */
  .hand-card,
  .target-option,
  .discard-card {
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
  }

  /* Card Animation Effects */
  @keyframes cardPlay {
    0% {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
    50% {
      transform: scale(1.1) rotate(5deg);
      opacity: 0.9;
    }
    100% {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
  }

  @keyframes cardDraw {
    0% {
      transform: translateY(-20px) scale(0.8);
      opacity: 0;
    }
    50% {
      transform: translateY(-10px) scale(0.9);
      opacity: 0.7;
    }
    100% {
      transform: translateY(0) scale(1);
      opacity: 1;
    }
  }

  @keyframes cardDiscard {
    0% {
      transform: scale(1) rotate(0deg);
      opacity: 1;
    }
    50% {
      transform: scale(0.9) rotate(-10deg);
      opacity: 0.7;
    }
    100% {
      transform: scale(0.8) rotate(-20deg) translateY(20px);
      opacity: 0;
    }
  }

  @keyframes cardHover {
    0% {
      transform: translateY(0) scale(1);
      box-shadow: 0 2px 4px rgba(0,0,0,0.2);
    }
    100% {
      transform: translateY(-5px) scale(1.05);
      box-shadow: 0 8px 16px rgba(0,0,0,0.3);
    }
  }

  @keyframes cardSelect {
    0% {
      transform: scale(1);
      border-color: transparent;
    }
    50% {
      transform: scale(1.02);
      border-color: #ffd700;
    }
    100% {
      transform: scale(1);
      border-color: #ffd700;
    }
  }

  @keyframes cardFlip {
    0% {
      transform: rotateY(0deg);
    }
    50% {
      transform: rotateY(90deg);
    }
    100% {
      transform: rotateY(0deg);
    }
  }

  @keyframes cardShake {
    0%, 100% {
      transform: translateX(0);
    }
    25% {
      transform: translateX(-2px);
    }
    75% {
      transform: translateX(2px);
    }
  }

  @keyframes cardGlow {
    0%, 100% {
      box-shadow: 0 0 5px rgba(255, 215, 0, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(255, 215, 0, 0.8);
    }
  }

  @keyframes movingCard {
    0% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 0.8;
      transform: translate(calc(-50% + var(--end-x) * 0.5), calc(-50% + var(--end-y) * 0.5)) scale(0.8);
    }
    100% {
      opacity: 0;
      transform: translate(calc(-50% + var(--end-x)), calc(-50% + var(--end-y))) scale(0.6);
    }
  }

  /* Card animation classes */
  .card-play-animation {
    animation: cardPlay 0.6s ease-in-out;
  }

  .card-draw-animation {
    animation: cardDraw 0.4s ease-out;
  }

  .card-discard-animation {
    animation: cardDiscard 0.5s ease-in;
  }

  .card-hover-animation {
    animation: cardHover 0.2s ease-out forwards;
  }

  .card-select-animation {
    animation: cardSelect 0.3s ease-in-out;
  }

  .card-flip-animation {
    animation: cardFlip 0.6s ease-in-out;
  }

  .card-shake-animation {
    animation: cardShake 0.5s ease-in-out;
  }

  .card-glow-animation {
    animation: cardGlow 1s ease-in-out infinite;
  }

  /* Enhanced card hover effects */
  .hand-card:hover {
    animation: cardHover 0.2s ease-out forwards;
    z-index: 10;
    position: relative;
  }

  .hand-card:active {
    animation: cardSelect 0.1s ease-in-out;
  }

  .equipment-card:hover {
    animation: cardHover 0.2s ease-out forwards;
  }

  .discard-card:hover {
    animation: cardHover 0.2s ease-out forwards;
  }

  /* Moving card overlay */
  .moving-card {
    position: fixed;
    pointer-events: none;
    z-index: 1000;
    animation: movingCard 0.8s ease-in-out forwards;
    transition: all 0.8s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  }

  .moving-card img {
    width: 50px;
    height: 70px;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
  }

  /* Card interaction feedback */
  .hand-card.selected {
    animation: cardGlow 1s ease-in-out infinite;
    border: 2px solid #ffd700;
  }

  .hand-card.disabled {
    opacity: 0.5;
    filter: grayscale(50%);
    animation: cardShake 0.5s ease-in-out;
  }

  /* Smooth transitions for all cards */
  .hand-card,
  .equipment-card,
  .discard-card,
  .target-option {
    transition: all 0.2s ease-in-out;
  }

  /* Safe area for notched devices */
  @supports (padding: max(0px)) {
    .bang-game {
      padding-left: max(10px, env(safe-area-inset-left));
      padding-right: max(10px, env(safe-area-inset-right));
      padding-top: max(10px, env(safe-area-inset-top));
      padding-bottom: max(10px, env(safe-area-inset-bottom));
    }
  }

  /* ========== VISUAL EFFECTS ========== */

  /* Damage effect - red overlay */
  @keyframes damageFlash {
    0% {
      box-shadow: inset 0 0 0 0 rgba(255, 0, 0, 0);
      border-color: #8B4513;
    }
    25% {
      box-shadow: inset 0 0 50px 10px rgba(255, 0, 0, 0.8);
      border-color: #ff0000;
    }
    50% {
      box-shadow: inset 0 0 30px 5px rgba(255, 0, 0, 0.6);
      border-color: #ff3333;
    }
    100% {
      box-shadow: inset 0 0 0 0 rgba(255, 0, 0, 0);
      border-color: #8B4513;
    }
  }

  .damage-effect {
    animation: damageFlash 1.5s ease-out;
  }

  /* Healing effect - green glow */
  @keyframes healingGlow {
    0% {
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
      border-color: #8B4513;
    }
    25% {
      box-shadow: 0 0 20px 5px rgba(0, 255, 0, 0.8), 0 4px 8px rgba(0,0,0,0.6);
      border-color: #00ff00;
    }
    50% {
      box-shadow: 0 0 30px 10px rgba(0, 255, 0, 0.6), 0 4px 8px rgba(0,0,0,0.6);
      border-color: #33ff33;
    }
    100% {
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
      border-color: #8B4513;
    }
  }

  .healing-effect {
    animation: healingGlow 2s ease-out;
  }

  /* Card draw effect - blue shimmer */
  @keyframes cardDrawShimmer {
    0% {
      transform: translateY(0) scale(1);
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
    50% {
      transform: translateY(-5px) scale(1.02);
      box-shadow: 0 0 15px 3px rgba(0, 150, 255, 0.7), 0 6px 12px rgba(0,0,0,0.8);
    }
    100% {
      transform: translateY(0) scale(1);
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
  }

  .card-draw-effect {
    animation: cardDrawShimmer 1s ease-out;
  }

  /* Elimination effect - fade to grayscale with red flash */
  @keyframes eliminationEffect {
    0% {
      opacity: 1;
      filter: grayscale(0%) brightness(1);
      transform: scale(1);
    }
    25% {
      opacity: 0.8;
      filter: grayscale(0%) brightness(1.5) hue-rotate(0deg);
      transform: scale(1.05);
      box-shadow: inset 0 0 50px 20px rgba(255, 0, 0, 0.9);
    }
    50% {
      opacity: 0.6;
      filter: grayscale(50%) brightness(0.8) hue-rotate(0deg);
      transform: scale(0.98);
      box-shadow: inset 0 0 30px 10px rgba(255, 0, 0, 0.6);
    }
    100% {
      opacity: 0.4;
      filter: grayscale(100%) brightness(0.6);
      transform: scale(0.95);
      box-shadow: inset 0 0 0 0 rgba(255, 0, 0, 0);
    }
  }

  .elimination-effect {
    animation: eliminationEffect 2s ease-out forwards;
  }

  /* Turn start effect - golden glow */
  @keyframes turnStartGlow {
    0% {
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
      border-color: #8B4513;
    }
    50% {
      box-shadow: 0 0 25px 8px rgba(255, 215, 0, 0.8), 0 4px 8px rgba(0,0,0,0.6);
      border-color: #FFD700;
    }
    100% {
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
      border-color: #8B4513;
    }
  }

  .turn-start-effect {
    animation: turnStartGlow 1.5s ease-out;
  }

  /* Card play effect - purple pulse */
  @keyframes cardPlayPulse {
    0% {
      transform: scale(1);
      box-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }
    50% {
      transform: scale(1.1);
      box-shadow: 0 0 20px 5px rgba(138, 43, 226, 0.8), 0 4px 8px rgba(0,0,0,0.6);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }
  }

  .card-play-effect {
    animation: cardPlayPulse 0.8s ease-out;
  }

  /* BANG! attack effect - orange flash */
  @keyframes bangAttackFlash {
    0% {
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
      transform: scale(1);
    }
    25% {
      background: linear-gradient(135deg, #ff6600 0%, #ff4400 100%);
      transform: scale(1.03);
      box-shadow: 0 0 25px 8px rgba(255, 102, 0, 0.9);
    }
    50% {
      background: linear-gradient(135deg, #ff8833 0%, #ff6600 100%);
      transform: scale(1.01);
      box-shadow: 0 0 15px 5px rgba(255, 102, 0, 0.6);
    }
    100% {
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
      transform: scale(1);
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
  }

  .bang-attack-effect {
    animation: bangAttackFlash 1.2s ease-out;
  }

  /* Jail effect - prison bars overlay */
  @keyframes jailEffect {
    0% {
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    }
    100% {
      background:
        repeating-linear-gradient(
          90deg,
          #2a2a2a 0px,
          #2a2a2a 8px,
          #666 8px,
          #666 12px
        ),
        linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    }
  }

  .jail-effect {
    animation: jailEffect 0.5s ease-out forwards;
    position: relative;
  }

  .jail-effect::after {
    content: "🔒 JAILED";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(255, 0, 0, 0.9);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 12px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    z-index: 10;
    animation: fadeInScale 0.5s ease-out;
  }

  @keyframes fadeInScale {
    0% {
      opacity: 0;
      transform: translate(-50%, -50%) scale(0.5);
    }
    100% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
  }

  /* Dynamite effect - explosive flash */
  @keyframes dynamiteExplosion {
    0% {
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
      transform: scale(1);
    }
    25% {
      background: linear-gradient(135deg, #ffff00 0%, #ff8800 100%);
      transform: scale(1.15);
      box-shadow: 0 0 40px 15px rgba(255, 165, 0, 1);
    }
    50% {
      background: linear-gradient(135deg, #ff4400 0%, #cc0000 100%);
      transform: scale(1.1);
      box-shadow: 0 0 30px 10px rgba(255, 0, 0, 0.8);
    }
    75% {
      background: linear-gradient(135deg, #cc0000 0%, #880000 100%);
      transform: scale(1.05);
      box-shadow: 0 0 20px 5px rgba(136, 0, 0, 0.6);
    }
    100% {
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
      transform: scale(1);
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
  }

  .dynamite-explosion-effect {
    animation: dynamiteExplosion 2s ease-out;
  }

  /* Floating damage numbers */
  .floating-damage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 24px;
    font-weight: bold;
    color: #ff0000;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    pointer-events: none;
    z-index: 1000;
    animation: floatDamage 2s ease-out forwards;
  }

  @keyframes floatDamage {
    0% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -80px) scale(1.2);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -120px) scale(0.8);
    }
  }

  /* Floating healing numbers */
  .floating-heal {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-size: 20px;
    font-weight: bold;
    color: #00ff00;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
    pointer-events: none;
    z-index: 1000;
    animation: floatHeal 2s ease-out forwards;
  }

  @keyframes floatHeal {
    0% {
      opacity: 1;
      transform: translate(-50%, -50%) scale(1);
    }
    50% {
      opacity: 1;
      transform: translate(-50%, -60px) scale(1.1);
    }
    100% {
      opacity: 0;
      transform: translate(-50%, -100px) scale(0.9);
    }
  }

  /* Card steal effect - swipe animation */
  @keyframes cardStealSwipe {
    0% {
      transform: translateX(0) rotate(0deg);
      opacity: 1;
    }
    50% {
      transform: translateX(-20px) rotate(-5deg);
      opacity: 0.7;
    }
    100% {
      transform: translateX(-40px) rotate(-10deg);
      opacity: 0;
    }
  }

  .card-steal-effect {
    animation: cardStealSwipe 1s ease-out;
  }

  /* Equipment equip effect - golden shine */
  @keyframes equipmentShine {
    0% {
      box-shadow: 0 2px 4px rgba(0,0,0,0.4);
      border-color: #8B4513;
    }
    50% {
      box-shadow: 0 0 20px 5px rgba(255, 215, 0, 0.9), 0 2px 4px rgba(0,0,0,0.4);
      border-color: #FFD700;
    }
    100% {
      box-shadow: 0 2px 4px rgba(0,0,0,0.4);
      border-color: #8B4513;
    }
  }

  .equipment-equip-effect {
    animation: equipmentShine 1.5s ease-out;
  }

  /* Duel effect - red vs red clash */
  @keyframes duelClash {
    0% {
      border-color: #8B4513;
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
    25% {
      border-color: #ff0000;
      box-shadow: 0 0 20px 5px rgba(255, 0, 0, 0.8);
    }
    50% {
      border-color: #8B4513;
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
    75% {
      border-color: #ff0000;
      box-shadow: 0 0 20px 5px rgba(255, 0, 0, 0.8);
    }
    100% {
      border-color: #8B4513;
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
  }

  .duel-effect {
    animation: duelClash 2s ease-in-out;
  }

  /* Screen shake effect for major events */
  @keyframes screenShake {
    0%, 100% { transform: translateX(0); }
    10% { transform: translateX(-2px); }
    20% { transform: translateX(2px); }
    30% { transform: translateX(-2px); }
    40% { transform: translateX(2px); }
    50% { transform: translateX(-1px); }
    60% { transform: translateX(1px); }
    70% { transform: translateX(-1px); }
    80% { transform: translateX(1px); }
    90% { transform: translateX(-1px); }
  }

  .screen-shake {
    animation: screenShake 0.8s ease-in-out;
  }

  /* Victory celebration effect */
  @keyframes victoryPulse {
    0% {
      transform: scale(1);
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
    25% {
      transform: scale(1.05);
      box-shadow: 0 0 30px 10px rgba(255, 215, 0, 0.9);
    }
    50% {
      transform: scale(1.1);
      box-shadow: 0 0 40px 15px rgba(255, 215, 0, 1);
    }
    75% {
      transform: scale(1.05);
      box-shadow: 0 0 30px 10px rgba(255, 215, 0, 0.9);
    }
    100% {
      transform: scale(1);
      box-shadow: 0 4px 8px rgba(0,0,0,0.6);
    }
  }

  .victory-effect {
    animation: victoryPulse 2s ease-in-out infinite;
  }

  /* Defeat fade effect */
  @keyframes defeatFade {
    0% {
      opacity: 1;
      filter: brightness(1) saturate(1);
    }
    100% {
      opacity: 0.3;
      filter: brightness(0.4) saturate(0.2) grayscale(80%);
    }
  }

  .defeat-effect {
    animation: defeatFade 1.5s ease-out forwards;
  }

  /* Card glow effect for special cards */
  @keyframes specialCardGlow {
    0%, 100% {
      box-shadow: 0 2px 4px rgba(0,0,0,0.4);
    }
    50% {
      box-shadow: 0 0 15px 3px rgba(255, 255, 255, 0.6), 0 2px 4px rgba(0,0,0,0.4);
    }
  }

  .special-card-glow {
    animation: specialCardGlow 2s ease-in-out infinite;
  }

  /* Barrel defense effect - shield flash */
  @keyframes barrelDefense {
    0% {
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    }
    50% {
      background: linear-gradient(135deg, #4169E1 0%, #1E90FF 100%);
      box-shadow: 0 0 25px 8px rgba(65, 105, 225, 0.8);
    }
    100% {
      background: linear-gradient(135deg, #2a2a2a 0%, #1a1a1a 100%);
    }
  }

  .barrel-defense-effect {
    animation: barrelDefense 1s ease-out;
  }

  /* Miss defense effect - dodge swipe */
  @keyframes dodgeSwipe {
    0% {
      transform: translateX(0);
      opacity: 1;
    }
    25% {
      transform: translateX(-10px);
      opacity: 0.8;
    }
    50% {
      transform: translateX(10px);
      opacity: 0.6;
    }
    75% {
      transform: translateX(-5px);
      opacity: 0.8;
    }
    100% {
      transform: translateX(0);
      opacity: 1;
    }
  }

  .dodge-effect {
    animation: dodgeSwipe 0.8s ease-out;
  }

  /* Utility classes for effect combinations */
  .effect-container {
    position: relative;
    overflow: visible;
  }

  /* Pulse effect for important notifications */
  @keyframes importantPulse {
    0%, 100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.05);
      opacity: 0.8;
    }
  }

  .important-pulse {
    animation: importantPulse 1s ease-in-out infinite;
  }

  /* Fade in effect for new elements */
  @keyframes fadeIn {
    0% {
      opacity: 0;
      transform: translateY(10px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .fade-in-effect {
    animation: fadeIn 0.5s ease-out;
  }

  /* Slide in effect for cards */
  @keyframes slideInFromRight {
    0% {
      opacity: 0;
      transform: translateX(50px);
    }
    100% {
      opacity: 1;
      transform: translateX(0);
    }
  }

  .slide-in-effect {
    animation: slideInFromRight 0.6s ease-out;
  }

  /* Bounce effect for successful actions */
  @keyframes successBounce {
    0%, 100% {
      transform: scale(1);
    }
    25% {
      transform: scale(1.1);
    }
    50% {
      transform: scale(0.95);
    }
    75% {
      transform: scale(1.05);
    }
  }

  .success-bounce {
    animation: successBounce 0.8s ease-out;
  }

  /* Wobble effect for errors */
  @keyframes errorWobble {
    0%, 100% { transform: translateX(0); }
    15% { transform: translateX(-5px) rotate(-1deg); }
    30% { transform: translateX(5px) rotate(1deg); }
    45% { transform: translateX(-3px) rotate(-0.5deg); }
    60% { transform: translateX(3px) rotate(0.5deg); }
    75% { transform: translateX(-1px); }
  }

  .error-wobble {
    animation: errorWobble 0.8s ease-in-out;
  }

  /* Sparkle effect overlay */
  .sparkle-effect::before {
    content: "✨";
    position: absolute;
    top: -10px;
    right: -10px;
    font-size: 20px;
    animation: sparkleRotate 2s linear infinite;
    z-index: 10;
  }

  @keyframes sparkleRotate {
    0% { transform: rotate(0deg) scale(1); opacity: 1; }
    50% { transform: rotate(180deg) scale(1.2); opacity: 0.7; }
    100% { transform: rotate(360deg) scale(1); opacity: 1; }
  }

  /* Long press visual feedback */
  .hand-card.long-pressing {
    transform: scale(1.05);
    box-shadow: 0 0 20px 5px rgba(255, 215, 0, 0.8);
    border: 2px solid #FFD700;
    transition: all 0.2s ease;
  }

  .long-press-hint {
    margin-top: 15px;
    padding: 10px;
    background: rgba(255, 215, 0, 0.1);
    border: 1px solid rgba(255, 215, 0, 0.3);
    border-radius: 5px;
    font-size: 14px;
    color: #FFD700;
    text-align: center;
  }

  /* Touch feedback for mobile */
  @media (max-width: 768px) {
    .hand-card {
      transition: transform 0.1s ease, box-shadow 0.1s ease;
    }

    .hand-card:active {
      transform: scale(0.98);
    }

    .hand-card.long-pressing {
      transform: scale(1.08);
      box-shadow: 0 0 25px 8px rgba(255, 215, 0, 0.9);
    }
  }

  /* Disable effects for reduced motion preference */
  @media (prefers-reduced-motion: reduce) {
    .damage-effect,
    .healing-effect,
    .card-draw-effect,
    .elimination-effect,
    .turn-start-effect,
    .card-play-effect,
    .bang-attack-effect,
    .jail-effect,
    .dynamite-explosion-effect,
    .card-steal-effect,
    .equipment-equip-effect,
    .duel-effect,
    .screen-shake,
    .victory-effect,
    .defeat-effect,
    .special-card-glow,
    .barrel-defense-effect,
    .dodge-effect,
    .important-pulse,
    .fade-in-effect,
    .slide-in-effect,
    .success-bounce,
    .error-wobble,
    .sparkle-effect::before,
    .hand-card.long-pressing {
      animation: none !important;
      transition: none !important;
      transform: none !important;
    }
  }
  
  @media (max-width: 768px) {
    .player-card, .player-result {
      width: auto;
    }

    .character-image-result {
      width: 60px;
      height: 84px;
    }
  }

  /* Turn Timer Styles */
  .turn-timer {
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    padding: 8px 12px;
    border-radius: 6px;
    font-size: 14px;
    font-weight: bold;
    text-align: center;
    margin: 8px 0;
    box-shadow: 0 2px 6px rgba(0,0,0,0.3);
    border: 2px solid #388e3c;
    transition: all 0.3s ease;
  }

  /* Turn Timer at top of screen - centered */
  .turn-timer-top {
    position: relative;
    left: 50%;
    width: 20%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
    color: white;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 16px;
    font-weight: bold;
    text-align: center;
    box-shadow: 0 4px 12px rgba(0,0,0,0.4);
    border: 2px solid #388e3c;
    transition: all 0.3s ease;
    z-index: 35;
  }

  .turn-timer.timer-warning,
  .turn-timer-top.timer-warning {
    background: linear-gradient(135deg, #FF9800 0%, #f57c00 100%);
    border-color: #e65100;
    animation: pulse-warning 1s ease-in-out infinite alternate;
  }

  .turn-timer.timer-critical,
  .turn-timer-top.timer-critical {
    background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
    border-color: #b71c1c;
    animation: pulse-critical 0.5s ease-in-out infinite alternate;
  }

  @keyframes pulse-warning {
    from { transform: scale(1); box-shadow: 0 2px 6px rgba(0,0,0,0.3); }
    to { transform: scale(1.02); box-shadow: 0 4px 12px rgba(255, 152, 0, 0.4); }
  }

  @keyframes pulse-critical {
    from { transform: scale(1); box-shadow: 0 2px 6px rgba(0,0,0,0.3); }
    to { transform: scale(1.05); box-shadow: 0 6px 16px rgba(244, 67, 54, 0.6); }
  }

  /* Card Selection Modal */
  .card-selection-modal {
    background: linear-gradient(135deg, #2c3e50, #34495e);
    border: 3px solid #e74c3c;
    border-radius: 15px;
    padding: 25px;
    color: #fff;
    text-align: center;
    max-width: 600px;
    max-height: 80vh;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
    animation: modalSlideIn 0.3s ease-out;
    overflow-y: auto;
    margin: auto;
  }

  .card-selection-modal h3 {
    margin: 0 0 15px 0;
    color: #e74c3c;
    font-size: 24px;
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
  }

  .card-selection-modal p {
    margin: 0 0 20px 0;
    font-size: 16px;
    line-height: 1.4;
    color: #ecf0f1;
  }

  .card-selection-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
  }

  .selectable-card {
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    border-radius: 10px;
    padding: 8px;
    background: rgba(255, 255, 255, 0.1);
  }

  .selectable-card:hover {
    border-color: #e74c3c;
    transform: translateY(-5px) scale(1.05);
    box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
    background: rgba(231, 76, 60, 0.1);
  }

  .card-selection-image {
    width: 100%;
    height: auto;
    max-width: 80px;
    border-radius: 6px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }

  .card-selection-fallback {
    background: linear-gradient(135deg, #8b4513, #a0522d);
    color: white;
    padding: 10px;
    border-radius: 6px;
    text-align: center;
    min-height: 100px;
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 80px;
    margin: 0 auto;
  }

  .card-selection-fallback .card-name {
    font-weight: bold;
    font-size: 0.9em;
    margin-bottom: 5px;
  }

  .card-selection-fallback .card-suit {
    font-size: 0.8em;
    opacity: 0.8;
  }

  /* Green card styling */
  .equipment-card.green-card {
    border: 2px solid #4CAF50;
    box-shadow: 0 0 8px rgba(76, 175, 80, 0.3);
  }

  .equipment-card.usable-green {
    border-color: #8BC34A;
    box-shadow: 0 0 12px rgba(139, 195, 74, 0.5);
    cursor: pointer;
    animation: greenCardPulse 2s infinite;
  }

  .equipment-card.usable-green:hover {
    border-color: #CDDC39;
    box-shadow: 0 0 16px rgba(205, 220, 57, 0.7);
    transform: translateY(-2px);
  }

  @keyframes greenCardPulse {
    0%, 100% {
      box-shadow: 0 0 12px rgba(139, 195, 74, 0.5);
    }
    50% {
      box-shadow: 0 0 20px rgba(139, 195, 74, 0.8);
    }
  }