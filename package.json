{"name": "bang", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview", "mobile:build": "npm run build && npx cap sync", "mobile:android": "npm run mobile:build && npx cap open android", "mobile:ios": "npm run mobile:build && npx cap open ios", "mobile:run:android": "npm run mobile:build && npx cap run android", "mobile:run:ios": "npm run mobile:build && npx cap run ios"}, "dependencies": {"@capacitor/android": "^7.3.0", "@capacitor/cli": "^7.3.0", "@capacitor/core": "^7.3.0", "@capacitor/ios": "^7.3.0", "react": "^19.1.0", "react-dom": "^19.1.0"}, "devDependencies": {"@eslint/js": "^9.25.0", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "@vitejs/plugin-react": "^4.4.1", "eslint": "^9.25.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "vite": "^6.3.5"}}