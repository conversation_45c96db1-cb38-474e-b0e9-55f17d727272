(function(){const Ce=document.createElement("link").relList;if(Ce&&Ce.supports&&Ce.supports("modulepreload"))return;for(const v of document.querySelectorAll('link[rel="modulepreload"]'))_(v);new MutationObserver(v=>{for(const V of v)if(V.type==="childList")for(const B of V.addedNodes)B.tagName==="LINK"&&B.rel==="modulepreload"&&_(B)}).observe(document,{childList:!0,subtree:!0});function Te(v){const V={};return v.integrity&&(V.integrity=v.integrity),v.referrerPolicy&&(V.referrerPolicy=v.referrerPolicy),v.crossOrigin==="use-credentials"?V.credentials="include":v.crossOrigin==="anonymous"?V.credentials="omit":V.credentials="same-origin",V}function _(v){if(v.ep)return;v.ep=!0;const V=Te(v);fetch(v.href,V)}})();var cf={exports:{}},Ci={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var fh;function jm(){if(fh)return Ci;fh=1;var K=Symbol.for("react.transitional.element"),Ce=Symbol.for("react.fragment");function Te(_,v,V){var B=null;if(V!==void 0&&(B=""+V),v.key!==void 0&&(B=""+v.key),"key"in v){V={};for(var je in v)je!=="key"&&(V[je]=v[je])}else V=v;return v=V.ref,{$$typeof:K,type:_,key:B,ref:v!==void 0?v:null,props:V}}return Ci.Fragment=Ce,Ci.jsx=Te,Ci.jsxs=Te,Ci}var dh;function zm(){return dh||(dh=1,cf.exports=jm()),cf.exports}var g=zm(),sf={exports:{}},ce={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var oh;function Lm(){if(oh)return ce;oh=1;var K=Symbol.for("react.transitional.element"),Ce=Symbol.for("react.portal"),Te=Symbol.for("react.fragment"),_=Symbol.for("react.strict_mode"),v=Symbol.for("react.profiler"),V=Symbol.for("react.consumer"),B=Symbol.for("react.context"),je=Symbol.for("react.forward_ref"),$=Symbol.for("react.suspense"),M=Symbol.for("react.memo"),U=Symbol.for("react.lazy"),X=Symbol.iterator;function he(A){return A===null||typeof A!="object"?null:(A=X&&A[X]||A["@@iterator"],typeof A=="function"?A:null)}var ea={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},sa=Object.assign,E={};function ke(A,w,k){this.props=A,this.context=w,this.refs=E,this.updater=k||ea}ke.prototype.isReactComponent={},ke.prototype.setState=function(A,w){if(typeof A!="object"&&typeof A!="function"&&A!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,A,w,"setState")},ke.prototype.forceUpdate=function(A){this.updater.enqueueForceUpdate(this,A,"forceUpdate")};function ut(){}ut.prototype=ke.prototype;function Tt(A,w,k){this.props=A,this.context=w,this.refs=E,this.updater=k||ea}var Ze=Tt.prototype=new ut;Ze.constructor=Tt,sa(Ze,ke.prototype),Ze.isPureReactComponent=!0;var za=Array.isArray,ge={H:null,A:null,T:null,S:null,V:null},Na=Object.prototype.hasOwnProperty;function Ra(A,w,k,Z,I,fe){return k=fe.ref,{$$typeof:K,type:A,key:w,ref:k!==void 0?k:null,props:fe}}function We(A,w){return Ra(A.type,w,void 0,void 0,void 0,A.props)}function La(A){return typeof A=="object"&&A!==null&&A.$$typeof===K}function Za(A){var w={"=":"=0",":":"=2"};return"$"+A.replace(/[=:]/g,function(k){return w[k]})}var it=/\/+/g;function ze(A,w){return typeof A=="object"&&A!==null&&A.key!=null?Za(""+A.key):w.toString(36)}function qt(){}function ct(A){switch(A.status){case"fulfilled":return A.value;case"rejected":throw A.reason;default:switch(typeof A.status=="string"?A.then(qt,qt):(A.status="pending",A.then(function(w){A.status==="pending"&&(A.status="fulfilled",A.value=w)},function(w){A.status==="pending"&&(A.status="rejected",A.reason=w)})),A.status){case"fulfilled":return A.value;case"rejected":throw A.reason}}throw A}function aa(A,w,k,Z,I){var fe=typeof A;(fe==="undefined"||fe==="boolean")&&(A=null);var ie=!1;if(A===null)ie=!0;else switch(fe){case"bigint":case"string":case"number":ie=!0;break;case"object":switch(A.$$typeof){case K:case Ce:ie=!0;break;case U:return ie=A._init,aa(ie(A._payload),w,k,Z,I)}}if(ie)return I=I(A),ie=Z===""?"."+ze(A,0):Z,za(I)?(k="",ie!=null&&(k=ie.replace(it,"$&/")+"/"),aa(I,w,k,"",function(Ka){return Ka})):I!=null&&(La(I)&&(I=We(I,k+(I.key==null||A&&A.key===I.key?"":(""+I.key).replace(it,"$&/")+"/")+ie)),w.push(I)),1;ie=0;var ga=Z===""?".":Z+":";if(za(A))for(var me=0;me<A.length;me++)Z=A[me],fe=ga+ze(Z,me),ie+=aa(Z,w,k,fe,I);else if(me=he(A),typeof me=="function")for(A=me.call(A),me=0;!(Z=A.next()).done;)Z=Z.value,fe=ga+ze(Z,me++),ie+=aa(Z,w,k,fe,I);else if(fe==="object"){if(typeof A.then=="function")return aa(ct(A),w,k,Z,I);throw w=String(A),Error("Objects are not valid as a React child (found: "+(w==="[object Object]"?"object with keys {"+Object.keys(A).join(", ")+"}":w)+"). If you meant to render a collection of children, use an array instead.")}return ie}function G(A,w,k){if(A==null)return A;var Z=[],I=0;return aa(A,Z,"","",function(fe){return w.call(k,fe,I++)}),Z}function q(A){if(A._status===-1){var w=A._result;w=w(),w.then(function(k){(A._status===0||A._status===-1)&&(A._status=1,A._result=k)},function(k){(A._status===0||A._status===-1)&&(A._status=2,A._result=k)}),A._status===-1&&(A._status=0,A._result=w)}if(A._status===1)return A._result.default;throw A._result}var ee=typeof reportError=="function"?reportError:function(A){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var w=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof A=="object"&&A!==null&&typeof A.message=="string"?String(A.message):String(A),error:A});if(!window.dispatchEvent(w))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",A);return}console.error(A)};function le(){}return ce.Children={map:G,forEach:function(A,w,k){G(A,function(){w.apply(this,arguments)},k)},count:function(A){var w=0;return G(A,function(){w++}),w},toArray:function(A){return G(A,function(w){return w})||[]},only:function(A){if(!La(A))throw Error("React.Children.only expected to receive a single React element child.");return A}},ce.Component=ke,ce.Fragment=Te,ce.Profiler=v,ce.PureComponent=Tt,ce.StrictMode=_,ce.Suspense=$,ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=ge,ce.__COMPILER_RUNTIME={__proto__:null,c:function(A){return ge.H.useMemoCache(A)}},ce.cache=function(A){return function(){return A.apply(null,arguments)}},ce.cloneElement=function(A,w,k){if(A==null)throw Error("The argument must be a React element, but you passed "+A+".");var Z=sa({},A.props),I=A.key,fe=void 0;if(w!=null)for(ie in w.ref!==void 0&&(fe=void 0),w.key!==void 0&&(I=""+w.key),w)!Na.call(w,ie)||ie==="key"||ie==="__self"||ie==="__source"||ie==="ref"&&w.ref===void 0||(Z[ie]=w[ie]);var ie=arguments.length-2;if(ie===1)Z.children=k;else if(1<ie){for(var ga=Array(ie),me=0;me<ie;me++)ga[me]=arguments[me+2];Z.children=ga}return Ra(A.type,I,void 0,void 0,fe,Z)},ce.createContext=function(A){return A={$$typeof:B,_currentValue:A,_currentValue2:A,_threadCount:0,Provider:null,Consumer:null},A.Provider=A,A.Consumer={$$typeof:V,_context:A},A},ce.createElement=function(A,w,k){var Z,I={},fe=null;if(w!=null)for(Z in w.key!==void 0&&(fe=""+w.key),w)Na.call(w,Z)&&Z!=="key"&&Z!=="__self"&&Z!=="__source"&&(I[Z]=w[Z]);var ie=arguments.length-2;if(ie===1)I.children=k;else if(1<ie){for(var ga=Array(ie),me=0;me<ie;me++)ga[me]=arguments[me+2];I.children=ga}if(A&&A.defaultProps)for(Z in ie=A.defaultProps,ie)I[Z]===void 0&&(I[Z]=ie[Z]);return Ra(A,fe,void 0,void 0,null,I)},ce.createRef=function(){return{current:null}},ce.forwardRef=function(A){return{$$typeof:je,render:A}},ce.isValidElement=La,ce.lazy=function(A){return{$$typeof:U,_payload:{_status:-1,_result:A},_init:q}},ce.memo=function(A,w){return{$$typeof:M,type:A,compare:w===void 0?null:w}},ce.startTransition=function(A){var w=ge.T,k={};ge.T=k;try{var Z=A(),I=ge.S;I!==null&&I(k,Z),typeof Z=="object"&&Z!==null&&typeof Z.then=="function"&&Z.then(le,ee)}catch(fe){ee(fe)}finally{ge.T=w}},ce.unstable_useCacheRefresh=function(){return ge.H.useCacheRefresh()},ce.use=function(A){return ge.H.use(A)},ce.useActionState=function(A,w,k){return ge.H.useActionState(A,w,k)},ce.useCallback=function(A,w){return ge.H.useCallback(A,w)},ce.useContext=function(A){return ge.H.useContext(A)},ce.useDebugValue=function(){},ce.useDeferredValue=function(A,w){return ge.H.useDeferredValue(A,w)},ce.useEffect=function(A,w,k){var Z=ge.H;if(typeof k=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return Z.useEffect(A,w)},ce.useId=function(){return ge.H.useId()},ce.useImperativeHandle=function(A,w,k){return ge.H.useImperativeHandle(A,w,k)},ce.useInsertionEffect=function(A,w){return ge.H.useInsertionEffect(A,w)},ce.useLayoutEffect=function(A,w){return ge.H.useLayoutEffect(A,w)},ce.useMemo=function(A,w){return ge.H.useMemo(A,w)},ce.useOptimistic=function(A,w){return ge.H.useOptimistic(A,w)},ce.useReducer=function(A,w,k){return ge.H.useReducer(A,w,k)},ce.useRef=function(A){return ge.H.useRef(A)},ce.useState=function(A){return ge.H.useState(A)},ce.useSyncExternalStore=function(A,w,k){return ge.H.useSyncExternalStore(A,w,k)},ce.useTransition=function(){return ge.H.useTransition()},ce.version="19.1.0",ce}var hh;function mf(){return hh||(hh=1,sf.exports=Lm()),sf.exports}var Y=mf(),rf={exports:{}},Di={},ff={exports:{}},df={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var mh;function xm(){return mh||(mh=1,function(K){function Ce(G,q){var ee=G.length;G.push(q);e:for(;0<ee;){var le=ee-1>>>1,A=G[le];if(0<v(A,q))G[le]=q,G[ee]=A,ee=le;else break e}}function Te(G){return G.length===0?null:G[0]}function _(G){if(G.length===0)return null;var q=G[0],ee=G.pop();if(ee!==q){G[0]=ee;e:for(var le=0,A=G.length,w=A>>>1;le<w;){var k=2*(le+1)-1,Z=G[k],I=k+1,fe=G[I];if(0>v(Z,ee))I<A&&0>v(fe,Z)?(G[le]=fe,G[I]=ee,le=I):(G[le]=Z,G[k]=ee,le=k);else if(I<A&&0>v(fe,ee))G[le]=fe,G[I]=ee,le=I;else break e}}return q}function v(G,q){var ee=G.sortIndex-q.sortIndex;return ee!==0?ee:G.id-q.id}if(K.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var V=performance;K.unstable_now=function(){return V.now()}}else{var B=Date,je=B.now();K.unstable_now=function(){return B.now()-je}}var $=[],M=[],U=1,X=null,he=3,ea=!1,sa=!1,E=!1,ke=!1,ut=typeof setTimeout=="function"?setTimeout:null,Tt=typeof clearTimeout=="function"?clearTimeout:null,Ze=typeof setImmediate<"u"?setImmediate:null;function za(G){for(var q=Te(M);q!==null;){if(q.callback===null)_(M);else if(q.startTime<=G)_(M),q.sortIndex=q.expirationTime,Ce($,q);else break;q=Te(M)}}function ge(G){if(E=!1,za(G),!sa)if(Te($)!==null)sa=!0,Na||(Na=!0,ze());else{var q=Te(M);q!==null&&aa(ge,q.startTime-G)}}var Na=!1,Ra=-1,We=5,La=-1;function Za(){return ke?!0:!(K.unstable_now()-La<We)}function it(){if(ke=!1,Na){var G=K.unstable_now();La=G;var q=!0;try{e:{sa=!1,E&&(E=!1,Tt(Ra),Ra=-1),ea=!0;var ee=he;try{a:{for(za(G),X=Te($);X!==null&&!(X.expirationTime>G&&Za());){var le=X.callback;if(typeof le=="function"){X.callback=null,he=X.priorityLevel;var A=le(X.expirationTime<=G);if(G=K.unstable_now(),typeof A=="function"){X.callback=A,za(G),q=!0;break a}X===Te($)&&_($),za(G)}else _($);X=Te($)}if(X!==null)q=!0;else{var w=Te(M);w!==null&&aa(ge,w.startTime-G),q=!1}}break e}finally{X=null,he=ee,ea=!1}q=void 0}}finally{q?ze():Na=!1}}}var ze;if(typeof Ze=="function")ze=function(){Ze(it)};else if(typeof MessageChannel<"u"){var qt=new MessageChannel,ct=qt.port2;qt.port1.onmessage=it,ze=function(){ct.postMessage(null)}}else ze=function(){ut(it,0)};function aa(G,q){Ra=ut(function(){G(K.unstable_now())},q)}K.unstable_IdlePriority=5,K.unstable_ImmediatePriority=1,K.unstable_LowPriority=4,K.unstable_NormalPriority=3,K.unstable_Profiling=null,K.unstable_UserBlockingPriority=2,K.unstable_cancelCallback=function(G){G.callback=null},K.unstable_forceFrameRate=function(G){0>G||125<G?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):We=0<G?Math.floor(1e3/G):5},K.unstable_getCurrentPriorityLevel=function(){return he},K.unstable_next=function(G){switch(he){case 1:case 2:case 3:var q=3;break;default:q=he}var ee=he;he=q;try{return G()}finally{he=ee}},K.unstable_requestPaint=function(){ke=!0},K.unstable_runWithPriority=function(G,q){switch(G){case 1:case 2:case 3:case 4:case 5:break;default:G=3}var ee=he;he=G;try{return q()}finally{he=ee}},K.unstable_scheduleCallback=function(G,q,ee){var le=K.unstable_now();switch(typeof ee=="object"&&ee!==null?(ee=ee.delay,ee=typeof ee=="number"&&0<ee?le+ee:le):ee=le,G){case 1:var A=-1;break;case 2:A=250;break;case 5:A=1073741823;break;case 4:A=1e4;break;default:A=5e3}return A=ee+A,G={id:U++,callback:q,priorityLevel:G,startTime:ee,expirationTime:A,sortIndex:-1},ee>le?(G.sortIndex=ee,Ce(M,G),Te($)===null&&G===Te(M)&&(E?(Tt(Ra),Ra=-1):E=!0,aa(ge,ee-le))):(G.sortIndex=A,Ce($,G),sa||ea||(sa=!0,Na||(Na=!0,ze()))),G},K.unstable_shouldYield=Za,K.unstable_wrapCallback=function(G){var q=he;return function(){var ee=he;he=q;try{return G.apply(this,arguments)}finally{he=ee}}}}(df)),df}var yh;function qm(){return yh||(yh=1,ff.exports=xm()),ff.exports}var of={exports:{}},Ta={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var gh;function wm(){if(gh)return Ta;gh=1;var K=mf();function Ce($){var M="https://react.dev/errors/"+$;if(1<arguments.length){M+="?args[]="+encodeURIComponent(arguments[1]);for(var U=2;U<arguments.length;U++)M+="&args[]="+encodeURIComponent(arguments[U])}return"Minified React error #"+$+"; visit "+M+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function Te(){}var _={d:{f:Te,r:function(){throw Error(Ce(522))},D:Te,C:Te,L:Te,m:Te,X:Te,S:Te,M:Te},p:0,findDOMNode:null},v=Symbol.for("react.portal");function V($,M,U){var X=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:v,key:X==null?null:""+X,children:$,containerInfo:M,implementation:U}}var B=K.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function je($,M){if($==="font")return"";if(typeof M=="string")return M==="use-credentials"?M:""}return Ta.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=_,Ta.createPortal=function($,M){var U=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!M||M.nodeType!==1&&M.nodeType!==9&&M.nodeType!==11)throw Error(Ce(299));return V($,M,null,U)},Ta.flushSync=function($){var M=B.T,U=_.p;try{if(B.T=null,_.p=2,$)return $()}finally{B.T=M,_.p=U,_.d.f()}},Ta.preconnect=function($,M){typeof $=="string"&&(M?(M=M.crossOrigin,M=typeof M=="string"?M==="use-credentials"?M:"":void 0):M=null,_.d.C($,M))},Ta.prefetchDNS=function($){typeof $=="string"&&_.d.D($)},Ta.preinit=function($,M){if(typeof $=="string"&&M&&typeof M.as=="string"){var U=M.as,X=je(U,M.crossOrigin),he=typeof M.integrity=="string"?M.integrity:void 0,ea=typeof M.fetchPriority=="string"?M.fetchPriority:void 0;U==="style"?_.d.S($,typeof M.precedence=="string"?M.precedence:void 0,{crossOrigin:X,integrity:he,fetchPriority:ea}):U==="script"&&_.d.X($,{crossOrigin:X,integrity:he,fetchPriority:ea,nonce:typeof M.nonce=="string"?M.nonce:void 0})}},Ta.preinitModule=function($,M){if(typeof $=="string")if(typeof M=="object"&&M!==null){if(M.as==null||M.as==="script"){var U=je(M.as,M.crossOrigin);_.d.M($,{crossOrigin:U,integrity:typeof M.integrity=="string"?M.integrity:void 0,nonce:typeof M.nonce=="string"?M.nonce:void 0})}}else M==null&&_.d.M($)},Ta.preload=function($,M){if(typeof $=="string"&&typeof M=="object"&&M!==null&&typeof M.as=="string"){var U=M.as,X=je(U,M.crossOrigin);_.d.L($,U,{crossOrigin:X,integrity:typeof M.integrity=="string"?M.integrity:void 0,nonce:typeof M.nonce=="string"?M.nonce:void 0,type:typeof M.type=="string"?M.type:void 0,fetchPriority:typeof M.fetchPriority=="string"?M.fetchPriority:void 0,referrerPolicy:typeof M.referrerPolicy=="string"?M.referrerPolicy:void 0,imageSrcSet:typeof M.imageSrcSet=="string"?M.imageSrcSet:void 0,imageSizes:typeof M.imageSizes=="string"?M.imageSizes:void 0,media:typeof M.media=="string"?M.media:void 0})}},Ta.preloadModule=function($,M){if(typeof $=="string")if(M){var U=je(M.as,M.crossOrigin);_.d.m($,{as:typeof M.as=="string"&&M.as!=="script"?M.as:void 0,crossOrigin:U,integrity:typeof M.integrity=="string"?M.integrity:void 0})}else _.d.m($)},Ta.requestFormReset=function($){_.d.r($)},Ta.unstable_batchedUpdates=function($,M){return $(M)},Ta.useFormState=function($,M,U){return B.H.useFormState($,M,U)},Ta.useFormStatus=function(){return B.H.useHostTransitionStatus()},Ta.version="19.1.0",Ta}var vh;function Ym(){if(vh)return of.exports;vh=1;function K(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(K)}catch(Ce){console.error(Ce)}}return K(),of.exports=wm(),of.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Sh;function Vm(){if(Sh)return Di;Sh=1;var K=qm(),Ce=mf(),Te=Ym();function _(e){var a="https://react.dev/errors/"+e;if(1<arguments.length){a+="?args[]="+encodeURIComponent(arguments[1]);for(var t=2;t<arguments.length;t++)a+="&args[]="+encodeURIComponent(arguments[t])}return"Minified React error #"+e+"; visit "+a+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function v(e){return!(!e||e.nodeType!==1&&e.nodeType!==9&&e.nodeType!==11)}function V(e){var a=e,t=e;if(e.alternate)for(;a.return;)a=a.return;else{e=a;do a=e,(a.flags&4098)!==0&&(t=a.return),e=a.return;while(e)}return a.tag===3?t:null}function B(e){if(e.tag===13){var a=e.memoizedState;if(a===null&&(e=e.alternate,e!==null&&(a=e.memoizedState)),a!==null)return a.dehydrated}return null}function je(e){if(V(e)!==e)throw Error(_(188))}function $(e){var a=e.alternate;if(!a){if(a=V(e),a===null)throw Error(_(188));return a!==e?null:e}for(var t=e,l=a;;){var n=t.return;if(n===null)break;var u=n.alternate;if(u===null){if(l=n.return,l!==null){t=l;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===t)return je(n),e;if(u===l)return je(n),a;u=u.sibling}throw Error(_(188))}if(t.return!==l.return)t=n,l=u;else{for(var r=!1,o=n.child;o;){if(o===t){r=!0,t=n,l=u;break}if(o===l){r=!0,l=n,t=u;break}o=o.sibling}if(!r){for(o=u.child;o;){if(o===t){r=!0,t=u,l=n;break}if(o===l){r=!0,l=u,t=n;break}o=o.sibling}if(!r)throw Error(_(189))}}if(t.alternate!==l)throw Error(_(190))}if(t.tag!==3)throw Error(_(188));return t.stateNode.current===t?e:a}function M(e){var a=e.tag;if(a===5||a===26||a===27||a===6)return e;for(e=e.child;e!==null;){if(a=M(e),a!==null)return a;e=e.sibling}return null}var U=Object.assign,X=Symbol.for("react.element"),he=Symbol.for("react.transitional.element"),ea=Symbol.for("react.portal"),sa=Symbol.for("react.fragment"),E=Symbol.for("react.strict_mode"),ke=Symbol.for("react.profiler"),ut=Symbol.for("react.provider"),Tt=Symbol.for("react.consumer"),Ze=Symbol.for("react.context"),za=Symbol.for("react.forward_ref"),ge=Symbol.for("react.suspense"),Na=Symbol.for("react.suspense_list"),Ra=Symbol.for("react.memo"),We=Symbol.for("react.lazy"),La=Symbol.for("react.activity"),Za=Symbol.for("react.memo_cache_sentinel"),it=Symbol.iterator;function ze(e){return e===null||typeof e!="object"?null:(e=it&&e[it]||e["@@iterator"],typeof e=="function"?e:null)}var qt=Symbol.for("react.client.reference");function ct(e){if(e==null)return null;if(typeof e=="function")return e.$$typeof===qt?null:e.displayName||e.name||null;if(typeof e=="string")return e;switch(e){case sa:return"Fragment";case ke:return"Profiler";case E:return"StrictMode";case ge:return"Suspense";case Na:return"SuspenseList";case La:return"Activity"}if(typeof e=="object")switch(e.$$typeof){case ea:return"Portal";case Ze:return(e.displayName||"Context")+".Provider";case Tt:return(e._context.displayName||"Context")+".Consumer";case za:var a=e.render;return e=e.displayName,e||(e=a.displayName||a.name||"",e=e!==""?"ForwardRef("+e+")":"ForwardRef"),e;case Ra:return a=e.displayName||null,a!==null?a:ct(e.type)||"Memo";case We:a=e._payload,e=e._init;try{return ct(e(a))}catch{}}return null}var aa=Array.isArray,G=Ce.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=Te.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,ee={pending:!1,data:null,method:null,action:null},le=[],A=-1;function w(e){return{current:e}}function k(e){0>A||(e.current=le[A],le[A]=null,A--)}function Z(e,a){A++,le[A]=e.current,e.current=a}var I=w(null),fe=w(null),ie=w(null),ga=w(null);function me(e,a){switch(Z(ie,a),Z(fe,e),Z(I,null),a.nodeType){case 9:case 11:e=(e=a.documentElement)&&(e=e.namespaceURI)?xo(e):0;break;default:if(e=a.tagName,a=a.namespaceURI)a=xo(a),e=qo(a,e);else switch(e){case"svg":e=1;break;case"math":e=2;break;default:e=0}}k(I),Z(I,e)}function Ka(){k(I),k(fe),k(ie)}function Su(e){e.memoizedState!==null&&Z(ga,e);var a=I.current,t=qo(a,e.type);a!==t&&(Z(fe,e),Z(I,t))}function rl(e){fe.current===e&&(k(I),k(fe)),ga.current===e&&(k(ga),pi._currentValue=ee)}var fl=Object.prototype.hasOwnProperty,Zl=K.unstable_scheduleCallback,Oi=K.unstable_cancelCallback,wt=K.unstable_shouldYield,st=K.unstable_requestPaint,Oa=K.unstable_now,yf=K.unstable_getCurrentPriorityLevel,bn=K.unstable_ImmediatePriority,_a=K.unstable_UserBlockingPriority,Kl=K.unstable_NormalPriority,Pc=K.unstable_LowPriority,Cn=K.unstable_IdlePriority,Ic=K.log,rt=K.unstable_setDisableYieldValue,dl=null,va=null;function Nt(e){if(typeof Ic=="function"&&rt(e),va&&typeof va.setStrictMode=="function")try{va.setStrictMode(dl,e)}catch{}}var ra=Math.clz32?Math.clz32:Mi,Dn=Math.log,_i=Math.LN2;function Mi(e){return e>>>=0,e===0?32:31-(Dn(e)/_i|0)|0}var Jl=256,Rn=4194304;function ft(e){var a=e&42;if(a!==0)return a;switch(e&-e){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return e&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return e}}function xa(e,a,t){var l=e.pendingLanes;if(l===0)return 0;var n=0,u=e.suspendedLanes,r=e.pingedLanes;e=e.warmLanes;var o=l&134217727;return o!==0?(l=o&~u,l!==0?n=ft(l):(r&=o,r!==0?n=ft(r):t||(t=o&~e,t!==0&&(n=ft(t))))):(o=l&~u,o!==0?n=ft(o):r!==0?n=ft(r):t||(t=l&~e,t!==0&&(n=ft(t)))),n===0?0:a!==0&&a!==n&&(a&u)===0&&(u=n&-n,t=a&-a,u>=t||u===32&&(t&4194048)!==0)?a:n}function ol(e,a){return(e.pendingLanes&~(e.suspendedLanes&~e.pingedLanes)&a)===0}function Bi(e,a){switch(e){case 1:case 2:case 4:case 8:case 64:return a+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return a+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Au(){var e=Jl;return Jl<<=1,(Jl&4194048)===0&&(Jl=256),e}function pu(){var e=Rn;return Rn<<=1,(Rn&62914560)===0&&(Rn=4194304),e}function Yt(e){for(var a=[],t=0;31>t;t++)a.push(e);return a}function Wl(e,a){e.pendingLanes|=a,a!==268435456&&(e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0)}function On(e,a,t,l,n,u){var r=e.pendingLanes;e.pendingLanes=t,e.suspendedLanes=0,e.pingedLanes=0,e.warmLanes=0,e.expiredLanes&=t,e.entangledLanes&=t,e.errorRecoveryDisabledLanes&=t,e.shellSuspendCounter=0;var o=e.entanglements,S=e.expirationTimes,C=e.hiddenUpdates;for(t=r&~t;0<t;){var H=31-ra(t),x=1<<H;o[H]=0,S[H]=-1;var R=C[H];if(R!==null)for(C[H]=null,H=0;H<R.length;H++){var O=R[H];O!==null&&(O.lane&=-536870913)}t&=~x}l!==0&&Gi(e,l,0),u!==0&&n===0&&e.tag!==0&&(e.suspendedLanes|=u&~(r&~a))}function Gi(e,a,t){e.pendingLanes|=a,e.suspendedLanes&=~a;var l=31-ra(a);e.entangledLanes|=a,e.entanglements[l]=e.entanglements[l]|1073741824|t&4194090}function Hi(e,a){var t=e.entangledLanes|=a;for(e=e.entanglements;t;){var l=31-ra(t),n=1<<l;n&a|e[l]&a&&(e[l]|=a),t&=~n}}function bt(e){switch(e){case 2:e=1;break;case 8:e=4;break;case 32:e=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:e=128;break;case 268435456:e=134217728;break;default:e=0}return e}function Vt(e){return e&=-e,2<e?8<e?(e&134217727)!==0?32:268435456:8:2}function ta(){var e=q.p;return e!==0?e:(e=window.event,e===void 0?32:nh(e.type))}function Fl(e,a){var t=q.p;try{return q.p=e,a()}finally{q.p=t}}var Ae=Math.random().toString(36).slice(2),Ve="__reactFiber$"+Ae,ve="__reactProps$"+Ae,Ja="__reactContainer$"+Ae,la="__reactEvents$"+Ae,qa="__reactListeners$"+Ae,ba="__reactHandles$"+Ae,Wa="__reactResources$"+Ae,dt="__reactMarker$"+Ae;function hl(e){delete e[Ve],delete e[ve],delete e[la],delete e[qa],delete e[ba]}function ot(e){var a=e[Ve];if(a)return a;for(var t=e.parentNode;t;){if(a=t[Ja]||t[Ve]){if(t=a.alternate,a.child!==null||t!==null&&t.child!==null)for(e=Xo(e);e!==null;){if(t=e[Ve])return t;e=Xo(e)}return a}e=t,t=e.parentNode}return null}function ml(e){if(e=e[Ve]||e[Ja]){var a=e.tag;if(a===5||a===6||a===13||a===26||a===27||a===3)return e}return null}function Xt(e){var a=e.tag;if(a===5||a===26||a===27||a===6)return e.stateNode;throw Error(_(33))}function ht(e){var a=e[Wa];return a||(a=e[Wa]={hoistableStyles:new Map,hoistableScripts:new Map}),a}function qe(e){e[dt]=!0}var Ui=new Set,_n={};function fa(e,a){Ct(e,a),Ct(e+"Capture",a)}function Ct(e,a){for(_n[e]=a,e=0;e<a.length;e++)Ui.add(a[e])}var Dt=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Pl={},Mn={};function Rt(e){return fl.call(Mn,e)?!0:fl.call(Pl,e)?!1:Dt.test(e)?Mn[e]=!0:(Pl[e]=!0,!1)}function Bn(e,a,t){if(Rt(a))if(t===null)e.removeAttribute(a);else{switch(typeof t){case"undefined":case"function":case"symbol":e.removeAttribute(a);return;case"boolean":var l=a.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){e.removeAttribute(a);return}}e.setAttribute(a,""+t)}}function Il(e,a,t){if(t===null)e.removeAttribute(a);else{switch(typeof t){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(a);return}e.setAttribute(a,""+t)}}function mt(e,a,t,l){if(l===null)e.removeAttribute(t);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":e.removeAttribute(t);return}e.setAttributeNS(a,t,""+l)}}var Gn,yl;function $t(e){if(Gn===void 0)try{throw Error()}catch(t){var a=t.stack.trim().match(/\n( *(at )?)/);Gn=a&&a[1]||"",yl=-1<t.stack.indexOf(`
    at`)?" (<anonymous>)":-1<t.stack.indexOf("@")?"@unknown:0:0":""}return`
`+Gn+e+yl}var Eu=!1;function en(e,a){if(!e||Eu)return"";Eu=!0;var t=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(a){var x=function(){throw Error()};if(Object.defineProperty(x.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(x,[])}catch(O){var R=O}Reflect.construct(e,[],x)}else{try{x.call()}catch(O){R=O}e.call(x.prototype)}}else{try{throw Error()}catch(O){R=O}(x=e())&&typeof x.catch=="function"&&x.catch(function(){})}}catch(O){if(O&&R&&typeof O.stack=="string")return[O.stack,R.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),r=u[0],o=u[1];if(r&&o){var S=r.split(`
`),C=o.split(`
`);for(n=l=0;l<S.length&&!S[l].includes("DetermineComponentFrameRoot");)l++;for(;n<C.length&&!C[n].includes("DetermineComponentFrameRoot");)n++;if(l===S.length||n===C.length)for(l=S.length-1,n=C.length-1;1<=l&&0<=n&&S[l]!==C[n];)n--;for(;1<=l&&0<=n;l--,n--)if(S[l]!==C[n]){if(l!==1||n!==1)do if(l--,n--,0>n||S[l]!==C[n]){var H=`
`+S[l].replace(" at new "," at ");return e.displayName&&H.includes("<anonymous>")&&(H=H.replace("<anonymous>",e.displayName)),H}while(1<=l&&0<=n);break}}}finally{Eu=!1,Error.prepareStackTrace=t}return(t=e?e.displayName||e.name:"")?$t(t):""}function Hn(e){switch(e.tag){case 26:case 27:case 5:return $t(e.type);case 16:return $t("Lazy");case 13:return $t("Suspense");case 19:return $t("SuspenseList");case 0:case 15:return en(e.type,!1);case 11:return en(e.type.render,!1);case 1:return en(e.type,!0);case 31:return $t("Activity");default:return""}}function Un(e){try{var a="";do a+=Hn(e),e=e.return;while(e);return a}catch(t){return`
Error generating stack: `+t.message+`
`+t.stack}}function Xe(e){switch(typeof e){case"bigint":case"boolean":case"number":case"string":case"undefined":return e;case"object":return e;default:return""}}function jn(e){var a=e.type;return(e=e.nodeName)&&e.toLowerCase()==="input"&&(a==="checkbox"||a==="radio")}function es(e){var a=jn(e)?"checked":"value",t=Object.getOwnPropertyDescriptor(e.constructor.prototype,a),l=""+e[a];if(!e.hasOwnProperty(a)&&typeof t<"u"&&typeof t.get=="function"&&typeof t.set=="function"){var n=t.get,u=t.set;return Object.defineProperty(e,a,{configurable:!0,get:function(){return n.call(this)},set:function(r){l=""+r,u.call(this,r)}}),Object.defineProperty(e,a,{enumerable:t.enumerable}),{getValue:function(){return l},setValue:function(r){l=""+r},stopTracking:function(){e._valueTracker=null,delete e[a]}}}}function gl(e){e._valueTracker||(e._valueTracker=es(e))}function an(e){if(!e)return!1;var a=e._valueTracker;if(!a)return!0;var t=a.getValue(),l="";return e&&(l=jn(e)?e.checked?"true":"false":e.value),e=l,e!==t?(a.setValue(e),!0):!1}function vl(e){if(e=e||(typeof document<"u"?document:void 0),typeof e>"u")return null;try{return e.activeElement||e.body}catch{return e.body}}var as=/[\n"\\]/g;function Sa(e){return e.replace(as,function(a){return"\\"+a.charCodeAt(0).toString(16)+" "})}function Sl(e,a,t,l,n,u,r,o){e.name="",r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?e.type=r:e.removeAttribute("type"),a!=null?r==="number"?(a===0&&e.value===""||e.value!=a)&&(e.value=""+Xe(a)):e.value!==""+Xe(a)&&(e.value=""+Xe(a)):r!=="submit"&&r!=="reset"||e.removeAttribute("value"),a!=null?Tu(e,r,Xe(a)):t!=null?Tu(e,r,Xe(t)):l!=null&&e.removeAttribute("value"),n==null&&u!=null&&(e.defaultChecked=!!u),n!=null&&(e.checked=n&&typeof n!="function"&&typeof n!="symbol"),o!=null&&typeof o!="function"&&typeof o!="symbol"&&typeof o!="boolean"?e.name=""+Xe(o):e.removeAttribute("name")}function zn(e,a,t,l,n,u,r,o){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(e.type=u),a!=null||t!=null){if(!(u!=="submit"&&u!=="reset"||a!=null))return;t=t!=null?""+Xe(t):"",a=a!=null?""+Xe(a):t,o||a===e.value||(e.value=a),e.defaultValue=a}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,e.checked=o?e.checked:!!l,e.defaultChecked=!!l,r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(e.name=r)}function Tu(e,a,t){a==="number"&&vl(e.ownerDocument)===e||e.defaultValue===""+t||(e.defaultValue=""+t)}function Ot(e,a,t,l){if(e=e.options,a){a={};for(var n=0;n<t.length;n++)a["$"+t[n]]=!0;for(t=0;t<e.length;t++)n=a.hasOwnProperty("$"+e[t].value),e[t].selected!==n&&(e[t].selected=n),n&&l&&(e[t].defaultSelected=!0)}else{for(t=""+Xe(t),a=null,n=0;n<e.length;n++){if(e[n].value===t){e[n].selected=!0,l&&(e[n].defaultSelected=!0);return}a!==null||e[n].disabled||(a=e[n])}a!==null&&(a.selected=!0)}}function ji(e,a,t){if(a!=null&&(a=""+Xe(a),a!==e.value&&(e.value=a),t==null)){e.defaultValue!==a&&(e.defaultValue=a);return}e.defaultValue=t!=null?""+Xe(t):""}function Ln(e,a,t,l){if(a==null){if(l!=null){if(t!=null)throw Error(_(92));if(aa(l)){if(1<l.length)throw Error(_(93));l=l[0]}t=l}t==null&&(t=""),a=t}t=Xe(a),e.defaultValue=t,l=e.textContent,l===t&&l!==""&&l!==null&&(e.value=l)}function Al(e,a){if(a){var t=e.firstChild;if(t&&t===e.lastChild&&t.nodeType===3){t.nodeValue=a;return}}e.textContent=a}var Nu=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function Fa(e,a,t){var l=a.indexOf("--")===0;t==null||typeof t=="boolean"||t===""?l?e.setProperty(a,""):a==="float"?e.cssFloat="":e[a]="":l?e.setProperty(a,t):typeof t!="number"||t===0||Nu.has(a)?a==="float"?e.cssFloat=t:e[a]=(""+t).trim():e[a]=t+"px"}function xn(e,a,t){if(a!=null&&typeof a!="object")throw Error(_(62));if(e=e.style,t!=null){for(var l in t)!t.hasOwnProperty(l)||a!=null&&a.hasOwnProperty(l)||(l.indexOf("--")===0?e.setProperty(l,""):l==="float"?e.cssFloat="":e[l]="");for(var n in a)l=a[n],a.hasOwnProperty(n)&&t[n]!==l&&Fa(e,n,l)}else for(var u in a)a.hasOwnProperty(u)&&Fa(e,u,a[u])}function yt(e){if(e.indexOf("-")===-1)return!1;switch(e){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var bu=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),qn=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function Qt(e){return qn.test(""+e)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":e}var da=null;function tn(e){return e=e.target||e.srcElement||window,e.correspondingUseElement&&(e=e.correspondingUseElement),e.nodeType===3?e.parentNode:e}var gt=null,vt=null;function Cu(e){var a=ml(e);if(a&&(e=a.stateNode)){var t=e[ve]||null;e:switch(e=a.stateNode,a.type){case"input":if(Sl(e,t.value,t.defaultValue,t.defaultValue,t.checked,t.defaultChecked,t.type,t.name),a=t.name,t.type==="radio"&&a!=null){for(t=e;t.parentNode;)t=t.parentNode;for(t=t.querySelectorAll('input[name="'+Sa(""+a)+'"][type="radio"]'),a=0;a<t.length;a++){var l=t[a];if(l!==e&&l.form===e.form){var n=l[ve]||null;if(!n)throw Error(_(90));Sl(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(a=0;a<t.length;a++)l=t[a],l.form===e.form&&an(l)}break e;case"textarea":ji(e,t.value,t.defaultValue);break e;case"select":a=t.value,a!=null&&Ot(e,!!t.multiple,a,!1)}}}var wn=!1;function zi(e,a,t){if(wn)return e(a,t);wn=!0;try{var l=e(a);return l}finally{if(wn=!1,(gt!==null||vt!==null)&&(Bc(),gt&&(a=gt,e=vt,vt=gt=null,Cu(a),e)))for(a=0;a<e.length;a++)Cu(e[a])}}function ln(e,a){var t=e.stateNode;if(t===null)return null;var l=t[ve]||null;if(l===null)return null;t=l[a];e:switch(a){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(e=e.type,l=!(e==="button"||e==="input"||e==="select"||e==="textarea")),e=!l;break e;default:e=!1}if(e)return null;if(t&&typeof t!="function")throw Error(_(231,a,typeof t));return t}var ye=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),Pa=!1;if(ye)try{var nn={};Object.defineProperty(nn,"passive",{get:function(){Pa=!0}}),window.addEventListener("test",nn,nn),window.removeEventListener("test",nn,nn)}catch{Pa=!1}var _t=null,Du=null,Mt=null;function Bt(){if(Mt)return Mt;var e,a=Du,t=a.length,l,n="value"in _t?_t.value:_t.textContent,u=n.length;for(e=0;e<t&&a[e]===n[e];e++);var r=t-e;for(l=1;l<=r&&a[t-l]===n[u-l];l++);return Mt=n.slice(e,1<l?1-l:void 0)}function St(e){var a=e.keyCode;return"charCode"in e?(e=e.charCode,e===0&&a===13&&(e=13)):e=a,e===10&&(e=13),32<=e||e===13?e:0}function kt(){return!0}function na(){return!1}function Ne(e){function a(t,l,n,u,r){this._reactName=t,this._targetInst=n,this.type=l,this.nativeEvent=u,this.target=r,this.currentTarget=null;for(var o in e)e.hasOwnProperty(o)&&(t=e[o],this[o]=t?t(u):u[o]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?kt:na,this.isPropagationStopped=na,this}return U(a.prototype,{preventDefault:function(){this.defaultPrevented=!0;var t=this.nativeEvent;t&&(t.preventDefault?t.preventDefault():typeof t.returnValue!="unknown"&&(t.returnValue=!1),this.isDefaultPrevented=kt)},stopPropagation:function(){var t=this.nativeEvent;t&&(t.stopPropagation?t.stopPropagation():typeof t.cancelBubble!="unknown"&&(t.cancelBubble=!0),this.isPropagationStopped=kt)},persist:function(){},isPersistent:kt}),a}var oa={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(e){return e.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},Ma=Ne(oa),Fe=U({},oa,{view:0,detail:0}),pl=Ne(Fe),Ru,Yn,un,At=U({},Fe,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:_u,button:0,buttons:0,relatedTarget:function(e){return e.relatedTarget===void 0?e.fromElement===e.srcElement?e.toElement:e.fromElement:e.relatedTarget},movementX:function(e){return"movementX"in e?e.movementX:(e!==un&&(un&&e.type==="mousemove"?(Ru=e.screenX-un.screenX,Yn=e.screenY-un.screenY):Yn=Ru=0,un=e),Ru)},movementY:function(e){return"movementY"in e?e.movementY:Yn}}),Zt=Ne(At),El=U({},At,{dataTransfer:0}),ts=Ne(El),ls=U({},Fe,{relatedTarget:0}),Ou=Ne(ls),Li=U({},oa,{animationName:0,elapsedTime:0,pseudoElement:0}),xi=Ne(Li),qi=U({},oa,{clipboardData:function(e){return"clipboardData"in e?e.clipboardData:window.clipboardData}}),cn=Ne(qi),sn=U({},oa,{data:0}),Kt=Ne(sn),ns={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},us={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},is={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function cs(e){var a=this.nativeEvent;return a.getModifierState?a.getModifierState(e):(e=is[e])?!!a[e]:!1}function _u(){return cs}var ss=U({},Fe,{key:function(e){if(e.key){var a=ns[e.key]||e.key;if(a!=="Unidentified")return a}return e.type==="keypress"?(e=St(e),e===13?"Enter":String.fromCharCode(e)):e.type==="keydown"||e.type==="keyup"?us[e.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:_u,charCode:function(e){return e.type==="keypress"?St(e):0},keyCode:function(e){return e.type==="keydown"||e.type==="keyup"?e.keyCode:0},which:function(e){return e.type==="keypress"?St(e):e.type==="keydown"||e.type==="keyup"?e.keyCode:0}}),rs=Ne(ss),fs=U({},At,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),Mu=Ne(fs),ds=U({},Fe,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:_u}),wi=Ne(ds),Bu=U({},oa,{propertyName:0,elapsedTime:0,pseudoElement:0}),os=Ne(Bu),Gu=U({},At,{deltaX:function(e){return"deltaX"in e?e.deltaX:"wheelDeltaX"in e?-e.wheelDeltaX:0},deltaY:function(e){return"deltaY"in e?e.deltaY:"wheelDeltaY"in e?-e.wheelDeltaY:"wheelDelta"in e?-e.wheelDelta:0},deltaZ:0,deltaMode:0}),hs=Ne(Gu),ms=U({},oa,{newState:0,oldState:0}),ys=Ne(ms),gs=[9,13,27,32],Vn=ye&&"CompositionEvent"in window,rn=null;ye&&"documentMode"in document&&(rn=document.documentMode);var vs=ye&&"TextEvent"in window&&!rn,Yi=ye&&(!Vn||rn&&8<rn&&11>=rn),Vi=" ",Xi=!1;function $i(e,a){switch(e){case"keyup":return gs.indexOf(a.keyCode)!==-1;case"keydown":return a.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function Qi(e){return e=e.detail,typeof e=="object"&&"data"in e?e.data:null}var Tl=!1;function Ss(e,a){switch(e){case"compositionend":return Qi(a);case"keypress":return a.which!==32?null:(Xi=!0,Vi);case"textInput":return e=a.data,e===Vi&&Xi?null:e;default:return null}}function As(e,a){if(Tl)return e==="compositionend"||!Vn&&$i(e,a)?(e=Bt(),Mt=Du=_t=null,Tl=!1,e):null;switch(e){case"paste":return null;case"keypress":if(!(a.ctrlKey||a.altKey||a.metaKey)||a.ctrlKey&&a.altKey){if(a.char&&1<a.char.length)return a.char;if(a.which)return String.fromCharCode(a.which)}return null;case"compositionend":return Yi&&a.locale!=="ko"?null:a.data;default:return null}}var ki={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function Nl(e){var a=e&&e.nodeName&&e.nodeName.toLowerCase();return a==="input"?!!ki[e.type]:a==="textarea"}function Hu(e,a,t,l){gt?vt?vt.push(l):vt=[l]:gt=l,a=Lc(a,"onChange"),0<a.length&&(t=new Ma("onChange","change",null,t,l),e.push({event:t,listeners:a}))}var Gt=null,fn=null;function ps(e){Ho(e,0)}function Ba(e){var a=Xt(e);if(an(a))return e}function Uu(e,a){if(e==="change")return a}var ju=!1;if(ye){var zu;if(ye){var Lu="oninput"in document;if(!Lu){var Zi=document.createElement("div");Zi.setAttribute("oninput","return;"),Lu=typeof Zi.oninput=="function"}zu=Lu}else zu=!1;ju=zu&&(!document.documentMode||9<document.documentMode)}function Ki(){Gt&&(Gt.detachEvent("onpropertychange",Ji),fn=Gt=null)}function Ji(e){if(e.propertyName==="value"&&Ba(fn)){var a=[];Hu(a,fn,e,tn(e)),zi(ps,a)}}function Es(e,a,t){e==="focusin"?(Ki(),Gt=a,fn=t,Gt.attachEvent("onpropertychange",Ji)):e==="focusout"&&Ki()}function Ts(e){if(e==="selectionchange"||e==="keyup"||e==="keydown")return Ba(fn)}function Wi(e,a){if(e==="click")return Ba(a)}function Ga(e,a){if(e==="input"||e==="change")return Ba(a)}function Fi(e,a){return e===a&&(e!==0||1/e===1/a)||e!==e&&a!==a}var Aa=typeof Object.is=="function"?Object.is:Fi;function bl(e,a){if(Aa(e,a))return!0;if(typeof e!="object"||e===null||typeof a!="object"||a===null)return!1;var t=Object.keys(e),l=Object.keys(a);if(t.length!==l.length)return!1;for(l=0;l<t.length;l++){var n=t[l];if(!fl.call(a,n)||!Aa(e[n],a[n]))return!1}return!0}function xu(e){for(;e&&e.firstChild;)e=e.firstChild;return e}function qu(e,a){var t=xu(e);e=0;for(var l;t;){if(t.nodeType===3){if(l=e+t.textContent.length,e<=a&&l>=a)return{node:t,offset:a-e};e=l}e:{for(;t;){if(t.nextSibling){t=t.nextSibling;break e}t=t.parentNode}t=void 0}t=xu(t)}}function wu(e,a){return e&&a?e===a?!0:e&&e.nodeType===3?!1:a&&a.nodeType===3?wu(e,a.parentNode):"contains"in e?e.contains(a):e.compareDocumentPosition?!!(e.compareDocumentPosition(a)&16):!1:!1}function Yu(e){e=e!=null&&e.ownerDocument!=null&&e.ownerDocument.defaultView!=null?e.ownerDocument.defaultView:window;for(var a=vl(e.document);a instanceof e.HTMLIFrameElement;){try{var t=typeof a.contentWindow.location.href=="string"}catch{t=!1}if(t)e=a.contentWindow;else break;a=vl(e.document)}return a}function Xn(e){var a=e&&e.nodeName&&e.nodeName.toLowerCase();return a&&(a==="input"&&(e.type==="text"||e.type==="search"||e.type==="tel"||e.type==="url"||e.type==="password")||a==="textarea"||e.contentEditable==="true")}var Pi=ye&&"documentMode"in document&&11>=document.documentMode,Jt=null,$n=null,Cl=null,Qn=!1;function i(e,a,t){var l=t.window===t?t.document:t.nodeType===9?t:t.ownerDocument;Qn||Jt==null||Jt!==vl(l)||(l=Jt,"selectionStart"in l&&Xn(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),Cl&&bl(Cl,l)||(Cl=l,l=Lc($n,"onSelect"),0<l.length&&(a=new Ma("onSelect","select",null,a,t),e.push({event:a,listeners:l}),a.target=Jt)))}function c(e,a){var t={};return t[e.toLowerCase()]=a.toLowerCase(),t["Webkit"+e]="webkit"+a,t["Moz"+e]="moz"+a,t}var s={animationend:c("Animation","AnimationEnd"),animationiteration:c("Animation","AnimationIteration"),animationstart:c("Animation","AnimationStart"),transitionrun:c("Transition","TransitionRun"),transitionstart:c("Transition","TransitionStart"),transitioncancel:c("Transition","TransitionCancel"),transitionend:c("Transition","TransitionEnd")},d={},h={};ye&&(h=document.createElement("div").style,"AnimationEvent"in window||(delete s.animationend.animation,delete s.animationiteration.animation,delete s.animationstart.animation),"TransitionEvent"in window||delete s.transitionend.transition);function m(e){if(d[e])return d[e];if(!s[e])return e;var a=s[e],t;for(t in a)if(a.hasOwnProperty(t)&&t in h)return d[e]=a[t];return e}var y=m("animationend"),T=m("animationiteration"),D=m("animationstart"),L=m("transitionrun"),Q=m("transitionstart"),F=m("transitioncancel"),J=m("transitionend"),ue=new Map,Ue="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Ue.push("scrollEnd");function Ge(e,a){ue.set(e,a),fa(a,[e])}var Ht=new WeakMap;function pa(e,a){if(typeof e=="object"&&e!==null){var t=Ht.get(e);return t!==void 0?t:(a={value:e,source:a,stack:Un(a)},Ht.set(e,a),a)}return{value:e,source:a,stack:Un(a)}}var Ia=[],kn=0,Ns=0;function Ii(){for(var e=kn,a=Ns=kn=0;a<e;){var t=Ia[a];Ia[a++]=null;var l=Ia[a];Ia[a++]=null;var n=Ia[a];Ia[a++]=null;var u=Ia[a];if(Ia[a++]=null,l!==null&&n!==null){var r=l.pending;r===null?n.next=n:(n.next=r.next,r.next=n),l.pending=n}u!==0&&gf(t,n,u)}}function ec(e,a,t,l){Ia[kn++]=e,Ia[kn++]=a,Ia[kn++]=t,Ia[kn++]=l,Ns|=l,e.lanes|=l,e=e.alternate,e!==null&&(e.lanes|=l)}function bs(e,a,t,l){return ec(e,a,t,l),ac(e)}function Zn(e,a){return ec(e,null,null,a),ac(e)}function gf(e,a,t){e.lanes|=t;var l=e.alternate;l!==null&&(l.lanes|=t);for(var n=!1,u=e.return;u!==null;)u.childLanes|=t,l=u.alternate,l!==null&&(l.childLanes|=t),u.tag===22&&(e=u.stateNode,e===null||e._visibility&1||(n=!0)),e=u,u=u.return;return e.tag===3?(u=e.stateNode,n&&a!==null&&(n=31-ra(t),e=u.hiddenUpdates,l=e[n],l===null?e[n]=[a]:l.push(a),a.lane=t|536870912),u):null}function ac(e){if(50<oi)throw oi=0,Mr=null,Error(_(185));for(var a=e.return;a!==null;)e=a,a=e.return;return e.tag===3?e.stateNode:null}var Kn={};function ph(e,a,t,l){this.tag=e,this.key=t,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=a,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function wa(e,a,t,l){return new ph(e,a,t,l)}function Cs(e){return e=e.prototype,!(!e||!e.isReactComponent)}function Wt(e,a){var t=e.alternate;return t===null?(t=wa(e.tag,a,e.key,e.mode),t.elementType=e.elementType,t.type=e.type,t.stateNode=e.stateNode,t.alternate=e,e.alternate=t):(t.pendingProps=a,t.type=e.type,t.flags=0,t.subtreeFlags=0,t.deletions=null),t.flags=e.flags&65011712,t.childLanes=e.childLanes,t.lanes=e.lanes,t.child=e.child,t.memoizedProps=e.memoizedProps,t.memoizedState=e.memoizedState,t.updateQueue=e.updateQueue,a=e.dependencies,t.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext},t.sibling=e.sibling,t.index=e.index,t.ref=e.ref,t.refCleanup=e.refCleanup,t}function vf(e,a){e.flags&=65011714;var t=e.alternate;return t===null?(e.childLanes=0,e.lanes=a,e.child=null,e.subtreeFlags=0,e.memoizedProps=null,e.memoizedState=null,e.updateQueue=null,e.dependencies=null,e.stateNode=null):(e.childLanes=t.childLanes,e.lanes=t.lanes,e.child=t.child,e.subtreeFlags=0,e.deletions=null,e.memoizedProps=t.memoizedProps,e.memoizedState=t.memoizedState,e.updateQueue=t.updateQueue,e.type=t.type,a=t.dependencies,e.dependencies=a===null?null:{lanes:a.lanes,firstContext:a.firstContext}),e}function tc(e,a,t,l,n,u){var r=0;if(l=e,typeof e=="function")Cs(e)&&(r=1);else if(typeof e=="string")r=Tm(e,t,I.current)?26:e==="html"||e==="head"||e==="body"?27:5;else e:switch(e){case La:return e=wa(31,t,a,n),e.elementType=La,e.lanes=u,e;case sa:return dn(t.children,n,u,a);case E:r=8,n|=24;break;case ke:return e=wa(12,t,a,n|2),e.elementType=ke,e.lanes=u,e;case ge:return e=wa(13,t,a,n),e.elementType=ge,e.lanes=u,e;case Na:return e=wa(19,t,a,n),e.elementType=Na,e.lanes=u,e;default:if(typeof e=="object"&&e!==null)switch(e.$$typeof){case ut:case Ze:r=10;break e;case Tt:r=9;break e;case za:r=11;break e;case Ra:r=14;break e;case We:r=16,l=null;break e}r=29,t=Error(_(130,e===null?"null":typeof e,"")),l=null}return a=wa(r,t,a,n),a.elementType=e,a.type=l,a.lanes=u,a}function dn(e,a,t,l){return e=wa(7,e,l,a),e.lanes=t,e}function Ds(e,a,t){return e=wa(6,e,null,a),e.lanes=t,e}function Rs(e,a,t){return a=wa(4,e.children!==null?e.children:[],e.key,a),a.lanes=t,a.stateNode={containerInfo:e.containerInfo,pendingChildren:null,implementation:e.implementation},a}var Jn=[],Wn=0,lc=null,nc=0,et=[],at=0,on=null,Ft=1,Pt="";function hn(e,a){Jn[Wn++]=nc,Jn[Wn++]=lc,lc=e,nc=a}function Sf(e,a,t){et[at++]=Ft,et[at++]=Pt,et[at++]=on,on=e;var l=Ft;e=Pt;var n=32-ra(l)-1;l&=~(1<<n),t+=1;var u=32-ra(a)+n;if(30<u){var r=n-n%5;u=(l&(1<<r)-1).toString(32),l>>=r,n-=r,Ft=1<<32-ra(a)+n|t<<n|l,Pt=u+e}else Ft=1<<u|t<<n|l,Pt=e}function Os(e){e.return!==null&&(hn(e,1),Sf(e,1,0))}function _s(e){for(;e===lc;)lc=Jn[--Wn],Jn[Wn]=null,nc=Jn[--Wn],Jn[Wn]=null;for(;e===on;)on=et[--at],et[at]=null,Pt=et[--at],et[at]=null,Ft=et[--at],et[at]=null}var Ca=null,we=null,be=!1,mn=null,Ut=!1,Ms=Error(_(519));function yn(e){var a=Error(_(418,""));throw $u(pa(a,e)),Ms}function Af(e){var a=e.stateNode,t=e.type,l=e.memoizedProps;switch(a[Ve]=e,a[ve]=l,t){case"dialog":oe("cancel",a),oe("close",a);break;case"iframe":case"object":case"embed":oe("load",a);break;case"video":case"audio":for(t=0;t<mi.length;t++)oe(mi[t],a);break;case"source":oe("error",a);break;case"img":case"image":case"link":oe("error",a),oe("load",a);break;case"details":oe("toggle",a);break;case"input":oe("invalid",a),zn(a,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),gl(a);break;case"select":oe("invalid",a);break;case"textarea":oe("invalid",a),Ln(a,l.value,l.defaultValue,l.children),gl(a)}t=l.children,typeof t!="string"&&typeof t!="number"&&typeof t!="bigint"||a.textContent===""+t||l.suppressHydrationWarning===!0||Lo(a.textContent,t)?(l.popover!=null&&(oe("beforetoggle",a),oe("toggle",a)),l.onScroll!=null&&oe("scroll",a),l.onScrollEnd!=null&&oe("scrollend",a),l.onClick!=null&&(a.onclick=xc),a=!0):a=!1,a||yn(e)}function pf(e){for(Ca=e.return;Ca;)switch(Ca.tag){case 5:case 13:Ut=!1;return;case 27:case 3:Ut=!0;return;default:Ca=Ca.return}}function Vu(e){if(e!==Ca)return!1;if(!be)return pf(e),be=!0,!1;var a=e.tag,t;if((t=a!==3&&a!==27)&&((t=a===5)&&(t=e.type,t=!(t!=="form"&&t!=="button")||kr(e.type,e.memoizedProps)),t=!t),t&&we&&yn(e),pf(e),a===13){if(e=e.memoizedState,e=e!==null?e.dehydrated:null,!e)throw Error(_(317));e:{for(e=e.nextSibling,a=0;e;){if(e.nodeType===8)if(t=e.data,t==="/$"){if(a===0){we=Et(e.nextSibling);break e}a--}else t!=="$"&&t!=="$!"&&t!=="$?"||a++;e=e.nextSibling}we=null}}else a===27?(a=we,Yl(e.type)?(e=Wr,Wr=null,we=e):we=a):we=Ca?Et(e.stateNode.nextSibling):null;return!0}function Xu(){we=Ca=null,be=!1}function Ef(){var e=mn;return e!==null&&(ja===null?ja=e:ja.push.apply(ja,e),mn=null),e}function $u(e){mn===null?mn=[e]:mn.push(e)}var Bs=w(null),gn=null,It=null;function Dl(e,a,t){Z(Bs,a._currentValue),a._currentValue=t}function el(e){e._currentValue=Bs.current,k(Bs)}function Gs(e,a,t){for(;e!==null;){var l=e.alternate;if((e.childLanes&a)!==a?(e.childLanes|=a,l!==null&&(l.childLanes|=a)):l!==null&&(l.childLanes&a)!==a&&(l.childLanes|=a),e===t)break;e=e.return}}function Hs(e,a,t,l){var n=e.child;for(n!==null&&(n.return=e);n!==null;){var u=n.dependencies;if(u!==null){var r=n.child;u=u.firstContext;e:for(;u!==null;){var o=u;u=n;for(var S=0;S<a.length;S++)if(o.context===a[S]){u.lanes|=t,o=u.alternate,o!==null&&(o.lanes|=t),Gs(u.return,t,e),l||(r=null);break e}u=o.next}}else if(n.tag===18){if(r=n.return,r===null)throw Error(_(341));r.lanes|=t,u=r.alternate,u!==null&&(u.lanes|=t),Gs(r,t,e),r=null}else r=n.child;if(r!==null)r.return=n;else for(r=n;r!==null;){if(r===e){r=null;break}if(n=r.sibling,n!==null){n.return=r.return,r=n;break}r=r.return}n=r}}function Qu(e,a,t,l){e=null;for(var n=a,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var r=n.alternate;if(r===null)throw Error(_(387));if(r=r.memoizedProps,r!==null){var o=n.type;Aa(n.pendingProps.value,r.value)||(e!==null?e.push(o):e=[o])}}else if(n===ga.current){if(r=n.alternate,r===null)throw Error(_(387));r.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(e!==null?e.push(pi):e=[pi])}n=n.return}e!==null&&Hs(a,e,t,l),a.flags|=262144}function uc(e){for(e=e.firstContext;e!==null;){if(!Aa(e.context._currentValue,e.memoizedValue))return!0;e=e.next}return!1}function vn(e){gn=e,It=null,e=e.dependencies,e!==null&&(e.firstContext=null)}function Ea(e){return Tf(gn,e)}function ic(e,a){return gn===null&&vn(e),Tf(e,a)}function Tf(e,a){var t=a._currentValue;if(a={context:a,memoizedValue:t,next:null},It===null){if(e===null)throw Error(_(308));It=a,e.dependencies={lanes:0,firstContext:a},e.flags|=524288}else It=It.next=a;return t}var Eh=typeof AbortController<"u"?AbortController:function(){var e=[],a=this.signal={aborted:!1,addEventListener:function(t,l){e.push(l)}};this.abort=function(){a.aborted=!0,e.forEach(function(t){return t()})}},Th=K.unstable_scheduleCallback,Nh=K.unstable_NormalPriority,Pe={$$typeof:Ze,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function Us(){return{controller:new Eh,data:new Map,refCount:0}}function ku(e){e.refCount--,e.refCount===0&&Th(Nh,function(){e.controller.abort()})}var Zu=null,js=0,Fn=0,Pn=null;function bh(e,a){if(Zu===null){var t=Zu=[];js=0,Fn=Lr(),Pn={status:"pending",value:void 0,then:function(l){t.push(l)}}}return js++,a.then(Nf,Nf),a}function Nf(){if(--js===0&&Zu!==null){Pn!==null&&(Pn.status="fulfilled");var e=Zu;Zu=null,Fn=0,Pn=null;for(var a=0;a<e.length;a++)(0,e[a])()}}function Ch(e,a){var t=[],l={status:"pending",value:null,reason:null,then:function(n){t.push(n)}};return e.then(function(){l.status="fulfilled",l.value=a;for(var n=0;n<t.length;n++)(0,t[n])(a)},function(n){for(l.status="rejected",l.reason=n,n=0;n<t.length;n++)(0,t[n])(void 0)}),l}var bf=G.S;G.S=function(e,a){typeof a=="object"&&a!==null&&typeof a.then=="function"&&bh(e,a),bf!==null&&bf(e,a)};var Sn=w(null);function zs(){var e=Sn.current;return e!==null?e:He.pooledCache}function cc(e,a){a===null?Z(Sn,Sn.current):Z(Sn,a.pool)}function Cf(){var e=zs();return e===null?null:{parent:Pe._currentValue,pool:e}}var Ku=Error(_(460)),Df=Error(_(474)),sc=Error(_(542)),Ls={then:function(){}};function Rf(e){return e=e.status,e==="fulfilled"||e==="rejected"}function rc(){}function Of(e,a,t){switch(t=e[t],t===void 0?e.push(a):t!==a&&(a.then(rc,rc),a=t),a.status){case"fulfilled":return a.value;case"rejected":throw e=a.reason,Mf(e),e;default:if(typeof a.status=="string")a.then(rc,rc);else{if(e=He,e!==null&&100<e.shellSuspendCounter)throw Error(_(482));e=a,e.status="pending",e.then(function(l){if(a.status==="pending"){var n=a;n.status="fulfilled",n.value=l}},function(l){if(a.status==="pending"){var n=a;n.status="rejected",n.reason=l}})}switch(a.status){case"fulfilled":return a.value;case"rejected":throw e=a.reason,Mf(e),e}throw Ju=a,Ku}}var Ju=null;function _f(){if(Ju===null)throw Error(_(459));var e=Ju;return Ju=null,e}function Mf(e){if(e===Ku||e===sc)throw Error(_(483))}var Rl=!1;function xs(e){e.updateQueue={baseState:e.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function qs(e,a){e=e.updateQueue,a.updateQueue===e&&(a.updateQueue={baseState:e.baseState,firstBaseUpdate:e.firstBaseUpdate,lastBaseUpdate:e.lastBaseUpdate,shared:e.shared,callbacks:null})}function Ol(e){return{lane:e,tag:0,payload:null,callback:null,next:null}}function _l(e,a,t){var l=e.updateQueue;if(l===null)return null;if(l=l.shared,(De&2)!==0){var n=l.pending;return n===null?a.next=a:(a.next=n.next,n.next=a),l.pending=a,a=ac(e),gf(e,null,t),a}return ec(e,l,a,t),ac(e)}function Wu(e,a,t){if(a=a.updateQueue,a!==null&&(a=a.shared,(t&4194048)!==0)){var l=a.lanes;l&=e.pendingLanes,t|=l,a.lanes=t,Hi(e,t)}}function ws(e,a){var t=e.updateQueue,l=e.alternate;if(l!==null&&(l=l.updateQueue,t===l)){var n=null,u=null;if(t=t.firstBaseUpdate,t!==null){do{var r={lane:t.lane,tag:t.tag,payload:t.payload,callback:null,next:null};u===null?n=u=r:u=u.next=r,t=t.next}while(t!==null);u===null?n=u=a:u=u.next=a}else n=u=a;t={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},e.updateQueue=t;return}e=t.lastBaseUpdate,e===null?t.firstBaseUpdate=a:e.next=a,t.lastBaseUpdate=a}var Ys=!1;function Fu(){if(Ys){var e=Pn;if(e!==null)throw e}}function Pu(e,a,t,l){Ys=!1;var n=e.updateQueue;Rl=!1;var u=n.firstBaseUpdate,r=n.lastBaseUpdate,o=n.shared.pending;if(o!==null){n.shared.pending=null;var S=o,C=S.next;S.next=null,r===null?u=C:r.next=C,r=S;var H=e.alternate;H!==null&&(H=H.updateQueue,o=H.lastBaseUpdate,o!==r&&(o===null?H.firstBaseUpdate=C:o.next=C,H.lastBaseUpdate=S))}if(u!==null){var x=n.baseState;r=0,H=C=S=null,o=u;do{var R=o.lane&-536870913,O=R!==o.lane;if(O?(Se&R)===R:(l&R)===R){R!==0&&R===Fn&&(Ys=!0),H!==null&&(H=H.next={lane:0,tag:o.tag,payload:o.payload,callback:null,next:null});e:{var ne=e,ae=o;R=a;var Me=t;switch(ae.tag){case 1:if(ne=ae.payload,typeof ne=="function"){x=ne.call(Me,x,R);break e}x=ne;break e;case 3:ne.flags=ne.flags&-65537|128;case 0:if(ne=ae.payload,R=typeof ne=="function"?ne.call(Me,x,R):ne,R==null)break e;x=U({},x,R);break e;case 2:Rl=!0}}R=o.callback,R!==null&&(e.flags|=64,O&&(e.flags|=8192),O=n.callbacks,O===null?n.callbacks=[R]:O.push(R))}else O={lane:R,tag:o.tag,payload:o.payload,callback:o.callback,next:null},H===null?(C=H=O,S=x):H=H.next=O,r|=R;if(o=o.next,o===null){if(o=n.shared.pending,o===null)break;O=o,o=O.next,O.next=null,n.lastBaseUpdate=O,n.shared.pending=null}}while(!0);H===null&&(S=x),n.baseState=S,n.firstBaseUpdate=C,n.lastBaseUpdate=H,u===null&&(n.shared.lanes=0),Ll|=r,e.lanes=r,e.memoizedState=x}}function Bf(e,a){if(typeof e!="function")throw Error(_(191,e));e.call(a)}function Gf(e,a){var t=e.callbacks;if(t!==null)for(e.callbacks=null,e=0;e<t.length;e++)Bf(t[e],a)}var In=w(null),fc=w(0);function Hf(e,a){e=cl,Z(fc,e),Z(In,a),cl=e|a.baseLanes}function Vs(){Z(fc,cl),Z(In,In.current)}function Xs(){cl=fc.current,k(In),k(fc)}var Ml=0,se=null,Oe=null,Ke=null,dc=!1,eu=!1,An=!1,oc=0,Iu=0,au=null,Dh=0;function $e(){throw Error(_(321))}function $s(e,a){if(a===null)return!1;for(var t=0;t<a.length&&t<e.length;t++)if(!Aa(e[t],a[t]))return!1;return!0}function Qs(e,a,t,l,n,u){return Ml=u,se=a,a.memoizedState=null,a.updateQueue=null,a.lanes=0,G.H=e===null||e.memoizedState===null?gd:vd,An=!1,u=t(l,n),An=!1,eu&&(u=jf(a,t,l,n)),Uf(e),u}function Uf(e){G.H=Sc;var a=Oe!==null&&Oe.next!==null;if(Ml=0,Ke=Oe=se=null,dc=!1,Iu=0,au=null,a)throw Error(_(300));e===null||ua||(e=e.dependencies,e!==null&&uc(e)&&(ua=!0))}function jf(e,a,t,l){se=e;var n=0;do{if(eu&&(au=null),Iu=0,eu=!1,25<=n)throw Error(_(301));if(n+=1,Ke=Oe=null,e.updateQueue!=null){var u=e.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}G.H=Hh,u=a(t,l)}while(eu);return u}function Rh(){var e=G.H,a=e.useState()[0];return a=typeof a.then=="function"?ei(a):a,e=e.useState()[0],(Oe!==null?Oe.memoizedState:null)!==e&&(se.flags|=1024),a}function ks(){var e=oc!==0;return oc=0,e}function Zs(e,a,t){a.updateQueue=e.updateQueue,a.flags&=-2053,e.lanes&=~t}function Ks(e){if(dc){for(e=e.memoizedState;e!==null;){var a=e.queue;a!==null&&(a.pending=null),e=e.next}dc=!1}Ml=0,Ke=Oe=se=null,eu=!1,Iu=oc=0,au=null}function Ha(){var e={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Ke===null?se.memoizedState=Ke=e:Ke=Ke.next=e,Ke}function Je(){if(Oe===null){var e=se.alternate;e=e!==null?e.memoizedState:null}else e=Oe.next;var a=Ke===null?se.memoizedState:Ke.next;if(a!==null)Ke=a,Oe=e;else{if(e===null)throw se.alternate===null?Error(_(467)):Error(_(310));Oe=e,e={memoizedState:Oe.memoizedState,baseState:Oe.baseState,baseQueue:Oe.baseQueue,queue:Oe.queue,next:null},Ke===null?se.memoizedState=Ke=e:Ke=Ke.next=e}return Ke}function Js(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function ei(e){var a=Iu;return Iu+=1,au===null&&(au=[]),e=Of(au,e,a),a=se,(Ke===null?a.memoizedState:Ke.next)===null&&(a=a.alternate,G.H=a===null||a.memoizedState===null?gd:vd),e}function hc(e){if(e!==null&&typeof e=="object"){if(typeof e.then=="function")return ei(e);if(e.$$typeof===Ze)return Ea(e)}throw Error(_(438,String(e)))}function Ws(e){var a=null,t=se.updateQueue;if(t!==null&&(a=t.memoCache),a==null){var l=se.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(a={data:l.data.map(function(n){return n.slice()}),index:0})))}if(a==null&&(a={data:[],index:0}),t===null&&(t=Js(),se.updateQueue=t),t.memoCache=a,t=a.data[a.index],t===void 0)for(t=a.data[a.index]=Array(e),l=0;l<e;l++)t[l]=Za;return a.index++,t}function al(e,a){return typeof a=="function"?a(e):a}function mc(e){var a=Je();return Fs(a,Oe,e)}function Fs(e,a,t){var l=e.queue;if(l===null)throw Error(_(311));l.lastRenderedReducer=t;var n=e.baseQueue,u=l.pending;if(u!==null){if(n!==null){var r=n.next;n.next=u.next,u.next=r}a.baseQueue=n=u,l.pending=null}if(u=e.baseState,n===null)e.memoizedState=u;else{a=n.next;var o=r=null,S=null,C=a,H=!1;do{var x=C.lane&-536870913;if(x!==C.lane?(Se&x)===x:(Ml&x)===x){var R=C.revertLane;if(R===0)S!==null&&(S=S.next={lane:0,revertLane:0,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null}),x===Fn&&(H=!0);else if((Ml&R)===R){C=C.next,R===Fn&&(H=!0);continue}else x={lane:0,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},S===null?(o=S=x,r=u):S=S.next=x,se.lanes|=R,Ll|=R;x=C.action,An&&t(u,x),u=C.hasEagerState?C.eagerState:t(u,x)}else R={lane:x,revertLane:C.revertLane,action:C.action,hasEagerState:C.hasEagerState,eagerState:C.eagerState,next:null},S===null?(o=S=R,r=u):S=S.next=R,se.lanes|=x,Ll|=x;C=C.next}while(C!==null&&C!==a);if(S===null?r=u:S.next=o,!Aa(u,e.memoizedState)&&(ua=!0,H&&(t=Pn,t!==null)))throw t;e.memoizedState=u,e.baseState=r,e.baseQueue=S,l.lastRenderedState=u}return n===null&&(l.lanes=0),[e.memoizedState,l.dispatch]}function Ps(e){var a=Je(),t=a.queue;if(t===null)throw Error(_(311));t.lastRenderedReducer=e;var l=t.dispatch,n=t.pending,u=a.memoizedState;if(n!==null){t.pending=null;var r=n=n.next;do u=e(u,r.action),r=r.next;while(r!==n);Aa(u,a.memoizedState)||(ua=!0),a.memoizedState=u,a.baseQueue===null&&(a.baseState=u),t.lastRenderedState=u}return[u,l]}function zf(e,a,t){var l=se,n=Je(),u=be;if(u){if(t===void 0)throw Error(_(407));t=t()}else t=a();var r=!Aa((Oe||n).memoizedState,t);r&&(n.memoizedState=t,ua=!0),n=n.queue;var o=qf.bind(null,l,n,e);if(ai(2048,8,o,[e]),n.getSnapshot!==a||r||Ke!==null&&Ke.memoizedState.tag&1){if(l.flags|=2048,tu(9,yc(),xf.bind(null,l,n,t,a),null),He===null)throw Error(_(349));u||(Ml&124)!==0||Lf(l,a,t)}return t}function Lf(e,a,t){e.flags|=16384,e={getSnapshot:a,value:t},a=se.updateQueue,a===null?(a=Js(),se.updateQueue=a,a.stores=[e]):(t=a.stores,t===null?a.stores=[e]:t.push(e))}function xf(e,a,t,l){a.value=t,a.getSnapshot=l,wf(a)&&Yf(e)}function qf(e,a,t){return t(function(){wf(a)&&Yf(e)})}function wf(e){var a=e.getSnapshot;e=e.value;try{var t=a();return!Aa(e,t)}catch{return!0}}function Yf(e){var a=Zn(e,2);a!==null&&Qa(a,e,2)}function Is(e){var a=Ha();if(typeof e=="function"){var t=e;if(e=t(),An){Nt(!0);try{t()}finally{Nt(!1)}}}return a.memoizedState=a.baseState=e,a.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:e},a}function Vf(e,a,t,l){return e.baseState=t,Fs(e,Oe,typeof l=="function"?l:al)}function Oh(e,a,t,l,n){if(vc(e))throw Error(_(485));if(e=a.action,e!==null){var u={payload:n,action:e,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(r){u.listeners.push(r)}};G.T!==null?t(!0):u.isTransition=!1,l(u),t=a.pending,t===null?(u.next=a.pending=u,Xf(a,u)):(u.next=t.next,a.pending=t.next=u)}}function Xf(e,a){var t=a.action,l=a.payload,n=e.state;if(a.isTransition){var u=G.T,r={};G.T=r;try{var o=t(n,l),S=G.S;S!==null&&S(r,o),$f(e,a,o)}catch(C){er(e,a,C)}finally{G.T=u}}else try{u=t(n,l),$f(e,a,u)}catch(C){er(e,a,C)}}function $f(e,a,t){t!==null&&typeof t=="object"&&typeof t.then=="function"?t.then(function(l){Qf(e,a,l)},function(l){return er(e,a,l)}):Qf(e,a,t)}function Qf(e,a,t){a.status="fulfilled",a.value=t,kf(a),e.state=t,a=e.pending,a!==null&&(t=a.next,t===a?e.pending=null:(t=t.next,a.next=t,Xf(e,t)))}function er(e,a,t){var l=e.pending;if(e.pending=null,l!==null){l=l.next;do a.status="rejected",a.reason=t,kf(a),a=a.next;while(a!==l)}e.action=null}function kf(e){e=e.listeners;for(var a=0;a<e.length;a++)(0,e[a])()}function Zf(e,a){return a}function Kf(e,a){if(be){var t=He.formState;if(t!==null){e:{var l=se;if(be){if(we){a:{for(var n=we,u=Ut;n.nodeType!==8;){if(!u){n=null;break a}if(n=Et(n.nextSibling),n===null){n=null;break a}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){we=Et(n.nextSibling),l=n.data==="F!";break e}}yn(l)}l=!1}l&&(a=t[0])}}return t=Ha(),t.memoizedState=t.baseState=a,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Zf,lastRenderedState:a},t.queue=l,t=hd.bind(null,se,l),l.dispatch=t,l=Is(!1),u=ur.bind(null,se,!1,l.queue),l=Ha(),n={state:a,dispatch:null,action:e,pending:null},l.queue=n,t=Oh.bind(null,se,n,u,t),n.dispatch=t,l.memoizedState=e,[a,t,!1]}function Jf(e){var a=Je();return Wf(a,Oe,e)}function Wf(e,a,t){if(a=Fs(e,a,Zf)[0],e=mc(al)[0],typeof a=="object"&&a!==null&&typeof a.then=="function")try{var l=ei(a)}catch(r){throw r===Ku?sc:r}else l=a;a=Je();var n=a.queue,u=n.dispatch;return t!==a.memoizedState&&(se.flags|=2048,tu(9,yc(),_h.bind(null,n,t),null)),[l,u,e]}function _h(e,a){e.action=a}function Ff(e){var a=Je(),t=Oe;if(t!==null)return Wf(a,t,e);Je(),a=a.memoizedState,t=Je();var l=t.queue.dispatch;return t.memoizedState=e,[a,l,!1]}function tu(e,a,t,l){return e={tag:e,create:t,deps:l,inst:a,next:null},a=se.updateQueue,a===null&&(a=Js(),se.updateQueue=a),t=a.lastEffect,t===null?a.lastEffect=e.next=e:(l=t.next,t.next=e,e.next=l,a.lastEffect=e),e}function yc(){return{destroy:void 0,resource:void 0}}function Pf(){return Je().memoizedState}function gc(e,a,t,l){var n=Ha();l=l===void 0?null:l,se.flags|=e,n.memoizedState=tu(1|a,yc(),t,l)}function ai(e,a,t,l){var n=Je();l=l===void 0?null:l;var u=n.memoizedState.inst;Oe!==null&&l!==null&&$s(l,Oe.memoizedState.deps)?n.memoizedState=tu(a,u,t,l):(se.flags|=e,n.memoizedState=tu(1|a,u,t,l))}function If(e,a){gc(8390656,8,e,a)}function ed(e,a){ai(2048,8,e,a)}function ad(e,a){return ai(4,2,e,a)}function td(e,a){return ai(4,4,e,a)}function ld(e,a){if(typeof a=="function"){e=e();var t=a(e);return function(){typeof t=="function"?t():a(null)}}if(a!=null)return e=e(),a.current=e,function(){a.current=null}}function nd(e,a,t){t=t!=null?t.concat([e]):null,ai(4,4,ld.bind(null,a,e),t)}function ar(){}function ud(e,a){var t=Je();a=a===void 0?null:a;var l=t.memoizedState;return a!==null&&$s(a,l[1])?l[0]:(t.memoizedState=[e,a],e)}function id(e,a){var t=Je();a=a===void 0?null:a;var l=t.memoizedState;if(a!==null&&$s(a,l[1]))return l[0];if(l=e(),An){Nt(!0);try{e()}finally{Nt(!1)}}return t.memoizedState=[l,a],l}function tr(e,a,t){return t===void 0||(Ml&1073741824)!==0?e.memoizedState=a:(e.memoizedState=t,e=ro(),se.lanes|=e,Ll|=e,t)}function cd(e,a,t,l){return Aa(t,a)?t:In.current!==null?(e=tr(e,t,l),Aa(e,a)||(ua=!0),e):(Ml&42)===0?(ua=!0,e.memoizedState=t):(e=ro(),se.lanes|=e,Ll|=e,a)}function sd(e,a,t,l,n){var u=q.p;q.p=u!==0&&8>u?u:8;var r=G.T,o={};G.T=o,ur(e,!1,a,t);try{var S=n(),C=G.S;if(C!==null&&C(o,S),S!==null&&typeof S=="object"&&typeof S.then=="function"){var H=Ch(S,l);ti(e,a,H,$a(e))}else ti(e,a,l,$a(e))}catch(x){ti(e,a,{then:function(){},status:"rejected",reason:x},$a())}finally{q.p=u,G.T=r}}function Mh(){}function lr(e,a,t,l){if(e.tag!==5)throw Error(_(476));var n=rd(e).queue;sd(e,n,a,ee,t===null?Mh:function(){return fd(e),t(l)})}function rd(e){var a=e.memoizedState;if(a!==null)return a;a={memoizedState:ee,baseState:ee,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:ee},next:null};var t={};return a.next={memoizedState:t,baseState:t,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:al,lastRenderedState:t},next:null},e.memoizedState=a,e=e.alternate,e!==null&&(e.memoizedState=a),a}function fd(e){var a=rd(e).next.queue;ti(e,a,{},$a())}function nr(){return Ea(pi)}function dd(){return Je().memoizedState}function od(){return Je().memoizedState}function Bh(e){for(var a=e.return;a!==null;){switch(a.tag){case 24:case 3:var t=$a();e=Ol(t);var l=_l(a,e,t);l!==null&&(Qa(l,a,t),Wu(l,a,t)),a={cache:Us()},e.payload=a;return}a=a.return}}function Gh(e,a,t){var l=$a();t={lane:l,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null},vc(e)?md(a,t):(t=bs(e,a,t,l),t!==null&&(Qa(t,e,l),yd(t,a,l)))}function hd(e,a,t){var l=$a();ti(e,a,t,l)}function ti(e,a,t,l){var n={lane:l,revertLane:0,action:t,hasEagerState:!1,eagerState:null,next:null};if(vc(e))md(a,n);else{var u=e.alternate;if(e.lanes===0&&(u===null||u.lanes===0)&&(u=a.lastRenderedReducer,u!==null))try{var r=a.lastRenderedState,o=u(r,t);if(n.hasEagerState=!0,n.eagerState=o,Aa(o,r))return ec(e,a,n,0),He===null&&Ii(),!1}catch{}finally{}if(t=bs(e,a,n,l),t!==null)return Qa(t,e,l),yd(t,a,l),!0}return!1}function ur(e,a,t,l){if(l={lane:2,revertLane:Lr(),action:l,hasEagerState:!1,eagerState:null,next:null},vc(e)){if(a)throw Error(_(479))}else a=bs(e,t,l,2),a!==null&&Qa(a,e,2)}function vc(e){var a=e.alternate;return e===se||a!==null&&a===se}function md(e,a){eu=dc=!0;var t=e.pending;t===null?a.next=a:(a.next=t.next,t.next=a),e.pending=a}function yd(e,a,t){if((t&4194048)!==0){var l=a.lanes;l&=e.pendingLanes,t|=l,a.lanes=t,Hi(e,t)}}var Sc={readContext:Ea,use:hc,useCallback:$e,useContext:$e,useEffect:$e,useImperativeHandle:$e,useLayoutEffect:$e,useInsertionEffect:$e,useMemo:$e,useReducer:$e,useRef:$e,useState:$e,useDebugValue:$e,useDeferredValue:$e,useTransition:$e,useSyncExternalStore:$e,useId:$e,useHostTransitionStatus:$e,useFormState:$e,useActionState:$e,useOptimistic:$e,useMemoCache:$e,useCacheRefresh:$e},gd={readContext:Ea,use:hc,useCallback:function(e,a){return Ha().memoizedState=[e,a===void 0?null:a],e},useContext:Ea,useEffect:If,useImperativeHandle:function(e,a,t){t=t!=null?t.concat([e]):null,gc(4194308,4,ld.bind(null,a,e),t)},useLayoutEffect:function(e,a){return gc(4194308,4,e,a)},useInsertionEffect:function(e,a){gc(4,2,e,a)},useMemo:function(e,a){var t=Ha();a=a===void 0?null:a;var l=e();if(An){Nt(!0);try{e()}finally{Nt(!1)}}return t.memoizedState=[l,a],l},useReducer:function(e,a,t){var l=Ha();if(t!==void 0){var n=t(a);if(An){Nt(!0);try{t(a)}finally{Nt(!1)}}}else n=a;return l.memoizedState=l.baseState=n,e={pending:null,lanes:0,dispatch:null,lastRenderedReducer:e,lastRenderedState:n},l.queue=e,e=e.dispatch=Gh.bind(null,se,e),[l.memoizedState,e]},useRef:function(e){var a=Ha();return e={current:e},a.memoizedState=e},useState:function(e){e=Is(e);var a=e.queue,t=hd.bind(null,se,a);return a.dispatch=t,[e.memoizedState,t]},useDebugValue:ar,useDeferredValue:function(e,a){var t=Ha();return tr(t,e,a)},useTransition:function(){var e=Is(!1);return e=sd.bind(null,se,e.queue,!0,!1),Ha().memoizedState=e,[!1,e]},useSyncExternalStore:function(e,a,t){var l=se,n=Ha();if(be){if(t===void 0)throw Error(_(407));t=t()}else{if(t=a(),He===null)throw Error(_(349));(Se&124)!==0||Lf(l,a,t)}n.memoizedState=t;var u={value:t,getSnapshot:a};return n.queue=u,If(qf.bind(null,l,u,e),[e]),l.flags|=2048,tu(9,yc(),xf.bind(null,l,u,t,a),null),t},useId:function(){var e=Ha(),a=He.identifierPrefix;if(be){var t=Pt,l=Ft;t=(l&~(1<<32-ra(l)-1)).toString(32)+t,a="«"+a+"R"+t,t=oc++,0<t&&(a+="H"+t.toString(32)),a+="»"}else t=Dh++,a="«"+a+"r"+t.toString(32)+"»";return e.memoizedState=a},useHostTransitionStatus:nr,useFormState:Kf,useActionState:Kf,useOptimistic:function(e){var a=Ha();a.memoizedState=a.baseState=e;var t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return a.queue=t,a=ur.bind(null,se,!0,t),t.dispatch=a,[e,a]},useMemoCache:Ws,useCacheRefresh:function(){return Ha().memoizedState=Bh.bind(null,se)}},vd={readContext:Ea,use:hc,useCallback:ud,useContext:Ea,useEffect:ed,useImperativeHandle:nd,useInsertionEffect:ad,useLayoutEffect:td,useMemo:id,useReducer:mc,useRef:Pf,useState:function(){return mc(al)},useDebugValue:ar,useDeferredValue:function(e,a){var t=Je();return cd(t,Oe.memoizedState,e,a)},useTransition:function(){var e=mc(al)[0],a=Je().memoizedState;return[typeof e=="boolean"?e:ei(e),a]},useSyncExternalStore:zf,useId:dd,useHostTransitionStatus:nr,useFormState:Jf,useActionState:Jf,useOptimistic:function(e,a){var t=Je();return Vf(t,Oe,e,a)},useMemoCache:Ws,useCacheRefresh:od},Hh={readContext:Ea,use:hc,useCallback:ud,useContext:Ea,useEffect:ed,useImperativeHandle:nd,useInsertionEffect:ad,useLayoutEffect:td,useMemo:id,useReducer:Ps,useRef:Pf,useState:function(){return Ps(al)},useDebugValue:ar,useDeferredValue:function(e,a){var t=Je();return Oe===null?tr(t,e,a):cd(t,Oe.memoizedState,e,a)},useTransition:function(){var e=Ps(al)[0],a=Je().memoizedState;return[typeof e=="boolean"?e:ei(e),a]},useSyncExternalStore:zf,useId:dd,useHostTransitionStatus:nr,useFormState:Ff,useActionState:Ff,useOptimistic:function(e,a){var t=Je();return Oe!==null?Vf(t,Oe,e,a):(t.baseState=e,[e,t.queue.dispatch])},useMemoCache:Ws,useCacheRefresh:od},lu=null,li=0;function Ac(e){var a=li;return li+=1,lu===null&&(lu=[]),Of(lu,e,a)}function ni(e,a){a=a.props.ref,e.ref=a!==void 0?a:null}function pc(e,a){throw a.$$typeof===X?Error(_(525)):(e=Object.prototype.toString.call(a),Error(_(31,e==="[object Object]"?"object with keys {"+Object.keys(a).join(", ")+"}":e)))}function Sd(e){var a=e._init;return a(e._payload)}function Ad(e){function a(N,p){if(e){var b=N.deletions;b===null?(N.deletions=[p],N.flags|=16):b.push(p)}}function t(N,p){if(!e)return null;for(;p!==null;)a(N,p),p=p.sibling;return null}function l(N){for(var p=new Map;N!==null;)N.key!==null?p.set(N.key,N):p.set(N.index,N),N=N.sibling;return p}function n(N,p){return N=Wt(N,p),N.index=0,N.sibling=null,N}function u(N,p,b){return N.index=b,e?(b=N.alternate,b!==null?(b=b.index,b<p?(N.flags|=67108866,p):b):(N.flags|=67108866,p)):(N.flags|=1048576,p)}function r(N){return e&&N.alternate===null&&(N.flags|=67108866),N}function o(N,p,b,j){return p===null||p.tag!==6?(p=Ds(b,N.mode,j),p.return=N,p):(p=n(p,b),p.return=N,p)}function S(N,p,b,j){var W=b.type;return W===sa?H(N,p,b.props.children,j,b.key):p!==null&&(p.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===We&&Sd(W)===p.type)?(p=n(p,b.props),ni(p,b),p.return=N,p):(p=tc(b.type,b.key,b.props,null,N.mode,j),ni(p,b),p.return=N,p)}function C(N,p,b,j){return p===null||p.tag!==4||p.stateNode.containerInfo!==b.containerInfo||p.stateNode.implementation!==b.implementation?(p=Rs(b,N.mode,j),p.return=N,p):(p=n(p,b.children||[]),p.return=N,p)}function H(N,p,b,j,W){return p===null||p.tag!==7?(p=dn(b,N.mode,j,W),p.return=N,p):(p=n(p,b),p.return=N,p)}function x(N,p,b){if(typeof p=="string"&&p!==""||typeof p=="number"||typeof p=="bigint")return p=Ds(""+p,N.mode,b),p.return=N,p;if(typeof p=="object"&&p!==null){switch(p.$$typeof){case he:return b=tc(p.type,p.key,p.props,null,N.mode,b),ni(b,p),b.return=N,b;case ea:return p=Rs(p,N.mode,b),p.return=N,p;case We:var j=p._init;return p=j(p._payload),x(N,p,b)}if(aa(p)||ze(p))return p=dn(p,N.mode,b,null),p.return=N,p;if(typeof p.then=="function")return x(N,Ac(p),b);if(p.$$typeof===Ze)return x(N,ic(N,p),b);pc(N,p)}return null}function R(N,p,b,j){var W=p!==null?p.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return W!==null?null:o(N,p,""+b,j);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case he:return b.key===W?S(N,p,b,j):null;case ea:return b.key===W?C(N,p,b,j):null;case We:return W=b._init,b=W(b._payload),R(N,p,b,j)}if(aa(b)||ze(b))return W!==null?null:H(N,p,b,j,null);if(typeof b.then=="function")return R(N,p,Ac(b),j);if(b.$$typeof===Ze)return R(N,p,ic(N,b),j);pc(N,b)}return null}function O(N,p,b,j,W){if(typeof j=="string"&&j!==""||typeof j=="number"||typeof j=="bigint")return N=N.get(b)||null,o(p,N,""+j,W);if(typeof j=="object"&&j!==null){switch(j.$$typeof){case he:return N=N.get(j.key===null?b:j.key)||null,S(p,N,j,W);case ea:return N=N.get(j.key===null?b:j.key)||null,C(p,N,j,W);case We:var re=j._init;return j=re(j._payload),O(N,p,b,j,W)}if(aa(j)||ze(j))return N=N.get(b)||null,H(p,N,j,W,null);if(typeof j.then=="function")return O(N,p,b,Ac(j),W);if(j.$$typeof===Ze)return O(N,p,b,ic(p,j),W);pc(p,j)}return null}function ne(N,p,b,j){for(var W=null,re=null,P=p,te=p=0,ca=null;P!==null&&te<b.length;te++){P.index>te?(ca=P,P=null):ca=P.sibling;var Ee=R(N,P,b[te],j);if(Ee===null){P===null&&(P=ca);break}e&&P&&Ee.alternate===null&&a(N,P),p=u(Ee,p,te),re===null?W=Ee:re.sibling=Ee,re=Ee,P=ca}if(te===b.length)return t(N,P),be&&hn(N,te),W;if(P===null){for(;te<b.length;te++)P=x(N,b[te],j),P!==null&&(p=u(P,p,te),re===null?W=P:re.sibling=P,re=P);return be&&hn(N,te),W}for(P=l(P);te<b.length;te++)ca=O(P,N,te,b[te],j),ca!==null&&(e&&ca.alternate!==null&&P.delete(ca.key===null?te:ca.key),p=u(ca,p,te),re===null?W=ca:re.sibling=ca,re=ca);return e&&P.forEach(function(kl){return a(N,kl)}),be&&hn(N,te),W}function ae(N,p,b,j){if(b==null)throw Error(_(151));for(var W=null,re=null,P=p,te=p=0,ca=null,Ee=b.next();P!==null&&!Ee.done;te++,Ee=b.next()){P.index>te?(ca=P,P=null):ca=P.sibling;var kl=R(N,P,Ee.value,j);if(kl===null){P===null&&(P=ca);break}e&&P&&kl.alternate===null&&a(N,P),p=u(kl,p,te),re===null?W=kl:re.sibling=kl,re=kl,P=ca}if(Ee.done)return t(N,P),be&&hn(N,te),W;if(P===null){for(;!Ee.done;te++,Ee=b.next())Ee=x(N,Ee.value,j),Ee!==null&&(p=u(Ee,p,te),re===null?W=Ee:re.sibling=Ee,re=Ee);return be&&hn(N,te),W}for(P=l(P);!Ee.done;te++,Ee=b.next())Ee=O(P,N,te,Ee.value,j),Ee!==null&&(e&&Ee.alternate!==null&&P.delete(Ee.key===null?te:Ee.key),p=u(Ee,p,te),re===null?W=Ee:re.sibling=Ee,re=Ee);return e&&P.forEach(function(Um){return a(N,Um)}),be&&hn(N,te),W}function Me(N,p,b,j){if(typeof b=="object"&&b!==null&&b.type===sa&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case he:e:{for(var W=b.key;p!==null;){if(p.key===W){if(W=b.type,W===sa){if(p.tag===7){t(N,p.sibling),j=n(p,b.props.children),j.return=N,N=j;break e}}else if(p.elementType===W||typeof W=="object"&&W!==null&&W.$$typeof===We&&Sd(W)===p.type){t(N,p.sibling),j=n(p,b.props),ni(j,b),j.return=N,N=j;break e}t(N,p);break}else a(N,p);p=p.sibling}b.type===sa?(j=dn(b.props.children,N.mode,j,b.key),j.return=N,N=j):(j=tc(b.type,b.key,b.props,null,N.mode,j),ni(j,b),j.return=N,N=j)}return r(N);case ea:e:{for(W=b.key;p!==null;){if(p.key===W)if(p.tag===4&&p.stateNode.containerInfo===b.containerInfo&&p.stateNode.implementation===b.implementation){t(N,p.sibling),j=n(p,b.children||[]),j.return=N,N=j;break e}else{t(N,p);break}else a(N,p);p=p.sibling}j=Rs(b,N.mode,j),j.return=N,N=j}return r(N);case We:return W=b._init,b=W(b._payload),Me(N,p,b,j)}if(aa(b))return ne(N,p,b,j);if(ze(b)){if(W=ze(b),typeof W!="function")throw Error(_(150));return b=W.call(b),ae(N,p,b,j)}if(typeof b.then=="function")return Me(N,p,Ac(b),j);if(b.$$typeof===Ze)return Me(N,p,ic(N,b),j);pc(N,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,p!==null&&p.tag===6?(t(N,p.sibling),j=n(p,b),j.return=N,N=j):(t(N,p),j=Ds(b,N.mode,j),j.return=N,N=j),r(N)):t(N,p)}return function(N,p,b,j){try{li=0;var W=Me(N,p,b,j);return lu=null,W}catch(P){if(P===Ku||P===sc)throw P;var re=wa(29,P,null,N.mode);return re.lanes=j,re.return=N,re}finally{}}}var nu=Ad(!0),pd=Ad(!1),tt=w(null),jt=null;function Bl(e){var a=e.alternate;Z(Ie,Ie.current&1),Z(tt,e),jt===null&&(a===null||In.current!==null||a.memoizedState!==null)&&(jt=e)}function Ed(e){if(e.tag===22){if(Z(Ie,Ie.current),Z(tt,e),jt===null){var a=e.alternate;a!==null&&a.memoizedState!==null&&(jt=e)}}else Gl()}function Gl(){Z(Ie,Ie.current),Z(tt,tt.current)}function tl(e){k(tt),jt===e&&(jt=null),k(Ie)}var Ie=w(0);function Ec(e){for(var a=e;a!==null;){if(a.tag===13){var t=a.memoizedState;if(t!==null&&(t=t.dehydrated,t===null||t.data==="$?"||Jr(t)))return a}else if(a.tag===19&&a.memoizedProps.revealOrder!==void 0){if((a.flags&128)!==0)return a}else if(a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break;for(;a.sibling===null;){if(a.return===null||a.return===e)return null;a=a.return}a.sibling.return=a.return,a=a.sibling}return null}function ir(e,a,t,l){a=e.memoizedState,t=t(l,a),t=t==null?a:U({},a,t),e.memoizedState=t,e.lanes===0&&(e.updateQueue.baseState=t)}var cr={enqueueSetState:function(e,a,t){e=e._reactInternals;var l=$a(),n=Ol(l);n.payload=a,t!=null&&(n.callback=t),a=_l(e,n,l),a!==null&&(Qa(a,e,l),Wu(a,e,l))},enqueueReplaceState:function(e,a,t){e=e._reactInternals;var l=$a(),n=Ol(l);n.tag=1,n.payload=a,t!=null&&(n.callback=t),a=_l(e,n,l),a!==null&&(Qa(a,e,l),Wu(a,e,l))},enqueueForceUpdate:function(e,a){e=e._reactInternals;var t=$a(),l=Ol(t);l.tag=2,a!=null&&(l.callback=a),a=_l(e,l,t),a!==null&&(Qa(a,e,t),Wu(a,e,t))}};function Td(e,a,t,l,n,u,r){return e=e.stateNode,typeof e.shouldComponentUpdate=="function"?e.shouldComponentUpdate(l,u,r):a.prototype&&a.prototype.isPureReactComponent?!bl(t,l)||!bl(n,u):!0}function Nd(e,a,t,l){e=a.state,typeof a.componentWillReceiveProps=="function"&&a.componentWillReceiveProps(t,l),typeof a.UNSAFE_componentWillReceiveProps=="function"&&a.UNSAFE_componentWillReceiveProps(t,l),a.state!==e&&cr.enqueueReplaceState(a,a.state,null)}function pn(e,a){var t=a;if("ref"in a){t={};for(var l in a)l!=="ref"&&(t[l]=a[l])}if(e=e.defaultProps){t===a&&(t=U({},t));for(var n in e)t[n]===void 0&&(t[n]=e[n])}return t}var Tc=typeof reportError=="function"?reportError:function(e){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var a=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof e=="object"&&e!==null&&typeof e.message=="string"?String(e.message):String(e),error:e});if(!window.dispatchEvent(a))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",e);return}console.error(e)};function bd(e){Tc(e)}function Cd(e){console.error(e)}function Dd(e){Tc(e)}function Nc(e,a){try{var t=e.onUncaughtError;t(a.value,{componentStack:a.stack})}catch(l){setTimeout(function(){throw l})}}function Rd(e,a,t){try{var l=e.onCaughtError;l(t.value,{componentStack:t.stack,errorBoundary:a.tag===1?a.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function sr(e,a,t){return t=Ol(t),t.tag=3,t.payload={element:null},t.callback=function(){Nc(e,a)},t}function Od(e){return e=Ol(e),e.tag=3,e}function _d(e,a,t,l){var n=t.type.getDerivedStateFromError;if(typeof n=="function"){var u=l.value;e.payload=function(){return n(u)},e.callback=function(){Rd(a,t,l)}}var r=t.stateNode;r!==null&&typeof r.componentDidCatch=="function"&&(e.callback=function(){Rd(a,t,l),typeof n!="function"&&(xl===null?xl=new Set([this]):xl.add(this));var o=l.stack;this.componentDidCatch(l.value,{componentStack:o!==null?o:""})})}function Uh(e,a,t,l,n){if(t.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(a=t.alternate,a!==null&&Qu(a,t,n,!0),t=tt.current,t!==null){switch(t.tag){case 13:return jt===null?Gr():t.alternate===null&&Ye===0&&(Ye=3),t.flags&=-257,t.flags|=65536,t.lanes=n,l===Ls?t.flags|=16384:(a=t.updateQueue,a===null?t.updateQueue=new Set([l]):a.add(l),Ur(e,l,n)),!1;case 22:return t.flags|=65536,l===Ls?t.flags|=16384:(a=t.updateQueue,a===null?(a={transitions:null,markerInstances:null,retryQueue:new Set([l])},t.updateQueue=a):(t=a.retryQueue,t===null?a.retryQueue=new Set([l]):t.add(l)),Ur(e,l,n)),!1}throw Error(_(435,t.tag))}return Ur(e,l,n),Gr(),!1}if(be)return a=tt.current,a!==null?((a.flags&65536)===0&&(a.flags|=256),a.flags|=65536,a.lanes=n,l!==Ms&&(e=Error(_(422),{cause:l}),$u(pa(e,t)))):(l!==Ms&&(a=Error(_(423),{cause:l}),$u(pa(a,t))),e=e.current.alternate,e.flags|=65536,n&=-n,e.lanes|=n,l=pa(l,t),n=sr(e.stateNode,l,n),ws(e,n),Ye!==4&&(Ye=2)),!1;var u=Error(_(520),{cause:l});if(u=pa(u,t),di===null?di=[u]:di.push(u),Ye!==4&&(Ye=2),a===null)return!0;l=pa(l,t),t=a;do{switch(t.tag){case 3:return t.flags|=65536,e=n&-n,t.lanes|=e,e=sr(t.stateNode,l,e),ws(t,e),!1;case 1:if(a=t.type,u=t.stateNode,(t.flags&128)===0&&(typeof a.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(xl===null||!xl.has(u))))return t.flags|=65536,n&=-n,t.lanes|=n,n=Od(n),_d(n,e,t,l),ws(t,n),!1}t=t.return}while(t!==null);return!1}var Md=Error(_(461)),ua=!1;function ha(e,a,t,l){a.child=e===null?pd(a,null,t,l):nu(a,e.child,t,l)}function Bd(e,a,t,l,n){t=t.render;var u=a.ref;if("ref"in l){var r={};for(var o in l)o!=="ref"&&(r[o]=l[o])}else r=l;return vn(a),l=Qs(e,a,t,r,u,n),o=ks(),e!==null&&!ua?(Zs(e,a,n),ll(e,a,n)):(be&&o&&Os(a),a.flags|=1,ha(e,a,l,n),a.child)}function Gd(e,a,t,l,n){if(e===null){var u=t.type;return typeof u=="function"&&!Cs(u)&&u.defaultProps===void 0&&t.compare===null?(a.tag=15,a.type=u,Hd(e,a,u,l,n)):(e=tc(t.type,null,l,a,a.mode,n),e.ref=a.ref,e.return=a,a.child=e)}if(u=e.child,!gr(e,n)){var r=u.memoizedProps;if(t=t.compare,t=t!==null?t:bl,t(r,l)&&e.ref===a.ref)return ll(e,a,n)}return a.flags|=1,e=Wt(u,l),e.ref=a.ref,e.return=a,a.child=e}function Hd(e,a,t,l,n){if(e!==null){var u=e.memoizedProps;if(bl(u,l)&&e.ref===a.ref)if(ua=!1,a.pendingProps=l=u,gr(e,n))(e.flags&131072)!==0&&(ua=!0);else return a.lanes=e.lanes,ll(e,a,n)}return rr(e,a,t,l,n)}function Ud(e,a,t){var l=a.pendingProps,n=l.children,u=e!==null?e.memoizedState:null;if(l.mode==="hidden"){if((a.flags&128)!==0){if(l=u!==null?u.baseLanes|t:t,e!==null){for(n=a.child=e.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;a.childLanes=u&~l}else a.childLanes=0,a.child=null;return jd(e,a,l,t)}if((t&536870912)!==0)a.memoizedState={baseLanes:0,cachePool:null},e!==null&&cc(a,u!==null?u.cachePool:null),u!==null?Hf(a,u):Vs(),Ed(a);else return a.lanes=a.childLanes=536870912,jd(e,a,u!==null?u.baseLanes|t:t,t)}else u!==null?(cc(a,u.cachePool),Hf(a,u),Gl(),a.memoizedState=null):(e!==null&&cc(a,null),Vs(),Gl());return ha(e,a,n,t),a.child}function jd(e,a,t,l){var n=zs();return n=n===null?null:{parent:Pe._currentValue,pool:n},a.memoizedState={baseLanes:t,cachePool:n},e!==null&&cc(a,null),Vs(),Ed(a),e!==null&&Qu(e,a,l,!0),null}function bc(e,a){var t=a.ref;if(t===null)e!==null&&e.ref!==null&&(a.flags|=4194816);else{if(typeof t!="function"&&typeof t!="object")throw Error(_(284));(e===null||e.ref!==t)&&(a.flags|=4194816)}}function rr(e,a,t,l,n){return vn(a),t=Qs(e,a,t,l,void 0,n),l=ks(),e!==null&&!ua?(Zs(e,a,n),ll(e,a,n)):(be&&l&&Os(a),a.flags|=1,ha(e,a,t,n),a.child)}function zd(e,a,t,l,n,u){return vn(a),a.updateQueue=null,t=jf(a,l,t,n),Uf(e),l=ks(),e!==null&&!ua?(Zs(e,a,u),ll(e,a,u)):(be&&l&&Os(a),a.flags|=1,ha(e,a,t,u),a.child)}function Ld(e,a,t,l,n){if(vn(a),a.stateNode===null){var u=Kn,r=t.contextType;typeof r=="object"&&r!==null&&(u=Ea(r)),u=new t(l,u),a.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=cr,a.stateNode=u,u._reactInternals=a,u=a.stateNode,u.props=l,u.state=a.memoizedState,u.refs={},xs(a),r=t.contextType,u.context=typeof r=="object"&&r!==null?Ea(r):Kn,u.state=a.memoizedState,r=t.getDerivedStateFromProps,typeof r=="function"&&(ir(a,t,r,l),u.state=a.memoizedState),typeof t.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(r=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),r!==u.state&&cr.enqueueReplaceState(u,u.state,null),Pu(a,l,u,n),Fu(),u.state=a.memoizedState),typeof u.componentDidMount=="function"&&(a.flags|=4194308),l=!0}else if(e===null){u=a.stateNode;var o=a.memoizedProps,S=pn(t,o);u.props=S;var C=u.context,H=t.contextType;r=Kn,typeof H=="object"&&H!==null&&(r=Ea(H));var x=t.getDerivedStateFromProps;H=typeof x=="function"||typeof u.getSnapshotBeforeUpdate=="function",o=a.pendingProps!==o,H||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(o||C!==r)&&Nd(a,u,l,r),Rl=!1;var R=a.memoizedState;u.state=R,Pu(a,l,u,n),Fu(),C=a.memoizedState,o||R!==C||Rl?(typeof x=="function"&&(ir(a,t,x,l),C=a.memoizedState),(S=Rl||Td(a,t,S,l,R,C,r))?(H||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(a.flags|=4194308)):(typeof u.componentDidMount=="function"&&(a.flags|=4194308),a.memoizedProps=l,a.memoizedState=C),u.props=l,u.state=C,u.context=r,l=S):(typeof u.componentDidMount=="function"&&(a.flags|=4194308),l=!1)}else{u=a.stateNode,qs(e,a),r=a.memoizedProps,H=pn(t,r),u.props=H,x=a.pendingProps,R=u.context,C=t.contextType,S=Kn,typeof C=="object"&&C!==null&&(S=Ea(C)),o=t.getDerivedStateFromProps,(C=typeof o=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r!==x||R!==S)&&Nd(a,u,l,S),Rl=!1,R=a.memoizedState,u.state=R,Pu(a,l,u,n),Fu();var O=a.memoizedState;r!==x||R!==O||Rl||e!==null&&e.dependencies!==null&&uc(e.dependencies)?(typeof o=="function"&&(ir(a,t,o,l),O=a.memoizedState),(H=Rl||Td(a,t,H,l,R,O,S)||e!==null&&e.dependencies!==null&&uc(e.dependencies))?(C||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,O,S),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,O,S)),typeof u.componentDidUpdate=="function"&&(a.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(a.flags|=1024)):(typeof u.componentDidUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(a.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(a.flags|=1024),a.memoizedProps=l,a.memoizedState=O),u.props=l,u.state=O,u.context=S,l=H):(typeof u.componentDidUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(a.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===e.memoizedProps&&R===e.memoizedState||(a.flags|=1024),l=!1)}return u=l,bc(e,a),l=(a.flags&128)!==0,u||l?(u=a.stateNode,t=l&&typeof t.getDerivedStateFromError!="function"?null:u.render(),a.flags|=1,e!==null&&l?(a.child=nu(a,e.child,null,n),a.child=nu(a,null,t,n)):ha(e,a,t,n),a.memoizedState=u.state,e=a.child):e=ll(e,a,n),e}function xd(e,a,t,l){return Xu(),a.flags|=256,ha(e,a,t,l),a.child}var fr={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function dr(e){return{baseLanes:e,cachePool:Cf()}}function or(e,a,t){return e=e!==null?e.childLanes&~t:0,a&&(e|=lt),e}function qd(e,a,t){var l=a.pendingProps,n=!1,u=(a.flags&128)!==0,r;if((r=u)||(r=e!==null&&e.memoizedState===null?!1:(Ie.current&2)!==0),r&&(n=!0,a.flags&=-129),r=(a.flags&32)!==0,a.flags&=-33,e===null){if(be){if(n?Bl(a):Gl(),be){var o=we,S;if(S=o){e:{for(S=o,o=Ut;S.nodeType!==8;){if(!o){o=null;break e}if(S=Et(S.nextSibling),S===null){o=null;break e}}o=S}o!==null?(a.memoizedState={dehydrated:o,treeContext:on!==null?{id:Ft,overflow:Pt}:null,retryLane:536870912,hydrationErrors:null},S=wa(18,null,null,0),S.stateNode=o,S.return=a,a.child=S,Ca=a,we=null,S=!0):S=!1}S||yn(a)}if(o=a.memoizedState,o!==null&&(o=o.dehydrated,o!==null))return Jr(o)?a.lanes=32:a.lanes=536870912,null;tl(a)}return o=l.children,l=l.fallback,n?(Gl(),n=a.mode,o=Cc({mode:"hidden",children:o},n),l=dn(l,n,t,null),o.return=a,l.return=a,o.sibling=l,a.child=o,n=a.child,n.memoizedState=dr(t),n.childLanes=or(e,r,t),a.memoizedState=fr,l):(Bl(a),hr(a,o))}if(S=e.memoizedState,S!==null&&(o=S.dehydrated,o!==null)){if(u)a.flags&256?(Bl(a),a.flags&=-257,a=mr(e,a,t)):a.memoizedState!==null?(Gl(),a.child=e.child,a.flags|=128,a=null):(Gl(),n=l.fallback,o=a.mode,l=Cc({mode:"visible",children:l.children},o),n=dn(n,o,t,null),n.flags|=2,l.return=a,n.return=a,l.sibling=n,a.child=l,nu(a,e.child,null,t),l=a.child,l.memoizedState=dr(t),l.childLanes=or(e,r,t),a.memoizedState=fr,a=n);else if(Bl(a),Jr(o)){if(r=o.nextSibling&&o.nextSibling.dataset,r)var C=r.dgst;r=C,l=Error(_(419)),l.stack="",l.digest=r,$u({value:l,source:null,stack:null}),a=mr(e,a,t)}else if(ua||Qu(e,a,t,!1),r=(t&e.childLanes)!==0,ua||r){if(r=He,r!==null&&(l=t&-t,l=(l&42)!==0?1:bt(l),l=(l&(r.suspendedLanes|t))!==0?0:l,l!==0&&l!==S.retryLane))throw S.retryLane=l,Zn(e,l),Qa(r,e,l),Md;o.data==="$?"||Gr(),a=mr(e,a,t)}else o.data==="$?"?(a.flags|=192,a.child=e.child,a=null):(e=S.treeContext,we=Et(o.nextSibling),Ca=a,be=!0,mn=null,Ut=!1,e!==null&&(et[at++]=Ft,et[at++]=Pt,et[at++]=on,Ft=e.id,Pt=e.overflow,on=a),a=hr(a,l.children),a.flags|=4096);return a}return n?(Gl(),n=l.fallback,o=a.mode,S=e.child,C=S.sibling,l=Wt(S,{mode:"hidden",children:l.children}),l.subtreeFlags=S.subtreeFlags&65011712,C!==null?n=Wt(C,n):(n=dn(n,o,t,null),n.flags|=2),n.return=a,l.return=a,l.sibling=n,a.child=l,l=n,n=a.child,o=e.child.memoizedState,o===null?o=dr(t):(S=o.cachePool,S!==null?(C=Pe._currentValue,S=S.parent!==C?{parent:C,pool:C}:S):S=Cf(),o={baseLanes:o.baseLanes|t,cachePool:S}),n.memoizedState=o,n.childLanes=or(e,r,t),a.memoizedState=fr,l):(Bl(a),t=e.child,e=t.sibling,t=Wt(t,{mode:"visible",children:l.children}),t.return=a,t.sibling=null,e!==null&&(r=a.deletions,r===null?(a.deletions=[e],a.flags|=16):r.push(e)),a.child=t,a.memoizedState=null,t)}function hr(e,a){return a=Cc({mode:"visible",children:a},e.mode),a.return=e,e.child=a}function Cc(e,a){return e=wa(22,e,null,a),e.lanes=0,e.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},e}function mr(e,a,t){return nu(a,e.child,null,t),e=hr(a,a.pendingProps.children),e.flags|=2,a.memoizedState=null,e}function wd(e,a,t){e.lanes|=a;var l=e.alternate;l!==null&&(l.lanes|=a),Gs(e.return,a,t)}function yr(e,a,t,l,n){var u=e.memoizedState;u===null?e.memoizedState={isBackwards:a,rendering:null,renderingStartTime:0,last:l,tail:t,tailMode:n}:(u.isBackwards=a,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=t,u.tailMode=n)}function Yd(e,a,t){var l=a.pendingProps,n=l.revealOrder,u=l.tail;if(ha(e,a,l.children,t),l=Ie.current,(l&2)!==0)l=l&1|2,a.flags|=128;else{if(e!==null&&(e.flags&128)!==0)e:for(e=a.child;e!==null;){if(e.tag===13)e.memoizedState!==null&&wd(e,t,a);else if(e.tag===19)wd(e,t,a);else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===a)break e;for(;e.sibling===null;){if(e.return===null||e.return===a)break e;e=e.return}e.sibling.return=e.return,e=e.sibling}l&=1}switch(Z(Ie,l),n){case"forwards":for(t=a.child,n=null;t!==null;)e=t.alternate,e!==null&&Ec(e)===null&&(n=t),t=t.sibling;t=n,t===null?(n=a.child,a.child=null):(n=t.sibling,t.sibling=null),yr(a,!1,n,t,u);break;case"backwards":for(t=null,n=a.child,a.child=null;n!==null;){if(e=n.alternate,e!==null&&Ec(e)===null){a.child=n;break}e=n.sibling,n.sibling=t,t=n,n=e}yr(a,!0,t,null,u);break;case"together":yr(a,!1,null,null,void 0);break;default:a.memoizedState=null}return a.child}function ll(e,a,t){if(e!==null&&(a.dependencies=e.dependencies),Ll|=a.lanes,(t&a.childLanes)===0)if(e!==null){if(Qu(e,a,t,!1),(t&a.childLanes)===0)return null}else return null;if(e!==null&&a.child!==e.child)throw Error(_(153));if(a.child!==null){for(e=a.child,t=Wt(e,e.pendingProps),a.child=t,t.return=a;e.sibling!==null;)e=e.sibling,t=t.sibling=Wt(e,e.pendingProps),t.return=a;t.sibling=null}return a.child}function gr(e,a){return(e.lanes&a)!==0?!0:(e=e.dependencies,!!(e!==null&&uc(e)))}function jh(e,a,t){switch(a.tag){case 3:me(a,a.stateNode.containerInfo),Dl(a,Pe,e.memoizedState.cache),Xu();break;case 27:case 5:Su(a);break;case 4:me(a,a.stateNode.containerInfo);break;case 10:Dl(a,a.type,a.memoizedProps.value);break;case 13:var l=a.memoizedState;if(l!==null)return l.dehydrated!==null?(Bl(a),a.flags|=128,null):(t&a.child.childLanes)!==0?qd(e,a,t):(Bl(a),e=ll(e,a,t),e!==null?e.sibling:null);Bl(a);break;case 19:var n=(e.flags&128)!==0;if(l=(t&a.childLanes)!==0,l||(Qu(e,a,t,!1),l=(t&a.childLanes)!==0),n){if(l)return Yd(e,a,t);a.flags|=128}if(n=a.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),Z(Ie,Ie.current),l)break;return null;case 22:case 23:return a.lanes=0,Ud(e,a,t);case 24:Dl(a,Pe,e.memoizedState.cache)}return ll(e,a,t)}function Vd(e,a,t){if(e!==null)if(e.memoizedProps!==a.pendingProps)ua=!0;else{if(!gr(e,t)&&(a.flags&128)===0)return ua=!1,jh(e,a,t);ua=(e.flags&131072)!==0}else ua=!1,be&&(a.flags&1048576)!==0&&Sf(a,nc,a.index);switch(a.lanes=0,a.tag){case 16:e:{e=a.pendingProps;var l=a.elementType,n=l._init;if(l=n(l._payload),a.type=l,typeof l=="function")Cs(l)?(e=pn(l,e),a.tag=1,a=Ld(null,a,l,e,t)):(a.tag=0,a=rr(null,a,l,e,t));else{if(l!=null){if(n=l.$$typeof,n===za){a.tag=11,a=Bd(null,a,l,e,t);break e}else if(n===Ra){a.tag=14,a=Gd(null,a,l,e,t);break e}}throw a=ct(l)||l,Error(_(306,a,""))}}return a;case 0:return rr(e,a,a.type,a.pendingProps,t);case 1:return l=a.type,n=pn(l,a.pendingProps),Ld(e,a,l,n,t);case 3:e:{if(me(a,a.stateNode.containerInfo),e===null)throw Error(_(387));l=a.pendingProps;var u=a.memoizedState;n=u.element,qs(e,a),Pu(a,l,null,t);var r=a.memoizedState;if(l=r.cache,Dl(a,Pe,l),l!==u.cache&&Hs(a,[Pe],t,!0),Fu(),l=r.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:r.cache},a.updateQueue.baseState=u,a.memoizedState=u,a.flags&256){a=xd(e,a,l,t);break e}else if(l!==n){n=pa(Error(_(424)),a),$u(n),a=xd(e,a,l,t);break e}else{switch(e=a.stateNode.containerInfo,e.nodeType){case 9:e=e.body;break;default:e=e.nodeName==="HTML"?e.ownerDocument.body:e}for(we=Et(e.firstChild),Ca=a,be=!0,mn=null,Ut=!0,t=pd(a,null,l,t),a.child=t;t;)t.flags=t.flags&-3|4096,t=t.sibling}else{if(Xu(),l===n){a=ll(e,a,t);break e}ha(e,a,l,t)}a=a.child}return a;case 26:return bc(e,a),e===null?(t=Zo(a.type,null,a.pendingProps,null))?a.memoizedState=t:be||(t=a.type,e=a.pendingProps,l=qc(ie.current).createElement(t),l[Ve]=a,l[ve]=e,ya(l,t,e),qe(l),a.stateNode=l):a.memoizedState=Zo(a.type,e.memoizedProps,a.pendingProps,e.memoizedState),null;case 27:return Su(a),e===null&&be&&(l=a.stateNode=$o(a.type,a.pendingProps,ie.current),Ca=a,Ut=!0,n=we,Yl(a.type)?(Wr=n,we=Et(l.firstChild)):we=n),ha(e,a,a.pendingProps.children,t),bc(e,a),e===null&&(a.flags|=4194304),a.child;case 5:return e===null&&be&&((n=l=we)&&(l=rm(l,a.type,a.pendingProps,Ut),l!==null?(a.stateNode=l,Ca=a,we=Et(l.firstChild),Ut=!1,n=!0):n=!1),n||yn(a)),Su(a),n=a.type,u=a.pendingProps,r=e!==null?e.memoizedProps:null,l=u.children,kr(n,u)?l=null:r!==null&&kr(n,r)&&(a.flags|=32),a.memoizedState!==null&&(n=Qs(e,a,Rh,null,null,t),pi._currentValue=n),bc(e,a),ha(e,a,l,t),a.child;case 6:return e===null&&be&&((e=t=we)&&(t=fm(t,a.pendingProps,Ut),t!==null?(a.stateNode=t,Ca=a,we=null,e=!0):e=!1),e||yn(a)),null;case 13:return qd(e,a,t);case 4:return me(a,a.stateNode.containerInfo),l=a.pendingProps,e===null?a.child=nu(a,null,l,t):ha(e,a,l,t),a.child;case 11:return Bd(e,a,a.type,a.pendingProps,t);case 7:return ha(e,a,a.pendingProps,t),a.child;case 8:return ha(e,a,a.pendingProps.children,t),a.child;case 12:return ha(e,a,a.pendingProps.children,t),a.child;case 10:return l=a.pendingProps,Dl(a,a.type,l.value),ha(e,a,l.children,t),a.child;case 9:return n=a.type._context,l=a.pendingProps.children,vn(a),n=Ea(n),l=l(n),a.flags|=1,ha(e,a,l,t),a.child;case 14:return Gd(e,a,a.type,a.pendingProps,t);case 15:return Hd(e,a,a.type,a.pendingProps,t);case 19:return Yd(e,a,t);case 31:return l=a.pendingProps,t=a.mode,l={mode:l.mode,children:l.children},e===null?(t=Cc(l,t),t.ref=a.ref,a.child=t,t.return=a,a=t):(t=Wt(e.child,l),t.ref=a.ref,a.child=t,t.return=a,a=t),a;case 22:return Ud(e,a,t);case 24:return vn(a),l=Ea(Pe),e===null?(n=zs(),n===null&&(n=He,u=Us(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=t),n=u),a.memoizedState={parent:l,cache:n},xs(a),Dl(a,Pe,n)):((e.lanes&t)!==0&&(qs(e,a),Pu(a,null,null,t),Fu()),n=e.memoizedState,u=a.memoizedState,n.parent!==l?(n={parent:l,cache:l},a.memoizedState=n,a.lanes===0&&(a.memoizedState=a.updateQueue.baseState=n),Dl(a,Pe,l)):(l=u.cache,Dl(a,Pe,l),l!==n.cache&&Hs(a,[Pe],t,!0))),ha(e,a,a.pendingProps.children,t),a.child;case 29:throw a.pendingProps}throw Error(_(156,a.tag))}function nl(e){e.flags|=4}function Xd(e,a){if(a.type!=="stylesheet"||(a.state.loading&4)!==0)e.flags&=-16777217;else if(e.flags|=16777216,!Po(a)){if(a=tt.current,a!==null&&((Se&4194048)===Se?jt!==null:(Se&62914560)!==Se&&(Se&536870912)===0||a!==jt))throw Ju=Ls,Df;e.flags|=8192}}function Dc(e,a){a!==null&&(e.flags|=4),e.flags&16384&&(a=e.tag!==22?pu():536870912,e.lanes|=a,su|=a)}function ui(e,a){if(!be)switch(e.tailMode){case"hidden":a=e.tail;for(var t=null;a!==null;)a.alternate!==null&&(t=a),a=a.sibling;t===null?e.tail=null:t.sibling=null;break;case"collapsed":t=e.tail;for(var l=null;t!==null;)t.alternate!==null&&(l=t),t=t.sibling;l===null?a||e.tail===null?e.tail=null:e.tail.sibling=null:l.sibling=null}}function xe(e){var a=e.alternate!==null&&e.alternate.child===e.child,t=0,l=0;if(a)for(var n=e.child;n!==null;)t|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=e,n=n.sibling;else for(n=e.child;n!==null;)t|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=e,n=n.sibling;return e.subtreeFlags|=l,e.childLanes=t,a}function zh(e,a,t){var l=a.pendingProps;switch(_s(a),a.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return xe(a),null;case 1:return xe(a),null;case 3:return t=a.stateNode,l=null,e!==null&&(l=e.memoizedState.cache),a.memoizedState.cache!==l&&(a.flags|=2048),el(Pe),Ka(),t.pendingContext&&(t.context=t.pendingContext,t.pendingContext=null),(e===null||e.child===null)&&(Vu(a)?nl(a):e===null||e.memoizedState.isDehydrated&&(a.flags&256)===0||(a.flags|=1024,Ef())),xe(a),null;case 26:return t=a.memoizedState,e===null?(nl(a),t!==null?(xe(a),Xd(a,t)):(xe(a),a.flags&=-16777217)):t?t!==e.memoizedState?(nl(a),xe(a),Xd(a,t)):(xe(a),a.flags&=-16777217):(e.memoizedProps!==l&&nl(a),xe(a),a.flags&=-16777217),null;case 27:rl(a),t=ie.current;var n=a.type;if(e!==null&&a.stateNode!=null)e.memoizedProps!==l&&nl(a);else{if(!l){if(a.stateNode===null)throw Error(_(166));return xe(a),null}e=I.current,Vu(a)?Af(a):(e=$o(n,l,t),a.stateNode=e,nl(a))}return xe(a),null;case 5:if(rl(a),t=a.type,e!==null&&a.stateNode!=null)e.memoizedProps!==l&&nl(a);else{if(!l){if(a.stateNode===null)throw Error(_(166));return xe(a),null}if(e=I.current,Vu(a))Af(a);else{switch(n=qc(ie.current),e){case 1:e=n.createElementNS("http://www.w3.org/2000/svg",t);break;case 2:e=n.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;default:switch(t){case"svg":e=n.createElementNS("http://www.w3.org/2000/svg",t);break;case"math":e=n.createElementNS("http://www.w3.org/1998/Math/MathML",t);break;case"script":e=n.createElement("div"),e.innerHTML="<script><\/script>",e=e.removeChild(e.firstChild);break;case"select":e=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?e.multiple=!0:l.size&&(e.size=l.size);break;default:e=typeof l.is=="string"?n.createElement(t,{is:l.is}):n.createElement(t)}}e[Ve]=a,e[ve]=l;e:for(n=a.child;n!==null;){if(n.tag===5||n.tag===6)e.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===a)break e;for(;n.sibling===null;){if(n.return===null||n.return===a)break e;n=n.return}n.sibling.return=n.return,n=n.sibling}a.stateNode=e;e:switch(ya(e,t,l),t){case"button":case"input":case"select":case"textarea":e=!!l.autoFocus;break e;case"img":e=!0;break e;default:e=!1}e&&nl(a)}}return xe(a),a.flags&=-16777217,null;case 6:if(e&&a.stateNode!=null)e.memoizedProps!==l&&nl(a);else{if(typeof l!="string"&&a.stateNode===null)throw Error(_(166));if(e=ie.current,Vu(a)){if(e=a.stateNode,t=a.memoizedProps,l=null,n=Ca,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}e[Ve]=a,e=!!(e.nodeValue===t||l!==null&&l.suppressHydrationWarning===!0||Lo(e.nodeValue,t)),e||yn(a)}else e=qc(e).createTextNode(l),e[Ve]=a,a.stateNode=e}return xe(a),null;case 13:if(l=a.memoizedState,e===null||e.memoizedState!==null&&e.memoizedState.dehydrated!==null){if(n=Vu(a),l!==null&&l.dehydrated!==null){if(e===null){if(!n)throw Error(_(318));if(n=a.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(_(317));n[Ve]=a}else Xu(),(a.flags&128)===0&&(a.memoizedState=null),a.flags|=4;xe(a),n=!1}else n=Ef(),e!==null&&e.memoizedState!==null&&(e.memoizedState.hydrationErrors=n),n=!0;if(!n)return a.flags&256?(tl(a),a):(tl(a),null)}if(tl(a),(a.flags&128)!==0)return a.lanes=t,a;if(t=l!==null,e=e!==null&&e.memoizedState!==null,t){l=a.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==n&&(l.flags|=2048)}return t!==e&&t&&(a.child.flags|=8192),Dc(a,a.updateQueue),xe(a),null;case 4:return Ka(),e===null&&Yr(a.stateNode.containerInfo),xe(a),null;case 10:return el(a.type),xe(a),null;case 19:if(k(Ie),n=a.memoizedState,n===null)return xe(a),null;if(l=(a.flags&128)!==0,u=n.rendering,u===null)if(l)ui(n,!1);else{if(Ye!==0||e!==null&&(e.flags&128)!==0)for(e=a.child;e!==null;){if(u=Ec(e),u!==null){for(a.flags|=128,ui(n,!1),e=u.updateQueue,a.updateQueue=e,Dc(a,e),a.subtreeFlags=0,e=t,t=a.child;t!==null;)vf(t,e),t=t.sibling;return Z(Ie,Ie.current&1|2),a.child}e=e.sibling}n.tail!==null&&Oa()>_c&&(a.flags|=128,l=!0,ui(n,!1),a.lanes=4194304)}else{if(!l)if(e=Ec(u),e!==null){if(a.flags|=128,l=!0,e=e.updateQueue,a.updateQueue=e,Dc(a,e),ui(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!be)return xe(a),null}else 2*Oa()-n.renderingStartTime>_c&&t!==536870912&&(a.flags|=128,l=!0,ui(n,!1),a.lanes=4194304);n.isBackwards?(u.sibling=a.child,a.child=u):(e=n.last,e!==null?e.sibling=u:a.child=u,n.last=u)}return n.tail!==null?(a=n.tail,n.rendering=a,n.tail=a.sibling,n.renderingStartTime=Oa(),a.sibling=null,e=Ie.current,Z(Ie,l?e&1|2:e&1),a):(xe(a),null);case 22:case 23:return tl(a),Xs(),l=a.memoizedState!==null,e!==null?e.memoizedState!==null!==l&&(a.flags|=8192):l&&(a.flags|=8192),l?(t&536870912)!==0&&(a.flags&128)===0&&(xe(a),a.subtreeFlags&6&&(a.flags|=8192)):xe(a),t=a.updateQueue,t!==null&&Dc(a,t.retryQueue),t=null,e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),l=null,a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(l=a.memoizedState.cachePool.pool),l!==t&&(a.flags|=2048),e!==null&&k(Sn),null;case 24:return t=null,e!==null&&(t=e.memoizedState.cache),a.memoizedState.cache!==t&&(a.flags|=2048),el(Pe),xe(a),null;case 25:return null;case 30:return null}throw Error(_(156,a.tag))}function Lh(e,a){switch(_s(a),a.tag){case 1:return e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 3:return el(Pe),Ka(),e=a.flags,(e&65536)!==0&&(e&128)===0?(a.flags=e&-65537|128,a):null;case 26:case 27:case 5:return rl(a),null;case 13:if(tl(a),e=a.memoizedState,e!==null&&e.dehydrated!==null){if(a.alternate===null)throw Error(_(340));Xu()}return e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 19:return k(Ie),null;case 4:return Ka(),null;case 10:return el(a.type),null;case 22:case 23:return tl(a),Xs(),e!==null&&k(Sn),e=a.flags,e&65536?(a.flags=e&-65537|128,a):null;case 24:return el(Pe),null;case 25:return null;default:return null}}function $d(e,a){switch(_s(a),a.tag){case 3:el(Pe),Ka();break;case 26:case 27:case 5:rl(a);break;case 4:Ka();break;case 13:tl(a);break;case 19:k(Ie);break;case 10:el(a.type);break;case 22:case 23:tl(a),Xs(),e!==null&&k(Sn);break;case 24:el(Pe)}}function ii(e,a){try{var t=a.updateQueue,l=t!==null?t.lastEffect:null;if(l!==null){var n=l.next;t=n;do{if((t.tag&e)===e){l=void 0;var u=t.create,r=t.inst;l=u(),r.destroy=l}t=t.next}while(t!==n)}}catch(o){Be(a,a.return,o)}}function Hl(e,a,t){try{var l=a.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var u=n.next;l=u;do{if((l.tag&e)===e){var r=l.inst,o=r.destroy;if(o!==void 0){r.destroy=void 0,n=a;var S=t,C=o;try{C()}catch(H){Be(n,S,H)}}}l=l.next}while(l!==u)}}catch(H){Be(a,a.return,H)}}function Qd(e){var a=e.updateQueue;if(a!==null){var t=e.stateNode;try{Gf(a,t)}catch(l){Be(e,e.return,l)}}}function kd(e,a,t){t.props=pn(e.type,e.memoizedProps),t.state=e.memoizedState;try{t.componentWillUnmount()}catch(l){Be(e,a,l)}}function ci(e,a){try{var t=e.ref;if(t!==null){switch(e.tag){case 26:case 27:case 5:var l=e.stateNode;break;case 30:l=e.stateNode;break;default:l=e.stateNode}typeof t=="function"?e.refCleanup=t(l):t.current=l}}catch(n){Be(e,a,n)}}function zt(e,a){var t=e.ref,l=e.refCleanup;if(t!==null)if(typeof l=="function")try{l()}catch(n){Be(e,a,n)}finally{e.refCleanup=null,e=e.alternate,e!=null&&(e.refCleanup=null)}else if(typeof t=="function")try{t(null)}catch(n){Be(e,a,n)}else t.current=null}function Zd(e){var a=e.type,t=e.memoizedProps,l=e.stateNode;try{e:switch(a){case"button":case"input":case"select":case"textarea":t.autoFocus&&l.focus();break e;case"img":t.src?l.src=t.src:t.srcSet&&(l.srcset=t.srcSet)}}catch(n){Be(e,e.return,n)}}function vr(e,a,t){try{var l=e.stateNode;nm(l,e.type,t,a),l[ve]=a}catch(n){Be(e,e.return,n)}}function Kd(e){return e.tag===5||e.tag===3||e.tag===26||e.tag===27&&Yl(e.type)||e.tag===4}function Sr(e){e:for(;;){for(;e.sibling===null;){if(e.return===null||Kd(e.return))return null;e=e.return}for(e.sibling.return=e.return,e=e.sibling;e.tag!==5&&e.tag!==6&&e.tag!==18;){if(e.tag===27&&Yl(e.type)||e.flags&2||e.child===null||e.tag===4)continue e;e.child.return=e,e=e.child}if(!(e.flags&2))return e.stateNode}}function Ar(e,a,t){var l=e.tag;if(l===5||l===6)e=e.stateNode,a?(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t).insertBefore(e,a):(a=t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.appendChild(e),t=t._reactRootContainer,t!=null||a.onclick!==null||(a.onclick=xc));else if(l!==4&&(l===27&&Yl(e.type)&&(t=e.stateNode,a=null),e=e.child,e!==null))for(Ar(e,a,t),e=e.sibling;e!==null;)Ar(e,a,t),e=e.sibling}function Rc(e,a,t){var l=e.tag;if(l===5||l===6)e=e.stateNode,a?t.insertBefore(e,a):t.appendChild(e);else if(l!==4&&(l===27&&Yl(e.type)&&(t=e.stateNode),e=e.child,e!==null))for(Rc(e,a,t),e=e.sibling;e!==null;)Rc(e,a,t),e=e.sibling}function Jd(e){var a=e.stateNode,t=e.memoizedProps;try{for(var l=e.type,n=a.attributes;n.length;)a.removeAttributeNode(n[0]);ya(a,l,t),a[Ve]=e,a[ve]=t}catch(u){Be(e,e.return,u)}}var ul=!1,Qe=!1,pr=!1,Wd=typeof WeakSet=="function"?WeakSet:Set,ia=null;function xh(e,a){if(e=e.containerInfo,$r=Qc,e=Yu(e),Xn(e)){if("selectionStart"in e)var t={start:e.selectionStart,end:e.selectionEnd};else e:{t=(t=e.ownerDocument)&&t.defaultView||window;var l=t.getSelection&&t.getSelection();if(l&&l.rangeCount!==0){t=l.anchorNode;var n=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{t.nodeType,u.nodeType}catch{t=null;break e}var r=0,o=-1,S=-1,C=0,H=0,x=e,R=null;a:for(;;){for(var O;x!==t||n!==0&&x.nodeType!==3||(o=r+n),x!==u||l!==0&&x.nodeType!==3||(S=r+l),x.nodeType===3&&(r+=x.nodeValue.length),(O=x.firstChild)!==null;)R=x,x=O;for(;;){if(x===e)break a;if(R===t&&++C===n&&(o=r),R===u&&++H===l&&(S=r),(O=x.nextSibling)!==null)break;x=R,R=x.parentNode}x=O}t=o===-1||S===-1?null:{start:o,end:S}}else t=null}t=t||{start:0,end:0}}else t=null;for(Qr={focusedElem:e,selectionRange:t},Qc=!1,ia=a;ia!==null;)if(a=ia,e=a.child,(a.subtreeFlags&1024)!==0&&e!==null)e.return=a,ia=e;else for(;ia!==null;){switch(a=ia,u=a.alternate,e=a.flags,a.tag){case 0:break;case 11:case 15:break;case 1:if((e&1024)!==0&&u!==null){e=void 0,t=a,n=u.memoizedProps,u=u.memoizedState,l=t.stateNode;try{var ne=pn(t.type,n,t.elementType===t.type);e=l.getSnapshotBeforeUpdate(ne,u),l.__reactInternalSnapshotBeforeUpdate=e}catch(ae){Be(t,t.return,ae)}}break;case 3:if((e&1024)!==0){if(e=a.stateNode.containerInfo,t=e.nodeType,t===9)Kr(e);else if(t===1)switch(e.nodeName){case"HEAD":case"HTML":case"BODY":Kr(e);break;default:e.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((e&1024)!==0)throw Error(_(163))}if(e=a.sibling,e!==null){e.return=a.return,ia=e;break}ia=a.return}}function Fd(e,a,t){var l=t.flags;switch(t.tag){case 0:case 11:case 15:Ul(e,t),l&4&&ii(5,t);break;case 1:if(Ul(e,t),l&4)if(e=t.stateNode,a===null)try{e.componentDidMount()}catch(r){Be(t,t.return,r)}else{var n=pn(t.type,a.memoizedProps);a=a.memoizedState;try{e.componentDidUpdate(n,a,e.__reactInternalSnapshotBeforeUpdate)}catch(r){Be(t,t.return,r)}}l&64&&Qd(t),l&512&&ci(t,t.return);break;case 3:if(Ul(e,t),l&64&&(e=t.updateQueue,e!==null)){if(a=null,t.child!==null)switch(t.child.tag){case 27:case 5:a=t.child.stateNode;break;case 1:a=t.child.stateNode}try{Gf(e,a)}catch(r){Be(t,t.return,r)}}break;case 27:a===null&&l&4&&Jd(t);case 26:case 5:Ul(e,t),a===null&&l&4&&Zd(t),l&512&&ci(t,t.return);break;case 12:Ul(e,t);break;case 13:Ul(e,t),l&4&&eo(e,t),l&64&&(e=t.memoizedState,e!==null&&(e=e.dehydrated,e!==null&&(t=Zh.bind(null,t),dm(e,t))));break;case 22:if(l=t.memoizedState!==null||ul,!l){a=a!==null&&a.memoizedState!==null||Qe,n=ul;var u=Qe;ul=l,(Qe=a)&&!u?jl(e,t,(t.subtreeFlags&8772)!==0):Ul(e,t),ul=n,Qe=u}break;case 30:break;default:Ul(e,t)}}function Pd(e){var a=e.alternate;a!==null&&(e.alternate=null,Pd(a)),e.child=null,e.deletions=null,e.sibling=null,e.tag===5&&(a=e.stateNode,a!==null&&hl(a)),e.stateNode=null,e.return=null,e.dependencies=null,e.memoizedProps=null,e.memoizedState=null,e.pendingProps=null,e.stateNode=null,e.updateQueue=null}var Le=null,Ua=!1;function il(e,a,t){for(t=t.child;t!==null;)Id(e,a,t),t=t.sibling}function Id(e,a,t){if(va&&typeof va.onCommitFiberUnmount=="function")try{va.onCommitFiberUnmount(dl,t)}catch{}switch(t.tag){case 26:Qe||zt(t,a),il(e,a,t),t.memoizedState?t.memoizedState.count--:t.stateNode&&(t=t.stateNode,t.parentNode.removeChild(t));break;case 27:Qe||zt(t,a);var l=Le,n=Ua;Yl(t.type)&&(Le=t.stateNode,Ua=!1),il(e,a,t),gi(t.stateNode),Le=l,Ua=n;break;case 5:Qe||zt(t,a);case 6:if(l=Le,n=Ua,Le=null,il(e,a,t),Le=l,Ua=n,Le!==null)if(Ua)try{(Le.nodeType===9?Le.body:Le.nodeName==="HTML"?Le.ownerDocument.body:Le).removeChild(t.stateNode)}catch(u){Be(t,a,u)}else try{Le.removeChild(t.stateNode)}catch(u){Be(t,a,u)}break;case 18:Le!==null&&(Ua?(e=Le,Vo(e.nodeType===9?e.body:e.nodeName==="HTML"?e.ownerDocument.body:e,t.stateNode),bi(e)):Vo(Le,t.stateNode));break;case 4:l=Le,n=Ua,Le=t.stateNode.containerInfo,Ua=!0,il(e,a,t),Le=l,Ua=n;break;case 0:case 11:case 14:case 15:Qe||Hl(2,t,a),Qe||Hl(4,t,a),il(e,a,t);break;case 1:Qe||(zt(t,a),l=t.stateNode,typeof l.componentWillUnmount=="function"&&kd(t,a,l)),il(e,a,t);break;case 21:il(e,a,t);break;case 22:Qe=(l=Qe)||t.memoizedState!==null,il(e,a,t),Qe=l;break;default:il(e,a,t)}}function eo(e,a){if(a.memoizedState===null&&(e=a.alternate,e!==null&&(e=e.memoizedState,e!==null&&(e=e.dehydrated,e!==null))))try{bi(e)}catch(t){Be(a,a.return,t)}}function qh(e){switch(e.tag){case 13:case 19:var a=e.stateNode;return a===null&&(a=e.stateNode=new Wd),a;case 22:return e=e.stateNode,a=e._retryCache,a===null&&(a=e._retryCache=new Wd),a;default:throw Error(_(435,e.tag))}}function Er(e,a){var t=qh(e);a.forEach(function(l){var n=Kh.bind(null,e,l);t.has(l)||(t.add(l),l.then(n,n))})}function Ya(e,a){var t=a.deletions;if(t!==null)for(var l=0;l<t.length;l++){var n=t[l],u=e,r=a,o=r;e:for(;o!==null;){switch(o.tag){case 27:if(Yl(o.type)){Le=o.stateNode,Ua=!1;break e}break;case 5:Le=o.stateNode,Ua=!1;break e;case 3:case 4:Le=o.stateNode.containerInfo,Ua=!0;break e}o=o.return}if(Le===null)throw Error(_(160));Id(u,r,n),Le=null,Ua=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(a.subtreeFlags&13878)for(a=a.child;a!==null;)ao(a,e),a=a.sibling}var pt=null;function ao(e,a){var t=e.alternate,l=e.flags;switch(e.tag){case 0:case 11:case 14:case 15:Ya(a,e),Va(e),l&4&&(Hl(3,e,e.return),ii(3,e),Hl(5,e,e.return));break;case 1:Ya(a,e),Va(e),l&512&&(Qe||t===null||zt(t,t.return)),l&64&&ul&&(e=e.updateQueue,e!==null&&(l=e.callbacks,l!==null&&(t=e.shared.hiddenCallbacks,e.shared.hiddenCallbacks=t===null?l:t.concat(l))));break;case 26:var n=pt;if(Ya(a,e),Va(e),l&512&&(Qe||t===null||zt(t,t.return)),l&4){var u=t!==null?t.memoizedState:null;if(l=e.memoizedState,t===null)if(l===null)if(e.stateNode===null){e:{l=e.type,t=e.memoizedProps,n=n.ownerDocument||n;a:switch(l){case"title":u=n.getElementsByTagName("title")[0],(!u||u[dt]||u[Ve]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(l),n.head.insertBefore(u,n.querySelector("head > title"))),ya(u,l,t),u[Ve]=e,qe(u),l=u;break e;case"link":var r=Wo("link","href",n).get(l+(t.href||""));if(r){for(var o=0;o<r.length;o++)if(u=r[o],u.getAttribute("href")===(t.href==null||t.href===""?null:t.href)&&u.getAttribute("rel")===(t.rel==null?null:t.rel)&&u.getAttribute("title")===(t.title==null?null:t.title)&&u.getAttribute("crossorigin")===(t.crossOrigin==null?null:t.crossOrigin)){r.splice(o,1);break a}}u=n.createElement(l),ya(u,l,t),n.head.appendChild(u);break;case"meta":if(r=Wo("meta","content",n).get(l+(t.content||""))){for(o=0;o<r.length;o++)if(u=r[o],u.getAttribute("content")===(t.content==null?null:""+t.content)&&u.getAttribute("name")===(t.name==null?null:t.name)&&u.getAttribute("property")===(t.property==null?null:t.property)&&u.getAttribute("http-equiv")===(t.httpEquiv==null?null:t.httpEquiv)&&u.getAttribute("charset")===(t.charSet==null?null:t.charSet)){r.splice(o,1);break a}}u=n.createElement(l),ya(u,l,t),n.head.appendChild(u);break;default:throw Error(_(468,l))}u[Ve]=e,qe(u),l=u}e.stateNode=l}else Fo(n,e.type,e.stateNode);else e.stateNode=Jo(n,l,e.memoizedProps);else u!==l?(u===null?t.stateNode!==null&&(t=t.stateNode,t.parentNode.removeChild(t)):u.count--,l===null?Fo(n,e.type,e.stateNode):Jo(n,l,e.memoizedProps)):l===null&&e.stateNode!==null&&vr(e,e.memoizedProps,t.memoizedProps)}break;case 27:Ya(a,e),Va(e),l&512&&(Qe||t===null||zt(t,t.return)),t!==null&&l&4&&vr(e,e.memoizedProps,t.memoizedProps);break;case 5:if(Ya(a,e),Va(e),l&512&&(Qe||t===null||zt(t,t.return)),e.flags&32){n=e.stateNode;try{Al(n,"")}catch(O){Be(e,e.return,O)}}l&4&&e.stateNode!=null&&(n=e.memoizedProps,vr(e,n,t!==null?t.memoizedProps:n)),l&1024&&(pr=!0);break;case 6:if(Ya(a,e),Va(e),l&4){if(e.stateNode===null)throw Error(_(162));l=e.memoizedProps,t=e.stateNode;try{t.nodeValue=l}catch(O){Be(e,e.return,O)}}break;case 3:if(Vc=null,n=pt,pt=wc(a.containerInfo),Ya(a,e),pt=n,Va(e),l&4&&t!==null&&t.memoizedState.isDehydrated)try{bi(a.containerInfo)}catch(O){Be(e,e.return,O)}pr&&(pr=!1,to(e));break;case 4:l=pt,pt=wc(e.stateNode.containerInfo),Ya(a,e),Va(e),pt=l;break;case 12:Ya(a,e),Va(e);break;case 13:Ya(a,e),Va(e),e.child.flags&8192&&e.memoizedState!==null!=(t!==null&&t.memoizedState!==null)&&(Rr=Oa()),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Er(e,l)));break;case 22:n=e.memoizedState!==null;var S=t!==null&&t.memoizedState!==null,C=ul,H=Qe;if(ul=C||n,Qe=H||S,Ya(a,e),Qe=H,ul=C,Va(e),l&8192)e:for(a=e.stateNode,a._visibility=n?a._visibility&-2:a._visibility|1,n&&(t===null||S||ul||Qe||En(e)),t=null,a=e;;){if(a.tag===5||a.tag===26){if(t===null){S=t=a;try{if(u=S.stateNode,n)r=u.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none";else{o=S.stateNode;var x=S.memoizedProps.style,R=x!=null&&x.hasOwnProperty("display")?x.display:null;o.style.display=R==null||typeof R=="boolean"?"":(""+R).trim()}}catch(O){Be(S,S.return,O)}}}else if(a.tag===6){if(t===null){S=a;try{S.stateNode.nodeValue=n?"":S.memoizedProps}catch(O){Be(S,S.return,O)}}}else if((a.tag!==22&&a.tag!==23||a.memoizedState===null||a===e)&&a.child!==null){a.child.return=a,a=a.child;continue}if(a===e)break e;for(;a.sibling===null;){if(a.return===null||a.return===e)break e;t===a&&(t=null),a=a.return}t===a&&(t=null),a.sibling.return=a.return,a=a.sibling}l&4&&(l=e.updateQueue,l!==null&&(t=l.retryQueue,t!==null&&(l.retryQueue=null,Er(e,t))));break;case 19:Ya(a,e),Va(e),l&4&&(l=e.updateQueue,l!==null&&(e.updateQueue=null,Er(e,l)));break;case 30:break;case 21:break;default:Ya(a,e),Va(e)}}function Va(e){var a=e.flags;if(a&2){try{for(var t,l=e.return;l!==null;){if(Kd(l)){t=l;break}l=l.return}if(t==null)throw Error(_(160));switch(t.tag){case 27:var n=t.stateNode,u=Sr(e);Rc(e,u,n);break;case 5:var r=t.stateNode;t.flags&32&&(Al(r,""),t.flags&=-33);var o=Sr(e);Rc(e,o,r);break;case 3:case 4:var S=t.stateNode.containerInfo,C=Sr(e);Ar(e,C,S);break;default:throw Error(_(161))}}catch(H){Be(e,e.return,H)}e.flags&=-3}a&4096&&(e.flags&=-4097)}function to(e){if(e.subtreeFlags&1024)for(e=e.child;e!==null;){var a=e;to(a),a.tag===5&&a.flags&1024&&a.stateNode.reset(),e=e.sibling}}function Ul(e,a){if(a.subtreeFlags&8772)for(a=a.child;a!==null;)Fd(e,a.alternate,a),a=a.sibling}function En(e){for(e=e.child;e!==null;){var a=e;switch(a.tag){case 0:case 11:case 14:case 15:Hl(4,a,a.return),En(a);break;case 1:zt(a,a.return);var t=a.stateNode;typeof t.componentWillUnmount=="function"&&kd(a,a.return,t),En(a);break;case 27:gi(a.stateNode);case 26:case 5:zt(a,a.return),En(a);break;case 22:a.memoizedState===null&&En(a);break;case 30:En(a);break;default:En(a)}e=e.sibling}}function jl(e,a,t){for(t=t&&(a.subtreeFlags&8772)!==0,a=a.child;a!==null;){var l=a.alternate,n=e,u=a,r=u.flags;switch(u.tag){case 0:case 11:case 15:jl(n,u,t),ii(4,u);break;case 1:if(jl(n,u,t),l=u,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(C){Be(l,l.return,C)}if(l=u,n=l.updateQueue,n!==null){var o=l.stateNode;try{var S=n.shared.hiddenCallbacks;if(S!==null)for(n.shared.hiddenCallbacks=null,n=0;n<S.length;n++)Bf(S[n],o)}catch(C){Be(l,l.return,C)}}t&&r&64&&Qd(u),ci(u,u.return);break;case 27:Jd(u);case 26:case 5:jl(n,u,t),t&&l===null&&r&4&&Zd(u),ci(u,u.return);break;case 12:jl(n,u,t);break;case 13:jl(n,u,t),t&&r&4&&eo(n,u);break;case 22:u.memoizedState===null&&jl(n,u,t),ci(u,u.return);break;case 30:break;default:jl(n,u,t)}a=a.sibling}}function Tr(e,a){var t=null;e!==null&&e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),e=null,a.memoizedState!==null&&a.memoizedState.cachePool!==null&&(e=a.memoizedState.cachePool.pool),e!==t&&(e!=null&&e.refCount++,t!=null&&ku(t))}function Nr(e,a){e=null,a.alternate!==null&&(e=a.alternate.memoizedState.cache),a=a.memoizedState.cache,a!==e&&(a.refCount++,e!=null&&ku(e))}function Lt(e,a,t,l){if(a.subtreeFlags&10256)for(a=a.child;a!==null;)lo(e,a,t,l),a=a.sibling}function lo(e,a,t,l){var n=a.flags;switch(a.tag){case 0:case 11:case 15:Lt(e,a,t,l),n&2048&&ii(9,a);break;case 1:Lt(e,a,t,l);break;case 3:Lt(e,a,t,l),n&2048&&(e=null,a.alternate!==null&&(e=a.alternate.memoizedState.cache),a=a.memoizedState.cache,a!==e&&(a.refCount++,e!=null&&ku(e)));break;case 12:if(n&2048){Lt(e,a,t,l),e=a.stateNode;try{var u=a.memoizedProps,r=u.id,o=u.onPostCommit;typeof o=="function"&&o(r,a.alternate===null?"mount":"update",e.passiveEffectDuration,-0)}catch(S){Be(a,a.return,S)}}else Lt(e,a,t,l);break;case 13:Lt(e,a,t,l);break;case 23:break;case 22:u=a.stateNode,r=a.alternate,a.memoizedState!==null?u._visibility&2?Lt(e,a,t,l):si(e,a):u._visibility&2?Lt(e,a,t,l):(u._visibility|=2,uu(e,a,t,l,(a.subtreeFlags&10256)!==0)),n&2048&&Tr(r,a);break;case 24:Lt(e,a,t,l),n&2048&&Nr(a.alternate,a);break;default:Lt(e,a,t,l)}}function uu(e,a,t,l,n){for(n=n&&(a.subtreeFlags&10256)!==0,a=a.child;a!==null;){var u=e,r=a,o=t,S=l,C=r.flags;switch(r.tag){case 0:case 11:case 15:uu(u,r,o,S,n),ii(8,r);break;case 23:break;case 22:var H=r.stateNode;r.memoizedState!==null?H._visibility&2?uu(u,r,o,S,n):si(u,r):(H._visibility|=2,uu(u,r,o,S,n)),n&&C&2048&&Tr(r.alternate,r);break;case 24:uu(u,r,o,S,n),n&&C&2048&&Nr(r.alternate,r);break;default:uu(u,r,o,S,n)}a=a.sibling}}function si(e,a){if(a.subtreeFlags&10256)for(a=a.child;a!==null;){var t=e,l=a,n=l.flags;switch(l.tag){case 22:si(t,l),n&2048&&Tr(l.alternate,l);break;case 24:si(t,l),n&2048&&Nr(l.alternate,l);break;default:si(t,l)}a=a.sibling}}var ri=8192;function iu(e){if(e.subtreeFlags&ri)for(e=e.child;e!==null;)no(e),e=e.sibling}function no(e){switch(e.tag){case 26:iu(e),e.flags&ri&&e.memoizedState!==null&&bm(pt,e.memoizedState,e.memoizedProps);break;case 5:iu(e);break;case 3:case 4:var a=pt;pt=wc(e.stateNode.containerInfo),iu(e),pt=a;break;case 22:e.memoizedState===null&&(a=e.alternate,a!==null&&a.memoizedState!==null?(a=ri,ri=16777216,iu(e),ri=a):iu(e));break;default:iu(e)}}function uo(e){var a=e.alternate;if(a!==null&&(e=a.child,e!==null)){a.child=null;do a=e.sibling,e.sibling=null,e=a;while(e!==null)}}function fi(e){var a=e.deletions;if((e.flags&16)!==0){if(a!==null)for(var t=0;t<a.length;t++){var l=a[t];ia=l,co(l,e)}uo(e)}if(e.subtreeFlags&10256)for(e=e.child;e!==null;)io(e),e=e.sibling}function io(e){switch(e.tag){case 0:case 11:case 15:fi(e),e.flags&2048&&Hl(9,e,e.return);break;case 3:fi(e);break;case 12:fi(e);break;case 22:var a=e.stateNode;e.memoizedState!==null&&a._visibility&2&&(e.return===null||e.return.tag!==13)?(a._visibility&=-3,Oc(e)):fi(e);break;default:fi(e)}}function Oc(e){var a=e.deletions;if((e.flags&16)!==0){if(a!==null)for(var t=0;t<a.length;t++){var l=a[t];ia=l,co(l,e)}uo(e)}for(e=e.child;e!==null;){switch(a=e,a.tag){case 0:case 11:case 15:Hl(8,a,a.return),Oc(a);break;case 22:t=a.stateNode,t._visibility&2&&(t._visibility&=-3,Oc(a));break;default:Oc(a)}e=e.sibling}}function co(e,a){for(;ia!==null;){var t=ia;switch(t.tag){case 0:case 11:case 15:Hl(8,t,a);break;case 23:case 22:if(t.memoizedState!==null&&t.memoizedState.cachePool!==null){var l=t.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:ku(t.memoizedState.cache)}if(l=t.child,l!==null)l.return=t,ia=l;else e:for(t=e;ia!==null;){l=ia;var n=l.sibling,u=l.return;if(Pd(l),l===t){ia=null;break e}if(n!==null){n.return=u,ia=n;break e}ia=u}}}var wh={getCacheForType:function(e){var a=Ea(Pe),t=a.data.get(e);return t===void 0&&(t=e(),a.data.set(e,t)),t}},Yh=typeof WeakMap=="function"?WeakMap:Map,De=0,He=null,de=null,Se=0,Re=0,Xa=null,zl=!1,cu=!1,br=!1,cl=0,Ye=0,Ll=0,Tn=0,Cr=0,lt=0,su=0,di=null,ja=null,Dr=!1,Rr=0,_c=1/0,Mc=null,xl=null,ma=0,ql=null,ru=null,fu=0,Or=0,_r=null,so=null,oi=0,Mr=null;function $a(){if((De&2)!==0&&Se!==0)return Se&-Se;if(G.T!==null){var e=Fn;return e!==0?e:Lr()}return ta()}function ro(){lt===0&&(lt=(Se&536870912)===0||be?Au():536870912);var e=tt.current;return e!==null&&(e.flags|=32),lt}function Qa(e,a,t){(e===He&&(Re===2||Re===9)||e.cancelPendingCommit!==null)&&(du(e,0),wl(e,Se,lt,!1)),Wl(e,t),((De&2)===0||e!==He)&&(e===He&&((De&2)===0&&(Tn|=t),Ye===4&&wl(e,Se,lt,!1)),xt(e))}function fo(e,a,t){if((De&6)!==0)throw Error(_(327));var l=!t&&(a&124)===0&&(a&e.expiredLanes)===0||ol(e,a),n=l?$h(e,a):Hr(e,a,!0),u=l;do{if(n===0){cu&&!l&&wl(e,a,0,!1);break}else{if(t=e.current.alternate,u&&!Vh(t)){n=Hr(e,a,!1),u=!1;continue}if(n===2){if(u=a,e.errorRecoveryDisabledLanes&u)var r=0;else r=e.pendingLanes&-536870913,r=r!==0?r:r&536870912?536870912:0;if(r!==0){a=r;e:{var o=e;n=di;var S=o.current.memoizedState.isDehydrated;if(S&&(du(o,r).flags|=256),r=Hr(o,r,!1),r!==2){if(br&&!S){o.errorRecoveryDisabledLanes|=u,Tn|=u,n=4;break e}u=ja,ja=n,u!==null&&(ja===null?ja=u:ja.push.apply(ja,u))}n=r}if(u=!1,n!==2)continue}}if(n===1){du(e,0),wl(e,a,0,!0);break}e:{switch(l=e,u=n,u){case 0:case 1:throw Error(_(345));case 4:if((a&4194048)!==a)break;case 6:wl(l,a,lt,!zl);break e;case 2:ja=null;break;case 3:case 5:break;default:throw Error(_(329))}if((a&62914560)===a&&(n=Rr+300-Oa(),10<n)){if(wl(l,a,lt,!zl),xa(l,0,!0)!==0)break e;l.timeoutHandle=wo(oo.bind(null,l,t,ja,Mc,Dr,a,lt,Tn,su,zl,u,2,-0,0),n);break e}oo(l,t,ja,Mc,Dr,a,lt,Tn,su,zl,u,0,-0,0)}}break}while(!0);xt(e)}function oo(e,a,t,l,n,u,r,o,S,C,H,x,R,O){if(e.timeoutHandle=-1,x=a.subtreeFlags,(x&8192||(x&16785408)===16785408)&&(Ai={stylesheets:null,count:0,unsuspend:Nm},no(a),x=Cm(),x!==null)){e.cancelPendingCommit=x(Ao.bind(null,e,a,u,t,l,n,r,o,S,H,1,R,O)),wl(e,u,r,!C);return}Ao(e,a,u,t,l,n,r,o,S)}function Vh(e){for(var a=e;;){var t=a.tag;if((t===0||t===11||t===15)&&a.flags&16384&&(t=a.updateQueue,t!==null&&(t=t.stores,t!==null)))for(var l=0;l<t.length;l++){var n=t[l],u=n.getSnapshot;n=n.value;try{if(!Aa(u(),n))return!1}catch{return!1}}if(t=a.child,a.subtreeFlags&16384&&t!==null)t.return=a,a=t;else{if(a===e)break;for(;a.sibling===null;){if(a.return===null||a.return===e)return!0;a=a.return}a.sibling.return=a.return,a=a.sibling}}return!0}function wl(e,a,t,l){a&=~Cr,a&=~Tn,e.suspendedLanes|=a,e.pingedLanes&=~a,l&&(e.warmLanes|=a),l=e.expirationTimes;for(var n=a;0<n;){var u=31-ra(n),r=1<<u;l[u]=-1,n&=~r}t!==0&&Gi(e,t,a)}function Bc(){return(De&6)===0?(hi(0),!1):!0}function Br(){if(de!==null){if(Re===0)var e=de.return;else e=de,It=gn=null,Ks(e),lu=null,li=0,e=de;for(;e!==null;)$d(e.alternate,e),e=e.return;de=null}}function du(e,a){var t=e.timeoutHandle;t!==-1&&(e.timeoutHandle=-1,im(t)),t=e.cancelPendingCommit,t!==null&&(e.cancelPendingCommit=null,t()),Br(),He=e,de=t=Wt(e.current,null),Se=a,Re=0,Xa=null,zl=!1,cu=ol(e,a),br=!1,su=lt=Cr=Tn=Ll=Ye=0,ja=di=null,Dr=!1,(a&8)!==0&&(a|=a&32);var l=e.entangledLanes;if(l!==0)for(e=e.entanglements,l&=a;0<l;){var n=31-ra(l),u=1<<n;a|=e[n],l&=~u}return cl=a,Ii(),t}function ho(e,a){se=null,G.H=Sc,a===Ku||a===sc?(a=_f(),Re=3):a===Df?(a=_f(),Re=4):Re=a===Md?8:a!==null&&typeof a=="object"&&typeof a.then=="function"?6:1,Xa=a,de===null&&(Ye=1,Nc(e,pa(a,e.current)))}function mo(){var e=G.H;return G.H=Sc,e===null?Sc:e}function yo(){var e=G.A;return G.A=wh,e}function Gr(){Ye=4,zl||(Se&4194048)!==Se&&tt.current!==null||(cu=!0),(Ll&134217727)===0&&(Tn&134217727)===0||He===null||wl(He,Se,lt,!1)}function Hr(e,a,t){var l=De;De|=2;var n=mo(),u=yo();(He!==e||Se!==a)&&(Mc=null,du(e,a)),a=!1;var r=Ye;e:do try{if(Re!==0&&de!==null){var o=de,S=Xa;switch(Re){case 8:Br(),r=6;break e;case 3:case 2:case 9:case 6:tt.current===null&&(a=!0);var C=Re;if(Re=0,Xa=null,ou(e,o,S,C),t&&cu){r=0;break e}break;default:C=Re,Re=0,Xa=null,ou(e,o,S,C)}}Xh(),r=Ye;break}catch(H){ho(e,H)}while(!0);return a&&e.shellSuspendCounter++,It=gn=null,De=l,G.H=n,G.A=u,de===null&&(He=null,Se=0,Ii()),r}function Xh(){for(;de!==null;)go(de)}function $h(e,a){var t=De;De|=2;var l=mo(),n=yo();He!==e||Se!==a?(Mc=null,_c=Oa()+500,du(e,a)):cu=ol(e,a);e:do try{if(Re!==0&&de!==null){a=de;var u=Xa;a:switch(Re){case 1:Re=0,Xa=null,ou(e,a,u,1);break;case 2:case 9:if(Rf(u)){Re=0,Xa=null,vo(a);break}a=function(){Re!==2&&Re!==9||He!==e||(Re=7),xt(e)},u.then(a,a);break e;case 3:Re=7;break e;case 4:Re=5;break e;case 7:Rf(u)?(Re=0,Xa=null,vo(a)):(Re=0,Xa=null,ou(e,a,u,7));break;case 5:var r=null;switch(de.tag){case 26:r=de.memoizedState;case 5:case 27:var o=de;if(!r||Po(r)){Re=0,Xa=null;var S=o.sibling;if(S!==null)de=S;else{var C=o.return;C!==null?(de=C,Gc(C)):de=null}break a}}Re=0,Xa=null,ou(e,a,u,5);break;case 6:Re=0,Xa=null,ou(e,a,u,6);break;case 8:Br(),Ye=6;break e;default:throw Error(_(462))}}Qh();break}catch(H){ho(e,H)}while(!0);return It=gn=null,G.H=l,G.A=n,De=t,de!==null?0:(He=null,Se=0,Ii(),Ye)}function Qh(){for(;de!==null&&!wt();)go(de)}function go(e){var a=Vd(e.alternate,e,cl);e.memoizedProps=e.pendingProps,a===null?Gc(e):de=a}function vo(e){var a=e,t=a.alternate;switch(a.tag){case 15:case 0:a=zd(t,a,a.pendingProps,a.type,void 0,Se);break;case 11:a=zd(t,a,a.pendingProps,a.type.render,a.ref,Se);break;case 5:Ks(a);default:$d(t,a),a=de=vf(a,cl),a=Vd(t,a,cl)}e.memoizedProps=e.pendingProps,a===null?Gc(e):de=a}function ou(e,a,t,l){It=gn=null,Ks(a),lu=null,li=0;var n=a.return;try{if(Uh(e,n,a,t,Se)){Ye=1,Nc(e,pa(t,e.current)),de=null;return}}catch(u){if(n!==null)throw de=n,u;Ye=1,Nc(e,pa(t,e.current)),de=null;return}a.flags&32768?(be||l===1?e=!0:cu||(Se&536870912)!==0?e=!1:(zl=e=!0,(l===2||l===9||l===3||l===6)&&(l=tt.current,l!==null&&l.tag===13&&(l.flags|=16384))),So(a,e)):Gc(a)}function Gc(e){var a=e;do{if((a.flags&32768)!==0){So(a,zl);return}e=a.return;var t=zh(a.alternate,a,cl);if(t!==null){de=t;return}if(a=a.sibling,a!==null){de=a;return}de=a=e}while(a!==null);Ye===0&&(Ye=5)}function So(e,a){do{var t=Lh(e.alternate,e);if(t!==null){t.flags&=32767,de=t;return}if(t=e.return,t!==null&&(t.flags|=32768,t.subtreeFlags=0,t.deletions=null),!a&&(e=e.sibling,e!==null)){de=e;return}de=e=t}while(e!==null);Ye=6,de=null}function Ao(e,a,t,l,n,u,r,o,S){e.cancelPendingCommit=null;do Hc();while(ma!==0);if((De&6)!==0)throw Error(_(327));if(a!==null){if(a===e.current)throw Error(_(177));if(u=a.lanes|a.childLanes,u|=Ns,On(e,t,u,r,o,S),e===He&&(de=He=null,Se=0),ru=a,ql=e,fu=t,Or=u,_r=n,so=l,(a.subtreeFlags&10256)!==0||(a.flags&10256)!==0?(e.callbackNode=null,e.callbackPriority=0,Jh(Kl,function(){return bo(),null})):(e.callbackNode=null,e.callbackPriority=0),l=(a.flags&13878)!==0,(a.subtreeFlags&13878)!==0||l){l=G.T,G.T=null,n=q.p,q.p=2,r=De,De|=4;try{xh(e,a,t)}finally{De=r,q.p=n,G.T=l}}ma=1,po(),Eo(),To()}}function po(){if(ma===1){ma=0;var e=ql,a=ru,t=(a.flags&13878)!==0;if((a.subtreeFlags&13878)!==0||t){t=G.T,G.T=null;var l=q.p;q.p=2;var n=De;De|=4;try{ao(a,e);var u=Qr,r=Yu(e.containerInfo),o=u.focusedElem,S=u.selectionRange;if(r!==o&&o&&o.ownerDocument&&wu(o.ownerDocument.documentElement,o)){if(S!==null&&Xn(o)){var C=S.start,H=S.end;if(H===void 0&&(H=C),"selectionStart"in o)o.selectionStart=C,o.selectionEnd=Math.min(H,o.value.length);else{var x=o.ownerDocument||document,R=x&&x.defaultView||window;if(R.getSelection){var O=R.getSelection(),ne=o.textContent.length,ae=Math.min(S.start,ne),Me=S.end===void 0?ae:Math.min(S.end,ne);!O.extend&&ae>Me&&(r=Me,Me=ae,ae=r);var N=qu(o,ae),p=qu(o,Me);if(N&&p&&(O.rangeCount!==1||O.anchorNode!==N.node||O.anchorOffset!==N.offset||O.focusNode!==p.node||O.focusOffset!==p.offset)){var b=x.createRange();b.setStart(N.node,N.offset),O.removeAllRanges(),ae>Me?(O.addRange(b),O.extend(p.node,p.offset)):(b.setEnd(p.node,p.offset),O.addRange(b))}}}}for(x=[],O=o;O=O.parentNode;)O.nodeType===1&&x.push({element:O,left:O.scrollLeft,top:O.scrollTop});for(typeof o.focus=="function"&&o.focus(),o=0;o<x.length;o++){var j=x[o];j.element.scrollLeft=j.left,j.element.scrollTop=j.top}}Qc=!!$r,Qr=$r=null}finally{De=n,q.p=l,G.T=t}}e.current=a,ma=2}}function Eo(){if(ma===2){ma=0;var e=ql,a=ru,t=(a.flags&8772)!==0;if((a.subtreeFlags&8772)!==0||t){t=G.T,G.T=null;var l=q.p;q.p=2;var n=De;De|=4;try{Fd(e,a.alternate,a)}finally{De=n,q.p=l,G.T=t}}ma=3}}function To(){if(ma===4||ma===3){ma=0,st();var e=ql,a=ru,t=fu,l=so;(a.subtreeFlags&10256)!==0||(a.flags&10256)!==0?ma=5:(ma=0,ru=ql=null,No(e,e.pendingLanes));var n=e.pendingLanes;if(n===0&&(xl=null),Vt(t),a=a.stateNode,va&&typeof va.onCommitFiberRoot=="function")try{va.onCommitFiberRoot(dl,a,void 0,(a.current.flags&128)===128)}catch{}if(l!==null){a=G.T,n=q.p,q.p=2,G.T=null;try{for(var u=e.onRecoverableError,r=0;r<l.length;r++){var o=l[r];u(o.value,{componentStack:o.stack})}}finally{G.T=a,q.p=n}}(fu&3)!==0&&Hc(),xt(e),n=e.pendingLanes,(t&4194090)!==0&&(n&42)!==0?e===Mr?oi++:(oi=0,Mr=e):oi=0,hi(0)}}function No(e,a){(e.pooledCacheLanes&=a)===0&&(a=e.pooledCache,a!=null&&(e.pooledCache=null,ku(a)))}function Hc(e){return po(),Eo(),To(),bo()}function bo(){if(ma!==5)return!1;var e=ql,a=Or;Or=0;var t=Vt(fu),l=G.T,n=q.p;try{q.p=32>t?32:t,G.T=null,t=_r,_r=null;var u=ql,r=fu;if(ma=0,ru=ql=null,fu=0,(De&6)!==0)throw Error(_(331));var o=De;if(De|=4,io(u.current),lo(u,u.current,r,t),De=o,hi(0,!1),va&&typeof va.onPostCommitFiberRoot=="function")try{va.onPostCommitFiberRoot(dl,u)}catch{}return!0}finally{q.p=n,G.T=l,No(e,a)}}function Co(e,a,t){a=pa(t,a),a=sr(e.stateNode,a,2),e=_l(e,a,2),e!==null&&(Wl(e,2),xt(e))}function Be(e,a,t){if(e.tag===3)Co(e,e,t);else for(;a!==null;){if(a.tag===3){Co(a,e,t);break}else if(a.tag===1){var l=a.stateNode;if(typeof a.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(xl===null||!xl.has(l))){e=pa(t,e),t=Od(2),l=_l(a,t,2),l!==null&&(_d(t,l,a,e),Wl(l,2),xt(l));break}}a=a.return}}function Ur(e,a,t){var l=e.pingCache;if(l===null){l=e.pingCache=new Yh;var n=new Set;l.set(a,n)}else n=l.get(a),n===void 0&&(n=new Set,l.set(a,n));n.has(t)||(br=!0,n.add(t),e=kh.bind(null,e,a,t),a.then(e,e))}function kh(e,a,t){var l=e.pingCache;l!==null&&l.delete(a),e.pingedLanes|=e.suspendedLanes&t,e.warmLanes&=~t,He===e&&(Se&t)===t&&(Ye===4||Ye===3&&(Se&62914560)===Se&&300>Oa()-Rr?(De&2)===0&&du(e,0):Cr|=t,su===Se&&(su=0)),xt(e)}function Do(e,a){a===0&&(a=pu()),e=Zn(e,a),e!==null&&(Wl(e,a),xt(e))}function Zh(e){var a=e.memoizedState,t=0;a!==null&&(t=a.retryLane),Do(e,t)}function Kh(e,a){var t=0;switch(e.tag){case 13:var l=e.stateNode,n=e.memoizedState;n!==null&&(t=n.retryLane);break;case 19:l=e.stateNode;break;case 22:l=e.stateNode._retryCache;break;default:throw Error(_(314))}l!==null&&l.delete(a),Do(e,t)}function Jh(e,a){return Zl(e,a)}var Uc=null,hu=null,jr=!1,jc=!1,zr=!1,Nn=0;function xt(e){e!==hu&&e.next===null&&(hu===null?Uc=hu=e:hu=hu.next=e),jc=!0,jr||(jr=!0,Fh())}function hi(e,a){if(!zr&&jc){zr=!0;do for(var t=!1,l=Uc;l!==null;){if(e!==0){var n=l.pendingLanes;if(n===0)var u=0;else{var r=l.suspendedLanes,o=l.pingedLanes;u=(1<<31-ra(42|e)+1)-1,u&=n&~(r&~o),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(t=!0,Mo(l,u))}else u=Se,u=xa(l,l===He?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||ol(l,u)||(t=!0,Mo(l,u));l=l.next}while(t);zr=!1}}function Wh(){Ro()}function Ro(){jc=jr=!1;var e=0;Nn!==0&&(um()&&(e=Nn),Nn=0);for(var a=Oa(),t=null,l=Uc;l!==null;){var n=l.next,u=Oo(l,a);u===0?(l.next=null,t===null?Uc=n:t.next=n,n===null&&(hu=t)):(t=l,(e!==0||(u&3)!==0)&&(jc=!0)),l=n}hi(e)}function Oo(e,a){for(var t=e.suspendedLanes,l=e.pingedLanes,n=e.expirationTimes,u=e.pendingLanes&-62914561;0<u;){var r=31-ra(u),o=1<<r,S=n[r];S===-1?((o&t)===0||(o&l)!==0)&&(n[r]=Bi(o,a)):S<=a&&(e.expiredLanes|=o),u&=~o}if(a=He,t=Se,t=xa(e,e===a?t:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l=e.callbackNode,t===0||e===a&&(Re===2||Re===9)||e.cancelPendingCommit!==null)return l!==null&&l!==null&&Oi(l),e.callbackNode=null,e.callbackPriority=0;if((t&3)===0||ol(e,t)){if(a=t&-t,a===e.callbackPriority)return a;switch(l!==null&&Oi(l),Vt(t)){case 2:case 8:t=_a;break;case 32:t=Kl;break;case 268435456:t=Cn;break;default:t=Kl}return l=_o.bind(null,e),t=Zl(t,l),e.callbackPriority=a,e.callbackNode=t,a}return l!==null&&l!==null&&Oi(l),e.callbackPriority=2,e.callbackNode=null,2}function _o(e,a){if(ma!==0&&ma!==5)return e.callbackNode=null,e.callbackPriority=0,null;var t=e.callbackNode;if(Hc()&&e.callbackNode!==t)return null;var l=Se;return l=xa(e,e===He?l:0,e.cancelPendingCommit!==null||e.timeoutHandle!==-1),l===0?null:(fo(e,l,a),Oo(e,Oa()),e.callbackNode!=null&&e.callbackNode===t?_o.bind(null,e):null)}function Mo(e,a){if(Hc())return null;fo(e,a,!0)}function Fh(){cm(function(){(De&6)!==0?Zl(bn,Wh):Ro()})}function Lr(){return Nn===0&&(Nn=Au()),Nn}function Bo(e){return e==null||typeof e=="symbol"||typeof e=="boolean"?null:typeof e=="function"?e:Qt(""+e)}function Go(e,a){var t=a.ownerDocument.createElement("input");return t.name=a.name,t.value=a.value,e.id&&t.setAttribute("form",e.id),a.parentNode.insertBefore(t,a),e=new FormData(e),t.parentNode.removeChild(t),e}function Ph(e,a,t,l,n){if(a==="submit"&&t&&t.stateNode===n){var u=Bo((n[ve]||null).action),r=l.submitter;r&&(a=(a=r[ve]||null)?Bo(a.formAction):r.getAttribute("formAction"),a!==null&&(u=a,r=null));var o=new Ma("action","action",null,l,n);e.push({event:o,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Nn!==0){var S=r?Go(n,r):new FormData(n);lr(t,{pending:!0,data:S,method:n.method,action:u},null,S)}}else typeof u=="function"&&(o.preventDefault(),S=r?Go(n,r):new FormData(n),lr(t,{pending:!0,data:S,method:n.method,action:u},u,S))},currentTarget:n}]})}}for(var xr=0;xr<Ue.length;xr++){var qr=Ue[xr],Ih=qr.toLowerCase(),em=qr[0].toUpperCase()+qr.slice(1);Ge(Ih,"on"+em)}Ge(y,"onAnimationEnd"),Ge(T,"onAnimationIteration"),Ge(D,"onAnimationStart"),Ge("dblclick","onDoubleClick"),Ge("focusin","onFocus"),Ge("focusout","onBlur"),Ge(L,"onTransitionRun"),Ge(Q,"onTransitionStart"),Ge(F,"onTransitionCancel"),Ge(J,"onTransitionEnd"),Ct("onMouseEnter",["mouseout","mouseover"]),Ct("onMouseLeave",["mouseout","mouseover"]),Ct("onPointerEnter",["pointerout","pointerover"]),Ct("onPointerLeave",["pointerout","pointerover"]),fa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),fa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),fa("onBeforeInput",["compositionend","keypress","textInput","paste"]),fa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),fa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),fa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var mi="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),am=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(mi));function Ho(e,a){a=(a&4)!==0;for(var t=0;t<e.length;t++){var l=e[t],n=l.event;l=l.listeners;e:{var u=void 0;if(a)for(var r=l.length-1;0<=r;r--){var o=l[r],S=o.instance,C=o.currentTarget;if(o=o.listener,S!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=C;try{u(n)}catch(H){Tc(H)}n.currentTarget=null,u=S}else for(r=0;r<l.length;r++){if(o=l[r],S=o.instance,C=o.currentTarget,o=o.listener,S!==u&&n.isPropagationStopped())break e;u=o,n.currentTarget=C;try{u(n)}catch(H){Tc(H)}n.currentTarget=null,u=S}}}}function oe(e,a){var t=a[la];t===void 0&&(t=a[la]=new Set);var l=e+"__bubble";t.has(l)||(Uo(a,e,2,!1),t.add(l))}function wr(e,a,t){var l=0;a&&(l|=4),Uo(t,e,l,a)}var zc="_reactListening"+Math.random().toString(36).slice(2);function Yr(e){if(!e[zc]){e[zc]=!0,Ui.forEach(function(t){t!=="selectionchange"&&(am.has(t)||wr(t,!1,e),wr(t,!0,e))});var a=e.nodeType===9?e:e.ownerDocument;a===null||a[zc]||(a[zc]=!0,wr("selectionchange",!1,a))}}function Uo(e,a,t,l){switch(nh(a)){case 2:var n=Om;break;case 8:n=_m;break;default:n=af}t=n.bind(null,a,t,e),n=void 0,!Pa||a!=="touchstart"&&a!=="touchmove"&&a!=="wheel"||(n=!0),l?n!==void 0?e.addEventListener(a,t,{capture:!0,passive:n}):e.addEventListener(a,t,!0):n!==void 0?e.addEventListener(a,t,{passive:n}):e.addEventListener(a,t,!1)}function Vr(e,a,t,l,n){var u=l;if((a&1)===0&&(a&2)===0&&l!==null)e:for(;;){if(l===null)return;var r=l.tag;if(r===3||r===4){var o=l.stateNode.containerInfo;if(o===n)break;if(r===4)for(r=l.return;r!==null;){var S=r.tag;if((S===3||S===4)&&r.stateNode.containerInfo===n)return;r=r.return}for(;o!==null;){if(r=ot(o),r===null)return;if(S=r.tag,S===5||S===6||S===26||S===27){l=u=r;continue e}o=o.parentNode}}l=l.return}zi(function(){var C=u,H=tn(t),x=[];e:{var R=ue.get(e);if(R!==void 0){var O=Ma,ne=e;switch(e){case"keypress":if(St(t)===0)break e;case"keydown":case"keyup":O=rs;break;case"focusin":ne="focus",O=Ou;break;case"focusout":ne="blur",O=Ou;break;case"beforeblur":case"afterblur":O=Ou;break;case"click":if(t.button===2)break e;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":O=Zt;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":O=ts;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":O=wi;break;case y:case T:case D:O=xi;break;case J:O=os;break;case"scroll":case"scrollend":O=pl;break;case"wheel":O=hs;break;case"copy":case"cut":case"paste":O=cn;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":O=Mu;break;case"toggle":case"beforetoggle":O=ys}var ae=(a&4)!==0,Me=!ae&&(e==="scroll"||e==="scrollend"),N=ae?R!==null?R+"Capture":null:R;ae=[];for(var p=C,b;p!==null;){var j=p;if(b=j.stateNode,j=j.tag,j!==5&&j!==26&&j!==27||b===null||N===null||(j=ln(p,N),j!=null&&ae.push(yi(p,j,b))),Me)break;p=p.return}0<ae.length&&(R=new O(R,ne,null,t,H),x.push({event:R,listeners:ae}))}}if((a&7)===0){e:{if(R=e==="mouseover"||e==="pointerover",O=e==="mouseout"||e==="pointerout",R&&t!==da&&(ne=t.relatedTarget||t.fromElement)&&(ot(ne)||ne[Ja]))break e;if((O||R)&&(R=H.window===H?H:(R=H.ownerDocument)?R.defaultView||R.parentWindow:window,O?(ne=t.relatedTarget||t.toElement,O=C,ne=ne?ot(ne):null,ne!==null&&(Me=V(ne),ae=ne.tag,ne!==Me||ae!==5&&ae!==27&&ae!==6)&&(ne=null)):(O=null,ne=C),O!==ne)){if(ae=Zt,j="onMouseLeave",N="onMouseEnter",p="mouse",(e==="pointerout"||e==="pointerover")&&(ae=Mu,j="onPointerLeave",N="onPointerEnter",p="pointer"),Me=O==null?R:Xt(O),b=ne==null?R:Xt(ne),R=new ae(j,p+"leave",O,t,H),R.target=Me,R.relatedTarget=b,j=null,ot(H)===C&&(ae=new ae(N,p+"enter",ne,t,H),ae.target=b,ae.relatedTarget=Me,j=ae),Me=j,O&&ne)a:{for(ae=O,N=ne,p=0,b=ae;b;b=mu(b))p++;for(b=0,j=N;j;j=mu(j))b++;for(;0<p-b;)ae=mu(ae),p--;for(;0<b-p;)N=mu(N),b--;for(;p--;){if(ae===N||N!==null&&ae===N.alternate)break a;ae=mu(ae),N=mu(N)}ae=null}else ae=null;O!==null&&jo(x,R,O,ae,!1),ne!==null&&Me!==null&&jo(x,Me,ne,ae,!0)}}e:{if(R=C?Xt(C):window,O=R.nodeName&&R.nodeName.toLowerCase(),O==="select"||O==="input"&&R.type==="file")var W=Uu;else if(Nl(R))if(ju)W=Ga;else{W=Ts;var re=Es}else O=R.nodeName,!O||O.toLowerCase()!=="input"||R.type!=="checkbox"&&R.type!=="radio"?C&&yt(C.elementType)&&(W=Uu):W=Wi;if(W&&(W=W(e,C))){Hu(x,W,t,H);break e}re&&re(e,R,C),e==="focusout"&&C&&R.type==="number"&&C.memoizedProps.value!=null&&Tu(R,"number",R.value)}switch(re=C?Xt(C):window,e){case"focusin":(Nl(re)||re.contentEditable==="true")&&(Jt=re,$n=C,Cl=null);break;case"focusout":Cl=$n=Jt=null;break;case"mousedown":Qn=!0;break;case"contextmenu":case"mouseup":case"dragend":Qn=!1,i(x,t,H);break;case"selectionchange":if(Pi)break;case"keydown":case"keyup":i(x,t,H)}var P;if(Vn)e:{switch(e){case"compositionstart":var te="onCompositionStart";break e;case"compositionend":te="onCompositionEnd";break e;case"compositionupdate":te="onCompositionUpdate";break e}te=void 0}else Tl?$i(e,t)&&(te="onCompositionEnd"):e==="keydown"&&t.keyCode===229&&(te="onCompositionStart");te&&(Yi&&t.locale!=="ko"&&(Tl||te!=="onCompositionStart"?te==="onCompositionEnd"&&Tl&&(P=Bt()):(_t=H,Du="value"in _t?_t.value:_t.textContent,Tl=!0)),re=Lc(C,te),0<re.length&&(te=new Kt(te,e,null,t,H),x.push({event:te,listeners:re}),P?te.data=P:(P=Qi(t),P!==null&&(te.data=P)))),(P=vs?Ss(e,t):As(e,t))&&(te=Lc(C,"onBeforeInput"),0<te.length&&(re=new Kt("onBeforeInput","beforeinput",null,t,H),x.push({event:re,listeners:te}),re.data=P)),Ph(x,e,C,t,H)}Ho(x,a)})}function yi(e,a,t){return{instance:e,listener:a,currentTarget:t}}function Lc(e,a){for(var t=a+"Capture",l=[];e!==null;){var n=e,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=ln(e,t),n!=null&&l.unshift(yi(e,n,u)),n=ln(e,a),n!=null&&l.push(yi(e,n,u))),e.tag===3)return l;e=e.return}return[]}function mu(e){if(e===null)return null;do e=e.return;while(e&&e.tag!==5&&e.tag!==27);return e||null}function jo(e,a,t,l,n){for(var u=a._reactName,r=[];t!==null&&t!==l;){var o=t,S=o.alternate,C=o.stateNode;if(o=o.tag,S!==null&&S===l)break;o!==5&&o!==26&&o!==27||C===null||(S=C,n?(C=ln(t,u),C!=null&&r.unshift(yi(t,C,S))):n||(C=ln(t,u),C!=null&&r.push(yi(t,C,S)))),t=t.return}r.length!==0&&e.push({event:a,listeners:r})}var tm=/\r\n?/g,lm=/\u0000|\uFFFD/g;function zo(e){return(typeof e=="string"?e:""+e).replace(tm,`
`).replace(lm,"")}function Lo(e,a){return a=zo(a),zo(e)===a}function xc(){}function _e(e,a,t,l,n,u){switch(t){case"children":typeof l=="string"?a==="body"||a==="textarea"&&l===""||Al(e,l):(typeof l=="number"||typeof l=="bigint")&&a!=="body"&&Al(e,""+l);break;case"className":Il(e,"class",l);break;case"tabIndex":Il(e,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":Il(e,t,l);break;case"style":xn(e,l,u);break;case"data":if(a!=="object"){Il(e,"data",l);break}case"src":case"href":if(l===""&&(a!=="a"||t!=="href")){e.removeAttribute(t);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(t);break}l=Qt(""+l),e.setAttribute(t,l);break;case"action":case"formAction":if(typeof l=="function"){e.setAttribute(t,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(t==="formAction"?(a!=="input"&&_e(e,a,"name",n.name,n,null),_e(e,a,"formEncType",n.formEncType,n,null),_e(e,a,"formMethod",n.formMethod,n,null),_e(e,a,"formTarget",n.formTarget,n,null)):(_e(e,a,"encType",n.encType,n,null),_e(e,a,"method",n.method,n,null),_e(e,a,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){e.removeAttribute(t);break}l=Qt(""+l),e.setAttribute(t,l);break;case"onClick":l!=null&&(e.onclick=xc);break;case"onScroll":l!=null&&oe("scroll",e);break;case"onScrollEnd":l!=null&&oe("scrollend",e);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(_(61));if(t=l.__html,t!=null){if(n.children!=null)throw Error(_(60));e.innerHTML=t}}break;case"multiple":e.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":e.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){e.removeAttribute("xlink:href");break}t=Qt(""+l),e.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",t);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(t,""+l):e.removeAttribute(t);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(t,""):e.removeAttribute(t);break;case"capture":case"download":l===!0?e.setAttribute(t,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?e.setAttribute(t,l):e.removeAttribute(t);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?e.setAttribute(t,l):e.removeAttribute(t);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?e.removeAttribute(t):e.setAttribute(t,l);break;case"popover":oe("beforetoggle",e),oe("toggle",e),Bn(e,"popover",l);break;case"xlinkActuate":mt(e,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":mt(e,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":mt(e,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":mt(e,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":mt(e,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":mt(e,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":mt(e,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":mt(e,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":mt(e,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":Bn(e,"is",l);break;case"innerText":case"textContent":break;default:(!(2<t.length)||t[0]!=="o"&&t[0]!=="O"||t[1]!=="n"&&t[1]!=="N")&&(t=bu.get(t)||t,Bn(e,t,l))}}function Xr(e,a,t,l,n,u){switch(t){case"style":xn(e,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(_(61));if(t=l.__html,t!=null){if(n.children!=null)throw Error(_(60));e.innerHTML=t}}break;case"children":typeof l=="string"?Al(e,l):(typeof l=="number"||typeof l=="bigint")&&Al(e,""+l);break;case"onScroll":l!=null&&oe("scroll",e);break;case"onScrollEnd":l!=null&&oe("scrollend",e);break;case"onClick":l!=null&&(e.onclick=xc);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!_n.hasOwnProperty(t))e:{if(t[0]==="o"&&t[1]==="n"&&(n=t.endsWith("Capture"),a=t.slice(2,n?t.length-7:void 0),u=e[ve]||null,u=u!=null?u[t]:null,typeof u=="function"&&e.removeEventListener(a,u,n),typeof l=="function")){typeof u!="function"&&u!==null&&(t in e?e[t]=null:e.hasAttribute(t)&&e.removeAttribute(t)),e.addEventListener(a,l,n);break e}t in e?e[t]=l:l===!0?e.setAttribute(t,""):Bn(e,t,l)}}}function ya(e,a,t){switch(a){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":oe("error",e),oe("load",e);var l=!1,n=!1,u;for(u in t)if(t.hasOwnProperty(u)){var r=t[u];if(r!=null)switch(u){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(_(137,a));default:_e(e,a,u,r,t,null)}}n&&_e(e,a,"srcSet",t.srcSet,t,null),l&&_e(e,a,"src",t.src,t,null);return;case"input":oe("invalid",e);var o=u=r=n=null,S=null,C=null;for(l in t)if(t.hasOwnProperty(l)){var H=t[l];if(H!=null)switch(l){case"name":n=H;break;case"type":r=H;break;case"checked":S=H;break;case"defaultChecked":C=H;break;case"value":u=H;break;case"defaultValue":o=H;break;case"children":case"dangerouslySetInnerHTML":if(H!=null)throw Error(_(137,a));break;default:_e(e,a,l,H,t,null)}}zn(e,u,o,S,C,r,n,!1),gl(e);return;case"select":oe("invalid",e),l=r=u=null;for(n in t)if(t.hasOwnProperty(n)&&(o=t[n],o!=null))switch(n){case"value":u=o;break;case"defaultValue":r=o;break;case"multiple":l=o;default:_e(e,a,n,o,t,null)}a=u,t=r,e.multiple=!!l,a!=null?Ot(e,!!l,a,!1):t!=null&&Ot(e,!!l,t,!0);return;case"textarea":oe("invalid",e),u=n=l=null;for(r in t)if(t.hasOwnProperty(r)&&(o=t[r],o!=null))switch(r){case"value":l=o;break;case"defaultValue":n=o;break;case"children":u=o;break;case"dangerouslySetInnerHTML":if(o!=null)throw Error(_(91));break;default:_e(e,a,r,o,t,null)}Ln(e,l,n,u),gl(e);return;case"option":for(S in t)if(t.hasOwnProperty(S)&&(l=t[S],l!=null))switch(S){case"selected":e.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:_e(e,a,S,l,t,null)}return;case"dialog":oe("beforetoggle",e),oe("toggle",e),oe("cancel",e),oe("close",e);break;case"iframe":case"object":oe("load",e);break;case"video":case"audio":for(l=0;l<mi.length;l++)oe(mi[l],e);break;case"image":oe("error",e),oe("load",e);break;case"details":oe("toggle",e);break;case"embed":case"source":case"link":oe("error",e),oe("load",e);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(C in t)if(t.hasOwnProperty(C)&&(l=t[C],l!=null))switch(C){case"children":case"dangerouslySetInnerHTML":throw Error(_(137,a));default:_e(e,a,C,l,t,null)}return;default:if(yt(a)){for(H in t)t.hasOwnProperty(H)&&(l=t[H],l!==void 0&&Xr(e,a,H,l,t,void 0));return}}for(o in t)t.hasOwnProperty(o)&&(l=t[o],l!=null&&_e(e,a,o,l,t,null))}function nm(e,a,t,l){switch(a){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,r=null,o=null,S=null,C=null,H=null;for(O in t){var x=t[O];if(t.hasOwnProperty(O)&&x!=null)switch(O){case"checked":break;case"value":break;case"defaultValue":S=x;default:l.hasOwnProperty(O)||_e(e,a,O,null,l,x)}}for(var R in l){var O=l[R];if(x=t[R],l.hasOwnProperty(R)&&(O!=null||x!=null))switch(R){case"type":u=O;break;case"name":n=O;break;case"checked":C=O;break;case"defaultChecked":H=O;break;case"value":r=O;break;case"defaultValue":o=O;break;case"children":case"dangerouslySetInnerHTML":if(O!=null)throw Error(_(137,a));break;default:O!==x&&_e(e,a,R,O,l,x)}}Sl(e,r,o,S,C,H,u,n);return;case"select":O=r=o=R=null;for(u in t)if(S=t[u],t.hasOwnProperty(u)&&S!=null)switch(u){case"value":break;case"multiple":O=S;default:l.hasOwnProperty(u)||_e(e,a,u,null,l,S)}for(n in l)if(u=l[n],S=t[n],l.hasOwnProperty(n)&&(u!=null||S!=null))switch(n){case"value":R=u;break;case"defaultValue":o=u;break;case"multiple":r=u;default:u!==S&&_e(e,a,n,u,l,S)}a=o,t=r,l=O,R!=null?Ot(e,!!t,R,!1):!!l!=!!t&&(a!=null?Ot(e,!!t,a,!0):Ot(e,!!t,t?[]:"",!1));return;case"textarea":O=R=null;for(o in t)if(n=t[o],t.hasOwnProperty(o)&&n!=null&&!l.hasOwnProperty(o))switch(o){case"value":break;case"children":break;default:_e(e,a,o,null,l,n)}for(r in l)if(n=l[r],u=t[r],l.hasOwnProperty(r)&&(n!=null||u!=null))switch(r){case"value":R=n;break;case"defaultValue":O=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(_(91));break;default:n!==u&&_e(e,a,r,n,l,u)}ji(e,R,O);return;case"option":for(var ne in t)if(R=t[ne],t.hasOwnProperty(ne)&&R!=null&&!l.hasOwnProperty(ne))switch(ne){case"selected":e.selected=!1;break;default:_e(e,a,ne,null,l,R)}for(S in l)if(R=l[S],O=t[S],l.hasOwnProperty(S)&&R!==O&&(R!=null||O!=null))switch(S){case"selected":e.selected=R&&typeof R!="function"&&typeof R!="symbol";break;default:_e(e,a,S,R,l,O)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var ae in t)R=t[ae],t.hasOwnProperty(ae)&&R!=null&&!l.hasOwnProperty(ae)&&_e(e,a,ae,null,l,R);for(C in l)if(R=l[C],O=t[C],l.hasOwnProperty(C)&&R!==O&&(R!=null||O!=null))switch(C){case"children":case"dangerouslySetInnerHTML":if(R!=null)throw Error(_(137,a));break;default:_e(e,a,C,R,l,O)}return;default:if(yt(a)){for(var Me in t)R=t[Me],t.hasOwnProperty(Me)&&R!==void 0&&!l.hasOwnProperty(Me)&&Xr(e,a,Me,void 0,l,R);for(H in l)R=l[H],O=t[H],!l.hasOwnProperty(H)||R===O||R===void 0&&O===void 0||Xr(e,a,H,R,l,O);return}}for(var N in t)R=t[N],t.hasOwnProperty(N)&&R!=null&&!l.hasOwnProperty(N)&&_e(e,a,N,null,l,R);for(x in l)R=l[x],O=t[x],!l.hasOwnProperty(x)||R===O||R==null&&O==null||_e(e,a,x,R,l,O)}var $r=null,Qr=null;function qc(e){return e.nodeType===9?e:e.ownerDocument}function xo(e){switch(e){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function qo(e,a){if(e===0)switch(a){case"svg":return 1;case"math":return 2;default:return 0}return e===1&&a==="foreignObject"?0:e}function kr(e,a){return e==="textarea"||e==="noscript"||typeof a.children=="string"||typeof a.children=="number"||typeof a.children=="bigint"||typeof a.dangerouslySetInnerHTML=="object"&&a.dangerouslySetInnerHTML!==null&&a.dangerouslySetInnerHTML.__html!=null}var Zr=null;function um(){var e=window.event;return e&&e.type==="popstate"?e===Zr?!1:(Zr=e,!0):(Zr=null,!1)}var wo=typeof setTimeout=="function"?setTimeout:void 0,im=typeof clearTimeout=="function"?clearTimeout:void 0,Yo=typeof Promise=="function"?Promise:void 0,cm=typeof queueMicrotask=="function"?queueMicrotask:typeof Yo<"u"?function(e){return Yo.resolve(null).then(e).catch(sm)}:wo;function sm(e){setTimeout(function(){throw e})}function Yl(e){return e==="head"}function Vo(e,a){var t=a,l=0,n=0;do{var u=t.nextSibling;if(e.removeChild(t),u&&u.nodeType===8)if(t=u.data,t==="/$"){if(0<l&&8>l){t=l;var r=e.ownerDocument;if(t&1&&gi(r.documentElement),t&2&&gi(r.body),t&4)for(t=r.head,gi(t),r=t.firstChild;r;){var o=r.nextSibling,S=r.nodeName;r[dt]||S==="SCRIPT"||S==="STYLE"||S==="LINK"&&r.rel.toLowerCase()==="stylesheet"||t.removeChild(r),r=o}}if(n===0){e.removeChild(u),bi(a);return}n--}else t==="$"||t==="$?"||t==="$!"?n++:l=t.charCodeAt(0)-48;else l=0;t=u}while(t);bi(a)}function Kr(e){var a=e.firstChild;for(a&&a.nodeType===10&&(a=a.nextSibling);a;){var t=a;switch(a=a.nextSibling,t.nodeName){case"HTML":case"HEAD":case"BODY":Kr(t),hl(t);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(t.rel.toLowerCase()==="stylesheet")continue}e.removeChild(t)}}function rm(e,a,t,l){for(;e.nodeType===1;){var n=t;if(e.nodeName.toLowerCase()!==a.toLowerCase()){if(!l&&(e.nodeName!=="INPUT"||e.type!=="hidden"))break}else if(l){if(!e[dt])switch(a){case"meta":if(!e.hasAttribute("itemprop"))break;return e;case"link":if(u=e.getAttribute("rel"),u==="stylesheet"&&e.hasAttribute("data-precedence"))break;if(u!==n.rel||e.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||e.getAttribute("title")!==(n.title==null?null:n.title))break;return e;case"style":if(e.hasAttribute("data-precedence"))break;return e;case"script":if(u=e.getAttribute("src"),(u!==(n.src==null?null:n.src)||e.getAttribute("type")!==(n.type==null?null:n.type)||e.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&e.hasAttribute("async")&&!e.hasAttribute("itemprop"))break;return e;default:return e}}else if(a==="input"&&e.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&e.getAttribute("name")===u)return e}else return e;if(e=Et(e.nextSibling),e===null)break}return null}function fm(e,a,t){if(a==="")return null;for(;e.nodeType!==3;)if((e.nodeType!==1||e.nodeName!=="INPUT"||e.type!=="hidden")&&!t||(e=Et(e.nextSibling),e===null))return null;return e}function Jr(e){return e.data==="$!"||e.data==="$?"&&e.ownerDocument.readyState==="complete"}function dm(e,a){var t=e.ownerDocument;if(e.data!=="$?"||t.readyState==="complete")a();else{var l=function(){a(),t.removeEventListener("DOMContentLoaded",l)};t.addEventListener("DOMContentLoaded",l),e._reactRetry=l}}function Et(e){for(;e!=null;e=e.nextSibling){var a=e.nodeType;if(a===1||a===3)break;if(a===8){if(a=e.data,a==="$"||a==="$!"||a==="$?"||a==="F!"||a==="F")break;if(a==="/$")return null}}return e}var Wr=null;function Xo(e){e=e.previousSibling;for(var a=0;e;){if(e.nodeType===8){var t=e.data;if(t==="$"||t==="$!"||t==="$?"){if(a===0)return e;a--}else t==="/$"&&a++}e=e.previousSibling}return null}function $o(e,a,t){switch(a=qc(t),e){case"html":if(e=a.documentElement,!e)throw Error(_(452));return e;case"head":if(e=a.head,!e)throw Error(_(453));return e;case"body":if(e=a.body,!e)throw Error(_(454));return e;default:throw Error(_(451))}}function gi(e){for(var a=e.attributes;a.length;)e.removeAttributeNode(a[0]);hl(e)}var nt=new Map,Qo=new Set;function wc(e){return typeof e.getRootNode=="function"?e.getRootNode():e.nodeType===9?e:e.ownerDocument}var sl=q.d;q.d={f:om,r:hm,D:mm,C:ym,L:gm,m:vm,X:Am,S:Sm,M:pm};function om(){var e=sl.f(),a=Bc();return e||a}function hm(e){var a=ml(e);a!==null&&a.tag===5&&a.type==="form"?fd(a):sl.r(e)}var yu=typeof document>"u"?null:document;function ko(e,a,t){var l=yu;if(l&&typeof a=="string"&&a){var n=Sa(a);n='link[rel="'+e+'"][href="'+n+'"]',typeof t=="string"&&(n+='[crossorigin="'+t+'"]'),Qo.has(n)||(Qo.add(n),e={rel:e,crossOrigin:t,href:a},l.querySelector(n)===null&&(a=l.createElement("link"),ya(a,"link",e),qe(a),l.head.appendChild(a)))}}function mm(e){sl.D(e),ko("dns-prefetch",e,null)}function ym(e,a){sl.C(e,a),ko("preconnect",e,a)}function gm(e,a,t){sl.L(e,a,t);var l=yu;if(l&&e&&a){var n='link[rel="preload"][as="'+Sa(a)+'"]';a==="image"&&t&&t.imageSrcSet?(n+='[imagesrcset="'+Sa(t.imageSrcSet)+'"]',typeof t.imageSizes=="string"&&(n+='[imagesizes="'+Sa(t.imageSizes)+'"]')):n+='[href="'+Sa(e)+'"]';var u=n;switch(a){case"style":u=gu(e);break;case"script":u=vu(e)}nt.has(u)||(e=U({rel:"preload",href:a==="image"&&t&&t.imageSrcSet?void 0:e,as:a},t),nt.set(u,e),l.querySelector(n)!==null||a==="style"&&l.querySelector(vi(u))||a==="script"&&l.querySelector(Si(u))||(a=l.createElement("link"),ya(a,"link",e),qe(a),l.head.appendChild(a)))}}function vm(e,a){sl.m(e,a);var t=yu;if(t&&e){var l=a&&typeof a.as=="string"?a.as:"script",n='link[rel="modulepreload"][as="'+Sa(l)+'"][href="'+Sa(e)+'"]',u=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=vu(e)}if(!nt.has(u)&&(e=U({rel:"modulepreload",href:e},a),nt.set(u,e),t.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(t.querySelector(Si(u)))return}l=t.createElement("link"),ya(l,"link",e),qe(l),t.head.appendChild(l)}}}function Sm(e,a,t){sl.S(e,a,t);var l=yu;if(l&&e){var n=ht(l).hoistableStyles,u=gu(e);a=a||"default";var r=n.get(u);if(!r){var o={loading:0,preload:null};if(r=l.querySelector(vi(u)))o.loading=5;else{e=U({rel:"stylesheet",href:e,"data-precedence":a},t),(t=nt.get(u))&&Fr(e,t);var S=r=l.createElement("link");qe(S),ya(S,"link",e),S._p=new Promise(function(C,H){S.onload=C,S.onerror=H}),S.addEventListener("load",function(){o.loading|=1}),S.addEventListener("error",function(){o.loading|=2}),o.loading|=4,Yc(r,a,l)}r={type:"stylesheet",instance:r,count:1,state:o},n.set(u,r)}}}function Am(e,a){sl.X(e,a);var t=yu;if(t&&e){var l=ht(t).hoistableScripts,n=vu(e),u=l.get(n);u||(u=t.querySelector(Si(n)),u||(e=U({src:e,async:!0},a),(a=nt.get(n))&&Pr(e,a),u=t.createElement("script"),qe(u),ya(u,"link",e),t.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function pm(e,a){sl.M(e,a);var t=yu;if(t&&e){var l=ht(t).hoistableScripts,n=vu(e),u=l.get(n);u||(u=t.querySelector(Si(n)),u||(e=U({src:e,async:!0,type:"module"},a),(a=nt.get(n))&&Pr(e,a),u=t.createElement("script"),qe(u),ya(u,"link",e),t.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function Zo(e,a,t,l){var n=(n=ie.current)?wc(n):null;if(!n)throw Error(_(446));switch(e){case"meta":case"title":return null;case"style":return typeof t.precedence=="string"&&typeof t.href=="string"?(a=gu(t.href),t=ht(n).hoistableStyles,l=t.get(a),l||(l={type:"style",instance:null,count:0,state:null},t.set(a,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(t.rel==="stylesheet"&&typeof t.href=="string"&&typeof t.precedence=="string"){e=gu(t.href);var u=ht(n).hoistableStyles,r=u.get(e);if(r||(n=n.ownerDocument||n,r={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(e,r),(u=n.querySelector(vi(e)))&&!u._p&&(r.instance=u,r.state.loading=5),nt.has(e)||(t={rel:"preload",as:"style",href:t.href,crossOrigin:t.crossOrigin,integrity:t.integrity,media:t.media,hrefLang:t.hrefLang,referrerPolicy:t.referrerPolicy},nt.set(e,t),u||Em(n,e,t,r.state))),a&&l===null)throw Error(_(528,""));return r}if(a&&l!==null)throw Error(_(529,""));return null;case"script":return a=t.async,t=t.src,typeof t=="string"&&a&&typeof a!="function"&&typeof a!="symbol"?(a=vu(t),t=ht(n).hoistableScripts,l=t.get(a),l||(l={type:"script",instance:null,count:0,state:null},t.set(a,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(_(444,e))}}function gu(e){return'href="'+Sa(e)+'"'}function vi(e){return'link[rel="stylesheet"]['+e+"]"}function Ko(e){return U({},e,{"data-precedence":e.precedence,precedence:null})}function Em(e,a,t,l){e.querySelector('link[rel="preload"][as="style"]['+a+"]")?l.loading=1:(a=e.createElement("link"),l.preload=a,a.addEventListener("load",function(){return l.loading|=1}),a.addEventListener("error",function(){return l.loading|=2}),ya(a,"link",t),qe(a),e.head.appendChild(a))}function vu(e){return'[src="'+Sa(e)+'"]'}function Si(e){return"script[async]"+e}function Jo(e,a,t){if(a.count++,a.instance===null)switch(a.type){case"style":var l=e.querySelector('style[data-href~="'+Sa(t.href)+'"]');if(l)return a.instance=l,qe(l),l;var n=U({},t,{"data-href":t.href,"data-precedence":t.precedence,href:null,precedence:null});return l=(e.ownerDocument||e).createElement("style"),qe(l),ya(l,"style",n),Yc(l,t.precedence,e),a.instance=l;case"stylesheet":n=gu(t.href);var u=e.querySelector(vi(n));if(u)return a.state.loading|=4,a.instance=u,qe(u),u;l=Ko(t),(n=nt.get(n))&&Fr(l,n),u=(e.ownerDocument||e).createElement("link"),qe(u);var r=u;return r._p=new Promise(function(o,S){r.onload=o,r.onerror=S}),ya(u,"link",l),a.state.loading|=4,Yc(u,t.precedence,e),a.instance=u;case"script":return u=vu(t.src),(n=e.querySelector(Si(u)))?(a.instance=n,qe(n),n):(l=t,(n=nt.get(u))&&(l=U({},t),Pr(l,n)),e=e.ownerDocument||e,n=e.createElement("script"),qe(n),ya(n,"link",l),e.head.appendChild(n),a.instance=n);case"void":return null;default:throw Error(_(443,a.type))}else a.type==="stylesheet"&&(a.state.loading&4)===0&&(l=a.instance,a.state.loading|=4,Yc(l,t.precedence,e));return a.instance}function Yc(e,a,t){for(var l=t.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,u=n,r=0;r<l.length;r++){var o=l[r];if(o.dataset.precedence===a)u=o;else if(u!==n)break}u?u.parentNode.insertBefore(e,u.nextSibling):(a=t.nodeType===9?t.head:t,a.insertBefore(e,a.firstChild))}function Fr(e,a){e.crossOrigin==null&&(e.crossOrigin=a.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=a.referrerPolicy),e.title==null&&(e.title=a.title)}function Pr(e,a){e.crossOrigin==null&&(e.crossOrigin=a.crossOrigin),e.referrerPolicy==null&&(e.referrerPolicy=a.referrerPolicy),e.integrity==null&&(e.integrity=a.integrity)}var Vc=null;function Wo(e,a,t){if(Vc===null){var l=new Map,n=Vc=new Map;n.set(t,l)}else n=Vc,l=n.get(t),l||(l=new Map,n.set(t,l));if(l.has(e))return l;for(l.set(e,null),t=t.getElementsByTagName(e),n=0;n<t.length;n++){var u=t[n];if(!(u[dt]||u[Ve]||e==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var r=u.getAttribute(a)||"";r=e+r;var o=l.get(r);o?o.push(u):l.set(r,[u])}}return l}function Fo(e,a,t){e=e.ownerDocument||e,e.head.insertBefore(t,a==="title"?e.querySelector("head > title"):null)}function Tm(e,a,t){if(t===1||a.itemProp!=null)return!1;switch(e){case"meta":case"title":return!0;case"style":if(typeof a.precedence!="string"||typeof a.href!="string"||a.href==="")break;return!0;case"link":if(typeof a.rel!="string"||typeof a.href!="string"||a.href===""||a.onLoad||a.onError)break;switch(a.rel){case"stylesheet":return e=a.disabled,typeof a.precedence=="string"&&e==null;default:return!0}case"script":if(a.async&&typeof a.async!="function"&&typeof a.async!="symbol"&&!a.onLoad&&!a.onError&&a.src&&typeof a.src=="string")return!0}return!1}function Po(e){return!(e.type==="stylesheet"&&(e.state.loading&3)===0)}var Ai=null;function Nm(){}function bm(e,a,t){if(Ai===null)throw Error(_(475));var l=Ai;if(a.type==="stylesheet"&&(typeof t.media!="string"||matchMedia(t.media).matches!==!1)&&(a.state.loading&4)===0){if(a.instance===null){var n=gu(t.href),u=e.querySelector(vi(n));if(u){e=u._p,e!==null&&typeof e=="object"&&typeof e.then=="function"&&(l.count++,l=Xc.bind(l),e.then(l,l)),a.state.loading|=4,a.instance=u,qe(u);return}u=e.ownerDocument||e,t=Ko(t),(n=nt.get(n))&&Fr(t,n),u=u.createElement("link"),qe(u);var r=u;r._p=new Promise(function(o,S){r.onload=o,r.onerror=S}),ya(u,"link",t),a.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(a,e),(e=a.state.preload)&&(a.state.loading&3)===0&&(l.count++,a=Xc.bind(l),e.addEventListener("load",a),e.addEventListener("error",a))}}function Cm(){if(Ai===null)throw Error(_(475));var e=Ai;return e.stylesheets&&e.count===0&&Ir(e,e.stylesheets),0<e.count?function(a){var t=setTimeout(function(){if(e.stylesheets&&Ir(e,e.stylesheets),e.unsuspend){var l=e.unsuspend;e.unsuspend=null,l()}},6e4);return e.unsuspend=a,function(){e.unsuspend=null,clearTimeout(t)}}:null}function Xc(){if(this.count--,this.count===0){if(this.stylesheets)Ir(this,this.stylesheets);else if(this.unsuspend){var e=this.unsuspend;this.unsuspend=null,e()}}}var $c=null;function Ir(e,a){e.stylesheets=null,e.unsuspend!==null&&(e.count++,$c=new Map,a.forEach(Dm,e),$c=null,Xc.call(e))}function Dm(e,a){if(!(a.state.loading&4)){var t=$c.get(e);if(t)var l=t.get(null);else{t=new Map,$c.set(e,t);for(var n=e.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var r=n[u];(r.nodeName==="LINK"||r.getAttribute("media")!=="not all")&&(t.set(r.dataset.precedence,r),l=r)}l&&t.set(null,l)}n=a.instance,r=n.getAttribute("data-precedence"),u=t.get(r)||l,u===l&&t.set(null,n),t.set(r,n),this.count++,l=Xc.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),u?u.parentNode.insertBefore(n,u.nextSibling):(e=e.nodeType===9?e.head:e,e.insertBefore(n,e.firstChild)),a.state.loading|=4}}var pi={$$typeof:Ze,Provider:null,Consumer:null,_currentValue:ee,_currentValue2:ee,_threadCount:0};function Rm(e,a,t,l,n,u,r,o){this.tag=1,this.containerInfo=e,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Yt(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Yt(0),this.hiddenUpdates=Yt(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=r,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=o,this.incompleteTransitions=new Map}function Io(e,a,t,l,n,u,r,o,S,C,H,x){return e=new Rm(e,a,t,r,o,S,C,x),a=1,u===!0&&(a|=24),u=wa(3,null,null,a),e.current=u,u.stateNode=e,a=Us(),a.refCount++,e.pooledCache=a,a.refCount++,u.memoizedState={element:l,isDehydrated:t,cache:a},xs(u),e}function eh(e){return e?(e=Kn,e):Kn}function ah(e,a,t,l,n,u){n=eh(n),l.context===null?l.context=n:l.pendingContext=n,l=Ol(a),l.payload={element:t},u=u===void 0?null:u,u!==null&&(l.callback=u),t=_l(e,l,a),t!==null&&(Qa(t,e,a),Wu(t,e,a))}function th(e,a){if(e=e.memoizedState,e!==null&&e.dehydrated!==null){var t=e.retryLane;e.retryLane=t!==0&&t<a?t:a}}function ef(e,a){th(e,a),(e=e.alternate)&&th(e,a)}function lh(e){if(e.tag===13){var a=Zn(e,67108864);a!==null&&Qa(a,e,67108864),ef(e,67108864)}}var Qc=!0;function Om(e,a,t,l){var n=G.T;G.T=null;var u=q.p;try{q.p=2,af(e,a,t,l)}finally{q.p=u,G.T=n}}function _m(e,a,t,l){var n=G.T;G.T=null;var u=q.p;try{q.p=8,af(e,a,t,l)}finally{q.p=u,G.T=n}}function af(e,a,t,l){if(Qc){var n=tf(l);if(n===null)Vr(e,a,l,kc,t),uh(e,l);else if(Bm(n,e,a,t,l))l.stopPropagation();else if(uh(e,l),a&4&&-1<Mm.indexOf(e)){for(;n!==null;){var u=ml(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var r=ft(u.pendingLanes);if(r!==0){var o=u;for(o.pendingLanes|=2,o.entangledLanes|=2;r;){var S=1<<31-ra(r);o.entanglements[1]|=S,r&=~S}xt(u),(De&6)===0&&(_c=Oa()+500,hi(0))}}break;case 13:o=Zn(u,2),o!==null&&Qa(o,u,2),Bc(),ef(u,2)}if(u=tf(l),u===null&&Vr(e,a,l,kc,t),u===n)break;n=u}n!==null&&l.stopPropagation()}else Vr(e,a,l,null,t)}}function tf(e){return e=tn(e),lf(e)}var kc=null;function lf(e){if(kc=null,e=ot(e),e!==null){var a=V(e);if(a===null)e=null;else{var t=a.tag;if(t===13){if(e=B(a),e!==null)return e;e=null}else if(t===3){if(a.stateNode.current.memoizedState.isDehydrated)return a.tag===3?a.stateNode.containerInfo:null;e=null}else a!==e&&(e=null)}}return kc=e,null}function nh(e){switch(e){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(yf()){case bn:return 2;case _a:return 8;case Kl:case Pc:return 32;case Cn:return 268435456;default:return 32}default:return 32}}var nf=!1,Vl=null,Xl=null,$l=null,Ei=new Map,Ti=new Map,Ql=[],Mm="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function uh(e,a){switch(e){case"focusin":case"focusout":Vl=null;break;case"dragenter":case"dragleave":Xl=null;break;case"mouseover":case"mouseout":$l=null;break;case"pointerover":case"pointerout":Ei.delete(a.pointerId);break;case"gotpointercapture":case"lostpointercapture":Ti.delete(a.pointerId)}}function Ni(e,a,t,l,n,u){return e===null||e.nativeEvent!==u?(e={blockedOn:a,domEventName:t,eventSystemFlags:l,nativeEvent:u,targetContainers:[n]},a!==null&&(a=ml(a),a!==null&&lh(a)),e):(e.eventSystemFlags|=l,a=e.targetContainers,n!==null&&a.indexOf(n)===-1&&a.push(n),e)}function Bm(e,a,t,l,n){switch(a){case"focusin":return Vl=Ni(Vl,e,a,t,l,n),!0;case"dragenter":return Xl=Ni(Xl,e,a,t,l,n),!0;case"mouseover":return $l=Ni($l,e,a,t,l,n),!0;case"pointerover":var u=n.pointerId;return Ei.set(u,Ni(Ei.get(u)||null,e,a,t,l,n)),!0;case"gotpointercapture":return u=n.pointerId,Ti.set(u,Ni(Ti.get(u)||null,e,a,t,l,n)),!0}return!1}function ih(e){var a=ot(e.target);if(a!==null){var t=V(a);if(t!==null){if(a=t.tag,a===13){if(a=B(t),a!==null){e.blockedOn=a,Fl(e.priority,function(){if(t.tag===13){var l=$a();l=bt(l);var n=Zn(t,l);n!==null&&Qa(n,t,l),ef(t,l)}});return}}else if(a===3&&t.stateNode.current.memoizedState.isDehydrated){e.blockedOn=t.tag===3?t.stateNode.containerInfo:null;return}}}e.blockedOn=null}function Zc(e){if(e.blockedOn!==null)return!1;for(var a=e.targetContainers;0<a.length;){var t=tf(e.nativeEvent);if(t===null){t=e.nativeEvent;var l=new t.constructor(t.type,t);da=l,t.target.dispatchEvent(l),da=null}else return a=ml(t),a!==null&&lh(a),e.blockedOn=t,!1;a.shift()}return!0}function ch(e,a,t){Zc(e)&&t.delete(a)}function Gm(){nf=!1,Vl!==null&&Zc(Vl)&&(Vl=null),Xl!==null&&Zc(Xl)&&(Xl=null),$l!==null&&Zc($l)&&($l=null),Ei.forEach(ch),Ti.forEach(ch)}function Kc(e,a){e.blockedOn===a&&(e.blockedOn=null,nf||(nf=!0,K.unstable_scheduleCallback(K.unstable_NormalPriority,Gm)))}var Jc=null;function sh(e){Jc!==e&&(Jc=e,K.unstable_scheduleCallback(K.unstable_NormalPriority,function(){Jc===e&&(Jc=null);for(var a=0;a<e.length;a+=3){var t=e[a],l=e[a+1],n=e[a+2];if(typeof l!="function"){if(lf(l||t)===null)continue;break}var u=ml(t);u!==null&&(e.splice(a,3),a-=3,lr(u,{pending:!0,data:n,method:t.method,action:l},l,n))}}))}function bi(e){function a(S){return Kc(S,e)}Vl!==null&&Kc(Vl,e),Xl!==null&&Kc(Xl,e),$l!==null&&Kc($l,e),Ei.forEach(a),Ti.forEach(a);for(var t=0;t<Ql.length;t++){var l=Ql[t];l.blockedOn===e&&(l.blockedOn=null)}for(;0<Ql.length&&(t=Ql[0],t.blockedOn===null);)ih(t),t.blockedOn===null&&Ql.shift();if(t=(e.ownerDocument||e).$$reactFormReplay,t!=null)for(l=0;l<t.length;l+=3){var n=t[l],u=t[l+1],r=n[ve]||null;if(typeof u=="function")r||sh(t);else if(r){var o=null;if(u&&u.hasAttribute("formAction")){if(n=u,r=u[ve]||null)o=r.formAction;else if(lf(n)!==null)continue}else o=r.action;typeof o=="function"?t[l+1]=o:(t.splice(l,3),l-=3),sh(t)}}}function uf(e){this._internalRoot=e}Wc.prototype.render=uf.prototype.render=function(e){var a=this._internalRoot;if(a===null)throw Error(_(409));var t=a.current,l=$a();ah(t,l,e,a,null,null)},Wc.prototype.unmount=uf.prototype.unmount=function(){var e=this._internalRoot;if(e!==null){this._internalRoot=null;var a=e.containerInfo;ah(e.current,2,null,e,null,null),Bc(),a[Ja]=null}};function Wc(e){this._internalRoot=e}Wc.prototype.unstable_scheduleHydration=function(e){if(e){var a=ta();e={blockedOn:null,target:e,priority:a};for(var t=0;t<Ql.length&&a!==0&&a<Ql[t].priority;t++);Ql.splice(t,0,e),t===0&&ih(e)}};var rh=Ce.version;if(rh!=="19.1.0")throw Error(_(527,rh,"19.1.0"));q.findDOMNode=function(e){var a=e._reactInternals;if(a===void 0)throw typeof e.render=="function"?Error(_(188)):(e=Object.keys(e).join(","),Error(_(268,e)));return e=$(a),e=e!==null?M(e):null,e=e===null?null:e.stateNode,e};var Hm={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:G,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var Fc=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!Fc.isDisabled&&Fc.supportsFiber)try{dl=Fc.inject(Hm),va=Fc}catch{}}return Di.createRoot=function(e,a){if(!v(e))throw Error(_(299));var t=!1,l="",n=bd,u=Cd,r=Dd,o=null;return a!=null&&(a.unstable_strictMode===!0&&(t=!0),a.identifierPrefix!==void 0&&(l=a.identifierPrefix),a.onUncaughtError!==void 0&&(n=a.onUncaughtError),a.onCaughtError!==void 0&&(u=a.onCaughtError),a.onRecoverableError!==void 0&&(r=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(o=a.unstable_transitionCallbacks)),a=Io(e,1,!1,null,null,t,l,n,u,r,o,null),e[Ja]=a.current,Yr(e),new uf(a)},Di.hydrateRoot=function(e,a,t){if(!v(e))throw Error(_(299));var l=!1,n="",u=bd,r=Cd,o=Dd,S=null,C=null;return t!=null&&(t.unstable_strictMode===!0&&(l=!0),t.identifierPrefix!==void 0&&(n=t.identifierPrefix),t.onUncaughtError!==void 0&&(u=t.onUncaughtError),t.onCaughtError!==void 0&&(r=t.onCaughtError),t.onRecoverableError!==void 0&&(o=t.onRecoverableError),t.unstable_transitionCallbacks!==void 0&&(S=t.unstable_transitionCallbacks),t.formState!==void 0&&(C=t.formState)),a=Io(e,1,!0,a,t??null,l,n,u,r,o,S,C),a.context=eh(null),t=a.current,l=$a(),l=bt(l),n=Ol(l),n.callback=null,_l(t,n,l),t=l,a.current.lanes=t,Wl(a,t),xt(a),e[Ja]=a.current,Yr(e),new Wc(a)},Di.version="19.1.0",Di}var Ah;function Xm(){if(Ah)return rf.exports;Ah=1;function K(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(K)}catch(Ce){console.error(Ce)}}return K(),rf.exports=Vm(),rf.exports}var $m=Xm();const f={BANG:"BANG",MISSED:"MISSED",BEER:"BEER",PANIC:"PANIC",CAT_BALOU:"CAT_BALOU",STAGECOACH:"STAGECOACH",WELLS_FARGO:"WELLS_FARGO",GATLING:"GATLING",INDIANS:"INDIANS",DUEL:"DUEL",GENERAL_STORE:"GENERAL_STORE",SALOON:"SALOON",BARREL:"BARREL",SCOPE:"SCOPE",MUSTANG:"MUSTANG",JAIL:"JAIL",DYNAMITE:"DYNAMITE",VOLCANIC:"VOLCANIC",SCHOFIELD:"SCHOFIELD",REMINGTON:"REMINGTON",REV_CARABINE:"REV_CARABINE",WINCHESTER:"WINCHESTER",BINOCULAR:"BINOCULAR",HIDEOUT:"HIDEOUT",BRAWL:"BRAWL",DODGE:"DODGE",PUNCH:"PUNCH",RAG_TIME:"RAG_TIME",SPRINGFIELD:"SPRINGFIELD",TEQUILA:"TEQUILA",WHISKY:"WHISKY",BIBLE:"BIBLE",BUFFALO_RIFLE:"BUFFALO_RIFLE",CAN_CAN:"CAN_CAN",CANTEEN:"CANTEEN",CONESTOGA:"CONESTOGA",DERRINGER:"DERRINGER",HOWITZER:"HOWITZER",IRON_PLATE:"IRON_PLATE",KNIFE:"KNIFE",PEPPERBOX:"PEPPERBOX",PONY_EXPRESS:"PONY_EXPRESS",SOMBRERO:"SOMBRERO",TEN_GALLON_HAT:"TEN_GALLON_HAT"},z={HEARTS:"HEARTS",DIAMONDS:"DIAMONDS",CLUBS:"CLUBS",SPADES:"SPADES"},hf={[f.VOLCANIC]:1,[f.SCHOFIELD]:2,[f.REMINGTON]:3,[f.REV_CARABINE]:4,[f.WINCHESTER]:5},ka={WEAPON:"WEAPON",DEFENSIVE:"DEFENSIVE",SPECIAL:"SPECIAL",GREEN:"GREEN"},Da={[f.BANG]:"/images/cards/bang.png",[f.MISSED]:"/images/cards/missed.png",[f.BEER]:"/images/cards/beer.png",[f.PANIC]:"/images/cards/panic.png",[f.CAT_BALOU]:"/images/cards/cat_balou.png",[f.STAGECOACH]:"/images/cards/stagecoach.png",[f.WELLS_FARGO]:"/images/cards/wells_fargo.png",[f.GATLING]:"/images/cards/gatling.png",[f.INDIANS]:"/images/cards/indians.png",[f.DUEL]:"/images/cards/duel.png",[f.GENERAL_STORE]:"/images/cards/general_store.png",[f.SALOON]:"/images/cards/saloon.png",[f.BARREL]:"/images/cards/barrel.png",[f.SCOPE]:"/images/cards/scope.png",[f.MUSTANG]:"/images/cards/mustang.png",[f.JAIL]:"/images/cards/jail.png",[f.DYNAMITE]:"/images/cards/dynamite.png",[f.VOLCANIC]:"/images/cards/volcanic.png",[f.SCHOFIELD]:"/images/cards/schofield.png",[f.REMINGTON]:"/images/cards/remington.png",[f.REV_CARABINE]:"/images/cards/rev_carabine.png",[f.WINCHESTER]:"/images/cards/winchester.png",[f.BINOCULAR]:"/src/assets/cards/dodge_city/binocular.png",[f.HIDEOUT]:"/src/assets/cards/dodge_city/hideout.png",[f.BRAWL]:"/src/assets/cards/dodge_city/brawl.png",[f.DODGE]:"/src/assets/cards/dodge_city/dodge.png",[f.PUNCH]:"/src/assets/cards/dodge_city/punch.png",[f.RAG_TIME]:"/src/assets/cards/dodge_city/rag_time.png",[f.SPRINGFIELD]:"/src/assets/cards/dodge_city/springfield.png",[f.TEQUILA]:"/src/assets/cards/dodge_city/tequila.png",[f.WHISKY]:"/src/assets/cards/dodge_city/whisky.png",[f.BIBLE]:"/src/assets/cards/dodge_city/bible.png",[f.BUFFALO_RIFLE]:"/src/assets/cards/dodge_city/buffalo_rifle.png",[f.CAN_CAN]:"/src/assets/cards/dodge_city/can_can.png",[f.CANTEEN]:"/src/assets/cards/dodge_city/canteen.png",[f.CONESTOGA]:"/src/assets/cards/dodge_city/conestoga.png",[f.DERRINGER]:"/src/assets/cards/dodge_city/derringer.png",[f.HOWITZER]:"/src/assets/cards/dodge_city/howitzer.png",[f.IRON_PLATE]:"/src/assets/cards/dodge_city/iron_plate.png",[f.KNIFE]:"/src/assets/cards/dodge_city/knife.png",[f.PEPPERBOX]:"/src/assets/cards/dodge_city/pepperbox.png",[f.PONY_EXPRESS]:"/src/assets/cards/dodge_city/pony_express.png",[f.SOMBRERO]:"/src/assets/cards/dodge_city/sombrero.png",[f.TEN_GALLON_HAT]:"/src/assets/cards/dodge_city/ten_gallon_hat.png"},Ri={"Bart Cassidy":"/images/characters/bart_cassidy.png","Black Jack":"/images/characters/black_jack.png","Calamity Janet":"/images/characters/calamity_janet.png","El Gringo":"/images/characters/el_gringo.png","Jesse Jones":"/images/characters/jesse_jones.png",Jourdonnais:"/images/characters/jourdonnais.png","Kit Carlson":"/images/characters/kit_carlson.png","Lucky Duke":"/images/characters/lucky_duke.png","Paul Regret":"/images/characters/paul_regret.png","Pedro Ramirez":"/images/characters/pedro_ramirez.png","Rose Doolan":"/images/characters/rose_doolan.png","Sid Ketchum":"/images/characters/sid_ketchum.png","Slab the Killer":"/images/characters/slab_the_killer.png","Suzy Lafayette":"/images/characters/suzy_lafayette.png","Vulture Sam":"/images/characters/vulture_sam.png","Willy the Kid":"/images/characters/willy_the_kid.png","Apache Kid":"/src/assets/characters/dodge_city/apache_kid.png","Belle Star":"/src/assets/characters/dodge_city/belle_star.png","Bill Noface":"/src/assets/characters/dodge_city/bill_noface.png","Chuck Wengam":"/src/assets/characters/dodge_city/chuck_wengam.png","Doc Holyday":"/src/assets/characters/dodge_city/doc_holyday.png","Elena Fuente":"/src/assets/characters/dodge_city/elena_fuente.png","Greg Digger":"/src/assets/characters/dodge_city/greg_digger.png","Herb Hunter":"/src/assets/characters/dodge_city/herb_hunter.png","José Delgado":"/src/assets/characters/dodge_city/jose_delgado.png","Molly Stark":"/src/assets/characters/dodge_city/molly_stark.png","Pat Brennan":"/src/assets/characters/dodge_city/pat_brennan.png","Pixie Pete":"/src/assets/characters/dodge_city/pixie_pete.png","Sean Mallory":"/src/assets/characters/dodge_city/sean_mallory.png","Tequila Joe":"/src/assets/characters/dodge_city/tequila_joe.png","Vera Custer":"/src/assets/characters/dodge_city/vera_custer.png"},Qm="/images/box/card_back.png",pe={SHERIFF:"SHERIFF",DEPUTY:"DEPUTY",OUTLAW:"OUTLAW",RENEGADE:"RENEGADE"},km={[pe.SHERIFF]:"/images/roles/sheriff.png",[pe.DEPUTY]:"/images/roles/deputy.png",[pe.OUTLAW]:"/images/roles/outlaw.png",[pe.RENEGADE]:"/images/roles/renegade.png"},Zm=[{name:"Bart Cassidy",life:4,ability:"Draws a card when he loses a life point",abilityType:"ON_DAMAGE_TAKEN"},{name:"Black Jack",life:4,ability:"Shows second card when drawing; draws again if heart/diamond",abilityType:"ON_DRAW"},{name:"Calamity Janet",life:4,ability:"Can play BANG! as Missed! and vice versa",abilityType:"CARD_SUBSTITUTION"},{name:"El Gringo",life:3,ability:"Draws from attacker when hit",abilityType:"ON_DAMAGE_TAKEN"},{name:"Jesse Jones",life:4,ability:"Can draw first card from another player",abilityType:"DRAW_CHOICE"},{name:"Jourdonnais",life:4,ability:"Has built-in Barrel effect",abilityType:"BUILT_IN_BARREL"},{name:"Kit Carlson",life:4,ability:"Sees top 3 cards when drawing, chooses 2",abilityType:"DRAW_CHOICE"},{name:"Lucky Duke",life:4,ability:"Flips 2 cards for checks, chooses one",abilityType:"LUCKY_DRAW"},{name:"Paul Regret",life:3,ability:"Has built-in Mustang effect",abilityType:"BUILT_IN_MUSTANG"},{name:"Pedro Ramirez",life:4,ability:"Can draw first card from discard pile",abilityType:"DRAW_CHOICE"},{name:"Rose Doolan",life:4,ability:"She sees all players at a distance decreased by 1",abilityType:"BUILT_IN_SCOPE"},{name:"Sid Ketchum",life:4,ability:"Can discard 2 cards to regain 1 life point",abilityType:"ACTIVE_ABILITY"},{name:"Slab the Killer",life:4,ability:"Players need 2 Missed! to avoid his BANG!",abilityType:"ATTACK_MODIFIER"},{name:"Suzy Lafayette",life:4,ability:"Draws a card when she has no cards in hand",abilityType:"AUTO_DRAW"},{name:"Vulture Sam",life:4,ability:"Takes cards of eliminated players",abilityType:"ON_ELIMINATION"},{name:"Willy the Kid",life:4,ability:"Can play any number of BANG! cards",abilityType:"UNLIMITED_BANG"},{name:"Apache Kid",life:3,ability:"Unaffected by Diamond cards played by other players (except during Duels)",abilityType:"DIAMOND_IMMUNITY"},{name:"Belle Star",life:4,ability:"During her turn, no card in front of any other player has any effect",abilityType:"DISABLE_EQUIPMENT"},{name:"Bill Noface",life:4,ability:"Draws 1 card plus 1 for each injury (lost life point) during phase 1",abilityType:"INJURY_DRAW"},{name:"Chuck Wengam",life:4,ability:"Can lose 1 life to draw 2 cards (multiple times per turn, not last life)",abilityType:"LIFE_FOR_CARDS"},{name:"Doc Holyday",life:4,ability:"Once per turn, discard 2 cards for BANG! effect (doesn't count toward limit)",abilityType:"DISCARD_FOR_BANG"},{name:"Elena Fuente",life:3,ability:"Can use any card in her hand as a Missed!",abilityType:"ANY_AS_MISSED"},{name:"Greg Digger",life:4,ability:"Regains 2 life when another character is eliminated",abilityType:"HEAL_ON_ELIMINATION"},{name:"Herb Hunter",life:4,ability:"Draws 2 extra cards when another character is eliminated",abilityType:"DRAW_ON_ELIMINATION"},{name:"José Delgado",life:4,ability:"Can discard a blue card to draw 2 cards (twice per turn)",abilityType:"BLUE_FOR_CARDS"},{name:"Molly Stark",life:4,ability:"Draws 1 card when voluntarily discarding Missed!/Beer/BANG! on others' turns",abilityType:"VOLUNTARY_DISCARD_DRAW"},{name:"Pat Brennan",life:4,ability:"Can draw 1 card from in play instead of 2 from deck during phase 1",abilityType:"STEAL_OR_DRAW"},{name:"Pixie Pete",life:3,ability:"Draws 3 cards instead of 2 during phase 1",abilityType:"ENHANCED_DRAW"},{name:"Sean Mallory",life:3,ability:"Can hold up to 10 cards in hand during phase 3",abilityType:"EXTENDED_HAND_LIMIT"},{name:"Tequila Joe",life:4,ability:"Regains 2 life from Beer instead of 1 (only 1 from other healing cards)",abilityType:"ENHANCED_BEER"},{name:"Vera Custer",life:3,ability:"At turn start, copies another character's ability until next turn",abilityType:"COPY_ABILITY"}];function Km(){var Aa,bl,xu,qu,wu,Yu,Xn,Pi,Jt,$n,Cl,Qn;const[K,Ce]=Y.useState("setup"),[Te,_]=Y.useState(null),[v,V]=Y.useState([]),[B,je]=Y.useState(0),[$,M]=Y.useState([]),[U,X]=Y.useState([]),[he,ea]=Y.useState(4),[sa,E]=Y.useState("Welcome to BANG!"),[ke,ut]=Y.useState(null),[Tt,Ze]=Y.useState({}),[za,ge]=Y.useState({}),[Na,Ra]=Y.useState([]),We=(i,c=!1,s=3e3)=>{if(ke&&(clearTimeout(ke),ut(null)),E(i),c){const d=setTimeout(()=>{E(""),ut(null)},s);ut(d)}},La=Y.useCallback(()=>{bu(120),Qt(!0)},[]),Za=Y.useCallback(()=>{Qt(!1),bu(120)},[]),it=i=>{const c=Math.floor(i/60),s=i%60;return`${c}:${s.toString().padStart(2,"0")}`},[ze,qt]=Y.useState(!0),[ct,aa]=Y.useState(null),G=Y.useCallback(()=>{if(!ct&&ze){const i=new(window.AudioContext||window.webkitAudioContext);return aa(i),i}return ct},[ct,ze]),q=Y.useCallback(i=>{if(!ze)return;if(i==="dominating")try{const s=new Audio("/src//assets/audio/Dominating.mp3");s.volume=.7,s.play().catch(d=>{console.log("Could not play dominating sound:",d),q("killingSpree")});return}catch(s){console.log("Error creating dominating audio:",s),q("killingSpree");return}if(i==="unstoppable")try{const s=new Audio("/src//assets/audio//Unstoppable.mp3");s.volume=.8,s.play().catch(d=>{console.log("Could not play unstoppable sound:",d),q("victory")});return}catch(s){console.log("Error creating unstoppable audio:",s),q("victory");return}if(i==="godlike")try{const s=new Audio("/src/assets/audio/Godlike.mp3");s.volume=.7,s.play().catch(d=>{console.log("Could not play godlike sound:",d),q("damage")});return}catch(s){console.log("Error creating godlike audio:",s),q("damage");return}const c=G();if(c)try{const s=c.createOscillator(),d=c.createGain();switch(s.connect(d),d.connect(c.destination),i){case"turnStart":s.frequency.setValueAtTime(800,c.currentTime),s.frequency.setValueAtTime(600,c.currentTime+.1),d.gain.setValueAtTime(.3,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.8),s.start(c.currentTime),s.stop(c.currentTime+.8);break;case"damage":s.frequency.setValueAtTime(200,c.currentTime),s.frequency.exponentialRampToValueAtTime(100,c.currentTime+.3),d.gain.setValueAtTime(.4,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.3),s.start(c.currentTime),s.stop(c.currentTime+.3);break;case"cardPlay":s.frequency.setValueAtTime(400,c.currentTime),s.frequency.setValueAtTime(300,c.currentTime+.05),d.gain.setValueAtTime(.2,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.15),s.start(c.currentTime),s.stop(c.currentTime+.15);break;case"cardDraw":s.frequency.setValueAtTime(500,c.currentTime),s.frequency.setValueAtTime(450,c.currentTime+.1),d.gain.setValueAtTime(.15,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2);break;case"elimination":s.frequency.setValueAtTime(150,c.currentTime),s.frequency.exponentialRampToValueAtTime(50,c.currentTime+1),d.gain.setValueAtTime(.5,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+1),s.start(c.currentTime),s.stop(c.currentTime+1);break;case"defense":s.frequency.setValueAtTime(600,c.currentTime),s.frequency.setValueAtTime(800,c.currentTime+.1),d.gain.setValueAtTime(.25,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.4),s.start(c.currentTime),s.stop(c.currentTime+.4);break;case"heal":s.frequency.setValueAtTime(400,c.currentTime),s.frequency.setValueAtTime(600,c.currentTime+.2),s.frequency.setValueAtTime(800,c.currentTime+.4),d.gain.setValueAtTime(.2,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.6),s.start(c.currentTime),s.stop(c.currentTime+.6);break;case"killingSpree":{s.type="sawtooth",s.frequency.setValueAtTime(80,c.currentTime),s.frequency.exponentialRampToValueAtTime(40,c.currentTime+.3),d.gain.setValueAtTime(.6,c.currentTime),d.gain.exponentialRampToValueAtTime(.1,c.currentTime+.3),s.start(c.currentTime);const h=c.createOscillator(),m=c.createGain();h.connect(m),m.connect(c.destination),h.type="sawtooth",h.frequency.setValueAtTime(160,c.currentTime+.1),h.frequency.exponentialRampToValueAtTime(80,c.currentTime+.4),m.gain.setValueAtTime(.4,c.currentTime+.1),m.gain.exponentialRampToValueAtTime(.01,c.currentTime+.4),h.start(c.currentTime+.1),h.stop(c.currentTime+.4);const y=c.createOscillator(),T=c.createGain();y.connect(T),T.connect(c.destination),y.type="triangle",y.frequency.setValueAtTime(200,c.currentTime+.5),y.frequency.exponentialRampToValueAtTime(400,c.currentTime+1.2),T.gain.setValueAtTime(.5,c.currentTime+.5),T.gain.exponentialRampToValueAtTime(.01,c.currentTime+1.2),y.start(c.currentTime+.5),y.stop(c.currentTime+1.2);const D=c.createOscillator(),L=c.createGain();D.connect(L),L.connect(c.destination),D.type="square",D.frequency.setValueAtTime(300,c.currentTime+1),D.frequency.setValueAtTime(350,c.currentTime+1.5),L.gain.setValueAtTime(.3,c.currentTime+1),L.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),D.start(c.currentTime+1),D.stop(c.currentTime+2),s.stop(c.currentTime+.3);break}case"victory":{s.type="triangle",s.frequency.setValueAtTime(440,c.currentTime),s.frequency.setValueAtTime(554,c.currentTime+.3),s.frequency.setValueAtTime(659,c.currentTime+.6),s.frequency.setValueAtTime(880,c.currentTime+.9),d.gain.setValueAtTime(.5,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),s.start(c.currentTime);const h=c.createOscillator(),m=c.createGain();h.connect(m),m.connect(c.destination),h.type="triangle",h.frequency.setValueAtTime(330,c.currentTime),h.frequency.setValueAtTime(415,c.currentTime+.3),h.frequency.setValueAtTime(494,c.currentTime+.6),h.frequency.setValueAtTime(659,c.currentTime+.9),m.gain.setValueAtTime(.3,c.currentTime),m.gain.exponentialRampToValueAtTime(.01,c.currentTime+2),h.start(c.currentTime),h.stop(c.currentTime+2),s.stop(c.currentTime+2);break}case"warning":s.frequency.setValueAtTime(1e3,c.currentTime),s.frequency.setValueAtTime(800,c.currentTime+.1),d.gain.setValueAtTime(.3,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2);break;default:s.frequency.setValueAtTime(440,c.currentTime),d.gain.setValueAtTime(.2,c.currentTime),d.gain.exponentialRampToValueAtTime(.01,c.currentTime+.2),s.start(c.currentTime),s.stop(c.currentTime+.2)}}catch(s){console.log("Audio playback failed:",s)}},[ze,G]),ee=(i,c,s,d)=>{const h=`${Date.now()}-${Math.random()}-${Gi+1}`;Hi(T=>T+1);const m={id:h,card:i,fromPlayerIndex:c,toPlayerIndex:s,phase:"toTarget",progress:0,startTime:Date.now()};On(T=>[...T,m]);const y=()=>{const T=Date.now()-m.startTime,L=Math.min(T/800,1);On(Q=>Q.map(F=>F.id===h?{...F,progress:L}:F)),L<1?requestAnimationFrame(y):setTimeout(()=>{m.phase="toDiscard",m.startTime=Date.now(),m.progress=0;const Q=()=>{const F=Date.now()-m.startTime,ue=Math.min(F/600,1);On(Ue=>Ue.map(Ge=>Ge.id===h?{...Ge,progress:ue}:Ge)),ue<1?requestAnimationFrame(Q):On(Ue=>Ue.filter(Ge=>Ge.id!==h))};requestAnimationFrame(Q)},300)};requestAnimationFrame(y)},le=Y.useCallback((i,c,s=2e3)=>{const d=`${i}-${c}-${Date.now()}`;Ze(h=>({...h,[d]:{playerIndex:i,effectType:c,timestamp:Date.now()}})),setTimeout(()=>{Ze(h=>{const m={...h};return delete m[d],m})},s)},[Ze]),A=(i,c,s="damage")=>{const d=document.querySelector(`[data-player-index="${i}"]`);if(!d)return;const h=document.createElement("div");h.className=s==="damage"?"floating-damage":"floating-heal",h.textContent=s==="damage"?`-${c}`:`+${c}`,d.style.position="relative",d.appendChild(h),setTimeout(()=>{h.parentNode&&h.parentNode.removeChild(h)},2e3)},w=()=>{const i=document.querySelector(".game-board");i&&(i.classList.add("screen-shake"),setTimeout(()=>{i.classList.remove("screen-shake")},800))},[k,Z]=Y.useState(null),[I,fe]=Y.useState(!1),[ie,ga]=Y.useState(null),[me,Ka]=Y.useState(null),[Su,rl]=Y.useState(!1),[fl,Zl]=Y.useState(null),[Oi,wt]=Y.useState(!1),[st,Oa]=Y.useState(null),[yf,bn]=Y.useState(null),[_a,Kl]=Y.useState(null),[Pc,Cn]=Y.useState(!1),[Ic,rt]=Y.useState(!1),[dl,va]=Y.useState(!1),[Nt,ra]=Y.useState(!1),[Dn,_i]=Y.useState(null),[Mi,Jl]=Y.useState(null),[Rn,ft]=Y.useState(!1),[xa,ol]=Y.useState(null),[Bi,Au]=Y.useState(null),[pu,Yt]=Y.useState(!1),[Wl,On]=Y.useState([]),[Gi,Hi]=Y.useState(0),[bt,Vt]=Y.useState(!1),[ta,Fl]=Y.useState([]),[Ae,Ve]=Y.useState([]),[ve,Ja]=Y.useState(0),[la,qa]=Y.useState("draw"),[ba,Wa]=Y.useState(!1),[dt,hl]=Y.useState(!1),[ot,ml]=Y.useState(0),Xt=Y.useRef(-1),[ht,qe]=Y.useState(!1),[Ui,_n]=Y.useState(!1),[fa,Ct]=Y.useState(0),[Dt,Pl]=Y.useState([]),[Mn,Rt]=Y.useState(0),[Bn,Il]=Y.useState(new Set),[mt,Gn]=Y.useState(!1),[yl,$t]=Y.useState(null),[Eu,en]=Y.useState(!1),[Hn,Un]=Y.useState(null),[Xe,jn]=Y.useState(null),[es,gl]=Y.useState(!1),[an,vl]=Y.useState(null),[as,Sa]=Y.useState(!1),[Sl,zn]=Y.useState(null),[Tu,Ot]=Y.useState(null),[ji,Ln]=Y.useState(!1),[Al,Nu]=Y.useState(null),[Fa,xn]=Y.useState(null),[yt,bu]=Y.useState(120),[qn,Qt]=Y.useState(!1),[da,tn]=Y.useState(1),[gt,vt]=Y.useState([""]),Cu=Y.useCallback(()=>{var c;Qt(!1),E(`Time's up! ${(c=v[B])==null?void 0:c.name}'s turn is over.`);const i=v[B];if(i&&i.hand.length>i.health){const s=i.hand.length-i.health,d=[...v],h=[];for(let m=0;m<s;m++){const y=Math.floor(Math.random()*d[B].hand.length),T=d[B].hand.splice(y,1)[0];h.push(T)}V(d),X(m=>[...m,...h]),E(`${i.name} auto-discarded ${s} cards due to time limit.`)}qa("timeUp")},[v,B,Qt,E,X,V,qa]);Y.useEffect(()=>{la==="timeUp"&&setTimeout(()=>{let c=(B+1)%v.length;for(let s=0;s<v.length&&!v[c].isAlive;s++)c=(c+1)%v.length;je(c),qa("draw"),Wa(!1),Rt(0),E(`${v[c].name}'s turn!`)},1500)},[la,B,v,je,qa,Wa,Rt,E]),Y.useEffect(()=>{let i=null;return qn&&yt>0&&K==="playing"?i=setInterval(()=>{bu(c=>c<=1?(Cu(),0):c-1)},1e3):(!qn||yt<=0)&&clearInterval(i),()=>clearInterval(i)},[qn,K,yt,v,B,Cu]);const wn=()=>{var m;if(he<4||he>7){E("Please select 4-7 players");return}let i=zi();const c=ln(he),s=[...Zm].sort(()=>Math.random()-.5),d=[];for(let y=0;y<he;y++){const T=s[y],D=c[y],L=D===pe.SHERIFF?T.life+1:T.life,Q=ye(i,L),F=Q.drawnCards||[];i=Q.updatedDeck;let J,ue;y<da?(J=((m=gt[y])==null?void 0:m.trim())||`Player ${y+1}`,ue=!1):(J=`Bot ${y-da+1}`,ue=!0),d.push({id:y,character:T,role:D,health:L,maxHealth:L,hand:Array.isArray(F)?F:[],inPlay:[],isAlive:!0,isBot:ue,name:J})}const h=d.findIndex(y=>y.role===pe.SHERIFF);je(h),V(d),M(i),Ce("playing"),qa("draw"),Wa(!1),Rt(0),E(`Game started! ${d[h].name} is the Sheriff and goes first. Cards will be drawn automatically.`)},zi=()=>{const i=[{type:f.BANG,suit:z.SPADES,value:"A"},{type:f.BANG,suit:z.CLUBS,value:"2"},{type:f.BANG,suit:z.CLUBS,value:"3"},{type:f.BANG,suit:z.CLUBS,value:"4"},{type:f.BANG,suit:z.CLUBS,value:"5"},{type:f.BANG,suit:z.CLUBS,value:"6"},{type:f.BANG,suit:z.CLUBS,value:"7"},{type:f.BANG,suit:z.CLUBS,value:"8"},{type:f.BANG,suit:z.CLUBS,value:"9"},{type:f.BANG,suit:z.DIAMONDS,value:"2"},{type:f.BANG,suit:z.DIAMONDS,value:"3"},{type:f.BANG,suit:z.DIAMONDS,value:"4"},{type:f.BANG,suit:z.DIAMONDS,value:"5"},{type:f.BANG,suit:z.DIAMONDS,value:"6"},{type:f.BANG,suit:z.DIAMONDS,value:"7"},{type:f.BANG,suit:z.DIAMONDS,value:"8"},{type:f.BANG,suit:z.DIAMONDS,value:"9"},{type:f.BANG,suit:z.HEARTS,value:"12"},{type:f.BANG,suit:z.HEARTS,value:"13"},{type:f.BANG,suit:z.HEARTS,value:"A"},{type:f.BANG,suit:z.SPADES,value:"K"},{type:f.BANG,suit:z.SPADES,value:"Q"},{type:f.BANG,suit:z.SPADES,value:"J"},{type:f.BANG,suit:z.SPADES,value:"10"},{type:f.BANG,suit:z.SPADES,value:"9"},{type:f.MISSED,suit:z.SPADES,value:"8"},{type:f.MISSED,suit:z.SPADES,value:"7"},{type:f.MISSED,suit:z.SPADES,value:"6"},{type:f.MISSED,suit:z.SPADES,value:"5"},{type:f.MISSED,suit:z.SPADES,value:"4"},{type:f.MISSED,suit:z.SPADES,value:"3"},{type:f.MISSED,suit:z.SPADES,value:"2"},{type:f.MISSED,suit:z.CLUBS,value:"A"},{type:f.MISSED,suit:z.CLUBS,value:"K"},{type:f.MISSED,suit:z.CLUBS,value:"Q"},{type:f.MISSED,suit:z.CLUBS,value:"J"},{type:f.MISSED,suit:z.CLUBS,value:"10"},{type:f.BEER,suit:z.HEARTS,value:"6"},{type:f.BEER,suit:z.HEARTS,value:"7"},{type:f.BEER,suit:z.HEARTS,value:"8"},{type:f.BEER,suit:z.HEARTS,value:"9"},{type:f.BEER,suit:z.HEARTS,value:"10"},{type:f.BEER,suit:z.HEARTS,value:"J"},{type:f.PANIC,suit:z.HEARTS,value:"4"},{type:f.PANIC,suit:z.DIAMONDS,value:"J"},{type:f.PANIC,suit:z.DIAMONDS,value:"Q"},{type:f.PANIC,suit:z.DIAMONDS,value:"A"},{type:f.CAT_BALOU,suit:z.DIAMONDS,value:"K"},{type:f.CAT_BALOU,suit:z.DIAMONDS,value:"10"},{type:f.CAT_BALOU,suit:z.HEARTS,value:"K"},{type:f.CAT_BALOU,suit:z.HEARTS,value:"Q"},{type:f.STAGECOACH,suit:z.SPADES,value:"9"},{type:f.STAGECOACH,suit:z.SPADES,value:"9"},{type:f.WELLS_FARGO,suit:z.HEARTS,value:"3"},{type:f.GATLING,suit:z.HEARTS,value:"10"},{type:f.INDIANS,suit:z.DIAMONDS,value:"K"},{type:f.INDIANS,suit:z.DIAMONDS,value:"A"},{type:f.DUEL,suit:z.CLUBS,value:"8"},{type:f.DUEL,suit:z.DIAMONDS,value:"J"},{type:f.DUEL,suit:z.SPADES,value:"J"},{type:f.GENERAL_STORE,suit:z.CLUBS,value:"9"},{type:f.GENERAL_STORE,suit:z.SPADES,value:"Q"},{type:f.SALOON,suit:z.HEARTS,value:"5"},{type:f.BARREL,suit:z.SPADES,value:"K"},{type:f.BARREL,suit:z.SPADES,value:"Q"},{type:f.SCOPE,suit:z.SPADES,value:"A"},{type:f.MUSTANG,suit:z.HEARTS,value:"8"},{type:f.MUSTANG,suit:z.HEARTS,value:"9"},{type:f.JAIL,suit:z.SPADES,value:"10"},{type:f.JAIL,suit:z.SPADES,value:"J"},{type:f.JAIL,suit:z.HEARTS,value:"4"},{type:f.DYNAMITE,suit:z.HEARTS,value:"2"},{type:f.VOLCANIC,suit:z.SPADES,value:"10"},{type:f.VOLCANIC,suit:z.CLUBS,value:"10"},{type:f.SCHOFIELD,suit:z.CLUBS,value:"K"},{type:f.SCHOFIELD,suit:z.CLUBS,value:"Q"},{type:f.SCHOFIELD,suit:z.SPADES,value:"K"},{type:f.REMINGTON,suit:z.CLUBS,value:"K"},{type:f.REV_CARABINE,suit:z.CLUBS,value:"A"},{type:f.WINCHESTER,suit:z.SPADES,value:"8"},{type:f.DODGE,suit:z.CLUBS,value:"A"},{type:f.DODGE,suit:z.SPADES,value:"A"},{type:f.PUNCH,suit:z.HEARTS,value:"A"},{type:f.PUNCH,suit:z.DIAMONDS,value:"A"},{type:f.BRAWL,suit:z.CLUBS,value:"J"},{type:f.RAG_TIME,suit:z.HEARTS,value:"Q"},{type:f.TEQUILA,suit:z.HEARTS,value:"4"},{type:f.WHISKY,suit:z.HEARTS,value:"3"},{type:f.BINOCULAR,suit:z.CLUBS,value:"Q"},{type:f.HIDEOUT,suit:z.SPADES,value:"Q"},{type:f.BIBLE,suit:z.HEARTS,value:"K"},{type:f.CAN_CAN,suit:z.CLUBS,value:"K"},{type:f.CANTEEN,suit:z.HEARTS,value:"8"},{type:f.CONESTOGA,suit:z.HEARTS,value:"9"},{type:f.IRON_PLATE,suit:z.SPADES,value:"K"},{type:f.PONY_EXPRESS,suit:z.SPADES,value:"J"},{type:f.SOMBRERO,suit:z.CLUBS,value:"9"},{type:f.TEN_GALLON_HAT,suit:z.HEARTS,value:"10"},{type:f.DERRINGER,suit:z.CLUBS,value:"4"},{type:f.PEPPERBOX,suit:z.DIAMONDS,value:"8"},{type:f.KNIFE,suit:z.SPADES,value:"9"},{type:f.SPRINGFIELD,suit:z.CLUBS,value:"8"},{type:f.BUFFALO_RIFLE,suit:z.SPADES,value:"A"},{type:f.HOWITZER,suit:z.HEARTS,value:"2"}];return i.forEach(c=>{[f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.PEPPERBOX,f.KNIFE,f.BUFFALO_RIFLE].includes(c.type)?(c.equipmentType=ka.WEAPON,c.range=hf[c.type]):[f.BARREL,f.MUSTANG,f.SOMBRERO].includes(c.type)?c.equipmentType=ka.DEFENSIVE:[f.SCOPE,f.JAIL,f.DYNAMITE].includes(c.type)?c.equipmentType=ka.SPECIAL:[f.BINOCULAR,f.HIDEOUT,f.BIBLE,f.CAN_CAN,f.CANTEEN,f.CONESTOGA,f.PONY_EXPRESS,f.DERRINGER,f.TEN_GALLON_HAT,f.HOWITZER].includes(c.type)&&(c.equipmentType=ka.GREEN)}),i.sort(()=>Math.random()-.5)},ln=i=>{const c=[];c.push(pe.SHERIFF);const s=i<=4?2:i<=6?3:4;for(let m=0;m<s;m++)c.push(pe.OUTLAW);const d=i<=6?1:2;for(let m=0;m<d;m++)c.push(pe.RENEGADE);const h=i<=4?0:i===5?1:2;for(let m=0;m<h;m++)c.push(pe.DEPUTY);return c.sort(()=>Math.random()-.5)},ye=Y.useCallback((i,c)=>{let s=[...i],d=[...U];const h=[];for(let m=0;m<c;m++){if(s.length===0){if(d.length===0){console.warn("Cannot reshuffle: both deck and discard pile are empty!");break}const y=d[d.length-1],D=[...d.slice(0,-1)];for(let L=D.length-1;L>0;L--){const Q=Math.floor(Math.random()*(L+1));[D[L],D[Q]]=[D[Q],D[L]]}s=D,d=[y],M(s),X(d),E("Deck reshuffled from discard pile!")}s.length>0&&(h.push(s[0]),s=s.slice(1))}return{drawnCards:h,updatedDeck:s,updatedDiscardPile:d}},[U,M,X,E]),Pa=(i,c,s)=>{const d=s.filter(J=>J.isAlive).length,h=s.map((J,ue)=>({...J,originalIndex:ue})).filter(J=>J.isAlive),m=h.findIndex(J=>J.originalIndex===i),y=h.findIndex(J=>J.originalIndex===c);if(m===-1||y===-1)return 1/0;const T=(y-m+d)%d,D=(m-y+d)%d;let L=Math.min(T,D);const Q=s[c];Q&&Q.character.name==="Paul Regret"&&(L+=1);const F=s[i];return F&&F.character.name==="Rose Doolan"&&(L=Math.max(1,L-1)),L},nn=i=>{const c=i.inPlay.find(h=>h.equipmentType===ka.WEAPON),s=c?c.range:1,d=i.character.abilityType==="BUILT_IN_SCOPE"||i.inPlay.some(h=>h.type===f.SCOPE)?1:0;return s+d},_t=i=>1+(i.character.abilityType==="BUILT_IN_MUSTANG"||i.inPlay.some(s=>s.type===f.MUSTANG)?1:0),Du=(i,c,s)=>{const d=s[i],h=s[c];if(!d.isAlive||!h.isAlive)return!1;const m=Pa(i,c,s),y=nn(d),T=_t(h);return m<=y&&m>=T},Mt=i=>{if($.length===0)return!1;const c=$[0];if(M($.slice(1)),X([...U,c]),i.character.name==="Lucky Duke"&&$.length>0){const s=$[0];M($.slice(1)),X([...U,s]);const d=c.suit===z.HEARTS?c:s.suit===z.HEARTS?s:c;return E(`${i.character.name} flipped ${c.suit} ${c.value} and ${s.suit} ${s.value}, chose ${d.suit} ${d.value}`),d.suit===z.HEARTS}return E(`${i.character.name} flipped ${c.suit} ${c.value} for barrel check`),c.suit===z.HEARTS},Bt=i=>i.character.name==="Jourdonnais"||i.inPlay.some(c=>c.type===f.BARREL),St=Y.useCallback(i=>i.character.name==="Willy the Kid"||i.inPlay.some(c=>c.type===f.VOLCANIC)?!0:Mn===0,[Mn]),kt=i=>i.role===pe.SHERIFF,na=(i,c)=>{if(!i.inPlay.some(h=>h.type===c))return!1;const d=`${v.findIndex(h=>h.id===i.id)}-${c}`;return!Bn.has(d)},Ne=(i,c)=>{const s=[...v],d=s[i].inPlay.findIndex(h=>h.type===c);if(d>=0){const h=s[i].inPlay.splice(d,1)[0];return X(m=>[...m,h]),V(s),!0}return!1},oa=(i,c)=>{const s=i[c],d=i.find(y=>y.character.name==="Vulture Sam"&&y.isAlive);d&&d!==s&&(Ma(d,"ON_ELIMINATION",{eliminatedPlayer:s}),i[c].hand=[],i[c].inPlay=[]);const h=i.find(y=>y.character.name==="Greg Digger"&&y.isAlive);h&&h!==s&&Ma(h,"HEAL_ON_ELIMINATION",{eliminatedPlayer:s});const m=i.find(y=>y.character.name==="Herb Hunter"&&y.isAlive);m&&m!==s&&Ma(m,"DRAW_ON_ELIMINATION",{eliminatedPlayer:s})},Ma=(i,c,s={})=>{switch(c){case"ON_DAMAGE_TAKEN":if(i.character.name==="Bart Cassidy"){if($.length>0){const d=$[0];M($.slice(1)),V(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,d]}:m)),E(`${i.name} (Bart Cassidy) draws a card from taking damage!`)}}else if(i.character.name==="El Gringo"){const{attackerIndex:d}=s;if(d!==void 0&&v[d]&&v[d].hand.length>0){const h=v[d],m=Math.floor(Math.random()*h.hand.length),y=h.hand[m];V(T=>T.map((D,L)=>{if(L===d){const Q=[...D.hand];return Q.splice(m,1),{...D,hand:Q}}else if(D.id===i.id)return{...D,hand:[...D.hand,y]};return D})),E(`${i.name} (El Gringo) draws a card from ${h.name}!`)}}break;case"AUTO_DRAW":if(i.character.name==="Suzy Lafayette"&&i.hand.length===0&&$.length>0){const d=$[0];M($.slice(1)),V(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,d]}:m)),E(`${i.name} (Suzy Lafayette) draws a card for having no cards!`)}break;case"ON_ELIMINATION":if(i.character.name==="Vulture Sam"){const{eliminatedPlayer:d}=s;d&&d.hand.length>0&&(V(h=>h.map(m=>m.id===i.id?{...m,hand:[...m.hand,...d.hand]}:m)),E(`${i.name} (Vulture Sam) takes all cards from eliminated ${d.name}!`))}break;case"HEAL_ON_ELIMINATION":if(i.character.name==="Greg Digger"){const{eliminatedPlayer:d}=s;d&&d.id!==i.id&&(V(h=>h.map(m=>m.id===i.id?{...m,health:Math.min(m.health+2,m.character.life)}:m)),E(`${i.name} (Greg Digger) regained 2 life from elimination!`))}break;case"DRAW_ON_ELIMINATION":if(i.character.name==="Herb Hunter"){const{eliminatedPlayer:d}=s;if(d&&d.id!==i.id&&$.length>=2){const h=ye($,2);V(m=>m.map(y=>y.id===i.id?{...y,hand:[...y.hand,...h.drawnCards]}:y)),M(h.updatedDeck),E(`${i.name} (Herb Hunter) drew 2 cards from elimination!`)}}break}},Fe=i=>{Ka(i),rl(!0)},pl=i=>{const c={type:i.name,suit:"",value:"",isCharacter:!0,character:i};Ka(c),rl(!0)},Ru=()=>{rl(!1),Ka(null)},Yn=()=>{rt(!1),Ce("setup"),V([]),je(0),M([]),X([]),qa("draw"),Wa(!1),Rt(0),tn(1),vt([""]),E("Welcome to BANG!"),va(!1)},un=()=>{rt(!1),va(!0),E("You are now spectating the game. You can watch until the game ends.")},At=i=>{const c=setTimeout(()=>{wt(!0),Fe(i)},2e3);Zl(c)},Zt=()=>{fl&&(clearTimeout(fl),Zl(null)),wt(!1)},El=()=>{fl&&(clearTimeout(fl),Zl(null)),wt(!1)},ts=Y.useCallback((i,c,s)=>{st&&clearTimeout(st),bn({card:c,cardIndex:s}),wt(!1),Cn(!1);const d=i.currentTarget,h=setTimeout(()=>{wt(!0),Cn(!0),Kl(c),d.classList.add("long-pressing"),navigator.vibrate&&navigator.vibrate(50)},2e3);Oa(h)},[st]),ls=Y.useCallback(i=>{i.currentTarget.classList.remove("long-pressing"),st&&(clearTimeout(st),Oa(null)),wt(!1),bn(null),setTimeout(()=>{Cn(!1)},100)},[st]),Ou=Y.useCallback(i=>{st&&(clearTimeout(st),Oa(null)),i.currentTarget.classList.remove("long-pressing"),wt(!1),bn(null)},[st]),Li=(i,c,s=500)=>{ge(d=>({...d,[i]:{type:c,timestamp:Date.now()}})),setTimeout(()=>{ge(d=>{const h={...d};return delete h[i],h})},s)},xi=()=>{Li(`draw-${Date.now()}`,"cardDraw",400)},qi=i=>({[f.BANG]:"Deal 1 damage to a player within range. Target must play a Missed! card or lose 1 life point.",[f.MISSED]:"Play in response to a BANG! card to avoid taking damage.",[f.BEER]:"Regain 1 life point (up to your maximum). Cannot be played if you're already at maximum health. Requires 3+ players alive.",[f.SALOON]:"All players regain 1 life point (up to their maximum).",[f.PANIC]:"Draw a random card from a player at distance 1.",[f.CAT_BALOU]:"Force a player to discard a random card from their hand or remove a card from play.",[f.DUEL]:"Challenge another player to a duel. You and target alternate playing BANG! cards. First player who cannot play a BANG! loses 1 life point.",[f.JAIL]:"Place on another player. They must draw a Heart to escape or skip their turn. Cannot target the Sheriff or players already in jail.",[f.DYNAMITE]:"Place in front of you. Each turn, draw a card - if it's Spades 2-9, take 3 damage!",[f.BARREL]:"When targeted by BANG!, draw a card. If it's a Heart, the BANG! misses.",[f.MUSTANG]:"Increases distance from you to other players by 1.",[f.SCOPE]:"Decreases distance from other players to you by 1.",[f.VOLCANIC]:"Allows you to play unlimited BANG! cards per turn.",[f.SCHOFIELD]:"Range 2 weapon. Can target players at distance 1-2.",[f.REMINGTON]:"Range 3 weapon. Can target players at distance 1-3.",[f.REV_CARABINE]:"Range 4 weapon. Can target players at distance 1-4.",[f.WINCHESTER]:"Range 5 weapon. Can target players at distance 1-5.",[f.STAGECOACH]:"Draw 2 cards from the deck.",[f.WELLS_FARGO]:"Draw 3 cards from the deck.",[f.GENERAL_STORE]:"All players draw 1 card from a shared selection.",[f.INDIANS]:"All other players must play a BANG! card or lose 1 life point.",[f.GATLING]:"Deal 1 damage to all other players (they can play Missed! to avoid).",[f.DODGE]:"Play in response to a BANG! card to avoid taking damage (like Missed!) and draw 1 card.",[f.PUNCH]:"Deal 1 damage to a player at distance 1 (like BANG! but only range 1).",[f.BRAWL]:"Discard another card + Brawl: Force all other players to discard a card from hand or play.",[f.RAG_TIME]:"All players draw 1 card, then discard 1 card.",[f.TEQUILA]:"Discard another card + Tequila: Choose any player to regain 1 life point.",[f.WHISKY]:"Discard another card + Whisky: Regain 2 life points.",[f.BINOCULAR]:"GREEN: Discard to look at top 3 cards of deck, put them back in any order.",[f.HIDEOUT]:"GREEN: Discard when targeted by BANG! - draw a card, if Spade the BANG! misses.",[f.BIBLE]:"GREEN: Discard to avoid BANG! and draw 1 card.",[f.CAN_CAN]:"GREEN: Discard when playing Missed! to draw 1 card.",[f.CANTEEN]:"GREEN: Discard when playing Beer to regain 2 life instead of 1.",[f.CONESTOGA]:"GREEN: Discard during hand limit phase to discard 1 less card.",[f.IRON_PLATE]:"GREEN: Discard to avoid BANG!.",[f.PONY_EXPRESS]:"GREEN: Discard to draw 3 cards from deck.",[f.SOMBRERO]:"GREEN: Discard to avoid BANG!.",[f.TEN_GALLON_HAT]:"GREEN: Discard to avoid BANG!.",[f.DERRINGER]:"GREEN: Discard to play as BANG! at range 1 and draw 1 card.",[f.PEPPERBOX]:"Range 1 weapon. Can play unlimited BANG! cards per turn.",[f.KNIFE]:"Range 1 weapon. BANG! cards played cannot be avoided with Missed!",[f.SPRINGFIELD]:"Discard another card + Springfield: BANG! at distance 2. Doesn't count toward BANG! limit.",[f.BUFFALO_RIFLE]:"Range 5 weapon. Can target players at distance 1-5.",[f.HOWITZER]:"GREEN: Discard to play as BANG! against all other players. Doesn't count toward BANG! limit."})[i]||"No description available.",cn=Y.useCallback(()=>{if(Xt.current===ot)return;if(la!=="draw"||ba||dt){dt||E("You have already drawn cards this turn!");return}Xt.current=ot,hl(!0);const i=v[B],c=[...v];let s=[...$],d=0;const h=i.inPlay.findIndex(y=>y.type===f.JAIL);if(h>=0&&s.length>0){let y=!1;if(i.character.name==="Lucky Duke"){if(s.length>=2){const T=s[0],D=s[1];s=s.slice(2),X([...U,T,D]);const L=T.suit===z.HEARTS?T:D.suit===z.HEARTS?D:T;y=L.suit===z.HEARTS,E(`${i.character.name} (Lucky Duke) drew 2 cards for jail escape and chose ${L.suit} ${L.value}`)}}else{const T=s[0];s=s.slice(1),X([...U,T]),y=T.suit===z.HEARTS,E(`${i.character.name} flipped ${T.suit} ${T.value} for jail escape`)}if(y){const T=c[B].inPlay.splice(h,1)[0];X([...U,T]),E(`${i.character.name} escaped from jail!`)}else{E(`${i.character.name} remains in jail and skips their turn.`),V(c),M(s),Gt();return}}const m=i.inPlay.findIndex(y=>y.type===f.DYNAMITE);if(m>=0&&s.length>0){const y=s[0];if(s=s.slice(1),X([...U,y]),y.suit===z.SPADES&&["2","3","4","5","6","7","8","9"].includes(y.value)){const T=c[B].inPlay.splice(m,1)[0];if(X([...U,T]),c[B].health-=3,c[B].health<=0){c[B].isAlive=!1,oa(c,B),E(`${i.character.name} (${i.role}) was killed by dynamite!`),q("dominating"),B===0&&!c[0].isBot&&rt(!0),V(c),M(s),Ba(c);return}else q("godlike"),E(`${i.character.name} was hurt by dynamite! Health: ${c[B].health}`)}else{const T=c[B].inPlay.splice(m,1)[0];let D=(B+1)%v.length;for(;!c[D].isAlive;)D=(D+1)%v.length;c[D].inPlay.push(T),E(`${i.character.name} passed dynamite to ${c[D].character.name}`)}}if(i.character.abilityType==="DRAW_CHOICE"){if(i.character.name==="Jesse Jones"){const y=ye(s,2);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length}else if(i.character.name==="Kit Carlson"){const y=ye(s,3);y.drawnCards.length>=3?(c[B].hand.push(...y.drawnCards.slice(0,2)),X([...U,y.drawnCards[2]]),d=2):(c[B].hand.push(...y.drawnCards),d=y.drawnCards.length),s=y.updatedDeck}else if(i.character.name==="Pedro Ramirez")if(U.length>0){const y=U[U.length-1];c[B].hand.push(y),X(U.slice(0,-1)),d=1;const T=ye(s,1);T.drawnCards.length>0&&(c[B].hand.push(...T.drawnCards),s=T.updatedDeck,d=2)}else{const y=ye(s,2);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length}}else if(i.character.abilityType==="ON_DRAW"){const y=ye(s,2);if(c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length,y.drawnCards.length>=2&&(y.drawnCards[1].suit===z.HEARTS||y.drawnCards[1].suit===z.DIAMONDS)){const T=ye(s,1);T.drawnCards.length>0&&(c[B].hand.push(...T.drawnCards),s=T.updatedDeck,d=y.drawnCards.length+T.drawnCards.length,E(`${i.character.name} drew an extra card (Black Jack ability)!`))}}else if(i.character.abilityType==="INJURY_DRAW"){const y=i.character.life-i.health,T=1+y,D=ye(s,T);c[B].hand.push(...D.drawnCards),s=D.updatedDeck,d=D.drawnCards.length,E(`${i.character.name} drew ${T} cards (1 + ${y} injuries)!`)}else if(i.character.abilityType==="ENHANCED_DRAW"){const y=ye(s,3);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length,E(`${i.character.name} drew 3 cards!`)}else{const y=ye(s,2);c[B].hand.push(...y.drawnCards),s=y.updatedDeck,d=y.drawnCards.length}if(i.character.name==="Suzy Lafayette"&&c[B].hand.length===0){const y=ye(s,1);y.drawnCards.length>0&&(c[B].hand.push(...y.drawnCards),s=y.updatedDeck,E(`${i.character.name} drew a card (Suzy Lafayette ability)!`))}q("cardDraw"),le(B,"card-draw-effect",1e3),xi(),V(c),M(s),Wa(!0),qa("play"),E(`${i.name} drew ${d} cards. Now play cards or end turn.`),hl(!1)},[la,ba,dt,v,B,$,U,V,M,Wa,qa,E,X,q,le,A,xi,ye,hl]),sn=(i,c,s=null)=>{if(i!==B){E("It's not your turn!");return}if(la!=="play"){E(la==="draw"?"You must draw cards first!":"You can only play cards during the play phase!");return}const d=v[i],h=d.hand[c];if(q("cardPlay"),Li(`play-${i}-${c}`,"cardPlay",600),d.character.name==="Calamity Janet")if(h.type===f.MISSED&&s!==null){Kt(i,c,s,!0);return}else h.type,f.BANG;switch(h.type){case f.BANG:if(s===null){E("Select a target for BANG!");return}Kt(i,c,s);break;case f.MISSED:d.character.name==="Calamity Janet"&&s!==null?Kt(i,c,s,!0):E("Missed! cards can only be played defensively!");break;case f.BEER:ns(i,c);break;case f.PANIC:if(s===null){E("Select a target for Panic!");return}us(i,c,s);break;case f.CAT_BALOU:if(s===null){E("Select a target for Cat Balou!");return}is(i,c,s);break;case f.STAGECOACH:cs(i,c);break;case f.WELLS_FARGO:_u(i,c);break;case f.GATLING:ss(i,c);break;case f.INDIANS:rs(i,c);break;case f.DUEL:if(s===null){E("Select a target for Duel!");return}fs(i,c,s);break;case f.GENERAL_STORE:ds(i,c);break;case f.SALOON:os(i,c);break;case f.BARREL:case f.SCOPE:case f.MUSTANG:case f.VOLCANIC:case f.SCHOFIELD:case f.REMINGTON:case f.REV_CARABINE:case f.WINCHESTER:Gu(i,c);break;case f.JAIL:if(s===null){E("Select a target for Jail!");return}hs(i,c,s);break;case f.DYNAMITE:ms(i,c);break;case f.DODGE:Gu(i,c);break;case f.PUNCH:if(s===null){E("Select a target for Punch!");return}ys(i,c,s);break;case f.BRAWL:gs(i,c);break;case f.RAG_TIME:Vi(i,c);break;case f.TEQUILA:Xi(i,c,s);break;case f.WHISKY:Qi(i,c);break;case f.SPRINGFIELD:if(s===null){E("Select a target for Springfield!");return}Tl(i,c,s);break;case f.BINOCULAR:case f.HIDEOUT:case f.BIBLE:case f.CAN_CAN:case f.CANTEEN:case f.CONESTOGA:case f.IRON_PLATE:case f.PONY_EXPRESS:case f.SOMBRERO:case f.TEN_GALLON_HAT:case f.DERRINGER:case f.HOWITZER:case f.PEPPERBOX:case f.KNIFE:case f.BUFFALO_RIFLE:Gu(i,c);break;default:E(`Card ${h.type} not implemented yet`)}},Kt=(i,c,s,d=!1,h=null)=>{const m=v[i],y=v[s],T=m.hand[c];if(y.character.name==="Apache Kid"&&T.suit===z.DIAMONDS){E(`${T.type} (Diamond) cannot affect Apache Kid!`);return}if(!d&&!St(m)){E(`${m.name} has already played a BANG! card this turn!`);return}if(!Du(i,s,v)){E("Target is out of range!");return}const D=[...v],L=D[i].hand[c];ee(L,i,s),D[i].hand.splice(c,1),X(J=>[...J,L]),d||Rt(Mn+1);let Q;h===f.PUNCH?Q="Punch":d?Q="Missed! (as BANG!)":Q="BANG!",E(`${m.name} played ${Q} on ${y.name}!`);let F=!1;if(Bt(y)&&!y.isBot){Jl({type:"BANG",attackerIndex:i,targetIndex:s,cardIndex:c,isSubstitution:d}),_i({type:"BARREL_DEFENSE",playerIndex:s,message:`${y.name}, do you want to use your Barrel defense?`}),ra(!0);return}else Bt(y)&&y.isBot&&Mt(y)&&(F=!0,E(`${y.character.name} defended with Barrel!`));if(!F){const J=m.character.name==="Slab the Killer"?2:1;let ue=0;if(na(D[s],f.BIBLE)&&ue<J&&Ne(s,f.BIBLE)){ue++;const Ue=ye($,1);Ue.drawnCards.length>0&&(D[s].hand.push(...Ue.drawnCards),M(Ue.updatedDeck)),E(`${y.name} used Bible to defend and drew 1 card!`)}if(na(D[s],f.HIDEOUT)&&ue<J&&Ne(s,f.HIDEOUT)){const Ue=ye($,1);if(Ue.drawnCards.length>0){const Ge=Ue.drawnCards[0];D[s].hand.push(Ge),M(Ue.updatedDeck),Ge.suit===z.SPADES?(ue++,E(`${y.name} used Hideout, drew ${Ge.type} of Spades - BANG! missed!`)):E(`${y.name} used Hideout, drew ${Ge.type} of ${Ge.suit} - BANG! still hits!`)}}na(D[s],f.SOMBRERO)&&ue<J&&Ne(s,f.SOMBRERO)&&(ue++,E(`${y.name} used Sombrero to defend against BANG!!`)),na(D[s],f.IRON_PLATE)&&ue<J&&Ne(s,f.IRON_PLATE)&&(ue++,E(`${y.name} used Iron Plate to defend against BANG!!`)),na(D[s],f.TEN_GALLON_HAT)&&ue<J&&Ne(s,f.TEN_GALLON_HAT)&&(ue++,E(`${y.name} used Ten Gallon Hat to defend against BANG!!`));for(let Ue=ue;Ue<J;Ue++){const Ge=D[s].hand.findIndex(Ht=>Ht.type===f.MISSED||Ht.type===f.DODGE||D[s].character.name==="Calamity Janet"&&Ht.type===f.BANG);if(Ge>=0){const Ht=D[s].hand.splice(Ge,1)[0];if(X([...U,Ht]),ue++,Ht.type===f.DODGE){const pa=ye($,1);pa.drawnCards.length>0&&(D[s].hand.push(...pa.drawnCards),M(pa.updatedDeck)),E(`${y.name} used Dodge to defend and drew 1 card!`)}else D[s].character.name==="Calamity Janet"&&Ht.type===f.BANG&&E(`${y.name} (Calamity Janet) used BANG! as Missed!`)}else break}ue>=J&&(F=!0,E(`${y.character.name} defended with ${ue} defensive card(s)`))}F?Bt(y)?le(s,"barrel-defense-effect",1e3):le(s,"dodge-effect",800):(D[s].health-=1,le(s,"damage-effect",1500),A(s,1,"damage"),Ma(D[s],"ON_DAMAGE_TAKEN",{attackerIndex:i}),D[s].health<=0?(D[s].isAlive=!1,le(s,"elimination-effect",2e3),w(),q("dominating"),oa(D,s),E(`${y.character.name} (${y.role}) was eliminated!`),s===0&&!D[0].isBot&&rt(!0),Ba(D)):(q("godlike"),E(`${y.character.name} lost a life point! Health: ${D[s].health}`))),V(D)},ns=(i,c)=>{const s=v[i];if(v.filter(L=>L.isAlive).length<3){We("Beer can only be used when there are 3 or more players alive!",!0,4e3);return}if(s.health>=s.maxHealth){We(`${s.name} is already at maximum health! Cannot play Beer.`,!0,4e3);return}const h=[...v],m=h[i].hand.splice(c,1)[0];X([...U,m]);const y=s.character.name==="Tequila Joe"?2:1,T=Math.min(y,s.character.life-s.health);h[i].health+=T,q("heal"),le(i,"healing-effect",2e3),A(i,T,"heal");const D=s.character.name==="Tequila Joe"?`${s.name} (Tequila Joe) gained ${T} life points from Beer! Health: ${h[i].health}`:`${s.name} gained ${T} life point! Health: ${h[i].health}`;E(D),V(h)},us=(i,c,s)=>{const d=v[i],h=v[s];if(Pa(i,s,v)!==1){E("Panic can only target players at distance 1!");return}if(h.hand.length===0&&h.inPlay.length===0){E("Target has no cards to steal!");return}const m=[...v],y=m[i].hand.splice(c,1)[0];X([...U,y]);const T=[...h.hand,...h.inPlay],D=Math.floor(Math.random()*T.length),L=T[D];D<h.hand.length?m[s].hand.splice(D,1):m[s].inPlay.splice(D-h.hand.length,1),m[i].hand.push(L),E(`${d.character.name} stole ${L.type} from ${h.character.name}!`),V(m)},is=(i,c,s)=>{const d=v[i],h=v[s],m=h.inPlay.filter(F=>F.type!==f.JAIL),y=[...h.hand,...m];if(y.length===0){E("Target has no cards that can be discarded!");return}const T=[...v],D=T[i].hand.splice(c,1)[0];X([...U,D]);const L=Math.floor(Math.random()*y.length),Q=y[L];if(L<h.hand.length){const F=h.hand.findIndex(J=>J===Q);T[s].hand.splice(F,1)}else{const F=h.inPlay.findIndex(J=>J===Q);T[s].inPlay.splice(F,1)}X([...U,Q]),E(`${d.character.name} forced ${h.character.name} to discard ${Q.type}!`),V(T)},cs=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);const m=ye($,2);d[i].hand.push(...m.drawnCards),M(m.updatedDeck),E(`${s.character.name} drew ${m.drawnCards.length} cards with Stagecoach!`),V(d)},_u=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);const m=ye($,3);d[i].hand.push(...m.drawnCards),M(m.updatedDeck),E(`${s.character.name} drew ${m.drawnCards.length} cards with Wells Fargo!`),V(d)},ss=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);for(let m=0;m<d.length;m++)if(m!==i&&d[m].isAlive){const y=d[m];let T=!1;if(Bt(y)&&Mt(y)&&(T=!0,E(`${y.character.name} defended against Gatling with Barrel!`)),!T){const D=y.hand.findIndex(L=>L.type===f.MISSED||y.character.abilityType==="CARD_SUBSTITUTION"&&L.type===f.BANG);if(D>=0){const L=d[m].hand.splice(D,1)[0];X([...U,L]),T=!0}}T||(d[m].health-=1,d[m].health<=0?(d[m].isAlive=!1,oa(d,m),q("dominating"),E(`${y.character.name} (${y.role}) was eliminated by Gatling!`)):q("godlike"))}E(`${s.character.name} played Gatling!`),V(d),Ba(d)},rs=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);for(let m=0;m<d.length;m++)if(m!==i&&d[m].isAlive){const y=d[m],T=y.hand.findIndex(D=>D.type===f.BANG||y.character.name==="Calamity Janet"&&D.type===f.MISSED);if(T>=0){const D=d[m].hand.splice(T,1)[0];X([...U,D])}else d[m].health-=1,d[m].health<=0?(d[m].isAlive=!1,oa(d,m),q("dominating"),E(`${y.character.name} (${y.role}) was eliminated by Indians!`)):q("godlike")}E(`${s.character.name} played Indians!`),V(d),Ba(d)},fs=(i,c,s)=>{const d=v[i],h=v[s],m=[...v],y=m[i].hand.splice(c,1)[0];X([...U,y]),E(`${d.character.name} challenged ${h.character.name} to a duel!`),Mu(i,s,!0,m)},Mu=(i,c,s,d)=>{const h=s?i:c,m=d[h],y=s?c:i,T=m.hand.findIndex(D=>D.type===f.BANG||m.character.name==="Calamity Janet"&&D.type===f.MISSED);if(T>=0){const D=d[h].hand.splice(T,1)[0];X(F=>[...F,D]);const L=D.type===f.MISSED?"Missed! (as BANG!)":"BANG!";E(`${m.character.name} played ${L} in the duel!`),V([...d]);const Q=m.isBot?1e3:1500;setTimeout(()=>{Mu(i,c,!s,d)},Q)}else d[h].health-=1,Ma(d[h],"ON_DAMAGE_TAKEN",{attackerIndex:y}),d[h].health<=0?(d[h].isAlive=!1,q("dominating"),oa(d,h),E(`${m.character.name} (${m.role}) was eliminated in the duel!`),Ba(d)):(q("godlike"),E(`${m.character.name} lost the duel and 1 life point! Health: ${d[h].health}`)),V(d)},ds=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);const m=d.filter(L=>L.isAlive),y=ye($,m.length),T=y.drawnCards;M(y.updatedDeck);const D=[];for(let L=0;L<v.length;L++){const Q=(i+L)%v.length;v[Q].isAlive&&D.push(Q)}Fl(T),Ve(D),Ja(0),Vt(!0),V(d),E(`${s.character.name} played General Store! ${m.length} cards revealed. Starting with ${s.character.name}, each player chooses one card clockwise.`)},wi=i=>{if(!ta[i])return;const c=Ae[ve],s=ta[i],d=v[c];if(c!==Ae[ve])return;const h=ta.filter((T,D)=>D!==i);Fl(h);const m=[...v];m[c].hand.push(s),V(m),E(`${d.character.name} chose ${s.type}!`);const y=ve+1;Ja(y),y>=Ae.length||h.length===0?setTimeout(()=>{Vt(!1),Fl([]),Ve([]),Ja(0),h.length>0?(X(T=>[...T,...h]),E(`General Store complete! All players chose their cards. ${h.length} card(s) discarded.`)):E("General Store complete! All cards were chosen by players.")},1e3):setTimeout(()=>{const T=Ae[y],D=v[T].character.name;E(`${D}'s turn to choose from General Store (${h.length} cards left).`)},800)},Bu=()=>{if(!bt||ta.length===0||Ae.length===0||ve>=Ae.length)return;const i=Ae[ve],c=v[i];if(!c||!c.isBot)return;let s=0,d=Ga(ta[0],c,[]);for(let h=1;h<ta.length;h++){const m=Ga(ta[h],c,[]);m>d&&(d=m,s=h)}setTimeout(()=>{wi(s)},300)},os=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]),d.forEach(m=>{m.isAlive&&m.health<m.maxHealth&&(m.health+=1)}),E(`${s.name} played Saloon! All players regained 1 life point.`),V(d)},Gu=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];if(d[i].inPlay.findIndex(y=>y.type===h.type)>=0){d[i].hand.push(h),V(d),E(`${s.name} already has ${h.type} equipped! Cannot have duplicate equipment.`);return}if(h.equipmentType===ka.WEAPON){const y=d[i].inPlay.findIndex(T=>T.equipmentType===ka.WEAPON);if(y>=0){const T=d[i].inPlay.splice(y,1)[0];X([...U,T]),E(`${s.name} replaced ${T.type} with ${h.type}!`)}else E(`${s.name} equipped ${h.type}!`)}else h.equipmentType===ka.GREEN?E(`${s.name} equipped ${h.type}! (Can be used starting next turn)`):E(`${s.name} equipped ${h.type}!`);h.equipmentType===ka.GREEN&&Il(y=>new Set([...y,`${i}-${h.type}`])),d[i].inPlay.push(h),V(d)},hs=(i,c,s)=>{const d=v[i],h=v[s];if(h.role===pe.SHERIFF){We("Cannot jail the Sheriff!",!0,4e3);return}if(h.inPlay.some(D=>D.type===f.JAIL)){We(`${h.character.name} is already in jail! Cannot jail the same player twice.`,!0,4e3);return}const y=[...v],T=y[i].hand.splice(c,1)[0];y[s].inPlay.push(T),le(s,"jail-effect",500),E(`${d.character.name} put ${h.character.name} in jail!`),V(y)},ms=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];d[i].inPlay.push(h),E(`${s.character.name} placed Dynamite!`),V(d)},ys=(i,c,s)=>{if(Pa(i,s,v)!==1){E("Punch can only target players at distance 1!");return}Kt(i,c,s,!0,f.PUNCH)},gs=(i,c)=>{const s=v[i];if(s.hand.length<2){E("You need at least 2 cards to play Brawl (Brawl + another card)!");return}if(s.isBot){const d=[...v],h=d[i].hand.splice(c,1)[0],m=d[i].hand.splice(0,1)[0];X([...U,h,m]);let y=0;d.forEach((T,D)=>{if(D!==i&&T.isAlive){if(T.hand.length>0){const L=T.hand.splice(0,1)[0];X(Q=>[...Q,L]),y++}else if(T.inPlay.length>0){const L=T.inPlay.splice(0,1)[0];X(Q=>[...Q,L]),y++}}}),E(`${s.character.name} played Brawl! All other players discarded ${y} cards.`),V(d)}else{const d=[...v],h=d[i].hand.splice(c,1)[0];V(d),X([...U,h]),$t({type:"BRAWL_ADDITIONAL_DISCARD",playerIndex:i,brawlCard:h,message:"Choose an additional card to discard for Brawl:"}),Gn(!0)}},Vn=(i,c,s)=>{const d=v[i],h=v[c];if(s===f.DERRINGER&&Pa(i,c,v)!==1){E("Derringer can only target players at range 1!");return}const m=[...v];E(`${d.name} used ${s} on ${h.name}!`);let y=!1;if(Bt(h)&&h.isBot&&Mt(h)&&(y=!0,E(`${h.character.name} defended with Barrel!`)),!y){const T=d.character.name==="Slab the Killer"?2:1;let D=0;if(na(m[c],f.BIBLE)&&D<T&&Ne(c,f.BIBLE)){D++;const L=ye($,1);L.drawnCards.length>0&&(m[c].hand.push(...L.drawnCards),M(L.updatedDeck)),E(`${h.name} used Bible to defend and drew 1 card!`)}na(m[c],f.SOMBRERO)&&D<T&&Ne(c,f.SOMBRERO)&&(D++,E(`${h.name} used Sombrero to defend!`));for(let L=D;L<T;L++){const Q=m[c].hand.findIndex(F=>F.type===f.MISSED||F.type===f.DODGE||m[c].character.name==="Calamity Janet"&&F.type===f.BANG);if(Q>=0){const F=m[c].hand.splice(Q,1)[0];if(X(J=>[...J,F]),D++,F.type===f.DODGE){const J=ye($,1);J.drawnCards.length>0&&(m[c].hand.push(...J.drawnCards),M(J.updatedDeck)),E(`${h.name} used Dodge to defend and drew 1 card!`)}}else break}D>=T&&(y=!0,E(`${h.character.name} defended with ${D} defensive card(s)`))}y||(m[c].health-=1,Ma(m[c],"ON_DAMAGE_TAKEN",{attackerIndex:i}),m[c].health<=0?(m[c].isAlive=!1,oa(m,c),q("dominating"),E(`${h.character.name} was eliminated by ${s}!`),c===0&&!m[0].isBot&&rt(!0),Ba(m)):(q("godlike"),E(`${h.character.name} lost a life point! Health: ${m[c].health}`))),V(m)},rn=(i,c,s)=>{const d=v[i];if(B!==i){E("You can only activate green cards on your turn!");return}if(!na(d,c)){E("This green card cannot be used right now!");return}switch(c){case f.DERRINGER:{if(Pa(i,s,v)!==1){E("Derringer can only target players at range 1!");return}Ne(i,c),Vn(i,s,c);const h=ye($,1);if(h.drawnCards.length>0){const m=[...v];m[i].hand.push(...h.drawnCards),V(m),M(h.updatedDeck),E(`${d.name} used Derringer and drew 1 card!`)}break}case f.CAN_CAN:{Ne(i,c);const h=[...v],m=v[s];if(m.hand.length>0||m.inPlay.length>0)if(m.isBot){if(m.hand.length>0){const y=m.hand.map((D,L)=>({card:D,index:L,priority:Ga(D,m,[])}));y.sort((D,L)=>D.priority-L.priority);const T=h[s].hand.splice(y[0].index,1)[0];X(D=>[...D,T]),E(`${d.name} used Can Can! ${m.character.name} discarded ${T.type} from hand.`)}else if(m.inPlay.length>0){const y=h[s].inPlay.splice(0,1)[0];X(T=>[...T,y]),E(`${d.name} used Can Can! ${m.character.name} discarded ${y.type} from equipment.`)}}else{xn(s),Nu(i),Ln(!0),E(`${d.name} used Can Can! ${m.character.name}, choose a card to discard:`),V(h);return}else E(`${d.name} used Can Can on ${m.character.name}, but they have no cards to discard!`);V(h);break}default:E(`${c} targeting not implemented yet!`);break}},vs=(i,c)=>{const s=v[i];if(B!==i){E("You can only activate green cards on your turn!");return}if(!na(s,c)){E("This green card cannot be used right now!");return}switch(c){case f.DERRINGER:{const d=v.map((h,m)=>({player:h,index:m})).filter(({player:h,index:m})=>h.isAlive&&m!==i&&Pa(i,m,v)===1);if(d.length===0){E("No targets at range 1 for Derringer!");return}if(d.length===1){Ne(i,c),Vn(i,d[0].index,c);const h=ye($,1);if(h.drawnCards.length>0){const m=[...v];m[i].hand.push(...h.drawnCards),V(m),M(h.updatedDeck),E(`${s.name} used Derringer and drew 1 card!`)}}else Z({playerIndex:i,cardIndex:-1,card:{type:c}}),fe(!0);break}case f.HOWITZER:{Ne(i,c);const d=v.map((y,T)=>({player:y,index:T})).filter(({player:y,index:T})=>T!==i&&y.isAlive);let h=0;const m=[...v];for(const{player:y,index:T}of d){let D=!1;if(Bt(y)&&y.isBot&&Mt(y)){D=!0,E(`${y.character.name} defended with Barrel against Howitzer!`);continue}if(!D){let Q=0;if(na(m[T],f.BIBLE)&&Q<1&&Ne(T,f.BIBLE)){Q++;const F=ye($,1);F.drawnCards.length>0&&(m[T].hand.push(...F.drawnCards),M(F.updatedDeck)),E(`${y.character.name} used Bible to defend against Howitzer and drew 1 card!`)}if(na(m[T],f.SOMBRERO)&&Q<1&&Ne(T,f.SOMBRERO)&&(Q++,E(`${y.character.name} used Sombrero to defend against Howitzer!`)),na(m[T],f.IRON_PLATE)&&Q<1&&Ne(T,f.IRON_PLATE)&&(Q++,E(`${y.character.name} used Iron Plate to defend against Howitzer!`)),na(m[T],f.TEN_GALLON_HAT)&&Q<1&&Ne(T,f.TEN_GALLON_HAT)&&(Q++,E(`${y.character.name} used Ten Gallon Hat to defend against Howitzer!`)),y.isBot&&Q<1){const F=m[T].hand.findIndex(J=>J.type===f.MISSED||J.type===f.DODGE||m[T].character.name==="Calamity Janet"&&J.type===f.BANG);if(F>=0){const J=m[T].hand.splice(F,1)[0];if(X(ue=>[...ue,J]),Q++,J.type===f.DODGE){const ue=ye($,1);ue.drawnCards.length>0&&(m[T].hand.push(...ue.drawnCards),M(ue.updatedDeck)),E(`${y.character.name} used Dodge to defend against Howitzer and drew 1 card!`)}else E(`${y.character.name} used Missed! to defend against Howitzer!`)}}Q>=1&&(D=!0)}D||(m[T].health-=1,h++,Ma(m[T],"ON_DAMAGE_TAKEN",{attackerIndex:i}),m[T].health<=0?(m[T].isAlive=!1,oa(m,T),q("dominating"),E(`${y.character.name} was eliminated by Howitzer!`),T===0&&!m[0].isBot&&rt(!0),Ba(m)):(q("godlike"),E(`${y.character.name} lost a life point to Howitzer! Health: ${m[T].health}`)))}V(m),E(`${s.name} used Howitzer! ${h} players took damage.`);break}case f.PONY_EXPRESS:{Ne(i,c);const d=ye($,3);if(d.drawnCards.length>0){const h=[...v];h[i].hand.push(...d.drawnCards),V(h),M(d.updatedDeck),E(`${s.name} used Pony Express and drew ${d.drawnCards.length} cards!`)}else E(`${s.name} used Pony Express but no cards were available to draw!`);break}default:E(`${c} activation not implemented yet!`);break}},Yi=i=>{if(!yl)return;const{type:c,playerIndex:s}=yl,d=[...v];if(c==="BRAWL_ADDITIONAL_DISCARD"){const h=d[s].hand.splice(i,1)[0];X(y=>[...y,h]);let m=0;d.forEach((y,T)=>{if(T!==s&&y.isAlive){if(y.hand.length>0){const D=y.hand.splice(0,1)[0];X(L=>[...L,D]),m++}else if(y.inPlay.length>0){const D=y.inPlay.splice(0,1)[0];X(L=>[...L,D]),m++}}}),E(`${v[s].character.name} played Brawl! All other players discarded ${m} cards.`)}V(d),Gn(!1),$t(null)},Vi=(i,c)=>{const s=v[i],d=[...v],h=d[i].hand.splice(c,1)[0];X([...U,h]);let m=[...$];d.forEach(y=>{if(y.isAlive&&m.length>0){const T=m.shift();if(y.hand.push(T),y.isBot&&y.hand.length>0){const D=y.hand.reduce((Q,F,J)=>Ga(F,y,[])<Ga(Q.card,y,[])?{card:F,index:J}:Q,{card:y.hand[0],index:0}),L=y.hand.splice(D.index,1)[0];X(Q=>[...Q,L])}}}),M(m),E(`${s.character.name} played Rag Time! All players drew and discarded 1 card.`),V(d)},Xi=(i,c,s=null)=>{const d=v[i];if(d.hand.length<2){E("You need at least 2 cards to play Tequila (Tequila + another card)!");return}const h=s!==null?s:i,m=v[h];if(m.health>=m.character.life){E(`${m.character.name} is already at maximum health!`);return}if(d.isBot){const y=[...v],T=y[i].hand.splice(c,1)[0],D=y[i].hand.map((F,J)=>({card:F,index:J,priority:Ga(F,d,[])}));D.sort((F,J)=>F.priority-J.priority);const L=y[i].hand.splice(D[0].index,1)[0];X([...U,T,L]),y[h].health=Math.min(y[h].health+1,y[h].character.life),le(h,"heal-effect",1e3),A(h,1,"heal");const Q=h===i?"themselves":m.character.name;E(`${d.character.name} played Tequila on ${Q} who regained 1 life! Health: ${y[h].health}`),V(y)}else Un(c),jn(h),en(!0),E("Choose a card to discard along with Tequila:")},$i=i=>{const c=v[B],s=v[Xe],d=[...v],h=d[B].hand.splice(Hn,1)[0],m=i>Hn?i-1:i,y=d[B].hand.splice(m,1)[0];X([...U,h,y]),d[Xe].health=Math.min(d[Xe].health+1,d[Xe].character.life),le(Xe,"heal-effect",1e3),A(Xe,1,"heal");const T=Xe===B?"themselves":s.character.name;E(`${c.character.name} played Tequila on ${T} who regained 1 life! Health: ${d[Xe].health}`),V(d),en(!1),Un(null),jn(null)},Qi=(i,c)=>{const s=v[i];if(s.hand.length<2){E("You need at least 2 cards to play Whisky (Whisky + another card)!");return}if(s.health>=s.character.life){E(`${s.character.name} is already at maximum health!`);return}if(s.isBot){const d=[...v],h=d[i].hand.splice(c,1)[0],m=d[i].hand.map((D,L)=>({card:D,index:L,priority:Ga(D,s,[])}));m.sort((D,L)=>D.priority-L.priority);const y=d[i].hand.splice(m[0].index,1)[0];X([...U,h,y]);const T=Math.min(2,d[i].character.life-d[i].health);d[i].health+=T,le(i,"heal-effect",1e3),A(i,T,"heal"),E(`${s.character.name} drank Whisky and regained ${T} life! Health: ${d[i].health}`),V(d)}else vl(c),gl(!0),E("Choose a card to discard along with Whisky:")},Tl=(i,c,s)=>{const d=v[i];if(d.hand.length<2){E("You need at least 2 cards to play Springfield (Springfield + another card)!");return}if(Pa(i,s,v)>2){E("Springfield can only target players at distance 2 or less!");return}if(d.isBot){const m=[...v],y=m[i].hand.splice(c,1)[0],T=m[i].hand.map((L,Q)=>({card:L,index:Q,priority:Ga(L,d,[])}));T.sort((L,Q)=>L.priority-Q.priority);const D=m[i].hand.splice(T[0].index,1)[0];X([...U,y,D]),Kt(i,-1,s,!0,f.SPRINGFIELD)}else zn(c),Ot(s),Sa(!0),E("Choose a card to discard along with Springfield:")},Ss=i=>{const c=v[B],s=[...v],d=s[B].hand.splice(an,1)[0],h=i>an?i-1:i,m=s[B].hand.splice(h,1)[0];X([...U,d,m]);const y=Math.min(2,s[B].character.life-s[B].health);s[B].health+=y,le(B,"heal-effect",1e3),A(B,y,"heal"),E(`${c.character.name} drank Whisky and regained ${y} life! Health: ${s[B].health}`),V(s),gl(!1),vl(null)},As=i=>{const c=[...v],s=c[B].hand.splice(Sl,1)[0],d=i>Sl?i-1:i,h=c[B].hand.splice(d,1)[0];X([...U,s,h]),V(c),Kt(B,-1,Tu,!0,f.SPRINGFIELD),Sa(!1),zn(null),Ot(null)},ki=(i,c=!0)=>{const s=v[Al],d=v[Fa],h=[...v];if(c){const m=h[Fa].hand.splice(i,1)[0];X(y=>[...y,m]),E(`${s.character.name} used Can Can! ${d.character.name} discarded ${m.type} from hand.`)}else{const m=h[Fa].inPlay.splice(i,1)[0];X(y=>[...y,m]),E(`${s.character.name} used Can Can! ${d.character.name} discarded ${m.type} from equipment.`)}V(h),Ln(!1),Nu(null),xn(null)},Nl=Y.useCallback(()=>{Za();const i=v[B],c=[...v];if(i.character.name==="Suzy Lafayette"&&c[B].hand.length===0&&$.length>0){const d=$[0];c[B].hand.push(d),M($.slice(1)),E(`${i.character.name} drew a card at end of turn (Suzy Lafayette ability)!`)}V(c);let s=(B+1)%c.length;for(;!c[s].isAlive;)s=(s+1)%c.length;je(s),qa("draw"),Wa(!1),hl(!1),ml(d=>d+1),Rt(0),Il(new Set),setTimeout(()=>{le(s,"turn-start-effect",1500)},100),E(`${c[s].name}'s turn - Cards will be drawn automatically`),q("turnStart")},[v,B,$,M,V,je,qa,Wa,Rt,E,le,Za,q]),Hu=Y.useCallback((i,c)=>{const s=[...v],d=s[i],h=d.hand.map((y,T)=>({card:y,index:T,priority:Ga(y,d,[])}));h.sort((y,T)=>y.priority-T.priority);const m=[];for(let y=0;y<c;y++){const T=h[y],D=s[i].hand.splice(T.index-y,1)[0];m.push(D),X(L=>[...L,D])}V(s),E(`${d.name} discarded ${c} card${c>1?"s":""} to hand limit: ${m.map(y=>y.type).join(", ")}`),setTimeout(()=>Nl(),1e3)},[v,X,V,E,Nl]),Gt=Y.useCallback(()=>{if(!ba){E("You must draw cards before ending your turn!");return}const i=v[B],c=[...v];let s=i.health;i.character.name==="Sean Mallory"&&(s=Math.max(s,10));const d=c[B].hand.length-s;if(d>0)if(i.isBot){Hu(B,d);return}else{Ct(d),Pl([]),_n(!0),E(`You must discard ${d} card${d>1?"s":""} (hand limit: ${s})`);return}Nl()},[ba,v,B,Hu,Nl,E,Ct,Pl,_n]),fn=i=>{const c=[...Dt],s=c.indexOf(i);s>=0?c.splice(s,1):c.length<fa&&c.push(i),Pl(c)},ps=()=>{if(Dt.length!==fa){E(`Please select exactly ${fa} card${fa>1?"s":""} to discard.`);return}const i=[...v],c=[];[...Dt].sort((d,h)=>h-d).forEach(d=>{const h=i[B].hand.splice(d,1)[0];c.push(h),X(m=>[...m,h])}),V(i),_n(!1),Pl([]),Ct(0),E(`You discarded ${c.length} card${c.length>1?"s":""}: ${c.map(d=>d.type).join(", ")}`),setTimeout(()=>Nl(),1e3)},Ba=i=>{const c=i.find(T=>T.role===pe.SHERIFF),s=i.filter(T=>T.role===pe.OUTLAW&&T.isAlive),d=i.filter(T=>T.role===pe.DEPUTY&&T.isAlive),h=i.filter(T=>T.role===pe.RENEGADE&&T.isAlive),m=i[0],y=m&&!m.isBot&&m.isAlive;c.isAlive?s.length===0&&h.length===0&&(Ce("ended"),E("Game over! The Sheriff and Deputies win!"),y&&(m.role===pe.SHERIFF||m.role===pe.DEPUTY)?(_("won"),q("unstoppable")):_("lost")):h.length===1&&s.length===0&&d.length===0?(Ce("ended"),E("Game over! The Renegade wins!"),y&&m.role===pe.RENEGADE?(_("won"),q("unstoppable")):_("lost")):(Ce("ended"),E("Game over! The Outlaws win!"),y&&m.role===pe.OUTLAW?(_("won"),q("unstoppable")):_("lost"))},Uu=i=>{if(ra(!1),Dn.type==="BARREL_DEFENSE"){const c=v[Dn.playerIndex];let s=!1;i&&Mt(c)?(s=!0,E(`${c.character.name} defended with Barrel!`),q("defense")):i&&E(`${c.character.name} tried to use Barrel but failed!`),Es(s)}_i(null),Jl(null)},ju=i=>{if(ft(!1),xa.type==="MISSED_DEFENSE"&&Bi){const{attackerIndex:c,targetIndex:s,missedRequired:d,updatedPlayers:h}=Bi,m=v[s];let y=!1;if(i){let T=0;for(let D=0;D<d;D++){const L=h[s].hand.findIndex(Q=>Q.type===f.MISSED||m.character.name==="Calamity Janet"&&Q.type===f.BANG);if(L>=0){const Q=h[s].hand.splice(L,1)[0];X(F=>[...F,Q]),T++}else break}T>=d&&(y=!0,E(`${m.character.name} defended with ${T} Missed! card(s)`),q("defense"))}else E(`${m.character.name} chose not to use Missed! cards`);V(h),Ji(y,c,s)}ol(null),Au(null)},zu=()=>{Yt(!pu)},Lu=()=>{Ce("setup"),_(null),Yt(!1),V([]),M([]),X([]),je(0),E("Welcome to BANG!"),rt(!1),Vt(!1),ra(!1),ft(!1)},Zi=()=>{Yt(!1),_(null),v.length,v.filter(i=>!i.isBot).length,v.filter(i=>!i.isBot).map(i=>i.name),Ce("setup"),setTimeout(()=>{wn()},100)},Ki=()=>{qt(!ze),ze||q("click")},Ji=(i,c,s)=>{const d=v[s],h=[...v];i?le(s,"dodge-effect",800):(h[s].health-=1,le(s,"damage-effect",1500),A(s,1,"damage"),Ma(h[s],"ON_DAMAGE_TAKEN",{attackerIndex:c}),h[s].health<=0?(h[s].isAlive=!1,oa(h,s),E(`${d.character.name} (${d.role}) was eliminated!`),q("dominating"),s===0&&!h[0].isBot&&rt(!0),Ba(h)):(q("godlike"),E(`${d.character.name} lost a life point! Health: ${h[s].health}`))),V(h)},Es=i=>{if(!Mi)return;const{attackerIndex:c,targetIndex:s,isSubstitution:d}=Mi,h=v[c],m=v[s],y=[...v];if(!i){const T=h.character.name==="Slab the Killer"?2:1,D=y[s].hand.filter(L=>L.type===f.MISSED||m.character.name==="Calamity Janet"&&L.type===f.BANG);if(D.length>=T)if(m.isBot){let L=0;for(let Q=0;Q<T;Q++){const F=y[s].hand.findIndex(J=>J.type===f.MISSED||m.character.name==="Calamity Janet"&&J.type===f.BANG);if(F>=0){const J=y[s].hand.splice(F,1)[0];X(ue=>[...ue,J]),L++}else break}L>=T&&(i=!0,E(`${m.character.name} defended with ${L} Missed! card(s)`))}else{Au({attackerIndex:c,targetIndex:s,missedRequired:T,availableMissedCards:D.length,updatedPlayers:y}),ol({type:"MISSED_DEFENSE",playerIndex:s,missedRequired:T,availableMissedCards:D.length,message:`${m.character.name}, do you want to use ${T} Missed! card${T>1?"s":""} to defend?`}),ft(!0);return}}i?Bt(m)?le(s,"barrel-defense-effect",1e3):le(s,"dodge-effect",800):(y[s].health-=1,le(s,"damage-effect",1500),A(s,1,"damage"),Ma(y[s],"ON_DAMAGE_TAKEN",{attackerIndex:c}),y[s].health<=0?(y[s].isAlive=!1,oa(y,s),E(`${m.character.name} (${m.role}) was eliminated!`),q("dominating"),s===0&&!y[0].isBot&&rt(!0),Ba(y)):(q("godlike"),E(`${m.character.name} lost a life point! Health: ${y[s].health}`))),V(y)},Ts=()=>{const i=v[B];if(i.hand.length<2){E("Need at least 2 cards to use Sid Ketchum's ability!");return}if(i.health>=i.maxHealth){We("Already at maximum health!",!0,4e3);return}const c=[...v];for(let s=0;s<2;s++){const d=Math.floor(Math.random()*c[B].hand.length),h=c[B].hand.splice(d,1)[0];X([...U,h])}c[B].health+=1,le(B,"healing-effect",2e3),A(B,1,"heal"),V(c),E(`${i.character.name} used Sid Ketchum's ability to gain 1 life point!`)},Wi=Y.useCallback((i,c)=>{const s=v.filter((d,h)=>d.isAlive&&h!==i);switch(c){case pe.SHERIFF:case pe.DEPUTY:return s.filter(d=>d.role===pe.OUTLAW||d.role===pe.RENEGADE);case pe.OUTLAW:return s.filter(d=>d.role===pe.SHERIFF||d.role===pe.DEPUTY);case pe.RENEGADE:return s.length>2?s.filter(d=>d.role!==pe.RENEGADE):s;default:return s}},[v]),Ga=Y.useCallback((i,c,s)=>{let d=0;if([f.BARREL,f.MUSTANG,f.SCOPE,f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.JAIL,f.DYNAMITE].includes(i.type)&&c.inPlay.some(h=>h.type===i.type))return 0;switch(i.type){case f.BANG:s.length>0&&St(c)?d=8:St(c)||(d=0);break;case f.BEER:c.health<c.maxHealth&&(d=9);break;case f.MISSED:d=2;break;case f.BARREL:case f.MUSTANG:case f.SCOPE:d=6;break;case f.VOLCANIC:case f.SCHOFIELD:case f.REMINGTON:case f.REV_CARABINE:case f.WINCHESTER:if(!c.inPlay.some(h=>[f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER].includes(h.type)))d=7;else{const h=c.inPlay.find(T=>[f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER].includes(T.type)),m=hf[h.type]||1;(hf[i.type]||1)>m?d=6:d=1}break;case f.GATLING:s.length>=2&&(d=10);break;case f.INDIANS:s.length>=2&&(d=9);break;case f.DUEL:s.length>0&&(d=7);break;case f.PANIC:case f.CAT_BALOU:s.length>0&&(d=6);break;case f.STAGECOACH:case f.WELLS_FARGO:d=6;break;case f.SALOON:c.health<c.maxHealth?d=8:d=3;break;case f.JAIL:{s.filter(m=>m.role!==pe.SHERIFF&&!m.inPlay.some(y=>y.type===f.JAIL)).length>0?d=5:d=0;break}case f.DYNAMITE:d=4;break;case f.GENERAL_STORE:d=5;break;default:d=3}return d},[St]),Fi=Y.useCallback(async()=>{if(ht)return;const i=v[B];if(i.isBot){if(qe(!0),la==="draw"&&!ba&&(await new Promise(c=>setTimeout(c,1e3)),cn(),await new Promise(c=>setTimeout(c,500))),la==="play"){await new Promise(m=>setTimeout(m,1500));const c=Wi(B,i.role),s=[...i.hand];s.map((m,y)=>({card:m,index:y,priority:Ga(m,i,c)})).sort((m,y)=>y.priority-m.priority);const h=Math.min(3,s.length);for(let m=0;m<h;m++){let y=!1;const D=[...v[B].hand].map((L,Q)=>({card:L,index:Q,priority:Ga(L,v[B],c)}));D.sort((L,Q)=>Q.priority-L.priority);for(const L of D)if(L.priority>2){const Q=[f.BANG,f.PANIC,f.CAT_BALOU,f.DUEL,f.JAIL].includes(L.card.type);if(Q&&c.length>0){let F=-1;if(L.card.type===f.BANG||L.card.type===f.DUEL){const J=c.sort((ue,Ue)=>ue.health-Ue.health);F=v.findIndex(ue=>ue===J[0])}else if(L.card.type===f.JAIL){const J=c.filter(ue=>ue.role!==pe.SHERIFF&&!ue.inPlay.some(Ue=>Ue.type===f.JAIL));if(J.length>0){const ue=J[Math.floor(Math.random()*J.length)];F=v.findIndex(Ue=>Ue===ue)}}else{const J=c[Math.floor(Math.random()*c.length)];F=v.findIndex(ue=>ue===J)}if(F!==-1){sn(B,L.index,F),y=!0;break}}else if(!Q){sn(B,L.index),y=!0;break}}if(!y)break;await new Promise(L=>setTimeout(L,800))}await new Promise(m=>setTimeout(m,1e3)),Gt()}qe(!1)}},[ht,v,B,la,ba,cn,Gt,sn,Wi,Ga,qe]);return Y.useEffect(()=>{if(K==="playing"&&B!==-1&&v.length>0){const i=v[B];if(i&&i.isBot&&!ht){const c=setTimeout(()=>{Fi()},1e3);return()=>clearTimeout(c)}}},[B,la,K,v,ht,Fi]),Y.useEffect(()=>{bt&&ta.length===0&&(Vt(!1),Fl([]),Ve([]),Ja(0),E("General Store complete! All cards have been taken."))},[bt,ta.length]),Y.useEffect(()=>{if(bt&&Ae.length>0&&ve<Ae.length&&ta.length>0){const i=Ae[ve],c=v[i];if(c&&c.isBot){const s=setTimeout(()=>{Bu()},1200);return()=>clearTimeout(s)}}},[bt,ve,Ae,ta.length,v,Bu]),Y.useEffect(()=>()=>{ke&&clearTimeout(ke)},[ke]),Y.useEffect(()=>{if(K==="playing"&&B!==-1&&v.length>0){const i=v[B];if(i&&!i.isBot&&la==="draw"&&!ba&&!dt&&Xt.current!==ot){const c=setTimeout(()=>{cn()},500);return()=>{clearTimeout(c)}}}},[K,B,la,ba,dt,ot,cn,v]),Y.useEffect(()=>{if(K==="playing"&&B!==-1&&v.length>0){const i=v[B];i&&!i.isBot?La():Za()}else Za()},[K,B,La,Za]),g.jsxs("div",{className:`bang-game ${K==="setup"?"setup-mode":""}`,children:[g.jsxs("div",{className:`game-header ${K!=="setup"?"hidden":""}`,children:[g.jsx("h1",{children:"🤠 BANG! The Card Game "}),g.jsx("p",{className:"game-subtitle",children:"The Wild West Card Game with Official Graphics"})]}),K==="setup"&&g.jsxs("div",{className:"setup-screen",children:[g.jsxs("div",{className:"setup-form",children:[g.jsxs("div",{className:"game-mode",children:[g.jsx("label",{children:"Game Mode: "}),g.jsxs("select",{value:da,onChange:i=>{const c=parseInt(i.target.value);tn(c),he<c&&ea(c);const s=Array(c).fill("").map((d,h)=>gt[h]||"");vt(s)},children:[g.jsx("option",{value:"1",children:"Single Player (vs Bots)"}),g.jsx("option",{value:"2",children:"2 Human Players"}),g.jsx("option",{value:"3",children:"3 Human Players"}),g.jsx("option",{value:"4",children:"4 Human Players"}),g.jsx("option",{value:"5",children:"5 Human Players"}),g.jsx("option",{value:"6",children:"6 Human Players"}),g.jsx("option",{value:"7",children:"7 Human Players"})]})]}),g.jsx("div",{className:"human-players",children:Array(da).fill(0).map((i,c)=>g.jsxs("div",{className:"player-name",children:[g.jsxs("label",{children:["Player ",c+1," Name: "]}),g.jsx("input",{type:"text",value:gt[c]||"",onChange:s=>{const d=[...gt];d[c]=s.target.value,vt(d)},placeholder:`Enter Player ${c+1} name`,maxLength:"20"})]},`human-player-${c}`))}),g.jsxs("div",{className:"player-count",children:[g.jsx("label",{children:"Total Players: "}),g.jsx("select",{value:he,onChange:i=>ea(parseInt(i.target.value)),disabled:da===7,children:Array.from({length:8-da},(i,c)=>{const s=da+c;if(s<4)return null;const d=s-da;return g.jsxs("option",{value:s,children:[s," (",da," Human",da>1?"s":"",d>0?` + ${d} Bot${d>1?"s":""}`:"",")"]},s)}).filter(Boolean)})]})]}),he-da>0&&g.jsxs("p",{className:"bot-info",children:["🤖 ",he-da," AI bot",he-da>1?"s":""," will join the game and make intelligent decisions!"]}),g.jsx("button",{className:"start-button",onClick:wn,children:"Start Game"})]}),K==="playing"&&g.jsxs("div",{className:"playing-container",children:[qn&&g.jsxs("div",{className:`turn-timer ${yt<=30&&yt>10?"warning":""} ${yt<=10?"critical":""}`,children:["⏰ Time: ",it(yt)]}),g.jsx("button",{onClick:zu,className:"game-menu-button-fixed",title:"Game Menu",children:"☰"}),g.jsxs("div",{className:"top-ui-container",children:[g.jsx("div",{className:"message-box-playing",children:sa}),B!==-1&&!v[B].isBot&&g.jsxs("div",{className:"actions-right",children:[g.jsx("button",{onClick:cn,disabled:la!=="draw"||ba,className:la!=="draw"||ba?"disabled":"",children:ba?"Cards Drawn ✓":"Auto-Drawing..."}),g.jsx("button",{onClick:Gt,disabled:!ba,className:ba?"":"disabled",children:"End Turn"}),v[B].character.name==="Sid Ketchum"&&v[B].hand.length>=2&&v[B].health<v[B].maxHealth&&g.jsx("button",{onClick:()=>Ts(),children:"Sid Ketchum Ability"})]})]}),g.jsxs("div",{className:"game-board",children:[g.jsxs("div",{className:`current-player-area ${B!==0||v[0]&&v[0].isBot?"disabled":""}`,"data-player-index":"0",children:[v.length>0&&v[0]&&!v[0].isBot&&!dl&&g.jsxs("div",{className:"current-player effect-container","data-player-index":0,children:[g.jsx("div",{className:"player-status",children:g.jsxs("div",{className:"character-info",children:[g.jsxs("div",{className:"character-image-container",children:[g.jsx("img",{src:Ri[v[0].character.name],alt:v[0].character.name,className:"character-image",onContextMenu:i=>{i.preventDefault(),pl(v[0].character)},onDoubleClick:()=>{pl(v[0].character)},onTouchStart:()=>At({isCharacter:!0,character:v[0].character,type:v[0].character.name}),onTouchEnd:Zt,onTouchMove:El}),g.jsxs("div",{className:"player-name-overlay",children:[v[0].name,v[0].isBot&&g.jsx("span",{className:"bot-indicator-overlay",children:" 🤖"})]}),v[0].isAlive&&g.jsxs("div",{className:"health-display-overlay",children:["❤️ ",v[0].health,"/",v[0].maxHealth]})]}),!v[0].isAlive&&g.jsxs("div",{className:"role-display-below",children:["💀 ",v[0].role]}),g.jsxs("div",{className:"character-details",children:[kt(v[0])&&g.jsx("div",{className:"sheriff-badge-current",children:"⭐"}),g.jsx("div",{className:"role-display",children:g.jsx("img",{src:km[v[0].role],alt:v[0].role,className:"role-image"})})]})]})}),g.jsxs("div",{className:"hand-area",children:[g.jsxs("h4",{children:["Your Hand (",v[0].hand.length," cards)",g.jsxs("span",{className:"hand-limit-info",children:["- Hand Limit: ",v[0].health]})]}),g.jsx("div",{className:"hand-cards",children:v[0]&&v[0].hand&&Array.isArray(v[0].hand)&&v[0].hand.map((i,c)=>{if(!i||!i.type)return null;const s=`play-0-${c}`,d=za[s];return g.jsxs("div",{"data-card-index":c,className:`hand-card ${[f.BARREL,f.MUSTANG,f.SCOPE,f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.JAIL,f.DYNAMITE].includes(i.type)&&v[0].inPlay.some(h=>h.type===i.type)?"duplicate-equipment":""} ${i.type===f.BANG&&!St(v[0])?"bang-limit-reached":""} ${i.type===f.BEER&&v[0].health>=v[0].maxHealth?"beer-unplayable":""} ${d?`card-${d.type}-animation`:""}`,onClick:()=>{if(Pc)return;if(B!==0){E("It's not your turn!");return}[f.BANG,f.PANIC,f.CAT_BALOU,f.DUEL,f.JAIL,f.PUNCH,f.TEQUILA].includes(i.type)?(Z({playerIndex:0,cardIndex:c,card:i}),fe(!0)):sn(0,c)},onContextMenu:h=>{h.preventDefault(),Fe(i)},onDoubleClick:()=>{Fe(i)},onMouseEnter:()=>ga(i),onMouseLeave:()=>ga(null),onTouchStart:h=>ts(h,i,c),onTouchEnd:ls,onTouchMove:Ou,title:[f.BARREL,f.MUSTANG,f.SCOPE,f.VOLCANIC,f.SCHOFIELD,f.REMINGTON,f.REV_CARABINE,f.WINCHESTER,f.JAIL,f.DYNAMITE].includes(i.type)&&v[0].inPlay.some(h=>h.type===i.type)?`${i.type} - ${i.suit} ${i.value} (DUPLICATE - Cannot play!) | Right-click or double-click to preview`:i.type===f.BANG&&!St(v[0])?`${i.type} - ${i.suit} ${i.value} (BANG! limit reached - Need Volcanic or Willy the Kid!) | Right-click or double-click to preview`:i.type===f.BEER&&v[0].health>=v[0].maxHealth?`${i.type} - ${i.suit} ${i.value} (Already at maximum health - Cannot play!) | Right-click or double-click to preview`:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"hand-card-image",onError:h=>{h.target.style.display="none",h.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},`hand-${c}-${i.type}-${i.suit}-${i.value}-${Date.now()}`)})})]}),v[0].inPlay.length>0&&g.jsxs("div",{className:"equipment-area",children:[g.jsx("h4",{children:"Equipment:"}),g.jsx("div",{className:"equipment-cards",children:v[0]&&v[0].inPlay&&Array.isArray(v[0].inPlay)&&v[0].inPlay.map((i,c)=>!i||!i.type?null:g.jsx("div",{className:`equipment-card ${i.equipmentType===ka.GREEN?"green-card":""} ${i.equipmentType===ka.GREEN&&na(v[0],i.type)?"usable-green":""}`,title:`${i.type} ${i.equipmentType===ka.GREEN?"(Green - Click to activate)":""}`,onClick:()=>{i.equipmentType===ka.GREEN&&na(v[0],i.type)&&vs(0,i.type)},onContextMenu:s=>{s.preventDefault(),Fe(i)},onDoubleClick:()=>{Fe(i)},onTouchStart:()=>At(i),onTouchEnd:Zt,onTouchMove:El,children:g.jsx("img",{src:Da[i.type],alt:i.type,className:"equipment-card-image"})},`equipment-${c}-${i.type}-${Date.now()}`))})]})]}),dl&&g.jsx("div",{className:"spectator-mode",children:g.jsxs("div",{className:"spectator-info",children:[g.jsx("h3",{children:"👻 Spectator Mode"}),g.jsx("p",{children:"You have been eliminated but are watching the game continue."}),g.jsx("p",{children:"The game will end when a winning condition is met."}),g.jsx("button",{className:"exit-spectator-button",onClick:Yn,children:"Exit to Main Menu"})]})})]}),g.jsx("div",{className:"central-area",children:g.jsxs("div",{className:"game-info",children:[g.jsxs("div",{className:"deck-info",children:[g.jsxs("h4",{children:["Deck (",$.length,")"]}),g.jsx("div",{className:"deck-card",children:g.jsx("img",{src:Qm,alt:"Card Back",className:"deck-image"})})]}),g.jsxs("div",{className:"discard-info",children:[g.jsxs("h4",{children:["Discard (",U.length,")"]}),g.jsx("div",{className:`discard-card discard-pile ${U.length===0?"empty":""}`,children:U.length>0&&U[U.length-1]&&U[U.length-1].type&&g.jsx("img",{src:Da[U[U.length-1].type],alt:U[U.length-1].type,className:"discard-image",onContextMenu:i=>{i.preventDefault(),Fe(U[U.length-1])},onDoubleClick:()=>{Fe(U[U.length-1])},onTouchStart:()=>At(U[U.length-1]),onTouchEnd:Zt,onTouchMove:El,title:`${U[U.length-1].type} - ${U[U.length-1].suit} ${U[U.length-1].value} | Right-click, double-click, or touch-hold to preview`})})]})]})}),g.jsx("div",{className:"other-players-area",children:v.map((i,c)=>c!==0&&g.jsxs("div",{className:`other-player ${i.isAlive?"":"dead-player"} effect-container`,"data-player-index":c,children:[g.jsxs("div",{className:"player-info",children:[g.jsxs("div",{className:"character-image-container",children:[g.jsx("img",{src:Ri[i.character.name],alt:i.character.name,className:"character-image",onError:s=>{s.target.style.display="none"},onContextMenu:s=>{s.preventDefault(),pl(i.character)},onDoubleClick:()=>{pl(i.character)},onTouchStart:()=>At({isCharacter:!0,character:i.character,type:i.character.name}),onTouchEnd:Zt,onTouchMove:El}),g.jsxs("div",{className:"player-name-overlay",children:[i.name,i.isBot&&g.jsx("span",{className:"bot-indicator-overlay",children:" 🤖"})]}),i.isAlive&&g.jsxs("div",{className:"health-display-overlay",children:["❤️ ",i.health,"/",i.maxHealth]})]}),!i.isAlive&&g.jsxs("div",{className:"role-display-below",children:["💀 ",i.role]}),g.jsxs("div",{className:"player-details",children:[kt(i)&&g.jsx("div",{className:"sheriff-badge-below",children:"⭐"}),g.jsxs("div",{className:"cards-count",children:["🃏 ",i.hand.length]})]})]}),i.inPlay.length>0&&g.jsx("div",{className:"other-player-equipment",children:i&&i.inPlay&&Array.isArray(i.inPlay)&&i.inPlay.map((s,d)=>!s||!s.type?null:g.jsx("div",{className:"small-card",title:s.type,onContextMenu:h=>{h.preventDefault(),Fe(s)},onDoubleClick:()=>{Fe(s)},onTouchStart:()=>At(s),onTouchEnd:Zt,onTouchMove:El,children:g.jsx("img",{src:Da[s.type],alt:s.type,className:"small-card-image"})},`player-${c}-equipment-${d}-${s.type}-${Date.now()}`))})]},`other-player-${c}-${i.character.name}`))})]})]}),K==="ended"&&g.jsxs("div",{className:"game-over",children:[g.jsx("div",{className:"game-result-header",children:Te==="won"?g.jsxs(g.Fragment,{children:[g.jsx("h2",{className:"victory-title",children:"🎉 Victory! 🎉"}),g.jsx("p",{className:"victory-subtitle",children:"Congratulations! You have won the game!"})]}):Te==="lost"?g.jsxs(g.Fragment,{children:[g.jsx("h2",{className:"defeat-title",children:"💀 Defeat 💀"}),g.jsx("p",{className:"defeat-subtitle",children:"Better luck next time!"})]}):g.jsxs(g.Fragment,{children:[g.jsx("h2",{children:"Game Over!"}),g.jsx("p",{children:"The game has ended."})]})}),g.jsx("div",{className:"final-roles",children:v.map((i,c)=>g.jsxs("div",{className:`player-result ${i.isAlive?"":"dead-player"}`,children:[g.jsx("img",{src:Ri[i.character.name],alt:i.character.name,className:"character-image-result",onError:s=>{s.target.style.display="none"},onContextMenu:s=>{s.preventDefault(),pl(i.character)},onDoubleClick:()=>{pl(i.character)},onTouchStart:()=>At({isCharacter:!0,character:i.character,type:i.character.name}),onTouchEnd:Zt,onTouchMove:El}),g.jsxs("h3",{children:[i.name,i.isBot&&g.jsx("span",{className:"bot-indicator-small",children:" 🤖"})]}),kt(i)&&g.jsx("div",{className:"sheriff-badge-result",children:"⭐"}),g.jsx("p",{className:"character-name",children:i.character.name}),g.jsx("p",{className:"role",children:i.role}),g.jsx("p",{className:"status",children:i.isAlive?"Survived":"Eliminated"})]},`final-result-${c}-${i.character.name}`))}),g.jsx("button",{className:"restart-button",onClick:()=>{Ce("setup"),V([]),je(0),M([]),X([]),qa("draw"),Wa(!1),Rt(0),tn(1),vt([""]),E("Welcome to BANG!")},children:"Play Again"})]}),I&&k&&g.jsxs("div",{className:"target-selection",children:[g.jsxs("h3",{children:["Select a target for ",k.card.type]}),g.jsx("div",{className:"target-options",children:v.map((i,c)=>i.isAlive&&(k.card.type===f.JAIL?B!==c&&i.role!==pe.SHERIFF&&!i.inPlay.some(d=>d.type===f.JAIL):k.card.type===f.TEQUILA?!0:B!==c)&&g.jsxs("div",{className:`target-option ${k.card.type===f.JAIL&&(i.role===pe.SHERIFF||i.inPlay.some(d=>d.type===f.JAIL))?"invalid-target":""}`,onClick:()=>{k.cardIndex===-1?rn(k.playerIndex,k.card.type,c):sn(k.playerIndex,k.cardIndex,c),fe(!1),Z(null)},children:[g.jsxs("div",{className:"target-player-name",children:[i.name,i.isBot&&g.jsx("span",{className:"bot-indicator-small",children:" 🤖"}),kt(i)&&g.jsx("span",{className:"sheriff-badge-target",children:" ⭐"}),k.card.type===f.JAIL&&i.inPlay.some(d=>d.type===f.JAIL)&&g.jsx("span",{className:"jail-indicator",children:" 🔒 IN JAIL"})]}),g.jsx("div",{className:"target-character-name",children:i.character.name}),g.jsxs("div",{className:"target-info",children:["Health: ",i.health," | Distance: ",Pa(B,c,v)]})]},`target-${c}-${i.character.name}`))}),g.jsx("button",{onClick:()=>{fe(!1),Z(null)},children:"Cancel"})]}),Ui&&g.jsxs("div",{className:"discard-selection",children:[g.jsx("h3",{children:"Discard Cards - Hand Limit Exceeded"}),g.jsxs("p",{children:["You must discard ",fa," card",fa>1?"s":""," (Hand limit: ",v[B].health,")"]}),g.jsx("div",{className:"discard-cards",children:v[B]&&v[B].hand&&Array.isArray(v[B].hand)&&v[B].hand.map((i,c)=>!i||!i.type?null:g.jsxs("div",{className:`discard-card ${Dt.includes(c)?"selected":""}`,onClick:()=>fn(c),onContextMenu:s=>{s.preventDefault(),Fe(i)},onDoubleClick:()=>{Fe(i)},title:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"discard-card-image"}),g.jsx("div",{className:"card-name",children:i.type}),Dt.includes(c)&&g.jsx("div",{className:"selected-indicator",children:"✓"})]},`discard-${c}-${i.type}-${i.suit}-${i.value}-${Date.now()}`))}),g.jsx("div",{className:"discard-actions",children:g.jsxs("button",{onClick:ps,disabled:Dt.length!==fa,className:Dt.length!==fa?"disabled":"",children:["Discard Selected Cards (",Dt.length,"/",fa,")"]})})]}),Ic&&g.jsx("div",{className:"death-modal-overlay",children:g.jsx("div",{className:"death-modal",children:g.jsxs("div",{className:"death-modal-content",children:[g.jsx("h2",{children:"💀 You Have Been Eliminated!"}),g.jsx("p",{children:"Your character has been eliminated from the game."}),g.jsx("p",{children:"What would you like to do?"}),g.jsxs("div",{className:"death-modal-buttons",children:[g.jsx("button",{className:"spectate-button",onClick:un,children:"👻 Spectate Game"}),g.jsx("button",{className:"exit-button",onClick:Yn,children:"🚪 Exit to Main Menu"})]})]})})}),bt&&ta.length>0&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"general-store-modal",children:[g.jsx("h3",{children:"General Store"}),g.jsx("p",{children:Ae.length>0&&ve<Ae.length?`${(Aa=v[Ae[ve]])==null?void 0:Aa.character.name}'s turn to choose`:"Choose a card"}),g.jsxs("p",{className:"general-store-instruction",children:["Cards available: ",ta.length," | Players remaining: ",Ae.length-ve]}),g.jsx("p",{className:"general-store-rule",children:"Rule: Starting with the player who played General Store, each player chooses one card clockwise."}),g.jsx("div",{className:"general-store-cards",children:ta.map((i,c)=>{var s;return!i||!i.type?null:g.jsxs("div",{className:`general-store-card ${(s=v[Ae[ve]])!=null&&s.isBot?"disabled":""}`,onClick:()=>{var h;const d=Ae[ve];!((h=v[d])!=null&&h.isBot)&&d===Ae[ve]&&ta[c]&&wi(c)},onContextMenu:d=>{d.preventDefault(),Fe(i)},onDoubleClick:()=>{Fe(i)},title:`${i.type} - ${i.suit} ${i.value} | Right-click or double-click to preview`,children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"general-store-card-image"}),g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.value,i.suit]})]},`general-store-${c}-${i.type}-${i.suit}-${i.value}`)})}),Ae.length>0&&ve<Ae.length&&g.jsx("div",{className:"general-store-info",children:g.jsxs("p",{children:["Turn order: ",Ae.map((i,c)=>{var s;return`${c===ve?"→ ":""}${(s=v[i])==null?void 0:s.character.name}${c===ve?" ←":""}`}).join(" → ")]})}),g.jsxs("div",{className:"general-store-actions",children:[g.jsx("button",{className:"close-general-store-button",onClick:()=>{Vt(!1),Fl([]),Ve([]),Ja(0),E("General Store cancelled.")},children:"Cancel General Store"}),Ae.length>0&&ve<Ae.length&&((bl=v[Ae[ve]])==null?void 0:bl.isBot)&&g.jsx("button",{className:"force-bot-selection-button",onClick:()=>Bu(),children:"Force Bot Selection"})]})]})}),Nt&&Dn&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"ability-choice-modal",children:[g.jsx("h3",{children:"Character Ability"}),g.jsx("p",{children:Dn.message}),g.jsxs("div",{className:"ability-choice-buttons",children:[g.jsx("button",{className:"ability-yes-button",onClick:()=>Uu(!0),children:"Use Ability"}),g.jsx("button",{className:"ability-no-button",onClick:()=>Uu(!1),children:"Don't Use"})]})]})}),Rn&&xa&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"missed-choice-modal",children:[g.jsx("h3",{children:"Defend with Missed!"}),g.jsx("p",{children:xa.message}),g.jsxs("div",{className:"missed-choice-info",children:[g.jsxs("p",{children:["You have ",xa.availableMissedCards," Missed! card",xa.availableMissedCards>1?"s":""," available."]}),g.jsxs("p",{children:["You need ",xa.missedRequired," Missed! card",xa.missedRequired>1?"s":""," to defend."]})]}),g.jsxs("div",{className:"missed-choice-buttons",children:[g.jsxs("button",{className:"missed-yes-button",onClick:()=>ju(!0),children:["Use Missed! Card",xa.missedRequired>1?"s":""]}),g.jsx("button",{className:"missed-no-button",onClick:()=>ju(!1),children:"Take Damage"})]})]})}),mt&&yl&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"card-selection-modal",children:[g.jsx("h3",{children:"Card Selection"}),g.jsx("p",{children:yl.message}),g.jsx("div",{className:"card-selection-grid",children:(xu=v[yl.playerIndex])==null?void 0:xu.hand.map((i,c)=>g.jsxs("div",{className:"selectable-card",onClick:()=>Yi(c),children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"card-selection-image",onError:s=>{s.target.style.display="none",s.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-selection-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},c))})]})}),pu&&g.jsx("div",{className:"modal-overlay",onClick:()=>Yt(!1),children:g.jsxs("div",{className:"game-menu-modal",onClick:i=>i.stopPropagation(),children:[g.jsx("h3",{children:"Game Menu"}),g.jsxs("div",{className:"game-menu-buttons",children:[g.jsx("button",{className:"menu-button",onClick:Ki,children:ze?"🔊 Sound: ON":"🔇 Sound: OFF"}),g.jsx("button",{className:"menu-button",onClick:Zi,children:"🔄 Restart Game"}),g.jsx("button",{className:"menu-button",onClick:Lu,children:"🏠 Main Menu"}),g.jsx("button",{className:"menu-button menu-close",onClick:()=>Yt(!1),children:"✕ Close"})]})]})}),Wl.map(i=>{if(!i||!i.card||!i.card.type)return null;const c=document.querySelector(`[data-player-index="${i.fromPlayerIndex}"]`),s=i.toPlayerIndex>=0?document.querySelector(`[data-player-index="${i.toPlayerIndex}"]`):document.querySelector(".discard-pile"),d=document.querySelector(".discard-pile");if(!c||!s&&i.phase==="toTarget"||!d&&i.phase==="toDiscard")return null;const h=c.getBoundingClientRect(),m=i.phase==="toTarget"?s.getBoundingClientRect():d.getBoundingClientRect(),y=h.left+h.width/2,T=h.top+h.height/2,D=m.left+m.width/2,L=m.top+m.height/2,Q=y+(D-y)*i.progress,F=T+(L-T)*i.progress,J=i.phase==="toTarget"?1+i.progress*.2:1.2-i.progress*.2,ue=i.progress*360;return g.jsx("div",{className:`animating-card ${i.card.type==="BANG!"?"bang-card":""}`,style:{left:Q-30,top:F-42,transform:`scale(${J}) rotate(${ue}deg)`,opacity:1-i.progress*.1},children:g.jsxs("div",{className:`card ${i.card.suit==="♥"||i.card.suit==="♦"?"red":"black"}`,children:[g.jsx("div",{children:i.card.type||"Unknown"}),g.jsxs("div",{children:[i.card.value||"",i.card.suit||""]})]})},i.id)}),Su&&me&&g.jsx("div",{className:"card-zoom-overlay",onClick:Ru,children:g.jsx("div",{className:"card-zoom-modal",onClick:i=>i.stopPropagation(),children:g.jsxs("div",{className:"card-zoom-content",children:[g.jsxs("div",{className:"card-zoom-image",children:[g.jsx("img",{src:me.isCharacter?Ri[me.type]:Da[me.type],alt:me.type,className:"zoomed-card-image",onError:i=>{i.target.style.display="none",i.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-zoom-fallback",style:{display:"none"},children:[g.jsx("div",{className:"zoomed-card-name",children:me.type}),g.jsxs("div",{className:"zoomed-card-suit",children:[me.suit," ",me.value]})]})]}),g.jsx("div",{className:"card-description",children:me.isCharacter?me.character.ability:qi(me.type)})]})})}),_a&&g.jsx("div",{className:"card-zoom-overlay",onClick:()=>Kl(null),children:g.jsx("div",{className:"card-zoom-modal",onClick:i=>i.stopPropagation(),children:g.jsxs("div",{className:"card-zoom-content",children:[g.jsxs("div",{className:"card-zoom-image",children:[g.jsx("img",{src:_a.isCharacter?Ri[_a.type]:Da[_a.type],alt:_a.type,className:"zoomed-card-image",onError:i=>{i.target.style.display="none",i.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-zoom-fallback",style:{display:"none"},children:[g.jsx("div",{className:"zoomed-card-name",children:_a.type}),g.jsxs("div",{className:"zoomed-card-suit",children:[_a.suit," ",_a.value]})]})]}),g.jsxs("div",{className:"card-description",children:[_a.isCharacter?(qu=_a.character)==null?void 0:qu.ability:qi(_a.type),g.jsxs("div",{className:"long-press-hint",children:["💡 ",g.jsx("strong",{children:"Mobile Tip:"})," Hold cards for 2s to zoom, tap quickly to play"]})]})]})})}),Eu&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard with Tequila"}),g.jsx("div",{className:"card-selection-grid",children:(wu=v[B])==null?void 0:wu.hand.filter((i,c)=>c!==Hn).map((i,c)=>{const s=c>=Hn?c+1:c;return g.jsxs("div",{className:"selectable-card",onClick:()=>$i(s),children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"card-image",onError:d=>{d.target.style.display="none",d.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},s)})}),g.jsx("button",{className:"cancel-button",onClick:()=>{en(!1),Un(null),jn(null),E("Tequila cancelled.")},children:"Cancel"})]})}),es&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard with Whisky"}),g.jsx("div",{className:"card-selection-grid",children:(Yu=v[B])==null?void 0:Yu.hand.filter((i,c)=>c!==an).map((i,c)=>{const s=c>=an?c+1:c;return g.jsxs("div",{className:"selectable-card",onClick:()=>Ss(s),children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"card-image",onError:d=>{d.target.style.display="none",d.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},s)})}),g.jsx("button",{className:"cancel-button",onClick:()=>{gl(!1),vl(null),E("Whisky cancelled.")},children:"Cancel"})]})}),as&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard with Springfield"}),g.jsx("div",{className:"card-selection-grid",children:(Xn=v[B])==null?void 0:Xn.hand.filter((i,c)=>c!==Sl).map((i,c)=>{const s=c>=Sl?c+1:c;return g.jsxs("div",{className:"selectable-card",onClick:()=>As(s),children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"card-image",onError:d=>{d.target.style.display="none",d.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},s)})}),g.jsx("button",{className:"cancel-button",onClick:()=>{Sa(!1),zn(null),Ot(null),E("Springfield cancelled.")},children:"Cancel"})]})}),ji&&Fa!==null&&g.jsx("div",{className:"modal-overlay",children:g.jsxs("div",{className:"modal",children:[g.jsx("h3",{children:"Choose a card to discard (Can Can)"}),g.jsxs("p",{children:[(Pi=v[Fa])==null?void 0:Pi.character.name," must discard a card"]}),((Jt=v[Fa])==null?void 0:Jt.hand.length)>0&&g.jsxs("div",{children:[g.jsx("h4",{children:"Hand Cards:"}),g.jsx("div",{className:"card-selection-grid",children:($n=v[Fa])==null?void 0:$n.hand.map((i,c)=>g.jsxs("div",{className:"selectable-card",onClick:()=>ki(c,!0),children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"card-image",onError:s=>{s.target.style.display="none",s.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},`hand-${c}`))})]}),((Cl=v[Fa])==null?void 0:Cl.inPlay.length)>0&&g.jsxs("div",{children:[g.jsx("h4",{children:"Equipment Cards:"}),g.jsx("div",{className:"card-selection-grid",children:(Qn=v[Fa])==null?void 0:Qn.inPlay.map((i,c)=>g.jsxs("div",{className:"selectable-card",onClick:()=>ki(c,!1),children:[g.jsx("img",{src:Da[i.type],alt:i.type,className:"card-image",onError:s=>{s.target.style.display="none",s.target.nextSibling.style.display="block"}}),g.jsxs("div",{className:"card-fallback",style:{display:"none"},children:[g.jsx("div",{className:"card-name",children:i.type}),g.jsxs("div",{className:"card-suit",children:[i.suit," ",i.value]})]})]},`equipment-${c}`))})]}),g.jsx("button",{className:"cancel-button",onClick:()=>{Ln(!1),Nu(null),xn(null),E("Can Can cancelled.")},children:"Cancel"})]})}),Na.map(i=>g.jsx("div",{className:"moving-card",style:{left:i.startX,top:i.startY,transform:"translate(-50%, -50%)","--end-x":`${i.endX-i.startX}px`,"--end-y":`${i.endY-i.startY}px`},children:g.jsx("img",{src:Da[i.card.type]||"/images/cards/card-back.png",alt:i.card.type,onError:c=>{c.target.src="/images/cards/card-back.png"}})},i.id))]})}$m.createRoot(document.getElementById("root")).render(g.jsx(Y.StrictMode,{children:g.jsx(Km,{})}));
